//+------------------------------------------------------------------+
//|                                                     fsliteea.mq5 |
//|                                                        Sakis-Pit |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Sakis-Pit"
#property link      ""
#property version   "1.00"
#property indicator_chart_window
#property strict
#property indicator_buffers 6
#property indicator_color1 clrBlue
#property indicator_color2 clrRed
#include <initmql4.mqh>

//+INPUTS------------------------------------------------------------+
int periods = 8000; // Candles back to check
double dist = 0.25; // Distance of wick vs previous body

input ENUM_TIMEFRAMES perioda = PERIOD_M5;
double bobbit[], bobbit1[], bobbit2[];
double doddit[], doddit1[], doddit2[];
double Pip;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
	SetIndexStyle(0, DRAW_NONE, 0, 0);
	SetIndexBuffer(0, bobbit);
	SetIndexStyle(1, DRAW_NONE, 0, 0);
	SetIndexBuffer(1, bobbit1);
	SetIndexStyle(2, DRAW_NONE, 0, 0);
	SetIndexBuffer(2, bobbit2);
	SetIndexStyle(3, DRAW_NONE, 0, 0);
	SetIndexBuffer(3, doddit);
	SetIndexStyle(4, DRAW_NONE, 0, 0);
	SetIndexBuffer(4, doddit1);
	SetIndexStyle(5, DRAW_NONE, 0, 0);
	SetIndexBuffer(5, doddit2);
	
	return(INIT_SUCCEEDED);
  }
  
int ArraySortMQL4(double &array[],
                  int count=WHOLE_ARRAY,
                  int start=0,
                  int sort_dir=MODE_ASCEND)
  {
   switch(sort_dir)
     {
      case MODE_ASCEND:
         ArraySetAsSeries(array,true);
      case MODE_DESCEND:
         ArraySetAsSeries(array,false);

      default: ArraySetAsSeries(array,true);
     }
   ArraySortMQL4(array);
   return(0);
  }
  
  datetime iTimeMQL4(string symbol,int tf,int index)

{
   if(index < 0) return(-1);
   ENUM_TIMEFRAMES timeframe=TFMigrate(tf);
   datetime Arr[];
   if(CopyTime(symbol, timeframe, index, 1, Arr)>0)
        return(Arr[0]);
   else return(-1);
}

int iLowestMQL4(string symbol,
                int tf,
                int type,
                int count=WHOLE_ARRAY,
                int start=0)
  {
   if(start<0) return(-1);
   ENUM_TIMEFRAMES timeframe=TFMigrate(tf);
   if(count<=0) count=Bars(symbol,timeframe);
   if(type<=MODE_OPEN)
     {
      double Open[];
      ArraySetAsSeries(Open,true);
      CopyOpen(symbol,timeframe,start,count,Open);
      return(ArrayMinimum(Open,0,count)+start);
     }
   if(type==MODE_LOW)
     {
      double Low[];
      ArraySetAsSeries(Low,true);
      CopyLow(symbol,timeframe,start,count,Low);
      return(ArrayMinimum(Low,0,count)+start);
     }
   if(type==MODE_HIGH)
     {
      double High[];
      ArraySetAsSeries(High,true);
      CopyHigh(symbol,timeframe,start,count,High);
      return(ArrayMinimum(High,0,count)+start);
     }
   if(type==MODE_CLOSE)
     {
      double Close[];
      ArraySetAsSeries(Close,true);
      CopyClose(symbol,timeframe,start,count,Close);
      return(ArrayMinimum(Close,0,count)+start);
     }
   if(type==MODE_VOLUME)
     {
      long Volume[];
      ArraySetAsSeries(Volume,true);
      CopyTickVolume(symbol,timeframe,start,count,Volume);
      return(ArrayMinimum(Volume,0,count)+start);
     }
   if(type>=MODE_TIME)
     {
      datetime Time[];
      ArraySetAsSeries(Time,true);
      CopyTime(symbol,timeframe,start,count,Time);
      return(ArrayMinimum(Time,0,count)+start);
     }
//---
   return(0);
  }
  
int iHighestMQL4(string symbol,
                 int tf,
                 int type,
                 int count=WHOLE_ARRAY,
                 int start=0)
  {
   if(start<0) return(-1);
   ENUM_TIMEFRAMES timeframe=TFMigrate(tf);
   if(count<=0) count=Bars(symbol,timeframe);
   if(type<=MODE_OPEN)
     {
      double Open[];
      ArraySetAsSeries(Open,true);
      CopyOpen(symbol,timeframe,start,count,Open);
      return(ArrayMaximum(Open,0,count)+start);
     }
   if(type==MODE_LOW)
     {
      double Low[];
      ArraySetAsSeries(Low,true);
      CopyLow(symbol,timeframe,start,count,Low);
      return(ArrayMaximum(Low,0,count)+start);
     }
   if(type==MODE_HIGH)
     {
      double High[];
      ArraySetAsSeries(High,true);
      CopyHigh(symbol,timeframe,start,count,High);
      return(ArrayMaximum(High,0,count)+start);
     }
   if(type==MODE_CLOSE)
     {
      double Close[];
      ArraySetAsSeries(Close,true);
      CopyClose(symbol,timeframe,start,count,Close);
      return(ArrayMaximum(Close,0,count)+start);
     }
   if(type==MODE_VOLUME)
     {
      long Volume[];
      ArraySetAsSeries(Volume,true);
      CopyTickVolume(symbol,timeframe,start,count,Volume);
      return(ArrayMaximum(Volume,0,count)+start);
     }
   if(type>=MODE_TIME)
     {
      datetime Time[];
      ArraySetAsSeries(Time,true);
      CopyTime(symbol,timeframe,start,count,Time);
      return(ArrayMaximum(Time,0,count)+start);
      //---
     }
   return(0);
  }

double iLowMQL4(string symbol,int tf,int index)

{
   if(index < 0) return(-1);
   double Arr[];
   ENUM_TIMEFRAMES timeframe=TFMigrate(tf);
   if(CopyLow(symbol,timeframe, index, 1, Arr)>0)
        return(Arr[0]);
   else return(-1);
}

double iHighMQL4(string symbol,int tf,int index)

{
   if(index < 0) return(-1);
   double Arr[];
   ENUM_TIMEFRAMES timeframe=TFMigrate(tf);
   if(CopyHigh(symbol,timeframe, index, 1, Arr)>0) 
        return(Arr[0]);
   else return(-1);
}

double iOpenMQL4(string symbol,int tf,int index)

{   
   if(index < 0) return(-1);
   double Arr[];
   ENUM_TIMEFRAMES timeframe=TFMigrate(tf);
   if(CopyOpen(symbol,timeframe, index, 1, Arr)>0) 
        return(Arr[0]);
   else return(-1);
}

int iBarsMQL4(string symbol,int tf)
  {
   ENUM_TIMEFRAMES timeframe=TFMigrate(tf);
   return(Bars(symbol,timeframe));
  }
  
  void SetIndexStyle(int index,
                       int type,
                       int style=EMPTY,
                       int width=EMPTY,
                       color clr=CLR_NONE)
  {
   if(width>-1)
      PlotIndexSetInteger(index,PLOT_LINE_WIDTH,width);
   if(clr!=CLR_NONE)
      PlotIndexSetInteger(index,PLOT_LINE_COLOR,clr);
   switch(type)
     {
      case 0:
         PlotIndexSetInteger(index,PLOT_DRAW_TYPE,DRAW_LINE);
      case 1:
         PlotIndexSetInteger(index,PLOT_DRAW_TYPE,DRAW_SECTION);
      case 2:
         PlotIndexSetInteger(index,PLOT_DRAW_TYPE,DRAW_HISTOGRAM);
      case 3:
         PlotIndexSetInteger(index,PLOT_DRAW_TYPE,DRAW_ARROW);
      case 4:
         PlotIndexSetInteger(index,PLOT_DRAW_TYPE,DRAW_ZIGZAG);
      case 12:
         PlotIndexSetInteger(index,PLOT_DRAW_TYPE,DRAW_NONE);

      default:
         PlotIndexSetInteger(index,PLOT_DRAW_TYPE,DRAW_LINE);
     }
   switch(style)
     {
      case 0:
         PlotIndexSetInteger(index,PLOT_LINE_STYLE,STYLE_SOLID);
      case 1:
         PlotIndexSetInteger(index,PLOT_LINE_STYLE,STYLE_DASH);
      case 2:
         PlotIndexSetInteger(index,PLOT_LINE_STYLE,STYLE_DOT);
      case 3:
         PlotIndexSetInteger(index,PLOT_LINE_STYLE,STYLE_DASHDOT);
      case 4:
         PlotIndexSetInteger(index,PLOT_LINE_STYLE,STYLE_DASHDOTDOT);

      default: return;
     }
    }
double iCloseMQL4(string symbol,int tf,int index)
{
   if(index < 0) return(-1);
   double Arr[];
   ENUM_TIMEFRAMES timeframe=TFMigrate(tf);
   if(CopyClose(symbol,timeframe, index, 1, Arr)>0) 
        return(Arr[0]);
   else return(-1);
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
  {
//---
   
  }
//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
  {
//---
   
  }
//+------------------------------------------------------------------+

//+MAIN PROGRAM------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	bool new_1h_check = false;
	static datetime start_1h_time = 0;
	if (start_1h_time < iTimeMQL4(NULL, PERIOD_CURRENT, 0))
	{
		new_1h_check = true;
		start_1h_time = iTimeMQL4(NULL, PERIOD_CURRENT, 0);
	}
	if (new_1h_check)
	{
		bigtfnumbers(perioda);
		new_1h_check = false;
	}
	return(rates_total);
}
//+------------------------------------------------------------------+

//+BIG TF LINES BOX--------------------------------------------------+
void bigtfnumbers(const int periodb) {
	static datetime start_mont_time = 0, start_week_time = 0, start_dail_time = 0, start_four_time = 0, start_hour_time = 0, start_thir_time = 0, start_fift_time = 0, start_five_time = 0;
	
	// 1 Month
	bool new_mont_check = false;
	if (start_mont_time < iTimeMQL4(NULL, periodb, 0))
	{
		new_mont_check = true;
		start_mont_time = iTimeMQL4(NULL, periodb, 0);
	}
	if (new_mont_check)
	{
		double month[], montl[];
		double monco[], monol[], monoc[], monho[];
		ArrayResize(month, iBarsMQL4(_Symbol, periodb)); ArrayResize(montl, iBarsMQL4(_Symbol, periodb));
		ArrayResize(monco, iBarsMQL4(_Symbol, periodb)); ArrayResize(monol, iBarsMQL4(_Symbol, periodb)); ArrayResize(monoc, iBarsMQL4(_Symbol, periodb)); ArrayResize(monho, iBarsMQL4(_Symbol, periodb));
		
		int x = 1;
		while (x >= 1 && x <= iBarsMQL4(_Symbol, periodb) - 2) {
		   if ((iCloseMQL4(_Symbol, periodb, x + 1) - iOpenMQL4(_Symbol, periodb, x + 1)) > 0 && (iCloseMQL4(_Symbol, periodb, x) - iOpenMQL4(_Symbol, periodb, x)) > 0)
			{
				monco[x] = iCloseMQL4(_Symbol, periodb, x + 1) - iOpenMQL4(_Symbol, periodb, x + 1);
				monol[x] = iOpenMQL4(_Symbol, periodb, x) - iLowMQL4(_Symbol, periodb, x);
				if (monol[x] < dist*monco[x] && iOpenMQL4(_Symbol, periodb, x + 1) + 0.75 * monco[x] < iLowMQL4(_Symbol, periodb, x) && iCloseMQL4(_Symbol, periodb, iLowestMQL4(_Symbol, periodb, MODE_CLOSE, x - 1, 1)) > iLowMQL4(_Symbol, periodb, x + 1)) { month[x] = iLowMQL4(_Symbol, periodb, x + 1); }
				else { month[x] = EMPTY_VALUE; }
			}
			x++;
		}
		
		/*for (int x = iBarsMQL4(_Symbol, periodb) - 2; x >= 1; x--) {
			if ((iCloseMQL4(_Symbol, periodb, x + 1) - iOpenMQL4(_Symbol, periodb, x + 1)) > 0 && (iCloseMQL4(_Symbol, periodb, x) - iOpenMQL4(_Symbol, periodb, x)) > 0)
			{
				monco[x] = iCloseMQL4(_Symbol, periodb, x + 1) - iOpenMQL4(_Symbol, periodb, x + 1);
				monol[x] = iOpenMQL4(_Symbol, periodb, x) - iLowMQL4(_Symbol, periodb, x);
				if (monol[x] < dist*monco[x] && iOpenMQL4(_Symbol, periodb, x + 1) + 0.75 * monco[x] < iLowMQL4(_Symbol, periodb, x) && iCloseMQL4(_Symbol, periodb, iLowMQL4est(_Symbol, periodb, MODE_CLOSE, x - 1, 1)) > iLowMQL4(_Symbol, periodb, x + 1)) { month[x] = iLowMQL4(_Symbol, periodb, x + 1); }
				else { month[x] = EMPTY_VALUE; }
			}
		}*/
		
		int y = 1;
		while (y >= 1 && y <= iBarsMQL4(_Symbol, periodb) - 2) {
		   if ((iOpenMQL4(_Symbol, periodb, x + 1) - iCloseMQL4(_Symbol, periodb, x + 1)) > 0 && (iOpenMQL4(_Symbol, periodb, x) - iCloseMQL4(_Symbol, periodb, x)) > 0)
			{
				monoc[x] = iOpenMQL4(_Symbol, periodb, x + 1) - iCloseMQL4(_Symbol, periodb, x + 1);
				monho[x] = iHighMQL4(_Symbol, periodb, x) - iOpenMQL4(_Symbol, periodb, x);
				if (monho[x] < dist*monoc[x] && iOpenMQL4(_Symbol, periodb, x + 1) - 0.75 * monoc[x] > iHighMQL4(_Symbol, periodb, x) && iCloseMQL4(_Symbol, periodb, iHighestMQL4(_Symbol, periodb, MODE_CLOSE, x - 1, 1)) < iHighMQL4(_Symbol, periodb, x + 1)) { montl[x] = iHighMQL4(_Symbol, periodb, x + 1); }
				else { montl[x] = EMPTY_VALUE; }
			}
			y++;
		}
		
		/*for (int x = iBarsMQL4(_Symbol, periodb) - 2; x >= 1; x--) {
			if ((iOpenMQL4(_Symbol, periodb, x + 1) - iCloseMQL4(_Symbol, periodb, x + 1)) > 0 && (iOpenMQL4(_Symbol, periodb, x) - iCloseMQL4(_Symbol, periodb, x)) > 0)
			{
				monoc[x] = iOpenMQL4(_Symbol, periodb, x + 1) - iCloseMQL4(_Symbol, periodb, x + 1);
				monho[x] = iHighMQL4(_Symbol, periodb, x) - iOpenMQL4(_Symbol, periodb, x);
				if (monho[x] < dist*monoc[x] && iOpenMQL4(_Symbol, periodb, x + 1) - 0.75 * monoc[x] > iHighMQL4(_Symbol, periodb, x) && iCloseMQL4(_Symbol, periodb, iHighMQL4est(_Symbol, periodb, MODE_CLOSE, x - 1, 1)) < iHighMQL4(_Symbol, periodb, x + 1)) { montl[x] = iHighMQL4(_Symbol, periodb, x + 1); }
				else { montl[x] = EMPTY_VALUE; }
			}
		}*/
		{//1 Month
			double newmonth[], newmontl[];
			ArrayResize(newmonth, iBarsMQL4(_Symbol, periodb)); ArrayResize(newmontl, iBarsMQL4(_Symbol, periodb));

			int a = 0;
			int b = EMPTY;
			while (a >= 0 && a <= ArraySize(month) - 1) {
			   if (month[a] != EMPTY_VALUE && month[a] > 0.00001) {
					++b; ArrayResize(newmonth, b + 1); newmonth[b] = month[a];
				}
				a++;
			}
			
			/*for (int y = ArraySize(month) - 1, z = EMPTY; y >= 0; y--) {
				if (month[y] != EMPTY_VALUE && month[y] > 0.00001) {
					++z; ArrayResize(newmonth, z + 1); newmonth[z] = month[y];
				}
			}*/
			
			int i = 0;
			int j = EMPTY;
			while (i >= 0 && i <= ArraySize(montl) - 1) {
			   if (montl[i] != EMPTY_VALUE && montl[i] > 0.00001) {
					++j; ArrayResize(newmontl, j + 1); newmontl[j] = montl[i];
				}
				i++;
			}
			
			/*for (int y = ArraySize(montl) - 1, z = EMPTY; y >= 0; y--) {
				if (montl[y] != EMPTY_VALUE && montl[y] > 0.00001) {
					++z; ArrayResize(newmontl, z + 1); newmontl[z] = montl[y];
				}
			}*/

			ArraySortMQL4(newmonth, 0, 0, MODE_DESCEND); ArraySortMQL4(newmontl, 0, 0, MODE_ASCEND);
			if (ArraySize(newmonth) >= 1) bobbit[0] = newmonth[0]; else bobbit[0] = 0;
			if (ArraySize(newmontl) >= 1) doddit[0] = newmontl[0]; else doddit[0] = 0;
			if (ArraySize(newmonth) >= 2) bobbit1[0] = newmonth[1]; else bobbit1[0] = 0;
			if (ArraySize(newmontl) >= 2) doddit1[0] = newmontl[1]; else doddit1[0] = 0;
			if (ArraySize(newmonth) >= 3) bobbit2[0] = newmonth[2]; else bobbit2[0] = 0;
			if (ArraySize(newmontl) >= 3) doddit2[0] = newmontl[2]; else doddit2[0] = 0;
			
		}
		new_mont_check = false;
	}
}