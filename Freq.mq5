#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict
#property indicator_plots 0
#define Name MQLInfoString(MQL_PROGRAM_NAME)

extern int periods = 750; // Previous bars to calculate (0 = all)
//bool useR = true; // Use range of previous bar as filter
//bool filterpast = true; // Filter based on similar previous bars
//bool dist = false; // Show stats for candles with exact high and low
extern int nod = 10; //Number of days to draw (mid max 100, days max 25)
input string test = "";

//double shown[13], showp[13];
double shownd[13][101], showpd[13][101];
double pasthfinder[], pastlfinder[];

bool nodata = false;

bool showpast = false;

//+INIT FUNCTION-----------------------------------------------------+
int OnInit()
{
	//Static builds
	/*
	{
		string obname;
		string PercsP[13] = { "-50/-20", "-20/0", "0/20", "20/40", "40/60", "60/80", "80/100", "100/120", "120/140", "140/160", "160/180", "180/200", "200+" };
		string PercsN[13] = { "50/20", "20/0", "0/-20", "-20/-40", "-40/-60", "-60/-80", "-80/-100", "-100/-120", "-120/-140", "-140/-160", "-160/-180", "-180/-200", "-200-" };
		for (int x = 0; x <= 12; x++) {
			obname = Name + "PercsP" + IntegerToString(x); LabelMake(obname, 2, 50 + 65 * x, 75, PercsP[x], 8, "Arial", clrBlue);
			obname = Name + "PercsN" + IntegerToString(x); LabelMake(obname, 2, 50 + 65 * x, 45, PercsN[x], 8, "Arial", clrRed);
		}
		obname = Name + "PercsPT" + IntegerToString(0); LabelMake(obname, 2, 875, 75, "", 8, "Arial", clrBlue);
		obname = Name + "PercsNT" + IntegerToString(0); LabelMake(obname, 2, 875, 45, "", 8, "Arial", clrRed);
		if (dist) { obname = Name + "PercsTT" + IntegerToString(0); LabelMake(obname, 2, 875, 60, "", 8, "Arial", clrBlack); }
	}
	*/

	if (nod > 100)
		nod = 100;
	if (ChartPeriod() <= 16388)
	{
		freq();
		for (int x = nod; x >= 0; x--)
		{
         datetime dt = iTime(_Symbol,PERIOD_D1,x);
			if (x <= 25)
			{
            if (TimeDayOfWeek(dt)==FRIDAY || TimeDayOfWeek(dt)==SATURDAY) continue;
				freqd(x);
			}
			if (TimeDayOfWeek(dt)==FRIDAY || TimeDayOfWeek(dt)==SATURDAY) continue;
			buildd(x);
		}
	}
	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+CALCULATE---------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	datetime expiry = D'2021.06.30 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("Freq expired on " + TimeToString(expiry, TIME_DATE) + ", contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true && ChartPeriod() <= 16388)
	{

		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, PERIOD_D1, 0)) // && (TimeCurrent() >= (iTime(NULL, PERIOD_D1, 0) + 35) && TimeCurrent() <= (iTime(NULL, PERIOD_D1, 0) + 4020)))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, PERIOD_D1, 0);
		}
		if (new_1m_check)
		{
			//int start1 = GetTickCount();
			freq();
			//int end1 = GetTickCount() - start1; Print (end1);
			//int start2 = GetTickCount();
			for (int x = nod; x >= 1; x--)
			{
         datetime dt = iTime(_Symbol,PERIOD_D1,x);
   			if (x <= 25)
   			{
               if (TimeDayOfWeek(dt)==FRIDAY || TimeDayOfWeek(dt)==SATURDAY) continue;
   				freqd(x);
   			}
   			if (TimeDayOfWeek(dt)==FRIDAY || TimeDayOfWeek(dt)==SATURDAY) continue;
   			buildd(x);
   		}
   			//int end2 = GetTickCount() - start2; Print (end2);
			new_1m_check = false;
		}

		bool new_2m_check = false;
		static datetime start_2m_time = 0;
		if (start_2m_time < iTime(NULL, PERIOD_H1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_H1, 0) + 35) && TimeCurrent() <= (iTime(NULL, PERIOD_H1, 0) + 420)))
		{
			new_2m_check = true;
			start_2m_time = iTime(NULL, PERIOD_H1, 0);
		}
		if (new_2m_check)
		{
			for (int x = nod; x >= 1; x--)
			{
         datetime dt = iTime(_Symbol,PERIOD_D1,x);
   			if (x <= 25)
   			{
               if (TimeDayOfWeek(dt)==FRIDAY || TimeDayOfWeek(dt)==SATURDAY) continue;
   				freqd(x);
   			}
   			if (TimeDayOfWeek(dt)==FRIDAY || TimeDayOfWeek(dt)==SATURDAY) continue;
   			buildd(x);
   		}
			new_2m_check = false;
		}

		bool new_3m_check = false;
		static datetime start_3m_time = 0;
		if (start_3m_time < iTime(NULL, PERIOD_M5, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_M5, 0) + 10) && TimeCurrent() <= (iTime(NULL, PERIOD_M5, 0) + 30)))
		{
			new_3m_check = true;
			start_3m_time = iTime(NULL, PERIOD_M5, 0);
		}
		if (new_3m_check)
		{
			freqd(0);
			buildd(0);
			new_3m_check = false;
		}
	}
	return (rates_total);
}
//+------------------------------------------------------------------+

//+ChartEvent function-----------------------------------------------+
void OnChartEvent(const int id,
				  const long &lparam,
				  const double &dparam,
				  const string &sparam)
{
	//---
	{ //Switch TF lines on/off
		if (id == CHARTEVENT_KEYDOWN)
		{
			if (lparam == StringGetCharacter("S", 0) && showpast == false)
			{
				showpast = true;
				for (int x = 100; x >= 1; x--)
				{
					buildd(x);
				}
			}
			else if (lparam == StringGetCharacter("S", 0) && showpast == true)
			{
				showpast = false;
				for (int x = 100; x >= 1; x--)
				{
					buildd(x);
				}
			}
		}
	}
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN CALCS--------------------------------------------------------+
void freq()
{
	if (iBars(_Symbol, PERIOD_D1) < periods || periods == 0)
		periods = iBars(_Symbol, PERIOD_D1) - 1;

	double pasth[], pastl[];
	ArrayResize(pasth, periods + 1);
	ArrayResize(pastl, periods + 1);

	ArrayResize(pastlfinder, periods + 1);
	ArrayInitialize(pastlfinder, 0);
	ArrayResize(pasthfinder, periods + 1);
	ArrayInitialize(pasthfinder, 0);

	double HD1[], LD1[];
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArrayResize(HD1, periods + 2);
	ArrayResize(LD1, periods + 2);
	CopyHigh(_Symbol, PERIOD_D1, 0, periods + 2, HD1);
	CopyLow(_Symbol, PERIOD_D1, 0, periods + 2, LD1);

	for (int x = periods - 1; x > 0; x--)
	{
		if ((HD1[x + 2] - LD1[x + 2]) != 0)
			pasth[x] = ((HD1[x + 1] - LD1[x + 2]) / (HD1[x + 2] - LD1[x + 2])) - 0.5;
		if ((HD1[x + 2] - LD1[x + 2]) != 0)
			pastl[x] = ((LD1[x + 1] - LD1[x + 2]) / (HD1[x + 2] - LD1[x + 2])) - 0.5;
	}
	//Categorize past lows
	for (int x = periods; x > 0; x--)
	{
		if (pastl[x] <= -2)
			pastlfinder[x] = 1;
		if (pastl[x] > -2 && pastl[x] <= -1.8)
			pastlfinder[x] = 2;
		if (pastl[x] > -1.8 && pastl[x] <= -1.6)
			pastlfinder[x] = 3;
		if (pastl[x] > -1.6 && pastl[x] <= -1.4)
			pastlfinder[x] = 4;
		if (pastl[x] > -1.4 && pastl[x] <= -1.2)
			pastlfinder[x] = 5;
		if (pastl[x] > -1.2 && pastl[x] <= -1.0)
			pastlfinder[x] = 6;
		if (pastl[x] > -1.0 && pastl[x] <= -0.8)
			pastlfinder[x] = 7;
		if (pastl[x] > -0.8 && pastl[x] <= -0.6)
			pastlfinder[x] = 8;
		if (pastl[x] > -0.6 && pastl[x] <= -0.4)
			pastlfinder[x] = 9;
		if (pastl[x] > -0.4 && pastl[x] <= -0.2)
			pastlfinder[x] = 10;
		if (pastl[x] > -0.2 && pastl[x] <= 0.0)
			pastlfinder[x] = 11;
		if (pastl[x] > 0.0 && pastl[x] <= 0.2)
			pastlfinder[x] = 12;
		if (pastl[x] > 0.2 && pastl[x] <= 0.5)
			pastlfinder[x] = 13;
	}

	//Categorize past highs
	for (int x = periods; x > 0; x--)
	{
		//if (pasth[x] < -0.2) pasthfinder[x] = 14;
		if (pasth[x] >= -0.5 && pasth[x] < -0.2)
			pasthfinder[x] = 14;
		if (pasth[x] >= -0.2 && pasth[x] < 0.0)
			pasthfinder[x] = 15;
		if (pasth[x] >= 0.0 && pasth[x] < 0.2)
			pasthfinder[x] = 16;
		if (pasth[x] >= 0.2 && pasth[x] < 0.4)
			pasthfinder[x] = 17;
		if (pasth[x] >= 0.4 && pasth[x] < 0.6)
			pasthfinder[x] = 18;
		if (pasth[x] >= 0.6 && pasth[x] < 0.8)
			pasthfinder[x] = 19;
		if (pasth[x] >= 0.8 && pasth[x] < 1)
			pasthfinder[x] = 20;
		if (pasth[x] >= 1.0 && pasth[x] < 1.2)
			pasthfinder[x] = 21;
		if (pasth[x] >= 1.2 && pasth[x] < 1.4)
			pasthfinder[x] = 22;
		if (pasth[x] >= 1.4 && pasth[x] < 1.6)
			pasthfinder[x] = 23;
		if (pasth[x] >= 1.6 && pasth[x] < 1.8)
			pasthfinder[x] = 24;
		if (pasth[x] >= 1.8 && pasth[x] < 2)
			pasthfinder[x] = 25;
		if (pasth[x] >= 2)
			pasthfinder[x] = 26;
	}
}
//+------------------------------------------------------------------+

//+MAIN CALC FOR D-DAYS----------------------------------------------+
void freqd(const int D)
{ //*****CH*****//
	if (iBars(_Symbol, PERIOD_D1) < periods || periods == 0)
		periods = iBars(_Symbol, PERIOD_D1) - 1;

	double closep[], closen[];
	ArrayResize(closep, periods);
	ArrayResize(closen, periods);
	double ranger[];
	ArrayResize(ranger, periods + 1);

	double Pip = (_Point * MathPow(10, MathMod(_Digits, 2)));

	double HD1[], LD1[], CD1[], OD1[];
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(OD1, true);
	ArrayResize(HD1, periods + 3);
	ArrayResize(LD1, periods + 3);
	ArrayResize(CD1, periods + 3);
	ArrayResize(OD1, periods + 3);
	CopyHigh(_Symbol, PERIOD_D1, 0, periods + 2, HD1);
	CopyLow(_Symbol, PERIOD_D1, 0, periods + 2, LD1);
	CopyClose(_Symbol, PERIOD_D1, 0, periods + 2, CD1);
	CopyOpen(_Symbol, PERIOD_D1, 0, periods + 2, OD1);

	int both = 0, botl = 0;

	double pastchd1 = 0, pastcld1 = 0;
	pastchd1 = ((HD1[D] - LD1[D + 1]) / (HD1[D + 1] - LD1[D + 1])) - 0.5; //*****CH*****//
	pastcld1 = ((LD1[D] - LD1[D + 1]) / (HD1[D + 1] - LD1[D + 1])) - 0.5; //*****CH*****//

	//Categorize low
	if (pastcld1 <= -2)
		botl = 1;
	if (pastcld1 > -2 && pastcld1 <= -1.8)
		botl = 2;
	if (pastcld1 > -1.8 && pastcld1 <= -1.6)
		botl = 3;
	if (pastcld1 > -1.6 && pastcld1 <= -1.4)
		botl = 4;
	if (pastcld1 > -1.4 && pastcld1 <= -1.2)
		botl = 5;
	if (pastcld1 > -1.2 && pastcld1 <= -1.0)
		botl = 6;
	if (pastcld1 > -1.0 && pastcld1 <= -0.8)
		botl = 7;
	if (pastcld1 > -0.8 && pastcld1 <= -0.6)
		botl = 8;
	if (pastcld1 > -0.6 && pastcld1 <= -0.4)
		botl = 9;
	if (pastcld1 > -0.4 && pastcld1 <= -0.2)
		botl = 10;
	if (pastcld1 > -0.2 && pastcld1 <= 0.0)
		botl = 11;
	if (pastcld1 > 0.0 && pastcld1 <= 0.2)
		botl = 12;
	if (pastcld1 > 0.2 && pastcld1 <= 0.5)
		botl = 13;

	//Categorize high
	//if (pastchd1 < -0.2) both = 14;
	if (pastchd1 >= -0.5 && pastchd1 < -0.2)
		both = 14;
	if (pastchd1 >= -0.2 && pastchd1 < 0.0)
		both = 15;
	if (pastchd1 >= 0.0 && pastchd1 < 0.2)
		both = 16;
	if (pastchd1 >= 0.2 && pastchd1 < 0.4)
		both = 17;
	if (pastchd1 >= 0.4 && pastchd1 < 0.6)
		both = 18;
	if (pastchd1 >= 0.6 && pastchd1 < 0.8)
		both = 19;
	if (pastchd1 >= 0.8 && pastchd1 < 1)
		both = 20;
	if (pastchd1 >= 1.0 && pastchd1 < 1.2)
		both = 21;
	if (pastchd1 >= 1.2 && pastchd1 < 1.4)
		both = 22;
	if (pastchd1 >= 1.4 && pastchd1 < 1.6)
		both = 23;
	if (pastchd1 >= 1.6 && pastchd1 < 1.8)
		both = 24;
	if (pastchd1 >= 1.8 && pastchd1 < 2)
		both = 25;
	if (pastchd1 >= 2)
		both = 26;

	double medR = 0, R1 = 0, R2 = 0, RP = 0;	//, R3 = 0, R4 = 0, RP = 0;
	bool Rb1 = false, Rb2 = false, Rb3 = false; //, Rb4 = false, Rb5 = false;

	for (int x = periods; x > D; x--)
	{ //*****CH*****//
		ranger[x] = (HD1[x] - LD1[x]) / Pip;
	}
	medR = ranger[ArrayMaximum(ranger, 0, 0)] / 3;
	R1 = medR;
	R2 = 2 * medR;				  // R3 = 3 * medR; R4 = 4 * medR;
	RP = (HD1[D] - LD1[D]) / Pip; //*****CH*****//
	if (RP > 0 && RP < R1)
		Rb1 = true;
	if (RP > R1 && RP < R2)
		Rb2 = true;
	if (RP > R2)
		Rb3 = true;

	bool mode1 = false;
	if (CD1[D] > OD1[D])
		mode1 = true;
	else
		mode1 = false; //*****CH*****//

	double upl = 0, lol = 0;
	if (Rb1)
	{
		lol = 0;
		upl = R1;
	}
	if (Rb2)
	{
		lol = R1;
		upl = R2;
	}
	if (Rb3)
	{
		lol = R2;
		upl = 3 * medR;
	}

	for (int x = periods - 1; x > D; x--)
	{ //*****CH*****//
		if (mode1)
		{
			if ((CD1[x] > OD1[x]) && (CD1[x + 1] > OD1[x + 1]) && (ranger[x + 1] > lol && ranger[x + 1] < upl) && (HD1[x + 1] - LD1[x + 1]) != 0 && pasthfinder[x] == both)
				closep[x] = ((HD1[x] - LD1[x + 1]) / (HD1[x + 1] - LD1[x + 1])) - 0.5;
			else
				closep[x] = -1;
		}

		if (!mode1)
		{
			if ((CD1[x] > OD1[x]) && (ranger[x + 1] > lol && ranger[x + 1] < upl) && (HD1[x + 1] - LD1[x + 1]) != 0 && pasthfinder[x] == both)
				closep[x] = ((HD1[x] - LD1[x + 1]) / (HD1[x + 1] - LD1[x + 1])) - 0.5;
			else
				closep[x] = -1;
		}

		if (mode1)
		{
			if ((CD1[x] < OD1[x]) && (ranger[x + 1] > lol && ranger[x + 1] < upl) && (HD1[x + 1] - LD1[x + 1]) != 0 && pastlfinder[x] == botl)
				closen[x] = ((LD1[x] - LD1[x + 1]) / (HD1[x + 1] - LD1[x + 1])) - 0.5;
			else
				closen[x] = 1;
		}

		if (!mode1)
		{
			if ((CD1[x] < OD1[x]) && (CD1[x + 1] < OD1[x + 1]) && (ranger[x + 1] > lol && ranger[x + 1] < upl) && (HD1[x + 1] - LD1[x + 1]) != 0 && pastlfinder[x] == botl)
				closen[x] = ((LD1[x] - LD1[x + 1]) / (HD1[x + 1] - LD1[x + 1])) - 0.5;
			else
				closen[x] = 1;
		}
	}

	double postot = 0, negtot = 0;

	for (int x = periods - 1; x > D; x--)
	{ //*****CH*****//
		if (closep[x] > -0.5)
			postot++;
		if (closen[x] < 0.5)
			negtot++;
	}

	double countp[13];
	ArrayInitialize(countp, 0);
	double countn[13];
	ArrayInitialize(countn, 0);

	for (int x = periods - 1; x > D; x--)
	{ //*****CH*****//
		//if (closep[x] < -0.2) countp[0]++;
		if (closep[x] > -0.5 && closep[x] <= -0.2)
			countp[0]++;
		if (closep[x] >= -0.2 && closep[x] < 0.0)
			countp[1]++;
		if (closep[x] >= 0.0 && closep[x] < 0.2)
			countp[2]++;
		if (closep[x] >= 0.2 && closep[x] < 0.4)
			countp[3]++;
		if (closep[x] >= 0.4 && closep[x] < 0.6)
			countp[4]++;
		if (closep[x] >= 0.6 && closep[x] < 0.8)
			countp[5]++;
		if (closep[x] >= 0.8 && closep[x] < 1.0)
			countp[6]++;
		if (closep[x] >= 1.0 && closep[x] < 1.2)
			countp[7]++;
		if (closep[x] >= 1.2 && closep[x] < 1.4)
			countp[8]++;
		if (closep[x] >= 1.4 && closep[x] < 1.6)
			countp[9]++;
		if (closep[x] >= 1.6 && closep[x] < 1.8)
			countp[10]++;
		if (closep[x] >= 1.8 && closep[x] < 2.0)
			countp[11]++;
		if (closep[x] >= 2)
			countp[12]++;

		//if (closen[x] > 0.2) countn[0]++;
		if (closen[x] < 0.5 && closen[x] >= 0.2)
			countn[0]++;
		if (closen[x] <= 0.2 && closen[x] > 0.0)
			countn[1]++;
		if (closen[x] <= 0.0 && closen[x] > -0.2)
			countn[2]++;
		if (closen[x] <= -0.2 && closen[x] > -0.4)
			countn[3]++;
		if (closen[x] <= -0.4 && closen[x] > -0.6)
			countn[4]++;
		if (closen[x] <= -0.6 && closen[x] > -0.8)
			countn[5]++;
		if (closen[x] <= -0.8 && closen[x] > -1.0)
			countn[6]++;
		if (closen[x] <= -1.0 && closen[x] > -1.2)
			countn[7]++;
		if (closen[x] <= -1.2 && closen[x] > -1.4)
			countn[8]++;
		if (closen[x] <= -1.4 && closen[x] > -1.6)
			countn[9]++;
		if (closen[x] <= -1.6 && closen[x] > -1.8)
			countn[10]++;
		if (closen[x] <= -1.8 && closen[x] > -2.0)
			countn[11]++;
		if (closen[x] <= -2)
			countn[12]++;
	}

	double statp[13];
	ArrayInitialize(statp, 0);
	double statn[13];
	ArrayInitialize(statn, 0);

	if (postot != 0 && negtot != 0)
	{
		nodata = false;
		for (int x = 12; x >= 0; x--)
		{
			statp[x] = (countp[x] / postot) * 100;
			statn[x] = (countn[x] / negtot) * 100;
		}
	}

	if (postot == 0 || negtot == 0)
		nodata = true;

	for (int x = 12; x >= 0; x--)
	{
		showpd[x][D] = statp[x];
		shownd[x][D] = statn[x];
	}
}
//+------------------------------------------------------------------+

//+CREATE PAST & CURRENT SHAPES--------------------------------------+
void buildd(const int D)
{ //*****CH*****//
	string obname;

	double shownc[13], showpc[13];
	ArrayInitialize(shownc, 0);
	ArrayInitialize(showpc, 0);

	shownc[12] = shownd[12][D];
	for (int x = 11; x >= 0; x--)
	{
		shownc[x] = shownc[x + 1] + shownd[x][D]; //*****CH*****//
	}
	showpc[12] = showpd[12][D];
	for (int x = 11; x >= 0; x--)
	{
		showpc[x] = showpc[x + 1] + showpd[x][D]; //*****CH*****//
	}

	double HD1[], LD1[], CD1[], OD1[];
	datetime TD1[];
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(OD1, true);
	ArraySetAsSeries(TD1, true);
	ArrayResize(HD1, periods + 2);
	ArrayResize(LD1, periods + 2);
	ArrayResize(CD1, periods + 2);
	ArrayResize(OD1, periods + 2);
	ArrayResize(TD1, periods + 2);
	CopyHigh(_Symbol, PERIOD_D1, 0, periods + 2, HD1);
	CopyLow(_Symbol, PERIOD_D1, 0, periods + 2, LD1);
	CopyClose(_Symbol, PERIOD_D1, 0, periods + 2, CD1);
	CopyOpen(_Symbol, PERIOD_D1, 0, periods + 2, OD1);
	CopyTime(_Symbol, PERIOD_D1, 0, periods + 2, TD1);

	double levels[23] = {-2, -1.8, -1.6, -1.4, -1.2, -1.0, -0.8, -0.6, -0.5, -0.4, -0.2, 0, 0.2, 0.4, 0.5, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0};

	double level0[23];
	ArrayInitialize(level0, 0);

	for (int x = 22; x >= 0; x--)
	{
		level0[x] = ((HD1[D] + LD1[D]) / 2) + (levels[x] * (HD1[D] - LD1[D])); //*****CH*****//
	}

	int p = 0;
	while (p >= 0 && p <= 12)
	{
		if (showpc[p] <= 49.99)
			break;
		p++;
	}
	int o = 0;
	while (o >= 0 && o <= 12)
	{
		if (shownc[o] <= 49.99)
			break;
		o++;
	}

	int r = 0;
	while (r >= 0 && r <= 12)
	{
		if (showpc[r] <= 69.99)
			break;
		r++;
	}
	int s = 0;
	while (s >= 0 && s <= 12)
	{
		if (shownc[s] <= 69.99)
			break;
		s++;
	}

	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT); // number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);
	
	int levelslow1[14] = {14, 12, 11, 10, 9, 7, 6, 5, 4, 3, 2, 1, 0, 0};
	int levelshigh1[14] = {8, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22, 22};

	if (ChartPeriod() > 5 && D >= 1)
	{
		//if (D == 1) { obname = Name + "ArrDnExtreme"; ArrowPrice(obname, level0[levelslow1[o]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), clrMagenta); obname = Name + "ArrUpExtreme"; ArrowPrice(obname, level0[levelshigh1[p]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), clrAqua); }
		//if (D == 1 && o > 1 && p > 1) { obname = Name + "ArrDnTZE"; ArrowPrice(obname, level0[levelslow1[o-1]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), clrMagenta); obname = Name + "ArrUpTZE"; ArrowPrice(obname, level0[levelshigh1[p-1]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), clrAqua); }

		if (D <= 25)
		{
			if (p > 1)
			{
				obname = Name + "UpExtLine" + IntegerToString(D);
				RecFill(obname, level0[levelshigh1[p]], level0[levelshigh1[p - 1]], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrBlue, "Extreme High Start D-" + IntegerToString(D - 1) + ": ", "Trade Zone High End D-" + IntegerToString(D - 1) + ": ");
				if (D > 1)
					ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false)] - 1);
				if (D > 2)
				{
					ObjectSetInteger(0, obname, OBJPROP_BACK, false);
					ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0);
				}
				if (HD1[D - 1] >= level0[levelshigh1[p - 1]])
				{
					ObjectSetInteger(0, Name + "UpExtLine" + IntegerToString(D), OBJPROP_COLOR, clrYellow);
					ObjectSetInteger(0, obname, OBJPROP_FILL, true);
					/*
					if (D == 1)
					{
						ObjectSetInteger(0, Name + "ArrUpExtreme", OBJPROP_COLOR, clrYellow);
						ObjectSetInteger(0, Name + "ArrUpTZE", OBJPROP_COLOR, clrYellow);
					}
					*/
				}
			}
			else
			{
				ObjectDelete(0, obname);
			}

			if (o > 1)
			{
				obname = Name + "DnExtLine" + IntegerToString(D);
				RecFill(obname, level0[levelslow1[o]], level0[levelslow1[o - 1]], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrRed, "Extreme Low Start D-" + IntegerToString(D - 1) + ": ", "Trade Zone Low End D-" + IntegerToString(D - 1) + ": ");
				if (D > 1)
					ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false)] - 1);
				if (D > 2)
				{
					ObjectSetInteger(0, obname, OBJPROP_BACK, false);
					ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0);
				}
				if (LD1[D - 1] <= level0[levelslow1[o - 1]])
				{
					ObjectSetInteger(0, Name + "DnExtLine" + IntegerToString(D), OBJPROP_COLOR, clrYellow);
					ObjectSetInteger(0, obname, OBJPROP_FILL, true);
					/*
					if (D == 1)
					{
						ObjectSetInteger(0, Name + "ArrDnExtreme", OBJPROP_COLOR, clrYellow);
						ObjectSetInteger(0, Name + "ArrDnTZE", OBJPROP_COLOR, clrYellow);
					}
					*/
				}
			}
			else
			{
				ObjectDelete(0, obname);
			}

			if (r > 1)
			{
				obname = Name + "UnNewLine" + IntegerToString(D);
				objtrend(obname, level0[levelshigh1[r - 1]], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrGreen, "<70% UpZnStart " + DoubleToString(level0[levelshigh1[r - 1]], _Digits));
				ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
				if (D > 1)
					ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false)] - 1);
				if (HD1[D - 1] >= level0[levelshigh1[r - 1]])
				{
					ObjectSetInteger(0, Name + "UnNewLine" + IntegerToString(D), OBJPROP_COLOR, clrGold);
				}
			}
			else
			{
				ObjectDelete(0, obname);
			}

			if (s > 1)
			{
				obname = Name + "DnNewLine" + IntegerToString(D);
				objtrend(obname, level0[levelslow1[s - 1]], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrDeepPink, "<70% DnZnStart " + DoubleToString(level0[levelslow1[s - 1]], _Digits));
				ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
				if (D > 1)
					ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false)] - 1);
				if (LD1[D - 1] <= level0[levelslow1[s - 1]])
				{
					ObjectSetInteger(0, Name + "DnNewLine" + IntegerToString(D), OBJPROP_COLOR, clrGold);
				}
			}
			else
			{
				ObjectDelete(0, obname);
			}

			obname = Name + "MidLineN" + IntegerToString(D);
			RecFill(obname, (HD1[D] - LD1[D]) * 0.559 + LD1[D], (HD1[D] - LD1[D]) * 0.441 + LD1[D], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrAquamarine, "TZ 50% upper D-" + IntegerToString(D - 1) + ": ", "TZ 50% lower D-" + IntegerToString(D - 1) + ": ");
			if (D > 1)
				ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false) + 1]);
			if (CD1[D - 1] < (HD1[D] + LD1[D]) / 2)
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMediumPurple);
			ObjectSetInteger(0, obname, OBJPROP_BACK, false);
			ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
			if (D > 2)
				ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);

			obname = Name + "MidLineN-" + IntegerToString(D);
			if (D <= 2)
				objtrend(obname, (HD1[D] + LD1[D]) / 2, iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrWhiteSmoke, "Mid" + IntegerToString(D) + ": " + DoubleToString((HD1[D] + LD1[D]) / 2, _Digits));
			ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
			ObjectSetInteger(0, obname, OBJPROP_STYLE, STYLE_DOT);
			if (D > 1)
				ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false) + 1]);
		}

		ObjectsDeleteAll(0, Name + "OlineCon-");
		ObjectsDeleteAll(0, Name + "Oline-");

		obname = Name + "OLine-" + IntegerToString(D);
		objtrend(obname, OD1[D], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrNONE, DoubleToString(OD1[D], _Digits));
		ObjectSetDouble(0, obname, OBJPROP_PRICE, 1, (HD1[D + 1] + LD1[D + 1]) / 2);
		ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false)]);
		ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
		if (HD1[D] < (HD1[D + 1] + LD1[D + 1]) / 2)
		{
			//ObjectDelete(obname);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOlive);
			ObjectSetDouble(0, obname, OBJPROP_PRICE, HD1[D]);
		}
		else if (LD1[D] > (HD1[D + 1] + LD1[D + 1]) / 2)
		{
			//ObjectDelete(obname);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOlive);
			ObjectSetDouble(0, obname, OBJPROP_PRICE, LD1[D]);
		}

		int t = 0, y = 0;

		if ((HD1[D] < (HD1[D + 1] + LD1[D + 1]) / 2) || (LD1[D] > (HD1[D + 1] + LD1[D + 1]) / 2))
		{
			obname = Name + "OLineCon-" + IntegerToString(D);
			objtrend(obname, (HD1[D + 1] + LD1[D + 1]) / 2, iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false), 4, clrLime, DoubleToString((HD1[D + 1] + LD1[D + 1]) / 2, _Digits) + " @ " + TimeToString(TD1[D], TIME_DATE));
			if ((HD1[D] < (HD1[D + 1] + LD1[D + 1]) / 2) && (HD1[iHighest(_Symbol, PERIOD_D1, MODE_HIGH, D, 1)] > ObjectGetDouble(0, obname, OBJPROP_PRICE, 1)))
			{
				for (int x = iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false); x >= 1; x--)
				{
					if (iHigh(_Symbol, PERIOD_CURRENT, x) < ObjectGetDouble(0, obname, OBJPROP_PRICE, 1))
						t++;
					if (iHigh(_Symbol, PERIOD_CURRENT, x) > ObjectGetDouble(0, obname, OBJPROP_PRICE, 1))
						break;
				}
				ObjectDelete(0, Name + "OLineCon-" + IntegerToString(D - 1));
				if (!showpast)
					ObjectDelete(0, obname);
				else if (showpast)
				{
					ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
					ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOlive);
					ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false) - t], false)]);
				}
			}
			if ((LD1[D] > (HD1[D + 1] + LD1[D + 1]) / 2) && (LD1[iLowest(_Symbol, PERIOD_D1, MODE_LOW, D, 1)] < ObjectGetDouble(0, obname, OBJPROP_PRICE, 1)))
			{
				for (int x = iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false); x >= 1; x--)
				{
					if (iLow(_Symbol, PERIOD_CURRENT, x) > ObjectGetDouble(0, obname, OBJPROP_PRICE, 1))
						y++;
					if (iLow(_Symbol, PERIOD_CURRENT, x) < ObjectGetDouble(0, obname, OBJPROP_PRICE, 1))
						break;
				}
				ObjectDelete(0, Name + "OLineCon-" + IntegerToString(D - 1));
				if (!showpast)
					ObjectDelete(0, obname);
				else if (showpast)
				{
					ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
					ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOlive);
					ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false) - y], false)]);
				}
			}
		}

		if (ObjectGetInteger(0, Name + "OLineCon-" + IntegerToString(D), OBJPROP_COLOR) == clrLime)
			ObjectSetInteger(0, Name + "OLine-" + IntegerToString(D), OBJPROP_COLOR, clrLime);
		if (!showpast && ObjectGetInteger(0, Name + "OLineCon-" + IntegerToString(D), OBJPROP_COLOR) != clrLime)
			ObjectDelete(0, Name + "OLine-" + IntegerToString(D));
	}

	if (ChartPeriod() <= 5 && D == 1)
	{
		if (p > 1)
		{
			obname = Name + "UpExtLine" + IntegerToString(D);
			RecFill(obname, level0[levelshigh1[p]], level0[levelshigh1[p - 1]], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrBlue, "Extreme High Start D-" + IntegerToString(D - 1) + ": ", "Trade Zone High End D-" + IntegerToString(D - 1) + ": ");
			if (D > 1)
				ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false)] - 1);
			if (HD1[D - 1] >= level0[levelshigh1[p - 1]])
				ObjectSetInteger(0, Name + "UpExtLine" + IntegerToString(D), OBJPROP_COLOR, clrYellow);
		}
		else
		{
			ObjectDelete(0, obname);
		}

		if (o > 1)
		{
			obname = Name + "DnExtLine" + IntegerToString(D);
			RecFill(obname, level0[levelslow1[o]], level0[levelslow1[o - 1]], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrRed, "Extreme Low Start D-" + IntegerToString(D - 1) + ": ", "Trade Zone Low End D-" + IntegerToString(D - 1) + ": ");
			if (D > 1)
				ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false)] - 1);
			if (LD1[D - 1] <= level0[levelslow1[o - 1]])
				ObjectSetInteger(0, Name + "DnExtLine" + IntegerToString(D), OBJPROP_COLOR, clrYellow);
		}
		else
		{
			ObjectDelete(0, obname);
		}

		obname = Name + "MidLineN" + IntegerToString(D);
		RecFill(obname, (HD1[D] - LD1[D]) * 0.559 + LD1[D], (HD1[D] - LD1[D]) * 0.441 + LD1[D], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrAquamarine, "TZ 50% upper D-" + IntegerToString(D - 1) + ": ", "TZ 50% lower D-" + IntegerToString(D - 1) + ": ");
		if (D > 1)
			ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false) + 1]);
		if (CD1[D - 1] < (HD1[D] + LD1[D]) / 2)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMediumPurple);
		ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		ObjectSetInteger(0, obname, OBJPROP_WIDTH, 3);

		obname = Name + "MidLineN-" + IntegerToString(D);
		objtrend(obname, (HD1[D] + LD1[D]) / 2, iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrWhiteSmoke, "Mid" + IntegerToString(D) + ": " + DoubleToString((HD1[D] + LD1[D]) / 2, _Digits));
		ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
		ObjectSetInteger(0, obname, OBJPROP_STYLE, STYLE_DOT);
		if (D > 1)
			ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false) + 1]);
	}

	if (D == 0)
	{
		if (p > 1)
		{
			obname = Name + "UpExtLine" + IntegerToString(D);
			RecFill(obname, level0[levelshigh1[p]], level0[levelshigh1[p - 1]], 0, 4, clrBlue, "Extreme High Start D-New: ", "Trade Zone High End D-New: ");
			ObjectSetInteger(0, obname, OBJPROP_TIME, Time[0] + 6 * Period());
			ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		}
		else
		{
			ObjectDelete(0, obname);
		}

		if (o > 1)
		{
			obname = Name + "DnExtLine" + IntegerToString(D);
			RecFill(obname, level0[levelslow1[o]], level0[levelslow1[o - 1]], 0, 4, clrRed, "Extreme Low Start D-New: ", "Trade Zone Low End D-New: ");
			ObjectSetInteger(0, obname, OBJPROP_TIME, Time[0] + 6 * Period());
			ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		}
		else
		{
			ObjectDelete(0, obname);
		}

		if (r > 1)
		{
			obname = Name + "UnNewLine" + IntegerToString(D);
			objtrend(obname, level0[levelshigh1[r - 1]], 0, 4, clrGreen, "<70% UpZnStart " + DoubleToString(level0[levelshigh1[r - 1]], _Digits));
			ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
			ObjectSetInteger(0, obname, OBJPROP_TIME, Time[0] + 6 * Period());
		}
		else
		{
			ObjectDelete(0, obname);
		}

		if (s > 1)
		{
			obname = Name + "DnNewLine" + IntegerToString(D);
			objtrend(obname, level0[levelslow1[s - 1]], 0, 4, clrDeepPink, "<70% DnZnStart " + DoubleToString(level0[levelslow1[s - 1]], _Digits));
			ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
			ObjectSetInteger(0, obname, OBJPROP_TIME, Time[0] + 6 * Period());
		}
		else
		{
			ObjectDelete(0, obname);
		}

		obname = Name + "MidLineN" + IntegerToString(D);
		RecFill(obname, (HD1[D] - LD1[D]) * 0.559 + LD1[D], (HD1[D] - LD1[D]) * 0.441 + LD1[D], 0, 5, clrAquamarine, "TZ 50% upper D-New: ", "TZ 50% lower D-New: ");
		if (CD1[0] < (HD1[D] + LD1[D]) / 2)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMediumPurple);
		ObjectSetInteger(0, obname, OBJPROP_TIME, Time[0] + 6 * Period());
		ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);

		obname = Name + "MidLineN-" + IntegerToString(D);
		objtrend(obname, (HD1[D] + LD1[D]) / 2, 0, 5, clrWhiteSmoke, "Mid: " + DoubleToString((HD1[D] + LD1[D]) / 2, _Digits));
		ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
		ObjectSetInteger(0, obname, OBJPROP_STYLE, STYLE_DOT);
		ObjectSetInteger(0, obname, OBJPROP_TIME, Time[0] + 6 * Period());
	}
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const string Font,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetText(name, label, FSize, Font, FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//+------------------------------------------------------------------+

/*
//+FIBMAKE-----------------------------------------------------------+
bool FibMake(const string name,
	datetime time1,
	double price1,
	datetime time2,
	double price2,
	const color clrFib)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_FIBO, 0, time1, price1, time2, price2))
		{
			Print(__FUNCTION__,
				": failed to create \"Fibonacci Retracement\"! Error code = ", GetLastError());
			return (false);
		}
	ObjectSetInteger(0, name, OBJPROP_LEVELS, 23);
	ObjectSetInteger(0, name, OBJPROP_COLOR, clrNONE);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_DOT);
	ObjectSetInteger(0, name, OBJPROP_LEVELCOLOR, clrFib);
	ObjectSetInteger(0, name, OBJPROP_LEVELSTYLE, STYLE_DOT);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 0);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
	ObjectSetInteger(0, name, OBJPROP_RAY_RIGHT, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_ZORDER, 0);

	double levels[23] = { -1.5, -1.3, -1.1, -0.9, -0.7, -0.5, -0.3, -0.1, 0, 0.1, 0.3, 0.5, 0.7, 0.9, 1, 1.1, 1.3, 1.5, 1.7, 1.9, 2.1, 2.3, 2.5 };
	string levell[23] = { "-2 - %$", "-1.8 - %$", "-1.6 - %$", "-1.4 - %$", "-1.2 - %$", "-1 - %$", "-0.8 - %$", "-0.6 - %$", "-50", "-0.4 - %$", "-0.2 - %$", "0 - %$", "0.2 - %$", "0.4 - %$", "50", "0.6 - %$", "0.8 - %$", "1 - %$", "1.2 - %$", "1.4 - %$", "1.6 - %$", "1.8 - %$", "2 - %$" };

	for (int x = 0; x <= 22; x++) {
		ObjectSetDouble(0, name, OBJPROP_LEVELVALUE, x, levels[x]);
		ObjectSetString(0, name, OBJPROP_LEVELTEXT, x, levell[x]);
	}
	return(true);
}
//+------------------------------------------------------------------+
*/

//+ARROWPRICE--------------------------------------------------------+
void ArrowPrice(const string name, const double x, const int y, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW_LEFT_PRICE, 0, 0, 0))
		{
			Print("error: can't create arrow_right_price! code #", GetLastError());
		}
	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT); // number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE, x);
	ObjectSetInteger(0, name, OBJPROP_TIME, Time[y]);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "FreqPrice " + StringSubstr(name, StringLen(Name), 0) + ": " + DoubleToString(x, _Digits));
}
//+------------------------------------------------------------------+

//+TL CREATE---------------------------------------------------------+
void objtrend(string name, double pr1, int t, int pir, color col, string buls)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT); // number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Time[0] + pir * Period() / ChartPeriod());
	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr1);
	ObjectSetInteger(0, name, OBJPROP_STYLE, 0);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 4);
	ObjectSetInteger(0, name, OBJPROP_RAY, false);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, buls);
}
//+------------------------------------------------------------------+

void RecFill(const string name, const double x, const double xs, const int t, const int pir, const color FCol, string buls, string bers)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT); // number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE, x);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, xs);
	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Time[0] + 60 * pir * Period() / ChartPeriod());
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, buls + DoubleToString(x, _Digits) + " - " + bers + DoubleToString(xs, _Digits) + " " + " M: " + DoubleToString((x + xs) / 2, _Digits));
}
//Create rectangle backgrounds - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+OBJECTSETTEXT FUNCTION--------------------------------------------+
void ObjectSetText(const string name,
				   const string text,
				   const int fontsize,
				   const string font,
				   const color col)
{
	if (ObjectFind(0, name) < 0)
		Print("Error: can't find label_object! code #", GetLastError());
	int object = (int)ObjectGetInteger(0, name, OBJPROP_TYPE);
	if (object != OBJ_LABEL && object != OBJ_TEXT)
		Print("Not a label or text object! code#", GetLastError());
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, fontsize);
	ObjectSetString(0, name, OBJPROP_FONT, font);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
}
//+------------------------------------------------------------------+

/*
//+PRINT PERCENTAGES-------------------------------------------------+
void build() {
	string obname;
	
	double shownc[13], showpc[13];
	ArrayInitialize(shownc, 0); ArrayInitialize(showpc, 13);

	shownc[12] = shown[12];
	for (int x = 11; x >= 0; x--) {
		shownc[x] = shownc[x + 1] + shown[x];
	}
	showpc[12] = showp[12];
	for (int x = 11; x >= 0; x--) {
		showpc[x] = showpc[x + 1] + showp[x];
	}
	
	/*
	for (int x = 0; x <= 12; x++) {
		if (!nodata) {
			//obname = Name + "PcsP" + IntegerToString(x); LabelMake(obname, 2, 300 + 85 * x, 45, DoubleToString(showp[x], 2) + " / " + DoubleToString(showpc[x], 2) + "%", 8, "Arial", clrYellow);
			//obname = Name + "PcsN" + IntegerToString(x); LabelMake(obname, 2, 300 + 85 * x, 15, DoubleToString(shown[x], 2) + " / " + DoubleToString(shownc[x], 2) + "%", 8, "Arial", clrBlack);
			obname = Name + "PcsP" + IntegerToString(x); LabelMake(obname, 2, 50 + 65 * x, 60, DoubleToString(showpc[x], 2) + "%", 8, "Arial", clrYellow);
			obname = Name + "PcsN" + IntegerToString(x); LabelMake(obname, 2, 50 + 65 * x, 30, DoubleToString(shownc[x], 2) + "%", 8, "Arial", clrBlack);
		}
	}

	if (nodata) { obname = Name + "PcsP" + "0"; LabelMake(obname, 2, 50, 60, "No Data!", 8, "Arial", clrBlue); obname = Name + "PcsN" + "0"; LabelMake(obname, 2, 50, 30, "No Data!", 8, "Arial", clrRed); }
	
	if (showfib) {
		obname = Name + "Fib"; FibMake(obname, Time[0] + 10 * Period() * 60, iHigh(_Symbol, PERIOD_D1, 1), Time[0] + 10 * Period() * 60, iLow(_Symbol, PERIOD_D1, 1), FibCol);
	}
	-/
	
	double levels[23] = { -2, -1.8, -1.6, -1.4, -1.2, -1.0, -0.8, -0.6, -0.5, -0.4, -0.2, 0, 0.2, 0.4, 0.5, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0 };

	double level0[23];
	ArrayInitialize(level0, 0);

	for (int x = 22; x >= 0; x--) {
		level0[x] = ((iHigh(_Symbol, PERIOD_D1, 1) + iLow(_Symbol, PERIOD_D1, 1)) / 2) + (levels[x] * (iHigh(_Symbol, PERIOD_D1, 1) - iLow(_Symbol, PERIOD_D1, 1)));
	}
	
	/*
	int levelslow[13] = { 0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14 };
	int levelshigh[13] = { 22, 21, 20, 19, 18, 17, 16, 15, 13, 12, 11, 10, 5 };

	for (int x = 0, i = 11; x <= 11; x++, i--) {
		ObjectSetString(0, Name + "PercsN" + IntegerToString(i), OBJPROP_TOOLTIP, DoubleToString(level0[levelslow[x + 1]], _Digits) + " - " + DoubleToString(level0[levelslow[x]], _Digits));
	}
	ObjectSetString(0, Name + "PercsN" + IntegerToString(12), OBJPROP_TOOLTIP, DoubleToString(level0[levelslow[0]], _Digits) + "-");

	for (int x = 0, i = 11; x <= 11; x++, i--) {
		ObjectSetString(0, Name + "PercsP" + IntegerToString(i), OBJPROP_TOOLTIP, DoubleToString(level0[levelshigh[x + 1]], _Digits) + " - " + DoubleToString(level0[levelshigh[x]], _Digits));
	}
	ObjectSetString(0, Name + "PercsP" + IntegerToString(12), OBJPROP_TOOLTIP, DoubleToString(level0[levelshigh[0]], _Digits) + "+");
	-/
	
	int p = 0;
	while (p >= 0 && p <= 12) {
		if (showpc[p] <= 49.99) break;
		p++;
	}
	int o = 0;
	while (o >= 0 && o <= 12) {
		if (shownc[o] <= 49.99) break;
		o++;
	}
	
	int levelslow1[14] = { 14, 12, 11, 10, 9, 7, 6, 5, 4, 3, 2, 1, 0, 0 };
	int levelshigh1[14] = { 8, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22, 22 };
	
	obname = Name + "MidLine-F"; objtrend(obname, (iHigh(_Symbol, PERIOD_D1, 0) + iLow(_Symbol, PERIOD_D1, 0)) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 10, clrSienna, "FutMid: " + DoubleToString((iHigh(_Symbol, PERIOD_D1, 0) + iLow(_Symbol, PERIOD_D1, 0)) / 2, _Digits)); ObjectSet(obname, OBJPROP_WIDTH, 1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DASH);
	
	//obname = Name + "MidLine"; objtrend(obname, (iHigh(_Symbol, PERIOD_D1, 1) + iLow(_Symbol, PERIOD_D1, 1)) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 5, clrAquamarine, "Trade Zone 50%: " + DoubleToString((iHigh(_Symbol, PERIOD_D1, 1) + iLow(_Symbol, PERIOD_D1, 1)) / 2, _Digits)); ObjectSet(obname, OBJPROP_WIDTH, 5); if (iClose(_Symbol, PERIOD_D1, 0) < (iHigh(_Symbol, PERIOD_D1, 1) + iLow(_Symbol, PERIOD_D1, 1)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
	obname = Name + "MidLine"; RecFill(obname, 0.559 * (iHigh(_Symbol, PERIOD_D1, 1) - iLow(_Symbol, PERIOD_D1, 1)) + iLow(_Symbol, PERIOD_D1, 1), 0.441 * (iHigh(_Symbol, PERIOD_D1, 1) - iLow(_Symbol, PERIOD_D1, 1)) + iLow(_Symbol, PERIOD_D1, 1), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 5, clrAquamarine, "TZ 50% upper: ", "TZ 50% lower: "); if (iClose(_Symbol, PERIOD_D1, 0) < (iHigh(_Symbol, PERIOD_D1, 1) + iLow(_Symbol, PERIOD_D1, 1)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
	obname = Name + "MidLine-1"; objtrend(obname, (iHigh(_Symbol, PERIOD_D1, 1) + iLow(_Symbol, PERIOD_D1, 1)) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 5, clrWhiteSmoke, "Mid: " + DoubleToString((iHigh(_Symbol, PERIOD_D1, 1) + iLow(_Symbol, PERIOD_D1, 1)) / 2, _Digits)); ObjectSet(obname, OBJPROP_WIDTH, 1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DASH); if (iHigh(_Symbol, PERIOD_D1, 0) >= (iHigh(_Symbol, PERIOD_D1, 1) + iLow(_Symbol, PERIOD_D1, 1)) / 2 && iLow(_Symbol, PERIOD_D1, 0) <= (iHigh(_Symbol, PERIOD_D1, 1) + iLow(_Symbol, PERIOD_D1, 1)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrNavy);
	
	/*
	//obname = Name + "MidLine1"; objtrend(obname, (iHigh(_Symbol, PERIOD_D1, 2) + iLow(_Symbol, PERIOD_D1, 2)) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 1), false), 5, clrAquamarine, "Trade Zone 50% -1: " + DoubleToString((iHigh(_Symbol, PERIOD_D1, 2) + iLow(_Symbol, PERIOD_D1, 2)) / 2, _Digits)); ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false) + 1]); ObjectSet(obname, OBJPROP_WIDTH, 4); if (iClose(_Symbol, PERIOD_D1, 1) < (iHigh(_Symbol, PERIOD_D1, 2) + iLow(_Symbol, PERIOD_D1, 2)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
	obname = Name + "MidLine1"; RecFill(obname, (iHigh(_Symbol, PERIOD_D1, 2) - iLow(_Symbol, PERIOD_D1, 2)) * 0.559 + iLow(_Symbol, PERIOD_D1, 2), (iHigh(_Symbol, PERIOD_D1, 2) - iLow(_Symbol, PERIOD_D1, 2)) * 0.441 + iLow(_Symbol, PERIOD_D1, 2), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 1), false), 5, clrAquamarine,  "TZ 50% upper - 1: ", "TZ 50% lower - 1: "); ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false) + 1]); if (iClose(_Symbol, PERIOD_D1, 1) < (iHigh(_Symbol, PERIOD_D1, 2) + iLow(_Symbol, PERIOD_D1, 2)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
	obname = Name + "MidLine-2"; objtrend(obname, (iHigh(_Symbol, PERIOD_D1, 2) + iLow(_Symbol, PERIOD_D1, 2)) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 1), false), 5, clrWhiteSmoke, "Mid: " + DoubleToString((iHigh(_Symbol, PERIOD_D1, 2) + iLow(_Symbol, PERIOD_D1, 2)) / 2, _Digits)); ObjectSet(obname, OBJPROP_WIDTH, 1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DASH); ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false) + 1]); if (iHigh(_Symbol, PERIOD_D1, 1) >= (iHigh(_Symbol, PERIOD_D1, 2) + iLow(_Symbol, PERIOD_D1, 2)) / 2 && iLow(_Symbol, PERIOD_D1, 1) <= (iHigh(_Symbol, PERIOD_D1, 2) + iLow(_Symbol, PERIOD_D1, 2)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrNavy);
	
	//obname = Name + "MidLine2"; objtrend(obname, (iHigh(_Symbol, PERIOD_D1, 3) + iLow(_Symbol, PERIOD_D1, 3)) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false), 5, clrAquamarine, "Trade Zone 50% -2: " + DoubleToString((iHigh(_Symbol, PERIOD_D1, 3) + iLow(_Symbol, PERIOD_D1, 3)) / 2, _Digits)); ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 1), false) + 1]); ObjectSet(obname, OBJPROP_WIDTH, 4); if (iClose(_Symbol, PERIOD_D1, 2) < (iHigh(_Symbol, PERIOD_D1, 3) + iLow(_Symbol, PERIOD_D1, 3)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
	obname = Name + "MidLine2"; RecFill(obname, (iHigh(_Symbol, PERIOD_D1, 3) - iLow(_Symbol, PERIOD_D1, 3)) * 0.559 + iLow(_Symbol, PERIOD_D1, 3), (iHigh(_Symbol, PERIOD_D1, 3) - iLow(_Symbol, PERIOD_D1, 3)) * 0.441 + iLow(_Symbol, PERIOD_D1, 3), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false), 5, clrAquamarine, "TZ 50% upper - 2: ", "TZ 50% lower - 2: "); ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 1), false) + 1]); if (iClose(_Symbol, PERIOD_D1, 2) < (iHigh(_Symbol, PERIOD_D1, 3) + iLow(_Symbol, PERIOD_D1, 3)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
	obname = Name + "MidLine-3"; objtrend(obname, (iHigh(_Symbol, PERIOD_D1, 3) + iLow(_Symbol, PERIOD_D1, 3)) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false), 5, clrWhiteSmoke, "Mid: " + DoubleToString((iHigh(_Symbol, PERIOD_D1, 3) + iLow(_Symbol, PERIOD_D1, 3)) / 2, _Digits)); ObjectSet(obname, OBJPROP_WIDTH, 1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DASH); ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 1), false) + 1]); if (iHigh(_Symbol, PERIOD_D1, 2) >= (iHigh(_Symbol, PERIOD_D1, 3) + iLow(_Symbol, PERIOD_D1, 3)) / 2 && iLow(_Symbol, PERIOD_D1, 2) <= (iHigh(_Symbol, PERIOD_D1, 3) + iLow(_Symbol, PERIOD_D1, 3)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrNavy);
	
	//obname = Name + "MidLine3"; objtrend(obname, (iHigh(_Symbol, PERIOD_D1, 4) + iLow(_Symbol, PERIOD_D1, 4)) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 3), false), 5, clrAquamarine, "Trade Zone 50% -3: " + DoubleToString((iHigh(_Symbol, PERIOD_D1, 4) + iLow(_Symbol, PERIOD_D1, 4)) / 2, _Digits)); ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false) + 1]); ObjectSet(obname, OBJPROP_WIDTH, 4); if (iClose(_Symbol, PERIOD_D1, 3) < (iHigh(_Symbol, PERIOD_D1, 4) + iLow(_Symbol, PERIOD_D1, 4)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
	obname = Name + "MidLine3"; RecFill(obname, (iHigh(_Symbol, PERIOD_D1, 4) - iLow(_Symbol, PERIOD_D1, 4)) * 0.559 + iLow(_Symbol, PERIOD_D1, 4), (iHigh(_Symbol, PERIOD_D1, 4) - iLow(_Symbol, PERIOD_D1, 4)) * 0.441 + iLow(_Symbol, PERIOD_D1, 4), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 3), false), 5, clrAquamarine, "TZ 50% upper - 3: ", "TZ 50% lower - 3: "); ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false) + 1]); if (iClose(_Symbol, PERIOD_D1, 3) < (iHigh(_Symbol, PERIOD_D1, 4) + iLow(_Symbol, PERIOD_D1, 4)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
	obname = Name + "MidLine-4"; objtrend(obname, (iHigh(_Symbol, PERIOD_D1, 4) + iLow(_Symbol, PERIOD_D1, 4)) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 3), false), 5, clrWhiteSmoke, "Mid: " + DoubleToString((iHigh(_Symbol, PERIOD_D1, 4) + iLow(_Symbol, PERIOD_D1, 4)) / 2, _Digits)); ObjectSet(obname, OBJPROP_WIDTH, 1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DASH); ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false) + 1]); if (iHigh(_Symbol, PERIOD_D1, 3) >= (iHigh(_Symbol, PERIOD_D1, 4) + iLow(_Symbol, PERIOD_D1, 4)) / 2 && iLow(_Symbol, PERIOD_D1, 3) <= (iHigh(_Symbol, PERIOD_D1, 4) + iLow(_Symbol, PERIOD_D1, 4)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrNavy);
	
	//obname = Name + "MidLine4"; objtrend(obname, (iHigh(_Symbol, PERIOD_D1, 5) + iLow(_Symbol, PERIOD_D1, 5)) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 4), false), 5, clrAquamarine, "Trade Zone 50% -4: " + DoubleToString((iHigh(_Symbol, PERIOD_D1, 5) + iLow(_Symbol, PERIOD_D1, 5)) / 2, _Digits)); ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 3), false) + 1]); ObjectSet(obname, OBJPROP_WIDTH, 4); if (iClose(_Symbol, PERIOD_D1, 4) < (iHigh(_Symbol, PERIOD_D1, 5) + iLow(_Symbol, PERIOD_D1, 5)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
	obname = Name + "MidLine4"; RecFill(obname, (iHigh(_Symbol, PERIOD_D1, 5) - iLow(_Symbol, PERIOD_D1, 5)) * 0.559 + iLow(_Symbol, PERIOD_D1, 5), (iHigh(_Symbol, PERIOD_D1, 5) - iLow(_Symbol, PERIOD_D1, 5)) * 0.441 + iLow(_Symbol, PERIOD_D1, 5), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 4), false), 5, clrAquamarine, "TZ 50% upper - 4: ", "TZ 50% lower - 4: "); ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 3), false) + 1]); if (iClose(_Symbol, PERIOD_D1, 4) < (iHigh(_Symbol, PERIOD_D1, 5) + iLow(_Symbol, PERIOD_D1, 5)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
	obname = Name + "MidLine-5"; objtrend(obname, (iHigh(_Symbol, PERIOD_D1, 5) + iLow(_Symbol, PERIOD_D1, 5)) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 4), false), 5, clrWhiteSmoke, "Mid: " + DoubleToString((iHigh(_Symbol, PERIOD_D1, 5) + iLow(_Symbol, PERIOD_D1, 5)) / 2, _Digits)); ObjectSet(obname, OBJPROP_WIDTH, 1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DASH); ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 3), false) + 1]); if (iHigh(_Symbol, PERIOD_D1, 4) >= (iHigh(_Symbol, PERIOD_D1, 5) + iLow(_Symbol, PERIOD_D1, 5)) / 2 && iLow(_Symbol, PERIOD_D1, 4) <= (iHigh(_Symbol, PERIOD_D1, 5) + iLow(_Symbol, PERIOD_D1, 5)) / 2) ObjectSet(obname, OBJPROP_COLOR, clrNavy);
	-/
	
	//if (p > 1) { obname = Name + "ArrUpExtreme"; ArrowPrice(obname, level0[levelshigh1[p]], 10 * Period() * 60, clrBlue); obname = Name + "UpExtLine"; objtrend(obname, level0[levelshigh1[p]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 5, clrBlue, "Extreme High Start: " + DoubleToString(level0[levelshigh1[p]], _Digits)); obname = Name + "UpTZLine"; objtrend(obname, level0[levelshigh1[p - 1]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 5, clrBlue, "Trade Zone High End: " + DoubleToString(level0[levelshigh1[p - 1]], _Digits)); } else { ObjectDelete(Name + "ArrUpExtreme"); ObjectDelete(Name + "UpExtLine"); ObjectDelete(Name + "UpTZLine"); }
	if (p > 1) { obname = Name + "ArrUpExtreme"; ArrowPrice(obname, level0[levelshigh1[p]], 10 * Period() * 60, clrBlue); obname = Name + "UpExtLine"; RecFill(obname, level0[levelshigh1[p]], level0[levelshigh1[p - 1]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 5, clrBlue, "Extreme High Start: ", "Trade Zone High End: "); } else { ObjectDelete(Name + "ArrUpExtreme"); ObjectDelete(Name + "UpExtLine"); }
	if (p > 1) { //if (iHigh(_Symbol, PERIOD_D1, 0) >= level0[levelshigh1[p - 1]]) ObjectSetInteger(0, Name + "UpTZLine", OBJPROP_COLOR, clrYellow);
	if (iHigh(_Symbol, PERIOD_D1, 0) >= level0[levelshigh1[p - 1]]) ObjectSetInteger(0, Name + "UpExtLine", OBJPROP_COLOR, clrYellow);
	if (iHigh(_Symbol, PERIOD_D1, 0) >= level0[levelshigh1[p]]) ObjectSetInteger(0, Name + "ArrUpExtreme", OBJPROP_COLOR, clrYellow); }
	//if (o > 1) { obname = Name + "ArrDnExtreme"; ArrowPrice(obname, level0[levelslow1[o]], 10 * Period() * 60, clrRed); obname = Name + "DnExtLine";  objtrend(obname, level0[levelslow1[o]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 5, clrRed, "Extreme Low Start: " + DoubleToString(level0[levelslow1[o]], _Digits)); obname = Name + "DnTZLine"; objtrend(obname, level0[levelslow1[o - 1]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 5, clrRed, "Trade Zone Low End: " + DoubleToString(level0[levelslow1[o - 1]], _Digits)); } else { ObjectDelete(Name + "ArrDnExtreme"); ObjectDelete(Name + "DnExtLine"); ObjectDelete(Name + "DnTZLine"); }
	if (o > 1) { obname = Name + "ArrDnExtreme"; ArrowPrice(obname, level0[levelslow1[o]], 10 * Period() * 60, clrRed); obname = Name + "DnExtLine"; RecFill(obname, level0[levelslow1[o]], level0[levelslow1[o - 1]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 5, clrRed, "Extreme Low Start: ", "Trade Zone Low End: "); } else { ObjectDelete(Name + "ArrDnExtreme"); ObjectDelete(Name + "DnExtLine"); }
	if (o > 1) { //if (iLow(_Symbol, PERIOD_D1, 0) <= level0[levelslow1[o - 1]]) ObjectSetInteger(0, Name + "DnTZLine", OBJPROP_COLOR, clrYellow);
	if (iLow(_Symbol, PERIOD_D1, 0) <= level0[levelslow1[o - 1]]) ObjectSetInteger(0, Name + "DnExtLine", OBJPROP_COLOR, clrYellow);
	if (iLow(_Symbol, PERIOD_D1, 0) <= level0[levelslow1[o]]) ObjectSetInteger(0, Name + "ArrDnExtreme", OBJPROP_COLOR, clrYellow); }
}
//+------------------------------------------------------------------+

//+COLOR CHECKER-----------------------------------------------------+
void checker() {
	double levels[23] = { -2, -1.8, -1.6, -1.4, -1.2, -1.0, -0.8, -0.6, -0.5, -0.4, -0.2, 0, 0.2, 0.4, 0.5, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0 };

	double level0[23];
	ArrayInitialize(level0, 0);

	for (int x = 22; x >= 0; x--) {
		level0[x] = ((iHigh(_Symbol, PERIOD_D1, 1) + iLow(_Symbol, PERIOD_D1, 1)) / 2) + (levels[x] * (iHigh(_Symbol, PERIOD_D1, 1) - iLow(_Symbol, PERIOD_D1, 1)));
	}

	int levelslow[13] = { 0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14 };
	int levelshigh[13] = { 22, 21, 20, 19, 18, 17, 16, 15, 13, 12, 11, 10, 5 };

	//CurL & LL
	if (Bid < level0[0]) { ObjectSet(Name + "PercsN" + IntegerToString(12), OBJPROP_COLOR, clrBlack); ObjectSetString(0, Name + "PercsN" + IntegerToString(12), OBJPROP_FONT, "Arial Black"); }
	else { ObjectSet(Name + "PercsN" + IntegerToString(12), OBJPROP_COLOR, clrRed); ObjectSetString(0, Name + "PercsN" + IntegerToString(12), OBJPROP_FONT, "Arial"); }
	for (int x = 0, i = 11; x <= 11; x++, i--) {
		if (Bid > level0[levelslow[x]] && Bid < level0[levelslow[x + 1]]) { ObjectSet(Name + "PercsN" + IntegerToString(i), OBJPROP_COLOR, clrBlack); ObjectSetString(0, Name + "PercsN" + IntegerToString(i), OBJPROP_FONT, "Arial Black"); }
		else { ObjectSet(Name + "PercsN" + IntegerToString(i), OBJPROP_COLOR, clrRed); ObjectSetString(0, Name + "PercsN" + IntegerToString(i), OBJPROP_FONT, "Arial"); }
	}

	if (iLow(_Symbol, PERIOD_D1, 0) < level0[0]) { ObjectSet(Name + "PercsN" + IntegerToString(12), OBJPROP_COLOR, clrDarkViolet); ObjectSetString(0, Name + "PercsN" + IntegerToString(12), OBJPROP_FONT, "Arial Black"); }
	for (int x = 0, i = 11; x <= 11; x++, i--) {
		if (iLow(_Symbol, PERIOD_D1, 0) > level0[levelslow[x]] && iLow(_Symbol, PERIOD_D1, 0) < level0[levelslow[x + 1]]) { ObjectSet(Name + "PercsN" + IntegerToString(i), OBJPROP_COLOR, clrDarkViolet); ObjectSetString(0, Name + "PercsN" + IntegerToString(i), OBJPROP_FONT, "Arial Black"); }
	}

	//CurH & HH
	if (Bid > level0[22]) { ObjectSet(Name + "PercsP" + IntegerToString(12), OBJPROP_COLOR, clrBlack); ObjectSetString(0, Name + "PercsP" + IntegerToString(12), OBJPROP_FONT, "Arial Black"); }
	else { ObjectSet(Name + "PercsP" + IntegerToString(12), OBJPROP_COLOR, clrBlue); ObjectSetString(0, Name + "PercsP" + IntegerToString(12), OBJPROP_FONT, "Arial"); }
	for (int x = 0, i = 11; x <= 11; x++, i--) {
		if (Bid > level0[levelshigh[x + 1]] && Bid < level0[levelshigh[x]]) { ObjectSet(Name + "PercsP" + IntegerToString(i), OBJPROP_COLOR, clrBlack); ObjectSetString(0, Name + "PercsP" + IntegerToString(i), OBJPROP_FONT, "Arial Black"); }
		else { ObjectSet(Name + "PercsP" + IntegerToString(i), OBJPROP_COLOR, clrBlue); ObjectSetString(0, Name + "PercsP" + IntegerToString(i), OBJPROP_FONT, "Arial"); }
	}

	if (iHigh(_Symbol, PERIOD_D1, 0) > level0[22]) { ObjectSet(Name + "PercsP" + IntegerToString(12), OBJPROP_COLOR, clrDarkGreen); ObjectSetString(0, Name + "PercsP" + IntegerToString(12), OBJPROP_FONT, "Arial Black"); }
	for (int x = 0, i = 11; x <= 11; x++, i--) {
		if (iHigh(_Symbol, PERIOD_D1, 0) > level0[levelshigh[x + 1]] && iHigh(_Symbol, PERIOD_D1, 0) < level0[levelshigh[x]]) { ObjectSet(Name + "PercsP" + IntegerToString(i), OBJPROP_COLOR, clrDarkGreen); ObjectSetString(0, Name + "PercsP" + IntegerToString(i), OBJPROP_FONT, "Arial Black"); }
	}
}
//+------------------------------------------------------------------+
*/

int TimeDayOfWeek(datetime date)
  {
   MqlDateTime tm;
   TimeToStruct(date,tm);
   return(tm.day_of_week);
  }