#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict
#property indicator_buffers 0

#define Name WindowExpertName()

extern int periods = 750; //Calculate stats for X periods
static bool a = 1, b = 1, c = 1;
double alerts[4][3];
input bool alertsoo = true; //Show alerts on / off
input string test = "";
bool debug = false;

double currentdaynext;
double moorday, moorweek, moor4h, moor1h;

bool showpast = false;

double PipValues;

double datr, watr, fatr, oatr;

//+INIT FUNCTION-----------------------------------------------------+
int OnInit()
{
	IndicatorShortName("FDS");
	if (test == "debug")
		debug = true;

	if (debug)
	{
		uint start1 = GetTickCount();
		drawd();
		uint end1 = GetTickCount() - start1;
		Print(end1);

		uint start2 = GetTickCount();
		drawpct();
		uint end2 = GetTickCount() - start2;
		Print(end2);

		uint start3 = GetTickCount();
		drawpctc();
		uint end3 = GetTickCount() - start3;
		Print(end3);

		uint start4 = GetTickCount();
		drawpct4();
		uint end4 = GetTickCount() - start4;
		Print(end4);

		uint start5 = GetTickCount();
		drawpct1();
		uint end5 = GetTickCount() - start5;
		Print(end5);

		uint start6 = GetTickCount();
		draww();
		uint end6 = GetTickCount() - start6;
		Print(end6);

		uint start7 = GetTickCount();
		drawpctw();
		uint end7 = GetTickCount() - start7;
		Print(end7);

		uint start8 = GetTickCount();
		drawm();
		uint end8 = GetTickCount() - start8;
		Print(end8);
	}
	PipValues = (_Point * MathPow(10, MathMod(_Digits, 2)));
	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+CALCULATE---------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	datetime expiry = D'2021.06.30 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("RocAdvance expired on " + TimeToStr(expiry, TIME_DATE) + ", contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true && ChartPeriod() <= 1440)
	{
		bool new_2m_check = false;
		static datetime start_2m_time = 0;
		if (start_2m_time < iTime(NULL, PERIOD_H1, 0))
		{
			new_2m_check = true;
			start_2m_time = iTime(NULL, PERIOD_H1, 0);
		}
		if (new_2m_check)
		{
			if (a == 1)
			{
				drawd();
				drawpct();
				drawpct4();
				drawpct1();
			}
			if (b == 1)
			{
				draww();
				drawpctw();
			}
			if (c == 1)
				drawm();
			if (alertsoo)
				alerter();
			//draw1h();
			new_2m_check = false;
		}
		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, PERIOD_M1, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, PERIOD_M1, 0);
		}
		if (new_1m_check && a == 1)
		{
			drawpctc();
			new_1m_check = false;
		}
		colorer();
	}
	return (rates_total);
}
//+------------------------------------------------------------------+

//+ChartEvent function-----------------------------------------------+
void OnChartEvent(const int id,
				  const long &lparam,
				  const double &dparam,
				  const string &sparam)
{
	//---
	{ //Switch TF lines on/off
		if (id == CHARTEVENT_KEYDOWN)
		{
			if (lparam == StringGetChar("A", 0))
			{
				ObjectsDeleteAll(0, Name);
				a = 0;
				b = 0;
				c = 0;
			}
			if (lparam == StringGetChar("Z", 0))
			{
				drawd();
				drawpct();
				drawpctc();
				drawpct4();
				drawpct1();
				draww();
				drawpctw();
				drawm();
				a = 1;
				b = 1;
				c = 1;
			}
			if (lparam == StringGetChar("D", 0) && ObjectFind(Name + "dcon50") < 0)
			{
				a = 1;
				drawd();
				drawpct();
				drawpctc();
				drawpct4();
				drawpct1();
			}
			else if (lparam == StringGetChar("D", 0) && ObjectFind(Name + "dcon50") == 0)
			{
				ObjectsDeleteAll(0, Name + "d");
				ObjectsDeleteAll(0, Name + "rd");
				ObjectsDeleteAll(0, Name + "4");
				ObjectsDeleteAll(0, Name + "c");
				ObjectsDeleteAll(0, Name + "1");
				a = 0;
			}
			if (lparam == StringGetChar("W", 0) && ObjectFind(Name + "wcon50") < 0)
			{
				b = 1;
				draww();
				drawpctw();
			}
			else if (lparam == StringGetChar("W", 0) && ObjectFind(Name + "wcon50") == 0)
			{
				ObjectsDeleteAll(0, Name + "w");
				ObjectsDeleteAll(0, Name + "rw");
				b = 0;
			}
			if (lparam == StringGetChar("M", 0) && ObjectFind(Name + "mcon50") < 0)
			{
				c = 1;
				drawm();
			}
			else if (lparam == StringGetChar("M", 0) && ObjectFind(Name + "mcon50") == 0)
			{
				ObjectsDeleteAll(0, Name + "m");
				ObjectsDeleteAll(0, Name + "rm");
				c = 0;
			}
			if (lparam == StringGetChar("S", 0) && ObjectFind(Name + "rd") < 0 && showpast == false)
			{
				showpast = true;
				drawd();
				draww();
				drawm();
			}
			else if (lparam == StringGetChar("S", 0) && ObjectFind(Name + "rd") < 0 && showpast == true)
			{
				showpast = false;
				ObjectsDeleteAll(0, Name + "r");
			}
		}
	}
}
//+------------------------------------------------------------------+

//+DRAW DAILY LINES--------------------------------------------------+
void drawd()
{
	double dop0 = iClose(_Symbol, PERIOD_D1, 1);
	double dop1 = iClose(_Symbol, PERIOD_D1, 2);

	double dp[12];
	double dn[12];
	ArrayInitialize(dp, 0);
	ArrayInitialize(dn, 0);

	for (int x = 12; x >= 1; x--)
	{
		dp[x - 1] = dop0 + x * 0.0025 * dop0;
		dn[x - 1] = dop0 - x * 0.0025 * dop0;
	}

	double dpr[12];
	double dnr[12];
	ArrayInitialize(dpr, 0);
	ArrayInitialize(dnr, 0);

	for (int x = 12; x >= 1; x--)
	{
		dpr[x - 1] = dop1 + x * 0.0025 * dop1;
		dnr[x - 1] = dop1 - x * 0.0025 * dop1;
	}

	string obname;
	int adv = 20 * Period() * 80;
	int adv2 = 20 * Period() * 60;

	int od = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 1), false);
	int cd = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false);

	obname = Name + "dop";
	objtrend2(obname, dop0, 0, 0, adv, adv2, 3, 0, clrGold, "DOpen-0");
	obname = Name + "rdop";
	objtrend2(obname, dop1, od, cd, 0, 0, 3, 0, clrGold, "DOpen-1");
	obname = Name + "dcon50";
	objtrend2(obname, dp[1], 0, 0, adv, adv, 3, 0, clrBlack, "D 0.50% - -0.50% " + DoubleToStr(dp[1], _Digits) + " - " + DoubleToStr(dn[1], _Digits));
	ObjectSetDouble(0, obname, OBJPROP_PRICE2, dn[1]);

	for (int x = 0; x <= 11; x++)
	{
		obname = Name + "d0025p" + IntegerToString(x);
		objtrend2(obname, dp[x], 0, 0, adv, adv2, 3, 0, clrBlack, "D: " + DoubleToStr(x * 0.25 + 0.25, 2) + "%");
		obname = Name + "d0025p T" + IntegerToString(x);
		Texter(obname, dp[x], Time[0] + adv, "D" + DoubleToStr(x * 0.25 + 0.25, 2) + " " + DoubleToStr(dp[x], _Digits), clrBlack);
		obname = Name + "d0025n" + IntegerToString(x);
		objtrend2(obname, dn[x], 0, 0, adv, adv2, 3, 0, clrBlack, "D: " + DoubleToStr(x * -0.25 - 0.25, 2) + "%");
		obname = Name + "d0025n T" + IntegerToString(x);
		Texter(obname, dn[x], Time[0] + adv, "D" + DoubleToStr(x * -0.25 - 0.25, 2) + " " + DoubleToStr(dn[x], _Digits), clrBlack);
		if (iHigh(_Symbol, PERIOD_H1, 1) >= dp[x] && iLow(_Symbol, PERIOD_H1, 1) <= dp[x])
		{
			ObjectSetInteger(0, Name + "d0025p" + IntegerToString(x), OBJPROP_WIDTH, 5);
			ObjectSetString(0, Name + "d0025p T" + IntegerToString(x), OBJPROP_FONT, "Arial Black");
		}
		if (iHigh(_Symbol, PERIOD_H1, 1) >= dn[x] && iLow(_Symbol, PERIOD_H1, 1) <= dn[x])
		{
			ObjectSetInteger(0, Name + "d0025n" + IntegerToString(x), OBJPROP_WIDTH, 5);
			ObjectSetString(0, Name + "d0025n T" + IntegerToString(x), OBJPROP_FONT, "Arial Black");
		}
		if (x > 1)
		{
			if (dp[x] > iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, iBarShift(_Symbol, PERIOD_H1, iTime(_Symbol, PERIOD_D1, 0), false), 0)) && dp[x - 1] < iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, iBarShift(_Symbol, PERIOD_H1, iTime(_Symbol, PERIOD_D1, 0), false), 0)))
			{
				ObjectSet(obname = Name + "d0025p" + IntegerToString(x - 1), OBJPROP_WIDTH, 5);
				ObjectSet(obname = Name + "d0025p" + IntegerToString(x - 1), OBJPROP_COLOR, clrBlue);
				ObjectSet(Name + "d0025p T" + IntegerToString(x - 1), OBJPROP_COLOR, clrBlue);
			}
			if (dn[x] < iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, iBarShift(_Symbol, PERIOD_H1, iTime(_Symbol, PERIOD_D1, 0), false), 0)) && dn[x - 1] > iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, iBarShift(_Symbol, PERIOD_H1, iTime(_Symbol, PERIOD_D1, 0), false), 0)))
			{
				ObjectSet(obname = Name + "d0025n" + IntegerToString(x - 1), OBJPROP_WIDTH, 5);
				ObjectSet(obname = Name + "d0025n" + IntegerToString(x - 1), OBJPROP_COLOR, clrRed);
				ObjectSet(Name + "d0025n T" + IntegerToString(x - 1), OBJPROP_COLOR, clrRed);
			}
		}
	}

	if (showpast)
	{
		int x = 0;
		while (x >= 0 && x <= 11)
		{
			if (iHigh(_Symbol, PERIOD_D1, 1) > dpr[x])
			{
				obname = Name + "rd0025p" + IntegerToString(x);
				objtrend2(obname, dpr[x], od, cd, 0, 0, 3, 0, C'153, 157, 149', "+" + DoubleToStr(x * 0.25 + 0.25, 2) + "% Daily - 1");
			}
			if (x > 0)
			{
				if (dpr[x] > iClose(_Symbol, PERIOD_D1, 1) && dpr[x - 1] < iClose(_Symbol, PERIOD_D1, 1))
				{
					ObjectSet(obname = Name + "rd0025p" + IntegerToString(x - 1), OBJPROP_WIDTH, 4);
					ObjectSet(obname = Name + "rd0025p" + IntegerToString(x - 1), OBJPROP_COLOR, clrNavy);
				}
			}
			x++;
		}
		x = 0;
		while (x >= 0 && x <= 11)
		{
			if (iLow(_Symbol, PERIOD_D1, 1) < dnr[x])
			{
				obname = Name + "rd0025n" + IntegerToString(x);
				objtrend2(obname, dnr[x], od, cd, 0, 0, 3, 0, C'153, 157, 149', DoubleToStr(x * -0.25 - 0.25, 2) + "% Daily - 1");
			}
			if (x > 0)
			{
				if (dnr[x] < iClose(_Symbol, PERIOD_D1, 1) && dnr[x - 1] > iClose(_Symbol, PERIOD_D1, 1))
				{
					ObjectSet(obname = Name + "rd0025n" + IntegerToString(x - 1), OBJPROP_WIDTH, 4);
					ObjectSet(obname = Name + "rd0025n" + IntegerToString(x - 1), OBJPROP_COLOR, clrMaroon);
				}
			}
			x++;
		}
	}

	alerts[0][0] = dp[1];
	alerts[1][0] = dp[3];
	alerts[2][0] = dn[1];
	alerts[3][0] = dn[3];
}
//+------------------------------------------------------------------+

//+DRAW WEEKLY LINES-------------------------------------------------+
void draww()
{
	double wop0 = iClose(_Symbol, PERIOD_W1, 1);
	double wop1 = iClose(_Symbol, PERIOD_W1, 2);

	double wp[12];
	double wn[12];
	ArrayInitialize(wp, 0);
	ArrayInitialize(wn, 0);

	for (int x = 12; x >= 1; x--)
	{
		wp[x - 1] = wop0 + x * 0.0050 * wop0;
		wn[x - 1] = wop0 - x * 0.0050 * wop0;
	}

	double wpr[12];
	double wnr[12];
	ArrayInitialize(wpr, 0);
	ArrayInitialize(wnr, 0);

	for (int x = 12; x >= 1; x--)
	{
		wpr[x - 1] = wop1 + x * 0.0050 * wop1;
		wnr[x - 1] = wop1 - x * 0.0050 * wop1;
	}

	string obname;
	int adv = 20 * Period() * 100;
	int adv2 = 20 * Period() * 50;

	int ow = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 1), false);
	int cw = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 0), false);

	obname = Name + "wop";
	objtrend2(obname, wop0, cw, 0, adv, 0, 2, 0, clrOrange, "WOpen-0");
	obname = Name + "rwop";
	objtrend2(obname, wop1, ow, cw, 0, 0, 2, 0, clrOrange, "WOpen-1");
	obname = Name + "wcon50";
	objtrend2(obname, wp[1], 0, 0, adv, adv, 2, 0, C'55, 55, 55', "W 1.00% - -1.00% " + DoubleToStr(wp[1], _Digits) + " - " + DoubleToStr(wn[1], _Digits));
	ObjectSetDouble(0, obname, OBJPROP_PRICE2, wn[1]);

	for (int x = 0; x <= 11; x++)
	{
		if (DayOfWeek() > 1)
		{
			obname = Name + "w0050p" + IntegerToString(x);
			objtrend2(obname, wp[x], 0, 0, adv, adv2, 2, 0, C'55, 55, 55', "W: " + DoubleToStr(x * 0.50 + 0.50, 2) + "%");
			obname = Name + "w0050p T" + IntegerToString(x);
			Texter(obname, wp[x], Time[0] + adv, "W" + DoubleToStr(x * 0.50 + 0.50, 2) + " " + DoubleToStr(wp[x], _Digits), C'55, 55, 55');
			obname = Name + "w0050n" + IntegerToString(x);
			objtrend2(obname, wn[x], 0, 0, adv, adv2, 2, 0, C'55, 55, 55', "W: " + DoubleToStr(x * -0.50 - 0.50, 2) + "%");
			obname = Name + "w0050n T" + IntegerToString(x);
			Texter(obname, wn[x], Time[0] + adv, "W" + DoubleToStr(x * -0.50 - 0.50, 2) + " " + DoubleToStr(wn[x], _Digits), C'55, 55, 55');
		}
		if (iHigh(_Symbol, PERIOD_H1, 1) >= wp[x] && iLow(_Symbol, PERIOD_H1, 1) <= wp[x])
		{
			ObjectSetInteger(0, Name + "w0050p" + IntegerToString(x), OBJPROP_WIDTH, 4);
			ObjectSetString(0, Name + "w0050p T" + IntegerToString(x), OBJPROP_FONT, "Arial Black");
		}
		if (iHigh(_Symbol, PERIOD_H1, 1) >= wn[x] && iLow(_Symbol, PERIOD_H1, 1) <= wn[x])
		{
			ObjectSetInteger(0, Name + "w0050n" + IntegerToString(x), OBJPROP_WIDTH, 4);
			ObjectSetString(0, Name + "w0050n T" + IntegerToString(x), OBJPROP_FONT, "Arial Black");
		}
		if (x > 1)
		{
			if (wp[x] > iHigh(_Symbol, PERIOD_H4, iHighest(_Symbol, PERIOD_H4, MODE_HIGH, iBarShift(_Symbol, PERIOD_H4, iTime(_Symbol, PERIOD_W1, 0), false), 0)) && wp[x - 1] < iHigh(_Symbol, PERIOD_H4, iHighest(_Symbol, PERIOD_H4, MODE_HIGH, iBarShift(_Symbol, PERIOD_H4, iTime(_Symbol, PERIOD_W1, 0), false), 0)))
			{
				ObjectSet(obname = Name + "w0050p" + IntegerToString(x - 1), OBJPROP_WIDTH, 4);
				ObjectSet(obname = Name + "w0050p" + IntegerToString(x - 1), OBJPROP_COLOR, clrBlue);
				ObjectSet(Name + "w0050p T" + IntegerToString(x - 1), OBJPROP_COLOR, clrBlue);
			}
			if (wn[x] < iLow(_Symbol, PERIOD_H4, iLowest(_Symbol, PERIOD_H4, MODE_LOW, iBarShift(_Symbol, PERIOD_H4, iTime(_Symbol, PERIOD_W1, 0), false), 0)) && wn[x - 1] > iLow(_Symbol, PERIOD_H4, iLowest(_Symbol, PERIOD_H4, MODE_LOW, iBarShift(_Symbol, PERIOD_H4, iTime(_Symbol, PERIOD_W1, 0), false), 0)))
			{
				ObjectSet(obname = Name + "w0050n" + IntegerToString(x - 1), OBJPROP_WIDTH, 4);
				ObjectSet(obname = Name + "w0050n" + IntegerToString(x - 1), OBJPROP_COLOR, clrRed);
				ObjectSet(Name + "w0050n T" + IntegerToString(x - 1), OBJPROP_COLOR, clrRed);
			}
		}
	}

	if (showpast)
	{
		int x = 0;
		while (x >= 0 && x <= 11)
		{
			if (iHigh(_Symbol, PERIOD_W1, 1) > wpr[x])
			{
				obname = Name + "rw0050p" + IntegerToString(x);
				objtrend2(obname, wpr[x], ow, cw, 0, 0, 2, 0, C'140, 146, 135', "+" + DoubleToStr(x * 0.50 + 0.50, 2) + "% Weekly - 1");
			}
			if (x > 0)
			{
				if (wpr[x] > iClose(_Symbol, PERIOD_W1, 1) && wpr[x - 1] < iClose(_Symbol, PERIOD_W1, 1))
				{
					ObjectSet(obname = Name + "rw0050p" + IntegerToString(x - 1), OBJPROP_WIDTH, 3);
					ObjectSet(obname = Name + "rw0050p" + IntegerToString(x - 1), OBJPROP_COLOR, clrNavy);
				}
			}
			x++;
		}
		x = 0;
		while (x >= 0 && x <= 11)
		{
			if (iLow(_Symbol, PERIOD_W1, 1) < wnr[x])
			{
				obname = Name + "rw0050n" + IntegerToString(x);
				objtrend2(obname, wnr[x], ow, cw, 0, 0, 2, 0, C'140, 146, 135', DoubleToStr(x * -0.50 - 0.50, 2) + "% Weekly - 1");
			}
			if (x > 0)
			{
				if (wnr[x] < iClose(_Symbol, PERIOD_W1, 1) && wnr[x - 1] > iClose(_Symbol, PERIOD_W1, 1))
				{
					ObjectSet(obname = Name + "rw0050n" + IntegerToString(x - 1), OBJPROP_WIDTH, 3);
					ObjectSet(obname = Name + "rw0050n" + IntegerToString(x - 1), OBJPROP_COLOR, clrMaroon);
				}
			}
			x++;
		}
	}

	alerts[0][1] = wp[1];
	alerts[1][1] = wp[3];
	alerts[2][1] = wn[1];
	alerts[3][1] = wn[3];
}
//+------------------------------------------------------------------+

//+DRAW MONTHLY LINES------------------------------------------------+
void drawm()
{
	double mop0 = iClose(_Symbol, PERIOD_MN1, 1);
	double mop1 = iClose(_Symbol, PERIOD_MN1, 2);

	double mp[12];
	double mn[12];
	ArrayInitialize(mp, 0);
	ArrayInitialize(mn, 0);

	for (int x = 12; x >= 1; x--)
	{
		mp[x - 1] = mop0 + x * 0.0050 * mop0;
		mn[x - 1] = mop0 - x * 0.0050 * mop0;
	}

	double mpr[12];
	double mnr[12];
	ArrayInitialize(mpr, 0);
	ArrayInitialize(mnr, 0);

	for (int x = 12; x >= 1; x--)
	{
		mpr[x - 1] = mop1 + x * 0.0050 * mop1;
		mnr[x - 1] = mop1 - x * 0.0050 * mop1;
	}

	string obname;
	int adv = 20 * Period() * 120;
	int adv2 = 20 * Period() * 40;

	int om = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 1), false);
	int cm = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 0), false);

	obname = Name + "mop";
	objtrend2(obname, mop0, cm, 0, adv, 0, 1, 0, clrYellow, "MOpen-0");
	obname = Name + "rmop";
	objtrend2(obname, mop1, om, cm, 0, 0, 1, 0, clrYellow, "MOpen-1");
	obname = Name + "mcon50";
	objtrend2(obname, mp[1], 0, 0, adv, adv, 1, 0, C'110, 110, 110', "M 1.00% - -1.00% " + DoubleToStr(mp[1], _Digits) + " - " + DoubleToStr(mn[1], _Digits));
	ObjectSetDouble(0, obname, OBJPROP_PRICE2, mn[1]);

	for (int x = 0; x <= 11; x++)
	{
		if (Day() > 1)
		{
			obname = Name + "m0050p" + IntegerToString(x);
			objtrend2(obname, mp[x], 0, 0, adv, adv2, 1, 0, C'110, 110, 110', "M: " + DoubleToStr(x * 0.50 + 0.50, 2) + "%");
		}
		if (Day() > 1)
		{
			obname = Name + "m0050p T" + IntegerToString(x);
			Texter(obname, mp[x], Time[0] + adv, "M" + DoubleToStr(x * 0.50 + 0.50, 2) + " " + DoubleToStr(mp[x], _Digits), C'110, 110, 110');
		}
		if (Day() > 1)
		{
			obname = Name + "m0050n" + IntegerToString(x);
			objtrend2(obname, mn[x], 0, 0, adv, adv2, 1, 0, C'110, 110, 110', "M: " + DoubleToStr(x * -0.50 - 0.50, 2) + "%");
		}
		if (Day() > 1)
		{
			obname = Name + "m0050n T" + IntegerToString(x);
			Texter(obname, mn[x], Time[0] + adv, "M" + DoubleToStr(x * -0.50 - 0.50, 2) + " " + DoubleToStr(mn[x], _Digits), C'110, 110, 110');
		}
		if (iHigh(_Symbol, PERIOD_H1, 1) >= mp[x] && iLow(_Symbol, PERIOD_H1, 1) <= mp[x])
		{
			ObjectSetInteger(0, Name + "m0050p" + IntegerToString(x), OBJPROP_WIDTH, 3);
			ObjectSetString(0, Name + "m0050p T" + IntegerToString(x), OBJPROP_FONT, "Arial Black");
		}
		if (iHigh(_Symbol, PERIOD_H1, 1) >= mn[x] && iLow(_Symbol, PERIOD_H1, 1) <= mn[x])
		{
			ObjectSetInteger(0, Name + "m0050n" + IntegerToString(x), OBJPROP_WIDTH, 3);
			ObjectSetString(0, Name + "m0050n T" + IntegerToString(x), OBJPROP_FONT, "Arial Black");
		}
		if (x > 1)
		{
			if (mp[x] > iHigh(_Symbol, PERIOD_D1, iHighest(_Symbol, PERIOD_D1, MODE_HIGH, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_MN1, 0), false), 0)) && mp[x - 1] < iHigh(_Symbol, PERIOD_D1, iHighest(_Symbol, PERIOD_D1, MODE_HIGH, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_MN1, 0), false), 0)))
			{
				ObjectSet(obname = Name + "m0050p" + IntegerToString(x - 1), OBJPROP_WIDTH, 3);
				ObjectSet(obname = Name + "m0050p" + IntegerToString(x - 1), OBJPROP_COLOR, clrBlue);
				ObjectSet(Name + "m0050p T" + IntegerToString(x - 1), OBJPROP_COLOR, clrBlue);
			}
			if (mn[x] < iLow(_Symbol, PERIOD_D1, iLowest(_Symbol, PERIOD_D1, MODE_LOW, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_MN1, 0), false), 0)) && mn[x - 1] > iLow(_Symbol, PERIOD_D1, iLowest(_Symbol, PERIOD_D1, MODE_LOW, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_MN1, 0), false), 0)))
			{
				ObjectSet(obname = Name + "m0050n" + IntegerToString(x - 1), OBJPROP_WIDTH, 3);
				ObjectSet(obname = Name + "m0050n" + IntegerToString(x - 1), OBJPROP_COLOR, clrRed);
				ObjectSet(Name + "m0050n T" + IntegerToString(x - 1), OBJPROP_COLOR, clrRed);
			}
		}
	}

	if (showpast)
	{
		int x = 0;
		while (x >= 0 && x <= 11)
		{
			if (iHigh(_Symbol, PERIOD_MN1, 1) > mpr[x])
			{
				obname = Name + "rm0050p" + IntegerToString(x);
				objtrend2(obname, mpr[x], om, cm, 0, 0, 1, 0, C'103, 107, 99', "+" + DoubleToStr(x * 0.50 + 0.50, 2) + "% Monthly - 1");
			}
			if (x > 0)
			{
				if (mpr[x] > iClose(_Symbol, PERIOD_MN1, 1) && mpr[x - 1] < iClose(_Symbol, PERIOD_MN1, 1))
				{
					ObjectSet(obname = Name + "rm0050p" + IntegerToString(x - 1), OBJPROP_WIDTH, 2);
					ObjectSet(obname = Name + "rm0050p" + IntegerToString(x - 1), OBJPROP_COLOR, clrNavy);
				}
			}
			x++;
		}
		x = 0;
		while (x >= 0 && x <= 11)
		{
			if (iLow(_Symbol, PERIOD_MN1, 1) < mnr[x])
			{
				obname = Name + "rm0050n" + IntegerToString(x);
				objtrend2(obname, mnr[x], om, cm, 0, 0, 1, 0, C'103, 107, 99', DoubleToStr(x * -0.50 - 0.50, 2) + "% Monthly - 1");
			}
			if (x > 0)
			{
				if (mnr[x] < iClose(_Symbol, PERIOD_MN1, 1) && mnr[x - 1] > iClose(_Symbol, PERIOD_MN1, 1))
				{
					ObjectSet(obname = Name + "rm0050n" + IntegerToString(x - 1), OBJPROP_WIDTH, 2);
					ObjectSet(obname = Name + "rm0050n" + IntegerToString(x - 1), OBJPROP_COLOR, clrMaroon);
				}
			}
			x++;
		}
	}

	alerts[0][2] = mp[1];
	alerts[1][2] = mp[3];
	alerts[2][2] = mn[1];
	alerts[3][2] = mn[3];
}
//+------------------------------------------------------------------+

//+DRAW PERCENTAGES FOR DAILY----------------------------------------+
void drawpct()
{
	int mixture[20][5];
	ArrayInitialize(mixture, 0);

	if (iBars(_Symbol, PERIOD_D1) < periods)
		periods = iBars(_Symbol, PERIOD_D1) - 4;

	double l = 0.00, l1 = 0.25, l2 = 0.50, l3 = 0.75, l4 = 1.00;

	double CD1[];
	ArraySetAsSeries(CD1, true);
	ArrayResize(CD1, periods + 3);
	CopyClose(_Symbol, PERIOD_D1, 0, periods + 3, CD1);

	for (int i = periods; i >= 1; i--)
	{
		//POS
		if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[0][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[0][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[0][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[0][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[0][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[1][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[1][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[1][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[1][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[1][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[2][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[2][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[2][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[2][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[2][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[3][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[3][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[3][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[3][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[3][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[4][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[4][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[4][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[4][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[4][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[5][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[5][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[5][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[5][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[5][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[6][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[6][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[6][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[6][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[6][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[7][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[7][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[7][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[7][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[7][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[8][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[8][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[8][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[8][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[8][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[9][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[9][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[9][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[9][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[9][4]++;

		//NEG
		if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[10][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[10][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[10][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[10][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[10][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[11][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[11][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[11][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[11][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[11][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[12][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[12][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[12][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[12][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[12][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[13][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[13][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[13][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[13][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[13][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[14][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[14][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[14][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[14][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[14][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[15][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[15][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[15][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[15][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[15][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[16][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[16][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[16][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[16][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[16][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[17][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[17][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[17][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[17][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[17][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[18][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[18][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[18][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[18][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[18][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[19][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[19][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[19][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[19][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[19][4]++;
	}

	int y1 = 0;
	if (pct(CD1[1], CD1[2]) >= l && pct(CD1[1], CD1[2]) <= l1)
		y1 = 1;
	if (pct(CD1[1], CD1[2]) > l1 && pct(CD1[1], CD1[2]) <= l2)
		y1 = 3;
	if (pct(CD1[1], CD1[2]) > l2 && pct(CD1[1], CD1[2]) <= l3)
		y1 = 5;
	if (pct(CD1[1], CD1[2]) > l3 && pct(CD1[1], CD1[2]) <= l4)
		y1 = 7;
	if (pct(CD1[1], CD1[2]) > l4)
		y1 = 9;
	if (pct(CD1[1], CD1[2]) < -l && pct(CD1[1], CD1[2]) >= -l1)
		y1 = 11;
	if (pct(CD1[1], CD1[2]) < -l1 && pct(CD1[1], CD1[2]) >= -l2)
		y1 = 13;
	if (pct(CD1[1], CD1[2]) < -l2 && pct(CD1[1], CD1[2]) >= -l3)
		y1 = 15;
	if (pct(CD1[1], CD1[2]) < -l3 && pct(CD1[1], CD1[2]) >= -l4)
		y1 = 17;
	if (pct(CD1[1], CD1[2]) < -l4)
		y1 = 19;

	double belgross = 0;

	for (int x = 4; x >= 0; x--)
	{
		for (int z = y1; z >= y1 - 1; z--)
			belgross += mixture[z][x];
	}

	double pctp[5];
	double pctn[5];
	ArrayInitialize(pctp, 0);
	ArrayInitialize(pctn, 0);

	if (belgross > 0)
	{
		for (int x = 4; x >= 0; x--)
		{
			pctp[x] = mixture[y1 - 1][x] / belgross * 100;
			pctn[x] = mixture[y1][x] / belgross * 100;
		}
	}

	double pctc[5];
	ArrayInitialize(pctc, 0);
	double pctpp = 0;
	double pctnn = 0;

	for (int x = 4; x >= 0; x--)
	{
		pctc[x] = pctp[x] + pctn[x];
		pctpp += pctp[x];
		pctnn += pctn[x];
	}

	string pctps[5] = {"<0.25", "<0.50", "<0.75", "<1.00", ">1.00"};
	string pctns[5] = {">-0.25", ">-0.50", ">-0.75", ">-1.00", "<-1.00"};

	string obname;
	for (int x = 4; x >= 0; x--)
	{
		obname = Name + "dpdpct";
		LabelMake(obname, 3, 120, 165, "PD%: " + DoubleToStr(pct(CD1[1], CD1[2]), 2) + "%", 8, "Arial", clrBlue);
		if (pct(CD1[1], CD1[2]) < 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		obname = Name + "dpctp l" + IntegerToString(x);
		LabelMake(obname, 3, 155, 90 + x * 15, pctps[x], 7, "Arial", clrBlue);
		obname = Name + "dpctn l" + IntegerToString(x);
		LabelMake(obname, 3, 155, 75 - x * 15, pctns[x], 7, "Arial", clrRed);
		obname = Name + "dpctp" + IntegerToString(x);
		LabelMake(obname, 3, 120, 90 + x * 15, DoubleToStr(pctp[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " > " + DoubleToStr(0.0025 * x * iClose(_Symbol, PERIOD_D1, 1) + iClose(_Symbol, PERIOD_D1, 1), _Digits) + " - < " + DoubleToStr(0.0025 * (x + 1) * iClose(_Symbol, PERIOD_D1, 1) + iClose(_Symbol, PERIOD_D1, 1), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " > " + DoubleToStr(0.0025 * x * iClose(_Symbol, PERIOD_D1, 1) + iClose(_Symbol, PERIOD_D1, 1), _Digits));
		obname = Name + "dpctn" + IntegerToString(x);
		LabelMake(obname, 3, 120, 75 - x * 15, DoubleToStr(pctn[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " < " + DoubleToStr(-0.0025 * x * iClose(_Symbol, PERIOD_D1, 1) + iClose(_Symbol, PERIOD_D1, 1), _Digits) + " - > " + DoubleToStr(-0.0025 * (x + 1) * iClose(_Symbol, PERIOD_D1, 1) + iClose(_Symbol, PERIOD_D1, 1), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " < " + DoubleToStr(-0.0025 * x * iClose(_Symbol, PERIOD_D1, 1) + iClose(_Symbol, PERIOD_D1, 1), _Digits));
		obname = Name + "dpctc" + IntegerToString(x);
		LabelMake(obname, 3, 80, 82 + x * 15, DoubleToStr(pctc[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, DoubleToStr(-0.0025 * (x + 1) * iClose(_Symbol, PERIOD_D1, 1) + iClose(_Symbol, PERIOD_D1, 1), _Digits) + " - " + DoubleToStr(0.0025 * (x + 1) * iClose(_Symbol, PERIOD_D1, 1) + iClose(_Symbol, PERIOD_D1, 1), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, DoubleToStr(-0.0025 * x * iClose(_Symbol, PERIOD_D1, 1) + iClose(_Symbol, PERIOD_D1, 1), _Digits) + " - " + DoubleToStr(0.0025 * x * iClose(_Symbol, PERIOD_D1, 1) + iClose(_Symbol, PERIOD_D1, 1), _Digits));
	}

	obname = Name + "ddiff";
	LabelMake(obname, 3, 80, 60, "PV: " + DoubleToStr(0.0025 * iClose(_Symbol, PERIOD_D1, 1), _Digits), 7, "Arial", clrBlack);
	obname = Name + "damtdays";
	LabelMake(obname, 3, 40, 112, "Amt:" + DoubleToStr(belgross, 0), 7, "Arial", clrBlack);
	obname = Name + "dpctpp";
	LabelMake(obname, 3, 40, 142, DoubleToStr(pctpp, 2) + "%", 7, "Arial", clrBlue);
	obname = Name + "dpctnn";
	LabelMake(obname, 3, 40, 127, DoubleToStr(pctnn, 2) + "%", 7, "Arial", clrRed);
	obname = Name + "dnpct";
	LabelMake(obname, 3, 80, 50, "D: " + DoubleToStr(pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) * 100, 2), 7, "Arial", clrBlue);

	//Previous day end pct calculation
	int w1 = 0;
	if (pct(CD1[2], CD1[3]) > l && pct(CD1[2], CD1[3]) <= l1)
		w1 = 1;
	if (pct(CD1[2], CD1[3]) > l1 && pct(CD1[2], CD1[3]) <= l2)
		w1 = 3;
	if (pct(CD1[2], CD1[3]) > l2 && pct(CD1[2], CD1[3]) <= l3)
		w1 = 5;
	if (pct(CD1[2], CD1[3]) > l3 && pct(CD1[2], CD1[3]) <= l4)
		w1 = 7;
	if (pct(CD1[2], CD1[3]) > l4)
		w1 = 9;
	if (pct(CD1[2], CD1[3]) < -l && pct(CD1[2], CD1[3]) >= -l1)
		w1 = 11;
	if (pct(CD1[2], CD1[3]) < -l1 && pct(CD1[2], CD1[3]) >= -l2)
		w1 = 13;
	if (pct(CD1[2], CD1[3]) < -l2 && pct(CD1[2], CD1[3]) >= -l3)
		w1 = 15;
	if (pct(CD1[2], CD1[3]) < -l3 && pct(CD1[2], CD1[3]) >= -l4)
		w1 = 17;
	if (pct(CD1[2], CD1[3]) < -l4)
		w1 = 19;

	//Erase previous day result
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) >= l && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) <= l1)
		mixture[w1 - 1][0]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) > l1 && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) <= l2)
		mixture[w1 - 1][1]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) > l2 && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) <= l3)
		mixture[w1 - 1][2]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) > l3 && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) <= l4)
		mixture[w1 - 1][3]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) > l4)
		mixture[w1 - 1][4]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) < l && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) >= -l1)
		mixture[w1][0]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) < -l1 && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) >= -l2)
		mixture[w1][1]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) < -l2 && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) >= -l3)
		mixture[w1][2]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) < -l3 && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) >= -l4)
		mixture[w1][3]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) < -l4)
		mixture[w1][4]--;
	//---

	double belgrosspr = 0;

	for (int x = 4; x >= 0; x--)
	{
		for (int z = w1; z >= w1 - 1; z--)
			belgrosspr += mixture[z][x];
	}

	double pctppr[5];
	double pctnpr[5];
	ArrayInitialize(pctppr, 0);
	ArrayInitialize(pctnpr, 0);

	if (belgrosspr > 0)
	{
		for (int x = 4; x >= 0; x--)
		{
			pctppr[x] = mixture[w1 - 1][x] / belgrosspr * 100;
			pctnpr[x] = mixture[w1][x] / belgrosspr * 100;
		}
	}

	double fpr = 0;
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) >= l && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) <= l1)
		fpr = pctppr[0];
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) > l1 && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) <= l2)
		fpr = pctppr[1];
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) > l2 && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) <= l3)
		fpr = pctppr[2];
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) > l3 && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) <= l4)
		fpr = pctppr[3];
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) > l4)
		fpr = pctppr[4];
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) < l && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) >= -l1)
		fpr = pctnpr[0];
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) < -l1 && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) >= -l2)
		fpr = pctnpr[1];
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) < -l2 && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) >= -l3)
		fpr = pctnpr[2];
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) < -l3 && pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) >= -l4)
		fpr = pctnpr[3];
	if (pct(iClose(_Symbol, PERIOD_D1, 1), iClose(_Symbol, PERIOD_D1, 2)) < -l4)
		fpr = pctnpr[4];

	obname = Name + "dprev";
	LabelMake(obname, 3, 40, 165, DoubleToStr(fpr, 2) + "%", 8, "Arial", clrGreen);

	double SD1[];
	ArrayResize(SD1, ArraySize(CD1) - 3);
	ArrayInitialize(SD1, 0);

	for (int x = ArraySize(SD1) - 1; x >= 1; x--)
	{
		SD1[x] = ((CD1[x] - CD1[x + 1]) / CD1[x + 1]) * 100;
	}

	ArraySort(SD1, 0, 0, MODE_ASCEND);

	double cab = 0;
	int aaa = 0;
	for (int x = ArraySize(SD1) - 1; x >= 1; x--)
	{
		cab += MathAbs(SD1[x]);
		aaa++;
	}

	double endcab = NormalizeDouble(cab / (double)aaa, 4);

	obname = Name + "dadm";
	LabelMake(obname, 3, 80, 40, "ADM: " + DoubleToStr(endcab * 100, 2), 7, "Arial", clrPurple);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "H: " + DoubleToStr(CD1[1] + CD1[1] * endcab / 100, _Digits) + " L: " + DoubleToStr(CD1[1] - CD1[1] * endcab / 100, _Digits));

	moorday = endcab;

	int adv = 20 * Period() * 80;
	int adv2 = 20 * Period() * 60;
	obname = Name + "davgh";
	objtrend2(obname, CD1[1] + CD1[1] * moorday / 100, 0, 0, adv, adv2, 3, 0, clrGold, "AvgH: " + DoubleToStr(CD1[1] + CD1[1] * moorday / 100, _Digits));
	obname = Name + "davgh2";
	objtrend2(obname, CD1[1] + CD1[1] * moorday / 50, 0, 0, adv, adv2, 3, 0, clrGold, "AvgH*2: " + DoubleToStr(CD1[1] + CD1[1] * moorday / 50, _Digits));
	obname = Name + "davgl";
	objtrend2(obname, CD1[1] - CD1[1] * moorday / 100, 0, 0, adv, adv2, 3, 0, clrGold, "AvgL: " + DoubleToStr(CD1[1] - CD1[1] * moorday / 100, _Digits));
	obname = Name + "davgl2";
	objtrend2(obname, CD1[1] - CD1[1] * moorday / 50, 0, 0, adv, adv2, 3, 0, clrGold, "AvgL*2: " + DoubleToStr(CD1[1] - CD1[1] * moorday / 50, _Digits));

	double HD1[], LD1[];
	ArrayResize(HD1, 22);
	ArrayResize(LD1, 22);
	ArrayInitialize(HD1, 0);
	ArrayInitialize(LD1, 0);
	CopyHigh(_Symbol, PERIOD_D1, 1, 22, HD1);
	CopyLow(_Symbol, PERIOD_D1, 1, 22, LD1);

	double adr21 = 0;
	int bcount = 0;
	for (int x = 21; x >= 1; x--)
	{
		adr21 += HD1[x] - LD1[x];
		bcount++;
	}
	double bavg = (CD1[1] + endcab / 100 * CD1[1]) - (CD1[1] - endcab / 100 * CD1[1]);
	double amr = ((bavg / PipValues) - (adr21 / (double)bcount) / PipValues);

	datr = amr;

	obname = Name + "damr";
	LabelMake(obname, 3, 80, 30, "A-21R: " + DoubleToStr(amr, 2), 7, "Arial", clrGreen);
	obname = Name + "damt";
	LabelMake(obname, 3, 80, 20, "", 7, "Arial", clrGreen);

	/* FOR TEST & CONFIRM REASONS WITH SCRIPT
	//Print(pct(CD1[1], OD1[1]));
	//Print(mixture[0][0] + mixture[0][1]+mixture[0][2]+mixture[0][3]+mixture[0][4]);
	//Print(mixture[1][1] + mixture[1][1]+mixture[1][2]+mixture[1][3]+mixture[1][4]);
	//Print(belgross);
	//POS
	double belgross1 = 0;

	for (int x = 4; x >= 0; x--) {
		for (int y = 1; y >= 0; y--)
		belgross1 += mixture[y][x];
	}
	Print(belgross1);

	double belgross2 = 0;

	for (int x = 4; x >= 0; x--) {
		for (int y = 3; y >= 2; y--)
		belgross2 += mixture[y][x];
		//belgross += mixture[1][x];
	}
	Print(belgross2);

	double belgross3 = 0;

	for (int x = 4; x >= 0; x--) {
		for (int y = 5; y >= 4; y--)
		belgross3 += mixture[y][x];
		//belgross += mixture[1][x];
	}
	Print(belgross3);

	double belgross4 = 0;

	for (int x = 4; x >= 0; x--) {
		for (int y = 7; y >= 6; y--)
		belgross4 += mixture[y][x];
		//belgross += mixture[1][x];
	}
	Print(belgross4);

	double belgross5 = 0;

	for (int x = 4; x >= 0; x--) {
		for (int y = 9; y >= 8; y--)
		belgross5 += mixture[y][x];
		//belgross += mixture[1][x];
	}
	Print(belgross5);

	//NEG
	double belgross6 = 0;

	for (int x = 4; x >= 0; x--) {
		for (int y = 11; y >= 10; y--)
		belgross6 += mixture[y][x];
		//belgross += mixture[1][x];
	}
	Print(belgross6);

	double belgross7 = 0;

	for (int x = 4; x >= 0; x--) {
		for (int y = 13; y >= 12; y--)
		belgross7 += mixture[y][x];
		//belgross += mixture[1][x];
	}
	Print(belgross7);

	double belgross8 = 0;

	for (int x = 4; x >= 0; x--) {
		for (int y = 15; y >= 14; y--)
		belgross8 += mixture[y][x];
		//belgross += mixture[1][x];
	}
	Print(belgross8);

	double belgross9 = 0;

	for (int x = 4; x >= 0; x--) {
		for (int y = 17; y >= 16; y--)
		belgross9 += mixture[y][x];
		//belgross += mixture[1][x];
	}
	Print(belgross9);

	double belgross10 = 0;

	for (int x = 4; x >= 0; x--) {
		for (int y = 19; y >= 18; y--)
		belgross10 += mixture[y][x];
		//belgross += mixture[1][x];
	}
	Print(belgross10);
	*/
}
//+------------------------------------------------------------------+

//+DRAW PERCENTAGES FOR CURRENT DAY----------------------------------+
void drawpctc()
{
	int mixture[20][5];
	ArrayInitialize(mixture, 0);

	if (iBars(_Symbol, PERIOD_D1) < periods)
		periods = iBars(_Symbol, PERIOD_D1) - 4;

	double l = 0.00, l1 = 0.25, l2 = 0.50, l3 = 0.75, l4 = 1.00;

	double CD1[];
	ArraySetAsSeries(CD1, true);
	ArrayResize(CD1, periods + 3);
	CopyClose(_Symbol, PERIOD_D1, 0, periods + 3, CD1);

	for (int i = periods; i >= 1; i--)
	{
		//POS
		if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[0][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[0][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[0][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[0][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[0][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[1][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[1][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[1][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[1][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[1][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[2][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[2][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[2][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[2][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[2][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[3][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[3][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[3][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[3][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[3][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[4][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[4][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[4][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[4][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[4][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[5][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[5][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[5][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[5][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[5][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[6][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[6][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[6][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[6][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[6][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[7][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[7][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[7][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[7][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[7][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[8][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[8][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[8][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[8][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[8][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[9][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[9][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[9][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[9][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[9][4]++;

		//NEG
		if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[10][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[10][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[10][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[10][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[10][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[11][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[11][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[11][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[11][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[11][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[12][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[12][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[12][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[12][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[12][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[13][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[13][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[13][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[13][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[13][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[14][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[14][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[14][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[14][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[14][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[15][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[15][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[15][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[15][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[15][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[16][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[16][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[16][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[16][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[16][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[17][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[17][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[17][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[17][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[17][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[18][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[18][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[18][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[18][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[18][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[19][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[19][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[19][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[19][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[19][4]++;
	}

	int y1 = 0;
	if (pct(CD1[0], CD1[1]) >= l && pct(CD1[0], CD1[1]) <= l1)
		y1 = 1;
	if (pct(CD1[0], CD1[1]) > l1 && pct(CD1[0], CD1[1]) <= l2)
		y1 = 3;
	if (pct(CD1[0], CD1[1]) > l2 && pct(CD1[0], CD1[1]) <= l3)
		y1 = 5;
	if (pct(CD1[0], CD1[1]) > l3 && pct(CD1[0], CD1[1]) <= l4)
		y1 = 7;
	if (pct(CD1[0], CD1[1]) > l4)
		y1 = 9;
	if (pct(CD1[0], CD1[1]) < -l && pct(CD1[0], CD1[1]) >= -l1)
		y1 = 11;
	if (pct(CD1[0], CD1[1]) < -l1 && pct(CD1[0], CD1[1]) >= -l2)
		y1 = 13;
	if (pct(CD1[0], CD1[1]) < -l2 && pct(CD1[0], CD1[1]) >= -l3)
		y1 = 15;
	if (pct(CD1[0], CD1[1]) < -l3 && pct(CD1[0], CD1[1]) >= -l4)
		y1 = 17;
	if (pct(CD1[0], CD1[1]) < -l4)
		y1 = 19;

	double belgross = 0;

	for (int x = 4; x >= 0; x--)
	{
		for (int z = y1; z >= y1 - 1; z--)
			belgross += mixture[z][x];
	}

	double pctp[5];
	double pctn[5];
	ArrayInitialize(pctp, 0);
	ArrayInitialize(pctn, 0);

	if (belgross > 0)
	{
		for (int x = 4; x >= 0; x--)
		{
			pctp[x] = mixture[y1 - 1][x] / belgross * 100;
			pctn[x] = mixture[y1][x] / belgross * 100;
		}
	}

	double pctc[5];
	ArrayInitialize(pctc, 0);
	double pctpp = 0;
	double pctnn = 0;

	for (int x = 4; x >= 0; x--)
	{
		pctc[x] = pctp[x] + pctn[x];
		pctpp += pctp[x];
		pctnn += pctn[x];
	}

	string pctps[5] = {"<0.25", "<0.50", "<0.75", "<1.00", ">1.00"};
	string pctns[5] = {">-0.25", ">-0.50", ">-0.75", ">-1.00", "<-1.00"};

	string obname;
	for (int x = 4; x >= 0; x--)
	{
		obname = Name + "cpdpct";
		LabelMake(obname, 3, 120, 705, "CD%: " + DoubleToStr(pct(CD1[0], CD1[1]), 2) + "%", 8, "Arial", clrBlue);
		if (pct(CD1[0], CD1[1]) < 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		obname = Name + "cpctp l" + IntegerToString(x);
		LabelMake(obname, 3, 155, 630 + x * 15, pctps[x], 7, "Arial", clrBlue);
		obname = Name + "cpctn l" + IntegerToString(x);
		LabelMake(obname, 3, 155, 615 - x * 15, pctns[x], 7, "Arial", clrRed);
		obname = Name + "cpctp" + IntegerToString(x);
		LabelMake(obname, 3, 120, 630 + x * 15, DoubleToStr(pctp[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " > " + DoubleToStr(0.0025 * x * iClose(_Symbol, PERIOD_D1, 0) + iClose(_Symbol, PERIOD_D1, 0), _Digits) + " - < " + DoubleToStr(0.0025 * (x + 1) * iClose(_Symbol, PERIOD_D1, 0) + iClose(_Symbol, PERIOD_D1, 0), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " > " + DoubleToStr(0.0025 * x * iClose(_Symbol, PERIOD_D1, 0) + iClose(_Symbol, PERIOD_D1, 0), _Digits));
		obname = Name + "cpctn" + IntegerToString(x);
		LabelMake(obname, 3, 120, 615 - x * 15, DoubleToStr(pctn[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " < " + DoubleToStr(-0.0025 * x * iClose(_Symbol, PERIOD_D1, 0) + iClose(_Symbol, PERIOD_D1, 0), _Digits) + " - > " + DoubleToStr(-0.0025 * (x + 1) * iClose(_Symbol, PERIOD_D1, 0) + iClose(_Symbol, PERIOD_D1, 0), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " < " + DoubleToStr(-0.0025 * x * iClose(_Symbol, PERIOD_D1, 0) + iClose(_Symbol, PERIOD_D1, 0), _Digits));
		obname = Name + "cpctc" + IntegerToString(x);
		LabelMake(obname, 3, 80, 622 + x * 15, DoubleToStr(pctc[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, DoubleToStr(-0.0025 * (x + 1) * iClose(_Symbol, PERIOD_D1, 0) + iClose(_Symbol, PERIOD_D1, 0), _Digits) + " - " + DoubleToStr(0.0025 * (x + 1) * iClose(_Symbol, PERIOD_D1, 0) + iClose(_Symbol, PERIOD_D1, 0), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, DoubleToStr(-0.0025 * x * iClose(_Symbol, PERIOD_D1, 0) + iClose(_Symbol, PERIOD_D1, 0), _Digits) + " - " + DoubleToStr(0.0025 * x * iClose(_Symbol, PERIOD_D1, 0) + iClose(_Symbol, PERIOD_D1, 0), _Digits));
	}

	obname = Name + "cdiff";
	LabelMake(obname, 3, 80, 600, "PV: " + DoubleToStr(0.0025 * iClose(_Symbol, PERIOD_D1, 0), _Digits), 7, "Arial", clrBlack);
	obname = Name + "camtdays";
	LabelMake(obname, 3, 40, 652, "Amt:" + DoubleToStr(belgross, 0), 7, "Arial", clrBlack);
	obname = Name + "cpctpp";
	LabelMake(obname, 3, 40, 682, DoubleToStr(pctpp, 2) + "%", 7, "Arial", clrBlue);
	obname = Name + "cpctnn";
	LabelMake(obname, 3, 40, 667, DoubleToStr(pctnn, 2) + "%", 7, "Arial", clrRed);

	//Previous day end pct calculation
	int w1 = 0;
	if (pct(CD1[1], CD1[2]) > l && pct(CD1[1], CD1[2]) <= l1)
		w1 = 1;
	if (pct(CD1[1], CD1[2]) > l1 && pct(CD1[1], CD1[2]) <= l2)
		w1 = 3;
	if (pct(CD1[1], CD1[2]) > l2 && pct(CD1[1], CD1[2]) <= l3)
		w1 = 5;
	if (pct(CD1[1], CD1[2]) > l3 && pct(CD1[1], CD1[2]) <= l4)
		w1 = 7;
	if (pct(CD1[1], CD1[2]) > l4)
		w1 = 9;
	if (pct(CD1[1], CD1[2]) < -l && pct(CD1[1], CD1[2]) >= -l1)
		w1 = 11;
	if (pct(CD1[1], CD1[2]) < -l1 && pct(CD1[1], CD1[2]) >= -l2)
		w1 = 13;
	if (pct(CD1[1], CD1[2]) < -l2 && pct(CD1[1], CD1[2]) >= -l3)
		w1 = 15;
	if (pct(CD1[1], CD1[2]) < -l3 && pct(CD1[1], CD1[2]) >= -l4)
		w1 = 17;
	if (pct(CD1[1], CD1[2]) < -l4)
		w1 = 19;

	/*
	//Erase previous day result
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= l && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) <= l1)
		mixture[w1 - 1][0]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) > l1 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) <= l2)
		mixture[w1 - 1][1]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) > l2 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) <= l3)
		mixture[w1 - 1][2]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) > l3 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) <= l4)
		mixture[w1 - 1][3]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) > l4)
		mixture[w1 - 1][4]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < l && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= -l1)
		mixture[w1][0]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < -l1 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= -l2)
		mixture[w1][1]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < -l2 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= -l3)
		mixture[w1][2]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < -l3 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= -l4)
		mixture[w1][3]--;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < -l4)
		mixture[w1][4]--;
	//--
	*/

	double belgrosspr = 0;

	for (int x = 4; x >= 0; x--)
	{
		for (int z = w1; z >= w1 - 1; z--)
			belgrosspr += mixture[z][x];
	}

	double pctppr[5];
	double pctnpr[5];
	ArrayInitialize(pctppr, 0);
	ArrayInitialize(pctnpr, 0);

	if (belgrosspr > 0)
	{
		for (int x = 4; x >= 0; x--)
		{
			pctppr[x] = mixture[w1 - 1][x] / belgrosspr * 100;
			pctnpr[x] = mixture[w1][x] / belgrosspr * 100;
		}
	}

	double fpr = 0;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= l && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) <= l1)
		fpr = pctppr[0];
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) > l1 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) <= l2)
		fpr = pctppr[1];
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) > l2 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) <= l3)
		fpr = pctppr[2];
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) > l3 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) <= l4)
		fpr = pctppr[3];
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) > l4)
		fpr = pctppr[4];
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < l && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= -l1)
		fpr = pctnpr[0];
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < -l1 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= -l2)
		fpr = pctnpr[1];
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < -l2 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= -l3)
		fpr = pctnpr[2];
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < -l3 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= -l4)
		fpr = pctnpr[3];
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < -l4)
		fpr = pctnpr[4];

	obname = Name + "cprev";
	LabelMake(obname, 3, 40, 705, DoubleToStr(fpr, 2) + "%", 8, "Arial", clrGreen);

	double SD1[];
	ArrayResize(SD1, ArraySize(CD1) - 3);
	ArrayInitialize(SD1, 0);

	for (int x = ArraySize(SD1) - 1; x >= 1; x--)
	{
		SD1[x] = ((CD1[x] - CD1[x + 1]) / CD1[x + 1]) * 100;
	}

	ArraySort(SD1, 0, 0, MODE_ASCEND);

	double cab = 0;
	int aaa = 0;
	for (int x = ArraySize(SD1) - 1; x >= 1; x--)
	{
		cab += MathAbs(SD1[x]);
		aaa++;
	}

	double endcab = NormalizeDouble(cab / (double)aaa, 4);
	currentdaynext = endcab;

	obname = Name + "cadm";
	LabelMake(obname, 3, 80, 580, "AWM: " + DoubleToStr(endcab * 100, 2), 7, "Arial", clrPurple);
}
//+------------------------------------------------------------------+

//+DRAW PERCENTAGES FOR WEEKLY---------------------------------------+
void drawpctw()
{
	int mixture[20][5];
	ArrayInitialize(mixture, 0);

	int perw = 312;
	if (iBars(_Symbol, PERIOD_W1) < perw)
		perw = iBars(_Symbol, PERIOD_W1) - 4;

	double l = 0.00, l1 = 1.00, l2 = 2.00, l3 = 3.00, l4 = 4.00;

	double CD1[];
	ArraySetAsSeries(CD1, true);
	ArrayResize(CD1, perw + 3);
	CopyClose(_Symbol, PERIOD_W1, 0, perw + 3, CD1);

	for (int i = perw; i >= 1; i--)
	{
		//POS
		if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[0][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[0][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[0][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[0][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[0][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[1][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[1][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[1][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[1][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[1][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[2][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[2][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[2][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[2][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[2][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[3][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[3][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[3][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[3][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[3][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[4][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[4][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[4][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[4][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[4][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[5][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[5][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[5][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[5][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[5][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[6][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[6][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[6][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[6][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[6][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[7][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[7][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[7][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[7][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[7][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[8][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[8][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[8][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[8][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[8][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[9][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[9][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[9][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[9][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[9][4]++;

		//NEG
		if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[10][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[10][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[10][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[10][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[10][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[11][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[11][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[11][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[11][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[11][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[12][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[12][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[12][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[12][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[12][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[13][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[13][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[13][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[13][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[13][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[14][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[14][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[14][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[14][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[14][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[15][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[15][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[15][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[15][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[15][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[16][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[16][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[16][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[16][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[16][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[17][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[17][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[17][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[17][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[17][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[18][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[18][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[18][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[18][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[18][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[19][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[19][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[19][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[19][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[19][4]++;
	}

	int y1 = 0;
	if (pct(CD1[1], CD1[2]) >= l && pct(CD1[1], CD1[2]) <= l1)
		y1 = 1;
	if (pct(CD1[1], CD1[2]) > l1 && pct(CD1[1], CD1[2]) <= l2)
		y1 = 3;
	if (pct(CD1[1], CD1[2]) > l2 && pct(CD1[1], CD1[2]) <= l3)
		y1 = 5;
	if (pct(CD1[1], CD1[2]) > l3 && pct(CD1[1], CD1[2]) <= l4)
		y1 = 7;
	if (pct(CD1[1], CD1[2]) > l4)
		y1 = 9;
	if (pct(CD1[1], CD1[2]) < -l && pct(CD1[1], CD1[2]) >= -l1)
		y1 = 11;
	if (pct(CD1[1], CD1[2]) < -l1 && pct(CD1[1], CD1[2]) >= -l2)
		y1 = 13;
	if (pct(CD1[1], CD1[2]) < -l2 && pct(CD1[1], CD1[2]) >= -l3)
		y1 = 15;
	if (pct(CD1[1], CD1[2]) < -l3 && pct(CD1[1], CD1[2]) >= -l4)
		y1 = 17;
	if (pct(CD1[1], CD1[2]) < -l4)
		y1 = 19;

	double belgross = 0;

	for (int x = 4; x >= 0; x--)
	{
		for (int z = y1; z >= y1 - 1; z--)
			belgross += mixture[z][x];
	}

	double pctp[5];
	double pctn[5];
	ArrayInitialize(pctp, 0);
	ArrayInitialize(pctn, 0);

	if (belgross > 0)
	{
		for (int x = 4; x >= 0; x--)
		{
			pctp[x] = mixture[y1 - 1][x] / belgross * 100;
			pctn[x] = mixture[y1][x] / belgross * 100;
		}
	}

	double pctc[5];
	ArrayInitialize(pctc, 0);
	double pctpp = 0;
	double pctnn = 0;

	for (int x = 4; x >= 0; x--)
	{
		pctc[x] = pctp[x] + pctn[x];
		pctpp += pctp[x];
		pctnn += pctn[x];
	}

	string pctps[5] = {"<1.00", "<2.00", "<3.00", "<4.00", ">4.00"};
	string pctns[5] = {">-1.00", ">-2.00", ">-3.00", ">-4.00", "<-4.00"};

	string obname;
	for (int x = 4; x >= 0; x--)
	{
		obname = Name + "wpdpct";
		LabelMake(obname, 3, 120, 345, "PW%: " + DoubleToStr(pct(CD1[1], CD1[2]), 2) + "%", 8, "Arial", clrBlue);
		if (pct(CD1[1], CD1[2]) < 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		obname = Name + "wpctp l" + IntegerToString(x);
		LabelMake(obname, 3, 155, 270 + x * 15, pctps[x], 7, "Arial", clrBlue);
		obname = Name + "wpctn l" + IntegerToString(x);
		LabelMake(obname, 3, 155, 255 - x * 15, pctns[x], 7, "Arial", clrRed);
		obname = Name + "wpctp" + IntegerToString(x);
		LabelMake(obname, 3, 120, 270 + x * 15, DoubleToStr(pctp[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " > " + DoubleToStr(0.0100 * x * iClose(_Symbol, PERIOD_W1, 1) + iClose(_Symbol, PERIOD_W1, 1), _Digits) + " - < " + DoubleToStr(0.0100 * (x + 1) * iClose(_Symbol, PERIOD_W1, 1) + iClose(_Symbol, PERIOD_W1, 1), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " > " + DoubleToStr(0.0100 * x * iClose(_Symbol, PERIOD_W1, 1) + iClose(_Symbol, PERIOD_W1, 1), _Digits));
		obname = Name + "wpctn" + IntegerToString(x);
		LabelMake(obname, 3, 120, 255 - x * 15, DoubleToStr(pctn[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " < " + DoubleToStr(-0.0100 * x * iClose(_Symbol, PERIOD_W1, 1) + iClose(_Symbol, PERIOD_W1, 1), _Digits) + " - > " + DoubleToStr(-0.0100 * (x + 1) * iClose(_Symbol, PERIOD_W1, 1) + iClose(_Symbol, PERIOD_W1, 1), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " < " + DoubleToStr(-0.0100 * x * iClose(_Symbol, PERIOD_W1, 1) + iClose(_Symbol, PERIOD_W1, 1), _Digits));
		obname = Name + "wpctc" + IntegerToString(x);
		LabelMake(obname, 3, 80, 262 + x * 15, DoubleToStr(pctc[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, DoubleToStr(-0.0100 * (x + 1) * iClose(_Symbol, PERIOD_W1, 1) + iClose(_Symbol, PERIOD_W1, 1), _Digits) + " - " + DoubleToStr(0.0100 * (x + 1) * iClose(_Symbol, PERIOD_W1, 1) + iClose(_Symbol, PERIOD_W1, 1), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, DoubleToStr(-0.0100 * x * iClose(_Symbol, PERIOD_W1, 1) + iClose(_Symbol, PERIOD_W1, 1), _Digits) + " - " + DoubleToStr(0.0100 * x * iClose(_Symbol, PERIOD_W1, 1) + iClose(_Symbol, PERIOD_W1, 1), _Digits));
	}

	obname = Name + "wdiff";
	LabelMake(obname, 3, 80, 240, "PV: " + DoubleToStr(0.0100 * iClose(_Symbol, PERIOD_W1, 1), _Digits), 7, "Arial", clrBlack);
	obname = Name + "wamtdays";
	LabelMake(obname, 3, 40, 292, "Amt:" + DoubleToStr(belgross, 0), 7, "Arial", clrBlack);
	obname = Name + "wpctpp";
	LabelMake(obname, 3, 40, 322, DoubleToStr(pctpp, 2) + "%", 7, "Arial", clrBlue);
	obname = Name + "wpctnn";
	LabelMake(obname, 3, 40, 307, DoubleToStr(pctnn, 2) + "%", 7, "Arial", clrRed);
	obname = Name + "wnpct";
	LabelMake(obname, 3, 80, 230, "W: " + DoubleToStr(pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) * 100, 2), 7, "Arial", clrBlue);

	//Previous week end pct calculation
	int w1 = 0;
	if (pct(CD1[2], CD1[3]) >= l && pct(CD1[2], CD1[3]) <= l1)
		w1 = 1;
	if (pct(CD1[2], CD1[3]) > l1 && pct(CD1[2], CD1[3]) <= l2)
		w1 = 3;
	if (pct(CD1[2], CD1[3]) > l2 && pct(CD1[2], CD1[3]) <= l3)
		w1 = 5;
	if (pct(CD1[2], CD1[3]) > l3 && pct(CD1[2], CD1[3]) <= l4)
		w1 = 7;
	if (pct(CD1[2], CD1[3]) > l4)
		w1 = 9;
	if (pct(CD1[2], CD1[3]) < -l && pct(CD1[2], CD1[3]) >= -l1)
		w1 = 11;
	if (pct(CD1[2], CD1[3]) < -l1 && pct(CD1[2], CD1[3]) >= -l2)
		w1 = 13;
	if (pct(CD1[2], CD1[3]) < -l2 && pct(CD1[2], CD1[3]) >= -l3)
		w1 = 15;
	if (pct(CD1[2], CD1[3]) < -l3 && pct(CD1[2], CD1[3]) >= -l4)
		w1 = 17;
	if (pct(CD1[2], CD1[3]) < -l4)
		w1 = 19;

	//Erase previous week result
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) >= l && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) <= l1)
		mixture[w1 - 1][0]--;
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) > l1 && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) <= l2)
		mixture[w1 - 1][1]--;
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) > l2 && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) <= l3)
		mixture[w1 - 1][2]--;
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) > l3 && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) <= l4)
		mixture[w1 - 1][3]--;
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) > l4)
		mixture[w1 - 1][4]--;
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) < l && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) >= -l1)
		mixture[w1][0]--;
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) < -l1 && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) >= -l2)
		mixture[w1][1]--;
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) < -l2 && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) >= -l3)
		mixture[w1][2]--;
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) < -l3 && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) >= -l4)
		mixture[w1][3]--;
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) < -l4)
		mixture[w1][4]--;
	//---

	double belgrosspr = 0;

	for (int x = 4; x >= 0; x--)
	{
		for (int z = w1; z >= w1 - 1; z--)
			belgrosspr += mixture[z][x];
	}

	double pctppr[5];
	double pctnpr[5];
	ArrayInitialize(pctppr, 0);
	ArrayInitialize(pctnpr, 0);

	if (belgrosspr > 0)
	{
		for (int x = 4; x >= 0; x--)
		{
			pctppr[x] = mixture[w1 - 1][x] / belgrosspr * 100;
			pctnpr[x] = mixture[w1][x] / belgrosspr * 100;
		}
	}

	double fpr = 0;
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) >= l && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) <= l1)
		fpr = pctppr[0];
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) > l1 && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) <= l2)
		fpr = pctppr[1];
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) > l2 && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) <= l3)
		fpr = pctppr[2];
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) > l3 && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) <= l4)
		fpr = pctppr[3];
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) > l4)
		fpr = pctppr[4];
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) < l && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) >= -l1)
		fpr = pctnpr[0];
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) < -l1 && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) >= -l2)
		fpr = pctnpr[1];
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) < -l2 && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) >= -l3)
		fpr = pctnpr[2];
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) < -l3 && pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) >= -l4)
		fpr = pctnpr[3];
	if (pct(iClose(_Symbol, PERIOD_W1, 1), iClose(_Symbol, PERIOD_W1, 2)) < -l4)
		fpr = pctnpr[4];

	obname = Name + "wprev";
	LabelMake(obname, 3, 40, 345, DoubleToStr(fpr, 2) + "%", 8, "Arial", clrGreen);

	double SD1[];
	ArrayResize(SD1, ArraySize(CD1) - 3);
	ArrayInitialize(SD1, 0);

	for (int x = ArraySize(SD1) - 1; x >= 1; x--)
	{
		SD1[x] = ((CD1[x] - CD1[x + 1]) / CD1[x + 1]) * 100;
	}

	ArraySort(SD1, 0, 0, MODE_ASCEND);

	double cab = 0;
	int aaa = 0;
	for (int x = ArraySize(SD1) - 1; x >= 1; x--)
	{
		cab += MathAbs(SD1[x]);
		aaa++;
	}

	double endcab = NormalizeDouble(cab / (double)aaa, 4);

	obname = Name + "wadm";
	LabelMake(obname, 3, 80, 220, "AWM: " + DoubleToStr(endcab * 100, 2), 7, "Arial", clrPurple);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "H: " + DoubleToStr(CD1[1] + CD1[1] * endcab / 100, _Digits) + " L: " + DoubleToStr(CD1[1] - CD1[1] * endcab / 100, _Digits));

	moorweek = endcab;

	int adv = 20 * Period() * 100;
	int adv2 = 20 * Period() * 50;
	obname = Name + "wavgh";
	objtrend2(obname, CD1[1] + CD1[1] * moorweek / 100, 0, 0, adv, adv2, 3, 0, clrGold, "AvgH: " + DoubleToStr(CD1[1] + CD1[1] * moorweek / 100, _Digits));
	obname = Name + "wavgl";
	objtrend2(obname, CD1[1] - CD1[1] * moorweek / 100, 0, 0, adv, adv2, 3, 0, clrGold, "AvgL: " + DoubleToStr(CD1[1] - CD1[1] * moorweek / 100, _Digits));

	double HD1[], LD1[];
	ArrayResize(HD1, 22);
	ArrayResize(LD1, 22);
	ArrayInitialize(HD1, 0);
	ArrayInitialize(LD1, 0);
	CopyHigh(_Symbol, PERIOD_W1, 1, 22, HD1);
	CopyLow(_Symbol, PERIOD_W1, 1, 22, LD1);

	double adr21 = 0;
	int bcount = 0;
	for (int x = 21; x >= 1; x--)
	{
		adr21 += HD1[x] - LD1[x];
		bcount++;
	}
	double bavg = (CD1[1] + endcab / 100 * CD1[1]) - (CD1[1] - endcab / 100 * CD1[1]);
	double amr = ((bavg / PipValues) - (adr21 / (double)bcount) / PipValues);

	watr = amr;

	obname = Name + "wamr";
	LabelMake(obname, 3, 80, 210, "A-21R: " + DoubleToStr(amr, 2), 7, "Arial", clrGreen);
	obname = Name + "wamt";
	LabelMake(obname, 3, 80, 200, "", 7, "Arial", clrGreen);
}
//+------------------------------------------------------------------+

//+DRAW PERCENTAGES FOR 4H-------------------------------------------+
void drawpct4()
{
	int mixture[20][5];
	ArrayInitialize(mixture, 0);

	int per4 = 1500;
	if (iBars(_Symbol, PERIOD_H4) < per4)
		per4 = iBars(_Symbol, PERIOD_H4) - 4;

	double l = 0.00, l1 = 0.125, l2 = 0.250, l3 = 0.375, l4 = 0.500;

	double CD1[];
	ArraySetAsSeries(CD1, true);
	ArrayResize(CD1, per4 + 3);
	CopyClose(_Symbol, PERIOD_H4, 0, per4 + 3, CD1);

	for (int i = per4; i >= 1; i--)
	{
		//POS
		if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[0][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[0][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[0][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[0][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[0][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[1][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[1][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[1][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[1][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[1][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[2][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[2][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[2][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[2][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[2][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[3][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[3][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[3][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[3][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[3][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[4][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[4][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[4][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[4][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[4][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[5][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[5][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[5][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[5][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[5][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[6][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[6][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[6][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[6][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[6][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[7][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[7][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[7][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[7][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[7][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[8][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[8][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[8][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[8][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[8][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[9][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[9][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[9][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[9][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[9][4]++;

		//NEG
		if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[10][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[10][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[10][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[10][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[10][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[11][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[11][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[11][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[11][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[11][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[12][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[12][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[12][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[12][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[12][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[13][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[13][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[13][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[13][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[13][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[14][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[14][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[14][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[14][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[14][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[15][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[15][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[15][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[15][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[15][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[16][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[16][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[16][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[16][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[16][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[17][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[17][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[17][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[17][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[17][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[18][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[18][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[18][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[18][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[18][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[19][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[19][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[19][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[19][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[19][4]++;
	}

	int y1 = 0;
	if (pct(CD1[1], CD1[2]) >= l && pct(CD1[1], CD1[2]) <= l1)
		y1 = 1;
	if (pct(CD1[1], CD1[2]) > l1 && pct(CD1[1], CD1[2]) <= l2)
		y1 = 3;
	if (pct(CD1[1], CD1[2]) > l2 && pct(CD1[1], CD1[2]) <= l3)
		y1 = 5;
	if (pct(CD1[1], CD1[2]) > l3 && pct(CD1[1], CD1[2]) <= l4)
		y1 = 7;
	if (pct(CD1[1], CD1[2]) > l4)
		y1 = 9;
	if (pct(CD1[1], CD1[2]) < -l && pct(CD1[1], CD1[2]) >= -l1)
		y1 = 11;
	if (pct(CD1[1], CD1[2]) < -l1 && pct(CD1[1], CD1[2]) >= -l2)
		y1 = 13;
	if (pct(CD1[1], CD1[2]) < -l2 && pct(CD1[1], CD1[2]) >= -l3)
		y1 = 15;
	if (pct(CD1[1], CD1[2]) < -l3 && pct(CD1[1], CD1[2]) >= -l4)
		y1 = 17;
	if (pct(CD1[1], CD1[2]) < -l4)
		y1 = 19;

	double belgross = 0;

	for (int x = 4; x >= 0; x--)
	{
		for (int z = y1; z >= y1 - 1; z--)
			belgross += mixture[z][x];
	}

	double pctp[5];
	double pctn[5];
	ArrayInitialize(pctp, 0);
	ArrayInitialize(pctn, 0);

	if (belgross > 0)
	{
		for (int x = 4; x >= 0; x--)
		{
			pctp[x] = mixture[y1 - 1][x] / belgross * 100;
			pctn[x] = mixture[y1][x] / belgross * 100;
		}
	}

	double pctc[5];
	ArrayInitialize(pctc, 0);
	double pctpp = 0;
	double pctnn = 0;

	for (int x = 4; x >= 0; x--)
	{
		pctc[x] = pctp[x] + pctn[x];
		pctpp += pctp[x];
		pctnn += pctn[x];
	}

	string pctps[5] = {"<0.125", "<0.250", "<0.375", "<0.500", ">0.500"};
	string pctns[5] = {">-0.125", ">-0.250", ">-0.375", ">-0.500", "<-0.500"};

	string obname;
	for (int x = 4; x >= 0; x--)
	{
		obname = Name + "4pdpct";
		LabelMake(obname, 3, 120, 525, "P4H%: " + DoubleToStr(pct(CD1[1], CD1[2]), 2) + "%", 8, "Arial", clrBlue);
		if (pct(CD1[1], CD1[2]) < 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		obname = Name + "4pctp l" + IntegerToString(x);
		LabelMake(obname, 3, 155, 450 + x * 15, pctps[x], 7, "Arial", clrBlue);
		obname = Name + "4pctn l" + IntegerToString(x);
		LabelMake(obname, 3, 155, 435 - x * 15, pctns[x], 7, "Arial", clrRed);
		obname = Name + "4pctp" + IntegerToString(x);
		LabelMake(obname, 3, 120, 450 + x * 15, DoubleToStr(pctp[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " > " + DoubleToStr(0.00125 * x * iClose(_Symbol, PERIOD_H4, 1) + iClose(_Symbol, PERIOD_H4, 1), _Digits) + " - < " + DoubleToStr(0.00125 * (x + 1) * iClose(_Symbol, PERIOD_H4, 1) + iClose(_Symbol, PERIOD_H4, 1), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " > " + DoubleToStr(0.00125 * x * iClose(_Symbol, PERIOD_H4, 1) + iClose(_Symbol, PERIOD_H4, 1), _Digits));
		obname = Name + "4pctn" + IntegerToString(x);
		LabelMake(obname, 3, 120, 435 - x * 15, DoubleToStr(pctn[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " < " + DoubleToStr(-0.00125 * x * iClose(_Symbol, PERIOD_H4, 1) + iClose(_Symbol, PERIOD_H4, 1), _Digits) + " - > " + DoubleToStr(-0.00125 * (x + 1) * iClose(_Symbol, PERIOD_H4, 1) + iClose(_Symbol, PERIOD_H4, 1), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " < " + DoubleToStr(-0.00125 * x * iClose(_Symbol, PERIOD_H4, 1) + iClose(_Symbol, PERIOD_H4, 1), _Digits));
		obname = Name + "4pctc" + IntegerToString(x);
		LabelMake(obname, 3, 80, 442 + x * 15, DoubleToStr(pctc[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, DoubleToStr(-0.00125 * (x + 1) * iClose(_Symbol, PERIOD_H4, 1) + iClose(_Symbol, PERIOD_H4, 1), _Digits) + " - " + DoubleToStr(0.00125 * (x + 1) * iClose(_Symbol, PERIOD_H4, 1) + iClose(_Symbol, PERIOD_H4, 1), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, DoubleToStr(-0.00125 * x * iClose(_Symbol, PERIOD_H4, 1) + iClose(_Symbol, PERIOD_H4, 1), _Digits) + " - " + DoubleToStr(0.00125 * x * iClose(_Symbol, PERIOD_H4, 1) + iClose(_Symbol, PERIOD_H4, 1), _Digits));
	}

	obname = Name + "4diff";
	LabelMake(obname, 3, 80, 420, "PV: " + DoubleToStr(0.00125 * iClose(_Symbol, PERIOD_H4, 1), _Digits), 7, "Arial", clrBlack);
	obname = Name + "4amtdays";
	LabelMake(obname, 3, 40, 472, "Amt:" + DoubleToStr(belgross, 0), 7, "Arial", clrBlack);
	obname = Name + "4pctpp";
	LabelMake(obname, 3, 40, 502, DoubleToStr(pctpp, 2) + "%", 7, "Arial", clrBlue);
	obname = Name + "4pctnn";
	LabelMake(obname, 3, 40, 487, DoubleToStr(pctnn, 2) + "%", 7, "Arial", clrRed);
	obname = Name + "4npct";
	LabelMake(obname, 3, 80, 410, "4H: " + DoubleToStr(pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) * 100, 2), 7, "Arial", clrBlue);

	//Previous H4 end pct calculation
	int w1 = 0;
	if (pct(CD1[2], CD1[3]) >= l && pct(CD1[2], CD1[3]) <= l1)
		w1 = 1;
	if (pct(CD1[2], CD1[3]) > l1 && pct(CD1[2], CD1[3]) <= l2)
		w1 = 3;
	if (pct(CD1[2], CD1[3]) > l2 && pct(CD1[2], CD1[3]) <= l3)
		w1 = 5;
	if (pct(CD1[2], CD1[3]) > l3 && pct(CD1[2], CD1[3]) <= l4)
		w1 = 7;
	if (pct(CD1[2], CD1[3]) > l4)
		w1 = 9;
	if (pct(CD1[2], CD1[3]) < -l && pct(CD1[2], CD1[3]) >= -l1)
		w1 = 11;
	if (pct(CD1[2], CD1[3]) < -l1 && pct(CD1[2], CD1[3]) >= -l2)
		w1 = 13;
	if (pct(CD1[2], CD1[3]) < -l2 && pct(CD1[2], CD1[3]) >= -l3)
		w1 = 15;
	if (pct(CD1[2], CD1[3]) < -l3 && pct(CD1[2], CD1[3]) >= -l4)
		w1 = 17;
	if (pct(CD1[2], CD1[3]) < -l4)
		w1 = 19;

	//Erase previous H4 result
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) >= l && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) <= l1)
		mixture[w1 - 1][0]--;
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) > l1 && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) <= l2)
		mixture[w1 - 1][1]--;
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) > l2 && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) <= l3)
		mixture[w1 - 1][2]--;
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) > l3 && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) <= l4)
		mixture[w1 - 1][3]--;
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) > l4)
		mixture[w1 - 1][4]--;
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) < l && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) >= -l1)
		mixture[w1][0]--;
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) < -l1 && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) >= -l2)
		mixture[w1][1]--;
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) < -l2 && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) >= -l3)
		mixture[w1][2]--;
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) < -l3 && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) >= -l4)
		mixture[w1][3]--;
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) < -l4)
		mixture[w1][4]--;
	//---

	double belgrosspr = 0;

	for (int x = 4; x >= 0; x--)
	{
		for (int z = w1; z >= w1 - 1; z--)
			belgrosspr += mixture[z][x];
	}

	double pctppr[5];
	double pctnpr[5];
	ArrayInitialize(pctppr, 0);
	ArrayInitialize(pctnpr, 0);

	if (belgrosspr > 0)
	{
		for (int x = 4; x >= 0; x--)
		{
			pctppr[x] = mixture[w1 - 1][x] / belgrosspr * 100;
			pctnpr[x] = mixture[w1][x] / belgrosspr * 100;
		}
	}

	double fpr = 0;
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) >= l && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) <= l1)
		fpr = pctppr[0];
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) > l1 && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) <= l2)
		fpr = pctppr[1];
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) > l2 && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) <= l3)
		fpr = pctppr[2];
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) > l3 && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) <= l4)
		fpr = pctppr[3];
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) > l4)
		fpr = pctppr[4];
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) < l && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) >= -l1)
		fpr = pctnpr[0];
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) < -l1 && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) >= -l2)
		fpr = pctnpr[1];
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) < -l2 && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) >= -l3)
		fpr = pctnpr[2];
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) < -l3 && pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) >= -l4)
		fpr = pctnpr[3];
	if (pct(iClose(_Symbol, PERIOD_H4, 1), iClose(_Symbol, PERIOD_H4, 2)) < -l4)
		fpr = pctnpr[4];

	obname = Name + "4prev";
	LabelMake(obname, 3, 40, 525, DoubleToStr(fpr, 2) + "%", 8, "Arial", clrGreen);

	double SD1[];
	ArrayResize(SD1, ArraySize(CD1) - 3);
	ArrayInitialize(SD1, 0);

	for (int x = ArraySize(SD1) - 1; x >= 1; x--)
	{
		SD1[x] = ((CD1[x] - CD1[x + 1]) / CD1[x + 1]) * 100;
	}

	ArraySort(SD1, 0, 0, MODE_ASCEND);

	double cab = 0;
	int aaa = 0;
	for (int x = ArraySize(SD1) - 1; x >= 1; x--)
	{
		cab += MathAbs(SD1[x]);
		aaa++;
	}

	double endcab = NormalizeDouble(cab / (double)aaa, 4);

	obname = Name + "4adm";
	LabelMake(obname, 3, 80, 400, "A4HM: " + DoubleToStr(endcab * 100, 2), 7, "Arial", clrPurple);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "H: " + DoubleToStr(CD1[1] + CD1[1] * endcab / 100, _Digits) + " L: " + DoubleToStr(CD1[1] - CD1[1] * endcab / 100, _Digits));

	moor4h = endcab;

	double HD1[], LD1[];
	ArrayResize(HD1, 22);
	ArrayResize(LD1, 22);
	ArrayInitialize(HD1, 0);
	ArrayInitialize(LD1, 0);
	CopyHigh(_Symbol, PERIOD_H4, 1, 22, HD1);
	CopyLow(_Symbol, PERIOD_H4, 1, 22, LD1);

	double adr21 = 0;
	int bcount = 0;
	for (int x = 21; x >= 1; x--)
	{
		adr21 += HD1[x] - LD1[x];
		bcount++;
	}
	double bavg = (CD1[1] + endcab / 100 * CD1[1]) - (CD1[1] - endcab / 100 * CD1[1]);
	double amr = ((bavg / PipValues) - (adr21 / (double)bcount) / PipValues);

	fatr = amr;

	obname = Name + "4amr";
	LabelMake(obname, 3, 80, 390, "A-21R: " + DoubleToStr(amr, 2), 7, "Arial", clrGreen);
	obname = Name + "4amt";
	LabelMake(obname, 3, 80, 380, "", 7, "Arial", clrGreen);
}
//+------------------------------------------------------------------+

//+DRAW PERCENTAGES FOR 1H-------------------------------------------+
void drawpct1()
{
	int mixture[20][5];
	ArrayInitialize(mixture, 0);

	int per4 = 1500;
	if (iBars(_Symbol, PERIOD_H1) < per4)
		per4 = iBars(_Symbol, PERIOD_H1) - 4;

	double l = 0.0000, l1 = 0.0625, l2 = 0.1250, l3 = 0.1875, l4 = 0.2500;

	double CD1[];
	ArraySetAsSeries(CD1, true);
	ArrayResize(CD1, per4 + 3);
	CopyClose(_Symbol, PERIOD_H1, 0, per4 + 3, CD1);

	for (int i = per4; i >= 1; i--)
	{
		//POS
		if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[0][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[0][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[0][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[0][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[0][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[1][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[1][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[1][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[1][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) >= l && pct(CD1[i + 1], CD1[i + 2]) <= l1) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[1][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[2][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[2][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[2][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[2][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[2][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[3][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[3][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[3][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[3][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l1 && pct(CD1[i + 1], CD1[i + 2]) <= l2) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[3][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[4][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[4][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[4][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[4][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[4][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[5][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[5][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[5][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[5][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l2 && pct(CD1[i + 1], CD1[i + 2]) <= l3) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[5][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[6][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[6][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[6][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[6][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[6][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[7][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[7][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[7][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[7][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l3 && pct(CD1[i + 1], CD1[i + 2]) <= l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[7][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[8][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[8][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[8][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[8][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[8][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[9][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[9][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[9][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[9][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) > l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[9][4]++;

		//NEG
		if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[10][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[10][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[10][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[10][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[10][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[11][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[11][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[11][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[11][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l && pct(CD1[i + 1], CD1[i + 2]) >= -l1) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[11][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[12][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[12][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[12][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[12][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[12][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[13][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[13][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[13][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[13][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l1 && pct(CD1[i + 1], CD1[i + 2]) >= -l2) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[13][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[14][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[14][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[14][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[14][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[14][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[15][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[15][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[15][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[15][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l2 && pct(CD1[i + 1], CD1[i + 2]) >= -l3) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[15][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[16][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[16][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[16][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[16][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[16][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[17][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[17][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[17][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[17][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l3 && pct(CD1[i + 1], CD1[i + 2]) >= -l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[17][4]++;

		if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) >= l && pct(CD1[i], CD1[i + 1]) <= l1))
			mixture[18][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l1 && pct(CD1[i], CD1[i + 1]) <= l2))
			mixture[18][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l2 && pct(CD1[i], CD1[i + 1]) <= l3))
			mixture[18][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l3 && pct(CD1[i], CD1[i + 1]) <= l4))
			mixture[18][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) > l4))
			mixture[18][4]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < l && pct(CD1[i], CD1[i + 1]) >= -l1))
			mixture[19][0]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l1 && pct(CD1[i], CD1[i + 1]) >= -l2))
			mixture[19][1]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l2 && pct(CD1[i], CD1[i + 1]) >= -l3))
			mixture[19][2]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l3 && pct(CD1[i], CD1[i + 1]) >= -l4))
			mixture[19][3]++;
		else if ((pct(CD1[i + 1], CD1[i + 2]) < -l4) && (pct(CD1[i], CD1[i + 1]) < -l4))
			mixture[19][4]++;
	}

	int y1 = 0;
	if (pct(CD1[1], CD1[2]) >= l && pct(CD1[1], CD1[2]) <= l1)
		y1 = 1;
	if (pct(CD1[1], CD1[2]) > l1 && pct(CD1[1], CD1[2]) <= l2)
		y1 = 3;
	if (pct(CD1[1], CD1[2]) > l2 && pct(CD1[1], CD1[2]) <= l3)
		y1 = 5;
	if (pct(CD1[1], CD1[2]) > l3 && pct(CD1[1], CD1[2]) <= l4)
		y1 = 7;
	if (pct(CD1[1], CD1[2]) > l4)
		y1 = 9;
	if (pct(CD1[1], CD1[2]) < -l && pct(CD1[1], CD1[2]) >= -l1)
		y1 = 11;
	if (pct(CD1[1], CD1[2]) < -l1 && pct(CD1[1], CD1[2]) >= -l2)
		y1 = 13;
	if (pct(CD1[1], CD1[2]) < -l2 && pct(CD1[1], CD1[2]) >= -l3)
		y1 = 15;
	if (pct(CD1[1], CD1[2]) < -l3 && pct(CD1[1], CD1[2]) >= -l4)
		y1 = 17;
	if (pct(CD1[1], CD1[2]) < -l4)
		y1 = 19;

	double belgross = 0;

	for (int x = 4; x >= 0; x--)
	{
		for (int z = y1; z >= y1 - 1; z--)
			belgross += mixture[z][x];
	}

	double pctp[5];
	double pctn[5];
	ArrayInitialize(pctp, 0);
	ArrayInitialize(pctn, 0);

	if (belgross > 0)
	{
		for (int x = 4; x >= 0; x--)
		{
			pctp[x] = mixture[y1 - 1][x] / belgross * 100;
			pctn[x] = mixture[y1][x] / belgross * 100;
		}
	}

	double pctc[5];
	ArrayInitialize(pctc, 0);
	double pctpp = 0;
	double pctnn = 0;

	for (int x = 4; x >= 0; x--)
	{
		pctc[x] = pctp[x] + pctn[x];
		pctpp += pctp[x];
		pctnn += pctn[x];
	}

	string pctps[5] = {"<0.0625", "<0.1250", "<0.1875", "<0.2500", ">0.2500"};
	string pctns[5] = {">-0.0625", ">-0.1250", ">-0.1875", ">-0.2500", "<-0.2500"};

	string obname;
	for (int x = 4; x >= 0; x--)
	{
		obname = Name + "1pdpct";
		LabelMake(obname, 2, 80, 165, "P1H%: " + DoubleToStr(pct(CD1[1], CD1[2]), 2) + "%", 8, "Arial", clrBlue);
		if (pct(CD1[1], CD1[2]) < 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		obname = Name + "1pctp l" + IntegerToString(x);
		LabelMake(obname, 2, 40, 90 + x * 15, pctps[x], 7, "Arial", clrBlue);
		obname = Name + "1pctn l" + IntegerToString(x);
		LabelMake(obname, 2, 40, 75 - x * 15, pctns[x], 7, "Arial", clrRed);
		obname = Name + "1pctp" + IntegerToString(x);
		LabelMake(obname, 2, 80, 90 + x * 15, DoubleToStr(pctp[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " > " + DoubleToStr(0.000625 * x * iClose(_Symbol, PERIOD_H1, 1) + iClose(_Symbol, PERIOD_H1, 1), _Digits) + " - < " + DoubleToStr(0.000625 * (x + 1) * iClose(_Symbol, PERIOD_H1, 1) + iClose(_Symbol, PERIOD_H1, 1), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " > " + DoubleToStr(0.000625 * x * iClose(_Symbol, PERIOD_H1, 1) + iClose(_Symbol, PERIOD_H1, 1), _Digits));
		obname = Name + "1pctn" + IntegerToString(x);
		LabelMake(obname, 2, 80, 75 - x * 15, DoubleToStr(pctn[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " < " + DoubleToStr(-0.000625 * x * iClose(_Symbol, PERIOD_H1, 1) + iClose(_Symbol, PERIOD_H1, 1), _Digits) + " - > " + DoubleToStr(-0.000625 * (x + 1) * iClose(_Symbol, PERIOD_H1, 1) + iClose(_Symbol, PERIOD_H1, 1), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, " < " + DoubleToStr(-0.000625 * x * iClose(_Symbol, PERIOD_H1, 1) + iClose(_Symbol, PERIOD_H1, 1), _Digits));
		obname = Name + "1pctc" + IntegerToString(x);
		LabelMake(obname, 2, 120, 82 + x * 15, DoubleToStr(pctc[x], 2) + "%", 7, "Arial", clrBlack);
		if (x <= 3)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, DoubleToStr(-0.000625 * (x + 1) * iClose(_Symbol, PERIOD_H1, 1) + iClose(_Symbol, PERIOD_H1, 1), _Digits) + " - " + DoubleToStr(0.000625 * (x + 1) * iClose(_Symbol, PERIOD_H1, 1) + iClose(_Symbol, PERIOD_H1, 1), _Digits));
		if (x == 4)
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, DoubleToStr(-0.000625 * x * iClose(_Symbol, PERIOD_H1, 1) + iClose(_Symbol, PERIOD_H1, 1), _Digits) + " - " + DoubleToStr(0.000625 * x * iClose(_Symbol, PERIOD_H1, 1) + iClose(_Symbol, PERIOD_H1, 1), _Digits));
	}

	obname = Name + "1diff";
	LabelMake(obname, 2, 120, 60, "PV: " + DoubleToStr(0.000625 * iClose(_Symbol, PERIOD_H1, 1), _Digits), 7, "Arial", clrBlack);
	obname = Name + "1amtdays";
	LabelMake(obname, 2, 155, 112, "Amt:" + DoubleToStr(belgross, 0), 7, "Arial", clrBlack);
	obname = Name + "1pctpp";
	LabelMake(obname, 2, 155, 142, DoubleToStr(pctpp, 2) + "%", 7, "Arial", clrBlue);
	obname = Name + "1pctnn";
	LabelMake(obname, 2, 155, 127, DoubleToStr(pctnn, 2) + "%", 7, "Arial", clrRed);
	obname = Name + "1npct";
	LabelMake(obname, 2, 120, 50, "1H: " + DoubleToStr(pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) * 100, 2), 7, "Arial", clrBlue);

	//Previous H4 end pct calculation
	int w1 = 0;
	if (pct(CD1[2], CD1[3]) >= l && pct(CD1[2], CD1[3]) <= l1)
		w1 = 1;
	if (pct(CD1[2], CD1[3]) > l1 && pct(CD1[2], CD1[3]) <= l2)
		w1 = 3;
	if (pct(CD1[2], CD1[3]) > l2 && pct(CD1[2], CD1[3]) <= l3)
		w1 = 5;
	if (pct(CD1[2], CD1[3]) > l3 && pct(CD1[2], CD1[3]) <= l4)
		w1 = 7;
	if (pct(CD1[2], CD1[3]) > l4)
		w1 = 9;
	if (pct(CD1[2], CD1[3]) < -l && pct(CD1[2], CD1[3]) >= -l1)
		w1 = 11;
	if (pct(CD1[2], CD1[3]) < -l1 && pct(CD1[2], CD1[3]) >= -l2)
		w1 = 13;
	if (pct(CD1[2], CD1[3]) < -l2 && pct(CD1[2], CD1[3]) >= -l3)
		w1 = 15;
	if (pct(CD1[2], CD1[3]) < -l3 && pct(CD1[2], CD1[3]) >= -l4)
		w1 = 17;
	if (pct(CD1[2], CD1[3]) < -l4)
		w1 = 19;

	//Erase previous H4 result
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) >= l && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) <= l1)
		mixture[w1 - 1][0]--;
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) > l1 && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) <= l2)
		mixture[w1 - 1][1]--;
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) > l2 && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) <= l3)
		mixture[w1 - 1][2]--;
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) > l3 && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) <= l4)
		mixture[w1 - 1][3]--;
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) > l4)
		mixture[w1 - 1][4]--;
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) < l && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) >= -l1)
		mixture[w1][0]--;
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) < -l1 && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) >= -l2)
		mixture[w1][1]--;
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) < -l2 && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) >= -l3)
		mixture[w1][2]--;
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) < -l3 && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) >= -l4)
		mixture[w1][3]--;
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) < -l4)
		mixture[w1][4]--;
	//---

	double belgrosspr = 0;

	for (int x = 4; x >= 0; x--)
	{
		for (int z = w1; z >= w1 - 1; z--)
			belgrosspr += mixture[z][x];
	}

	double pctppr[5];
	double pctnpr[5];
	ArrayInitialize(pctppr, 0);
	ArrayInitialize(pctnpr, 0);

	if (belgrosspr > 0)
	{
		for (int x = 4; x >= 0; x--)
		{
			pctppr[x] = mixture[w1 - 1][x] / belgrosspr * 100;
			pctnpr[x] = mixture[w1][x] / belgrosspr * 100;
		}
	}

	double fpr = 0;
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) >= l && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) <= l1)
		fpr = pctppr[0];
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) > l1 && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) <= l2)
		fpr = pctppr[1];
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) > l2 && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) <= l3)
		fpr = pctppr[2];
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) > l3 && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) <= l4)
		fpr = pctppr[3];
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) > l4)
		fpr = pctppr[4];
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) < l && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) >= -l1)
		fpr = pctnpr[0];
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) < -l1 && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) >= -l2)
		fpr = pctnpr[1];
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) < -l2 && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) >= -l3)
		fpr = pctnpr[2];
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) < -l3 && pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) >= -l4)
		fpr = pctnpr[3];
	if (pct(iClose(_Symbol, PERIOD_H1, 1), iClose(_Symbol, PERIOD_H1, 2)) < -l4)
		fpr = pctnpr[4];

	obname = Name + "1prev";
	LabelMake(obname, 2, 155, 165, DoubleToStr(fpr, 2) + "%", 8, "Arial", clrGreen);

	double SD1[];
	ArrayResize(SD1, ArraySize(CD1) - 3);
	ArrayInitialize(SD1, 0);

	for (int x = ArraySize(SD1) - 1; x >= 1; x--)
	{
		SD1[x] = ((CD1[x] - CD1[x + 1]) / CD1[x + 1]) * 100;
	}

	ArraySort(SD1, 0, 0, MODE_ASCEND);

	double cab = 0;
	int aaa = 0;
	for (int x = ArraySize(SD1) - 1; x >= 1; x--)
	{
		cab += MathAbs(SD1[x]);
		aaa++;
	}

	double endcab = NormalizeDouble(cab / (double)aaa, 4);

	obname = Name + "1adm";
	LabelMake(obname, 2, 120, 40, "A1HM: " + DoubleToStr(endcab * 100, 2), 7, "Arial", clrPurple);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "H: " + DoubleToStr(CD1[1] + CD1[1] * endcab / 100, _Digits) + " L: " + DoubleToStr(CD1[1] - CD1[1] * endcab / 100, _Digits));

	moor1h = endcab;

	double HD1[], LD1[];
	ArrayResize(HD1, 22);
	ArrayResize(LD1, 22);
	ArrayInitialize(HD1, 0);
	ArrayInitialize(LD1, 0);
	CopyHigh(_Symbol, PERIOD_H1, 1, 22, HD1);
	CopyLow(_Symbol, PERIOD_H1, 1, 22, LD1);

	double adr21 = 0;
	int bcount = 0;
	for (int x = 21; x >= 1; x--)
	{
		adr21 += HD1[x] - LD1[x];
		bcount++;
	}
	double bavg = (CD1[1] + endcab / 100 * CD1[1]) - (CD1[1] - endcab / 100 * CD1[1]);
	double amr = ((bavg / PipValues) - (adr21 / (double)bcount) / PipValues);

	oatr = amr;

	obname = Name + "1amr";
	LabelMake(obname, 2, 120, 30, "A-21R: " + DoubleToStr(amr, 2), 7, "Arial", clrGreen);
	obname = Name + "1amt";
	LabelMake(obname, 2, 120, 20, "", 7, "Arial", clrGreen);
}
//+------------------------------------------------------------------+

//+PCT COLORS--------------------------------------------------------+
void colorer()
{
	static int b1 = 0;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= 0 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) <= 0.25)
		b1 = 0;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) > 0.25 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) <= 0.50)
		b1 = 1;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) > 0.50 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) <= 0.75)
		b1 = 2;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) > 0.75 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) <= 1.00)
		b1 = 3;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) > 1.00)
		b1 = 4;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < 0 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= -0.25)
		b1 = 5;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < -0.25 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= -0.50)
		b1 = 6;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < -0.50 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= -0.75)
		b1 = 7;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < -0.75 && pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) >= -1.00)
		b1 = 8;
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < -1.00)
		b1 = 9;

	for (int x = 4; x >= 0; x--)
	{
		if (b1 == x)
			ObjectSetInteger(0, Name + "dpctp" + IntegerToString(x), OBJPROP_COLOR, clrBlue);
		else
			ObjectSetInteger(0, Name + "dpctp" + IntegerToString(x), OBJPROP_COLOR, clrBlack);
		if (b1 == x + 5)
			ObjectSetInteger(0, Name + "dpctn" + IntegerToString(x), OBJPROP_COLOR, clrRed);
		else
			ObjectSetInteger(0, Name + "dpctn" + IntegerToString(x), OBJPROP_COLOR, clrBlack);
		if (b1 == x || b1 == x + 5)
			ObjectSetInteger(0, Name + "dpctc" + IntegerToString(x), OBJPROP_COLOR, clrYellow);
		else
			ObjectSetInteger(0, Name + "dpctc" + IntegerToString(x), OBJPROP_COLOR, clrBlack);
	}

	static int c1 = 0;
	if (pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) >= 0 && pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) <= 1.00)
		c1 = 0;
	if (pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) > 1.00 && pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) <= 2.00)
		c1 = 1;
	if (pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) > 2.00 && pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) <= 3.00)
		c1 = 2;
	if (pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) > 3.00 && pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) <= 4.00)
		c1 = 3;
	if (pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) > 4.00)
		c1 = 4;
	if (pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) < 0 && pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) >= -1.00)
		c1 = 5;
	if (pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) < -1.00 && pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) >= -2.00)
		c1 = 6;
	if (pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) < -2.00 && pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) >= -3.00)
		c1 = 7;
	if (pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) < -3.00 && pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) >= -4.00)
		c1 = 8;
	if (pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) < -4.00)
		c1 = 9;

	for (int x = 4; x >= 0; x--)
	{
		if (c1 == x)
			ObjectSetInteger(0, Name + "wpctp" + IntegerToString(x), OBJPROP_COLOR, clrBlue);
		else
			ObjectSetInteger(0, Name + "wpctp" + IntegerToString(x), OBJPROP_COLOR, clrBlack);
		if (c1 == x + 5)
			ObjectSetInteger(0, Name + "wpctn" + IntegerToString(x), OBJPROP_COLOR, clrRed);
		else
			ObjectSetInteger(0, Name + "wpctn" + IntegerToString(x), OBJPROP_COLOR, clrBlack);
		if (c1 == x || c1 == x + 5)
			ObjectSetInteger(0, Name + "wpctc" + IntegerToString(x), OBJPROP_COLOR, clrYellow);
		else
			ObjectSetInteger(0, Name + "wpctc" + IntegerToString(x), OBJPROP_COLOR, clrBlack);
	}

	static int d1 = 0;
	if (pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) >= 0 && pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) <= 0.125)
		d1 = 0;
	if (pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) > 0.125 && pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) <= 0.250)
		d1 = 1;
	if (pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) > 0.250 && pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) <= 0.375)
		d1 = 2;
	if (pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) > 0.375 && pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) <= 0.500)
		d1 = 3;
	if (pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) > 0.500)
		d1 = 4;
	if (pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) < 0 && pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) >= -0.125)
		d1 = 5;
	if (pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) < -0.125 && pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) >= -0.250)
		d1 = 6;
	if (pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) < -0.250 && pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) >= -0.375)
		d1 = 7;
	if (pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) < -0.375 && pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) >= -0.500)
		d1 = 8;
	if (pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) < -0.500)
		d1 = 9;

	for (int x = 4; x >= 0; x--)
	{
		if (d1 == x)
			ObjectSetInteger(0, Name + "4pctp" + IntegerToString(x), OBJPROP_COLOR, clrBlue);
		else
			ObjectSetInteger(0, Name + "4pctp" + IntegerToString(x), OBJPROP_COLOR, clrBlack);
		if (d1 == x + 5)
			ObjectSetInteger(0, Name + "4pctn" + IntegerToString(x), OBJPROP_COLOR, clrRed);
		else
			ObjectSetInteger(0, Name + "4pctn" + IntegerToString(x), OBJPROP_COLOR, clrBlack);
		if (d1 == x || d1 == x + 5)
			ObjectSetInteger(0, Name + "4pctc" + IntegerToString(x), OBJPROP_COLOR, clrYellow);
		else
			ObjectSetInteger(0, Name + "4pctc" + IntegerToString(x), OBJPROP_COLOR, clrBlack);
	}

	static int e1 = 0;
	if (pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) >= 0 && pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) <= 0.0625)
		e1 = 0;
	if (pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) > 0.0625 && pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) <= 0.1250)
		e1 = 1;
	if (pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) > 0.1250 && pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) <= 0.1875)
		e1 = 2;
	if (pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) > 0.1875 && pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) <= 0.2500)
		e1 = 3;
	if (pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) > 0.2500)
		e1 = 4;
	if (pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) < 0 && pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) >= -0.0625)
		e1 = 5;
	if (pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) < -0.0625 && pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) >= -0.1250)
		e1 = 6;
	if (pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) < -0.1250 && pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) >= -0.1875)
		e1 = 7;
	if (pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) < -0.1875 && pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) >= -0.2500)
		e1 = 8;
	if (pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) < -0.2500)
		e1 = 9;

	for (int x = 4; x >= 0; x--)
	{
		if (e1 == x)
			ObjectSetInteger(0, Name + "1pctp" + IntegerToString(x), OBJPROP_COLOR, clrBlue);
		else
			ObjectSetInteger(0, Name + "1pctp" + IntegerToString(x), OBJPROP_COLOR, clrBlack);
		if (e1 == x + 5)
			ObjectSetInteger(0, Name + "1pctn" + IntegerToString(x), OBJPROP_COLOR, clrRed);
		else
			ObjectSetInteger(0, Name + "1pctn" + IntegerToString(x), OBJPROP_COLOR, clrBlack);
		if (e1 == x || e1 == x + 5)
			ObjectSetInteger(0, Name + "1pctc" + IntegerToString(x), OBJPROP_COLOR, clrYellow);
		else
			ObjectSetInteger(0, Name + "1pctc" + IntegerToString(x), OBJPROP_COLOR, clrBlack);
	}

	ObjectSetString(0, Name + "dnpct", OBJPROP_TEXT, "D: " + DoubleToStr(pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) * 100, 2));
	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) < 0)
		ObjectSetInteger(0, Name + "dnpct", OBJPROP_COLOR, clrRed);
	else
		ObjectSetInteger(0, Name + "dnpct", OBJPROP_COLOR, clrBlue);
	ObjectSetString(0, Name + "wnpct", OBJPROP_TEXT, "W: " + DoubleToStr(pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) * 100, 2));
	if (pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) < 0)
		ObjectSetInteger(0, Name + "wnpct", OBJPROP_COLOR, clrRed);
	else
		ObjectSetInteger(0, Name + "wnpct", OBJPROP_COLOR, clrBlue);
	ObjectSetString(0, Name + "4npct", OBJPROP_TEXT, "4H: " + DoubleToStr(pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) * 100, 2));
	if (pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) < 0)
		ObjectSetInteger(0, Name + "4npct", OBJPROP_COLOR, clrRed);
	else
		ObjectSetInteger(0, Name + "4npct", OBJPROP_COLOR, clrBlue);
	ObjectSetString(0, Name + "1npct", OBJPROP_TEXT, "1H: " + DoubleToStr(pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) * 100, 2));
	if (pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) < 0)
		ObjectSetInteger(0, Name + "1npct", OBJPROP_COLOR, clrRed);
	else
		ObjectSetInteger(0, Name + "1npct", OBJPROP_COLOR, clrBlue);

	ObjectSetString(0, Name + "cadm", OBJPROP_TOOLTIP, "H: " + DoubleToStr(iClose(_Symbol, PERIOD_D1, 0) + iClose(_Symbol, PERIOD_D1, 0) * currentdaynext / 100, _Digits) + " L: " + DoubleToStr(iClose(_Symbol, PERIOD_D1, 0) - iClose(_Symbol, PERIOD_D1, 0) * currentdaynext / 100, _Digits));

	if (pct(iClose(_Symbol, PERIOD_D1, 0), iClose(_Symbol, PERIOD_D1, 1)) * 100 > moorday * 100)
		ObjectSetString(0, Name + "dadm", OBJPROP_FONT, "Arial Black");
	else
		ObjectSetString(0, Name + "dadm", OBJPROP_FONT, "Arial");
	if (pct(iClose(_Symbol, PERIOD_W1, 0), iClose(_Symbol, PERIOD_W1, 1)) * 100 > moorweek * 100)
		ObjectSetString(0, Name + "wadm", OBJPROP_FONT, "Arial Black");
	else
		ObjectSetString(0, Name + "dadm", OBJPROP_FONT, "Arial");
	if (pct(iClose(_Symbol, PERIOD_H4, 0), iClose(_Symbol, PERIOD_H4, 1)) * 100 > moor4h * 100)
		ObjectSetString(0, Name + "4adm", OBJPROP_FONT, "Arial Black");
	else
		ObjectSetString(0, Name + "dadm", OBJPROP_FONT, "Arial");
	if (pct(iClose(_Symbol, PERIOD_H1, 0), iClose(_Symbol, PERIOD_H1, 1)) * 100 > moor1h * 100)
		ObjectSetString(0, Name + "1adm", OBJPROP_FONT, "Arial Black");
	else
		ObjectSetString(0, Name + "dadm", OBJPROP_FONT, "Arial");

	double Ddiff = ((iClose(_Symbol, PERIOD_D1, 1) + moorday / 100 * iClose(_Symbol, PERIOD_D1, 1)) - (iClose(_Symbol, PERIOD_D1, 1) - moorday / 100 * iClose(_Symbol, PERIOD_D1, 1))) / PipValues - (iHigh(_Symbol, PERIOD_D1, 0) - iLow(_Symbol, PERIOD_D1, 0)) / PipValues;
	ObjectSetString(0, Name + "damt", OBJPROP_TEXT, "CD: " + DoubleToStr(Ddiff, 2));
	if (Ddiff < 0 || Ddiff < datr)
		ObjectSetString(0, Name + "damt", OBJPROP_FONT, "Arial Black");
	double Wdiff = ((iClose(_Symbol, PERIOD_W1, 1) + moorweek / 100 * iClose(_Symbol, PERIOD_W1, 1)) - (iClose(_Symbol, PERIOD_W1, 1) - moorweek / 100 * iClose(_Symbol, PERIOD_W1, 1))) / PipValues - (iHigh(_Symbol, PERIOD_W1, 0) - iLow(_Symbol, PERIOD_W1, 0)) / PipValues;
	ObjectSetString(0, Name + "wamt", OBJPROP_TEXT, "CW: " + DoubleToStr(Wdiff, 2));
	if (Wdiff < 0 || Wdiff < watr)
		ObjectSetString(0, Name + "wamt", OBJPROP_FONT, "Arial Black");
	double Fdiff = ((iClose(_Symbol, PERIOD_H4, 1) + moor4h / 100 * iClose(_Symbol, PERIOD_H4, 1)) - (iClose(_Symbol, PERIOD_H4, 1) - moor4h / 100 * iClose(_Symbol, PERIOD_H4, 1))) / PipValues - (iHigh(_Symbol, PERIOD_H4, 0) - iLow(_Symbol, PERIOD_H4, 0)) / PipValues;
	ObjectSetString(0, Name + "4amt", OBJPROP_TEXT, "C4: " + DoubleToStr(Fdiff, 2));
	if (Fdiff < 0 || Fdiff < fatr)
		ObjectSetString(0, Name + "4amt", OBJPROP_FONT, "Arial Black");
	double Odiff = ((iClose(_Symbol, PERIOD_H1, 1) + moor1h / 100 * iClose(_Symbol, PERIOD_H1, 1)) - (iClose(_Symbol, PERIOD_H1, 1) - moor1h / 100 * iClose(_Symbol, PERIOD_H1, 1))) / PipValues - (iHigh(_Symbol, PERIOD_H1, 0) - iLow(_Symbol, PERIOD_H1, 0)) / PipValues;
	ObjectSetString(0, Name + "1amt", OBJPROP_TEXT, "C1: " + DoubleToStr(Odiff, 2));
	if (Odiff < 0 || Odiff < oatr)
		ObjectSetString(0, Name + "1amt", OBJPROP_FONT, "Arial Black");
}
//+------------------------------------------------------------------+

//+ALERTER-----------------------------------------------------------+
void alerter()
{
	if (iHigh(_Symbol, PERIOD_H1, 0) > alerts[0][0] && iLow(_Symbol, PERIOD_H1, 1) < alerts[0][0])
		Alert(_Symbol + " previous H1 bar at DAILY 0.5%");
	if (iHigh(_Symbol, PERIOD_H1, 0) > alerts[2][0] && iLow(_Symbol, PERIOD_H1, 1) < alerts[2][0])
		Alert(_Symbol + " previous H1 bar at DAILY -0.5%");
	if (iHigh(_Symbol, PERIOD_H1, 0) > alerts[1][0] && iLow(_Symbol, PERIOD_H1, 1) < alerts[1][0])
		Alert(_Symbol + " previous H1 bar at DAILY 1.0%");
	if (iHigh(_Symbol, PERIOD_H1, 0) > alerts[3][0] && iLow(_Symbol, PERIOD_H1, 1) < alerts[3][0])
		Alert(_Symbol + " previous H1 bar at DAILY -1.0%");
	if (iHigh(_Symbol, PERIOD_H1, 0) > alerts[0][1] && iLow(_Symbol, PERIOD_H1, 1) < alerts[0][1])
		Alert(_Symbol + " previous H1 bar at Weekly 0.5%");
	if (iHigh(_Symbol, PERIOD_H1, 0) > alerts[2][1] && iLow(_Symbol, PERIOD_H1, 1) < alerts[2][1])
		Alert(_Symbol + " previous H1 bar at Weekly -0.5%");
	if (iHigh(_Symbol, PERIOD_H1, 0) > alerts[1][1] && iLow(_Symbol, PERIOD_H1, 1) < alerts[1][1])
		Alert(_Symbol + " previous H1 bar at Weekly 1.0%");
	if (iHigh(_Symbol, PERIOD_H1, 0) > alerts[3][1] && iLow(_Symbol, PERIOD_H1, 1) < alerts[3][1])
		Alert(_Symbol + " previous H1 bar at Weekly -1.0%");
	//if( iHigh(_Symbol, PERIOD_H1, 0) > alerts[0][2] && iLow(_Symbol, PERIOD_H1, 1) < alerts[0][2]) Alert(_Symbol + " previous H1 bar at Monthly 0.5%");
	//if( iHigh(_Symbol, PERIOD_H1, 0) > alerts[2][2] && iLow(_Symbol, PERIOD_H1, 1) < alerts[2][2]) Alert(_Symbol + " previous H1 bar at Monthly -0.5%");
	//if( iHigh(_Symbol, PERIOD_H1, 0) > alerts[1][2] && iLow(_Symbol, PERIOD_H1, 1) < alerts[1][2]) Alert(_Symbol + " previous H1 bar at Monthly 1.0%");
	//if( iHigh(_Symbol, PERIOD_H1, 0) > alerts[3][2] && iLow(_Symbol, PERIOD_H1, 1) < alerts[3][2]) Alert(_Symbol + " previous H1 bar at Monthly -1.0%");
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const string Font,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetText(name, label, FSize, Font, FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//+------------------------------------------------------------------+

//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, int t1, int t2, int t3, int t4, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t1] + t4);
	ObjectSet(name, OBJPROP_TIME2, Time[t2] + t3);
	ObjectSet(name, OBJPROP_PRICE1, pr1);
	ObjectSet(name, OBJPROP_PRICE2, pr1);
	ObjectSet(name, OBJPROP_STYLE, st);
	ObjectSet(name, OBJPROP_WIDTH, wi);
	ObjectSet(name, OBJPROP_RAY, false);
	ObjectSet(name, OBJPROP_BACK, true);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett); // + " Price: " + DoubleToStr(pr1, _Digits));
}
//+------------------------------------------------------------------+

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const datetime y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, x);
	ObjectSetInteger(0, name, OBJPROP_TIME1, y + (Period() * 60));
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 7);
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) /* + " " + DoubleToStr(x, _Digits)*/);
}
//+------------------------------------------------------------------+

//+PCT CHANGE--------------------------------------------------------+
double pct(double a1, double b1)
{
	double mixt = 0;
	//Print (a1 + " " + b1);
	mixt = ((a1 - b1) / b1) * 100;
	return (mixt);
}
//+------------------------------------------------------------------+