//+------------------------------------------------------------------+
//|                                           Indices Tab.mq4        |
//|                                           Copyright 2019, Sakis  |
//|                                                                  |
//+------------------------------------------------------------------+

// Version History
// ---------------
// v1.0 Made Calcs & board

#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"
#property version "1.0"
#property strict
#property indicator_chart_window
#define Name MQLInfoString(MQL_PROGRAM_NAME) + "U"
#property indicator_plots 0

int p = 1000;
int o = 600;
int FontSize = 8;
string Font = "Calibri";

enum draw {
	nodraw = 0, //False
	draw = 1 // True
};
draw drawgfx = 1; // Show table

double cmres[9]; double cmres1[9]; double cmres2[9]; string cmress[9];

input string cmar0 = "@EP";
input string cmar1 = "@ENQ";
input string cmar2 = "@YM";
input string cmar3 = "@RTY";
input string cmar4 = "@DD";
input string cmar5 = "@MJNK";
input string cmar6 = "@HSI";
input string cmar7 = "@CLE";
input string cmar8 = "@EU6";


//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{

	if (GlobalVariableCheck("PCd" + _Symbol) == false) GlobalVariableSet("PCd" + _Symbol, 1);
	drawgfx = (draw)GlobalVariableGet("PCd" + _Symbol);
	if (GlobalVariableCheck("PCp" + _Symbol) == false) GlobalVariableSet("PCp" + _Symbol, p); else p = (int)GlobalVariableGet("PCp" + _Symbol);
	if (GlobalVariableCheck("PCo" + _Symbol) == false) GlobalVariableSet("PCo" + _Symbol, o); else o = (int)GlobalVariableGet("PCo" + _Symbol);
	
	cmress[0] =cmar0; cmress[1] = cmar1; cmress[2] = cmar2; cmress[3] = cmar3; cmress[4] = cmar4; cmress[5] = cmar5; cmress[6] = cmar6; cmress[7] = cmar7; cmress[8] = cmar8;
	
	counts();

	if (drawgfx) { reclabloc(); }

	//---
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || MQLInfoInteger(MQL_TESTER))
		if (!MQLInfoInteger(MQL_TESTER))
		{
			DeleteObjects();
		}
	Comment("");
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	//---
	datetime expiry = D'2019.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("Indices Tab expired on " + TimeToString(expiry, TIME_DATE) + ", contact sakisf for an update/new version.");
		YesStop = true;
	}
	
	counts();
	if (drawgfx) { buildtab(); }
   /*
	if (YesStop != true) {
		bool new_gm_check = false;
		static datetime start_gm_time = 0;
		if (start_gm_time < iTime(NULL, PERIOD_M1, 0))
		{
			new_gm_check = true;
			start_gm_time = iTime(NULL, PERIOD_M1, 0);
		}
		if (new_gm_check)
		{
			counts();
			if (drawgfx) { buildtab(); }
			new_gm_check = false;
		}
	}
	*/
	//--- return value of prev_calculated for next call
	return(rates_total);
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	for (int i = ObjectsTotal(0, -1) - 1; i >= 0; i--)
	{
		string ObName = ObjectName(0, i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(0, ObName);
		}
	}
}
//+------------------------------------------------------------------+

//+ChartEvent function-----------------------------------------------+
void OnChartEvent(const int id,
	const long &lparam,
	const double &dparam,
	const string &sparam)
{
	//---
	{//Debug
		if (id == CHARTEVENT_KEYDOWN) {
			if (lparam == StringGetCharacter("H", 0)) { GlobalVariableSet("PCd" + _Symbol, 1); drawgfx = (draw)GlobalVariableGet("PCd" + _Symbol); OnInit(); reclabloc(); buildtab(); counts(); }
			if (lparam == StringGetCharacter("J", 0)) { GlobalVariableSet("PCd" + _Symbol, 0); drawgfx = (draw)GlobalVariableGet("PCd" + _Symbol); OnInit(); ObjectsDeleteAll(0, Name); }
		}
	}
	{//Move rectangle
		if (id == CHARTEVENT_OBJECT_DRAG)
		{
			if (sparam == (Name + " MovRec"))
			{
				GlobalVariableSet("PCp" + _Symbol, (int)ObjectGetInteger(0, Name + " MovRec", OBJPROP_XDISTANCE));
				GlobalVariableSet("PCo" + _Symbol, (int)ObjectGetInteger(0, Name + " MovRec", OBJPROP_YDISTANCE));
				GlobalVariablesFlush();
				reclabloc(); counts(); if (drawgfx) { buildtab(); }
			}
		}
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
void reclabloc() {
	p = (int)GlobalVariableGet("PCp" + _Symbol); o = (int)GlobalVariableGet("PCo" + _Symbol);
	string obname;
	obname = Name + " LabRec";
	RecMake(obname, p - 5, o - 5, 330, 145, clrWhite, clrBlack);

	obname = Name + " MovRec";
	RecMake(obname, p - 4, o - 4, 0, 0, clrWhite, clrBlack);
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
	ObjectSetInteger(0, obname, OBJPROP_SELECTABLE, true);
	ObjectSetInteger(0, obname, OBJPROP_SELECTED, true);
}
//+------------------------------------------------------------------+

//+COUNT FUNCTION----------------------------------------------------+
void counts() {
	ArrayInitialize(cmres, 0);
	
	for (int i = 0; i <= 8; i++) {
		cmres[i] = ((SymbolInfoDouble(cmress[i], SYMBOL_LAST) - iOpen(cmress[i], PERIOD_D1, 0)) / iOpen(cmress[i], PERIOD_D1, 1)) * 100;
		cmres1[i] = ((SymbolInfoDouble(cmress[i], SYMBOL_LAST) - iOpen(cmress[i], PERIOD_W1, 0)) / iOpen(cmress[i], PERIOD_W1, 1)) * 100;
		cmres2[i] = ((SymbolInfoDouble(cmress[i], SYMBOL_LAST) - iOpen(cmress[i], PERIOD_MN1, 0)) / iOpen(cmress[i], PERIOD_MN1, 1)) * 100;
	}
}
//+------------------------------------------------------------------+

//+BUILD VISUAL NOTICE-----------------------------------------------+
void buildtab() {
	for (int i = 0; i <= 8; i++)
	{
		string obname = Name + "res" + IntegerToString(i);
		LabelMake(obname, 0, p + 5, o + 15 * i, cmress[i], FontSize, clrBlack);
		if (cmres[i] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (cmres[i] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen); else if (cmres[i] <= -0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	}

	for (int i = 0; i <= 8; i++)
	{
		string obname = Name + "tot" + IntegerToString(i);
		LabelMake(obname, 0, p + 50, o + 15 * i, "D: " + DoubleToString(cmres[i], 2) + "% (" + DoubleToString(iClose(cmress[i], PERIOD_D1, 0) - iOpen(cmress[i], PERIOD_D1, 0), 1) + ")", FontSize, clrBlack);
		if (cmres[i] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (cmres[i] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen); else if (cmres[i] <= -0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	}
	
	for (int i = 0; i <= 8; i++)
	{
		string obname = Name + "dif" + IntegerToString(i);
		LabelMake(obname, 8, p + 140, o + 15 * i, "W: " + DoubleToString(cmres1[i], 2) + "%" + " (" + DoubleToString(iClose(cmress[i], PERIOD_W1, 0) - iOpen(cmress[i], PERIOD_W1, 0), 1) + ")", FontSize, clrBlack);
		if (cmres1[i] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (cmres1[i] >= 1.0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen); else if (cmres1[i] <= -1.0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	}
	
	for (int i = 0; i <= 8; i++)
	{
		string obname = Name + "difmn" + IntegerToString(i);
		LabelMake(obname, 8, p + 230, o + 15 * i, "MN: " + DoubleToString(cmres2[i], 2) + "%" + " (" + DoubleToString(iClose(cmress[i], PERIOD_MN1, 0) - iOpen(cmress[i], PERIOD_MN1, 0), 1) + ")", FontSize, clrBlack);
		if (cmres2[i] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (cmres2[i] >= 1.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen); else if (cmres2[i] <= -1.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed"; break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed"; break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed"; break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed"; break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled"; break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart"; break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart"; break;
	default:text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
	const int corner,
	const int x,
	const int y,
	const string label,
	const int FSize,
	const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	//ObjectSetText(name, label, FSize, Font, FCol);
	ObjectSetString(0, name, OBJPROP_FONT, Font);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetString(0, name, OBJPROP_TEXT, label);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const int x, const int y, const int xs, const int ys, const color FCol, const color BCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_BGCOLOR, FCol);
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetInteger(0, name, OBJPROP_XSIZE, xs);
	ObjectSetInteger(0, name, OBJPROP_YSIZE, ys);
	ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, Name);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+