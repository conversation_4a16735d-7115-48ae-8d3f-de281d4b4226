//+------------------------------------------------------------------+
//|                                                        troll.mq5 |
//|                                  Copyright 2023, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_chart_window
string Name=MQLInfoString(MQL_PROGRAM_NAME);
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &Trime[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---

		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, PERIOD_H1, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, PERIOD_H1, 0);
		}
		if (new_1m_check)
		{
         troll();
         new_1m_check = false;
      }
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+

void troll()
{

string obname;

   //72-Day check of avg moves to close
   double CD1[];
   ArraySetAsSeries(CD1, true);
   ArrayResize(CD1, 120);
   CopyClose(_Symbol, PERIOD_D1, 0, 119, CD1);
   
   //General
   double variancea[];
   ArrayResize(variancea, 81);
   ArrayInitialize(variancea, 0);
   double variance0[];
   ArrayResize(variance0, 81);
   ArrayInitialize(variance0, 0);
   double variance1[];
   ArrayResize(variance1, 81);
   ArrayInitialize(variance1, 0);
   double variance2[];
   ArrayResize(variance2, 81);
   ArrayInitialize(variance2, 0);
   double variance3[];
   ArrayResize(variance3, 81);
   ArrayInitialize(variance3, 0);
   double variance4[];
   ArrayResize(variance4, 81);
   ArrayInitialize(variance4, 0);
   double variance5[];
   ArrayResize(variance5, 81);
   ArrayInitialize(variance5, 0);
   double variance6[];
   ArrayResize(variance6, 81);
   ArrayInitialize(variance6, 0);
   double variance7[];
   ArrayResize(variance7, 81);
   ArrayInitialize(variance7, 0);
   double variance8[];
   ArrayResize(variance8, 81);
   ArrayInitialize(variance8, 0);
   double variance9[];
   ArrayResize(variance9, 81);
   ArrayInitialize(variance9, 0);
   double variance10[];
   ArrayResize(variance10, 81);
   ArrayInitialize(variance10, 0);
   double variance11[];
   ArrayResize(variance11, 81);
   ArrayInitialize(variance11, 0);
   double variance12[];
   ArrayResize(variance12, 81);
   ArrayInitialize(variance12, 0);
   double variance13[];
   ArrayResize(variance13, 81);
   ArrayInitialize(variance13, 0);
   double variance14[];
   ArrayResize(variance14, 81);
   ArrayInitialize(variance14, 0);
   double variance15[];
   ArrayResize(variance15, 81);
   ArrayInitialize(variance15, 0);
   double variance16[];
   ArrayResize(variance16, 81);
   ArrayInitialize(variance16, 0);
   double variance17[];
   ArrayResize(variance17, 81);
   ArrayInitialize(variance17, 0);
   double variance18[];
   ArrayResize(variance18, 81);
   ArrayInitialize(variance18, 0);
   double variance19[];
   ArrayResize(variance19, 81);
   ArrayInitialize(variance19, 0);
   double variance20[];
   ArrayResize(variance20, 81);
   ArrayInitialize(variance20, 0);
   double variance21[];
   ArrayResize(variance21, 81);
   ArrayInitialize(variance21, 0);
   double variance22[];
   ArrayResize(variance22, 81);
   ArrayInitialize(variance22, 0);
   double variance23[];
   ArrayResize(variance23, 81);
   ArrayInitialize(variance23, 0);
   double variance24[];
   ArrayResize(variance24, 81);
   ArrayInitialize(variance24, 0);
   double variance25[];
   ArrayResize(variance25, 81);
   ArrayInitialize(variance25, 0);
   double variance26[];
   ArrayResize(variance26, 81);
   ArrayInitialize(variance26, 0);
   double variance27[];
   ArrayResize(variance27, 81);
   ArrayInitialize(variance27, 0);
   double variance28[];
   ArrayResize(variance28, 81);
   ArrayInitialize(variance28, 0);
   double variance29[];
   ArrayResize(variance29, 81);
   ArrayInitialize(variance29, 0);
   double variance30[];
   ArrayResize(variance30, 81);
   ArrayInitialize(variance30, 0);
   double variance31[];
   ArrayResize(variance31, 81);
   ArrayInitialize(variance31, 0);
   
   //sd
   double sda = 0, sd0 = 0;
   double sd[];
   ArrayResize(sd, 32);
   ArrayInitialize(sd, 0);
   
   int totalobs = 0;
   for (int s = 72; s >= 1; s--)
   {
      totalobs += s;
   }
   
   int r = 72;
   //////////
   
   //variance
   for (int s = 72; s >= 1; s--)
   {
      variancea[s] = MathAbs((CD1[r - s] - CD1[r - s + 1]) / CD1[r - s + 1]); //(72 - 72) = 0 - (72 - 72 + 1) = 1  /// @1 71 - 72 ///  = D1 Next
      variance0[s] = MathAbs((CD1[r - s + 1] - CD1[r - s + 2]) / CD1[r - s + 2]); //(72 - 72 + 1) = 1 - (72 - 72 + 2) = 2  ///  @1 72 - 73 /// = D1 - Current
      variance1[s] = MathAbs((CD1[r - s + 2] - CD1[r - s + 3]) / CD1[r - s + 3]); //(72 - 72 + 2) = 2 - (72 - 72 + 3) = 3  ///  @1 73 - 74 /// = D1 - 1
      variance2[s] = MathAbs((CD1[r - s + 3] - CD1[r - s + 4]) / CD1[r - s + 4]); //(72 - 72 + 3) = 3 - (72 - 72 + 4) = 4  ///  @1 74 - 75 /// = D1 - 2
      variance3[s] = MathAbs((CD1[r - s + 4] - CD1[r - s + 5]) / CD1[r - s + 5]); //(72 - 72 + 4) = 4 - (72 - 72 + 5) = 5  ///  @1 75 - 76 /// = D1 - 3
      variance4[s] = MathAbs((CD1[r - s + 5] - CD1[r - s + 6]) / CD1[r - s + 6]); //(72 - 72 + 5) = 5 - (72 - 72 + 6) = 6  ///  @1 76 - 77 /// = D1 - 4
      variance5[s] = MathAbs((CD1[r - s + 6] - CD1[r - s + 7]) / CD1[r - s + 7]); //(72 - 72 + 6) = 6 - (72 - 72 + 7) = 7  ///  @1 77 - 78 /// = D1 - 5
      variance6[s] = MathAbs((CD1[r - s + 7] - CD1[r - s + 8]) / CD1[r - s + 8]); //(72 - 72 + 7) = 7 - (72 - 72 + 8) = 8  ///  @1 78 - 79 /// = D1 - 6
      variance7[s] = MathAbs((CD1[r - s + 8] - CD1[r - s + 9]) / CD1[r - s + 9]); //(72 - 72 + 8) = 8 - (72 - 72 + 9) = 9  ///  @1 79 - 80 /// = D1 - 7
      variance8[s] = MathAbs((CD1[r - s + 9] - CD1[r - s + 10]) / CD1[r - s + 10]); //(72 - 72 + 9) = 9 - (72 - 72 + 10) = 10  ///  @1 80 - 81 /// = D1 - 8
      variance9[s] = MathAbs((CD1[r - s + 10] - CD1[r - s + 11]) / CD1[r - s + 11]); //(72 - 72 + 10) = 10 - (72 - 72 + 11) = 11  ///  @1 81 - 82 /// = D1 - 9
      variance10[s] = MathAbs((CD1[r - s + 11] - CD1[r - s + 12]) / CD1[r - s + 12]); //(72 - 72 + 11) = 11 - (72 - 72 + 12) = 12  ///  @1 82 - 83 /// = D1 - 10
      variance11[s] = MathAbs((CD1[r - s + 12] - CD1[r - s + 13]) / CD1[r - s + 13]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance12[s] = MathAbs((CD1[r - s + 13] - CD1[r - s + 14]) / CD1[r - s + 14]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance13[s] = MathAbs((CD1[r - s + 14] - CD1[r - s + 15]) / CD1[r - s + 15]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance14[s] = MathAbs((CD1[r - s + 15] - CD1[r - s + 16]) / CD1[r - s + 16]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance15[s] = MathAbs((CD1[r - s + 16] - CD1[r - s + 17]) / CD1[r - s + 17]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance16[s] = MathAbs((CD1[r - s + 17] - CD1[r - s + 18]) / CD1[r - s + 18]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance17[s] = MathAbs((CD1[r - s + 18] - CD1[r - s + 19]) / CD1[r - s + 19]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance18[s] = MathAbs((CD1[r - s + 19] - CD1[r - s + 20]) / CD1[r - s + 20]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance19[s] = MathAbs((CD1[r - s + 20] - CD1[r - s + 21]) / CD1[r - s + 21]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance20[s] = MathAbs((CD1[r - s + 21] - CD1[r - s + 22]) / CD1[r - s + 22]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance21[s] = MathAbs((CD1[r - s + 22] - CD1[r - s + 23]) / CD1[r - s + 23]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11      
      variance22[s] = MathAbs((CD1[r - s + 23] - CD1[r - s + 24]) / CD1[r - s + 24]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance23[s] = MathAbs((CD1[r - s + 24] - CD1[r - s + 25]) / CD1[r - s + 25]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance24[s] = MathAbs((CD1[r - s + 25] - CD1[r - s + 26]) / CD1[r - s + 26]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance25[s] = MathAbs((CD1[r - s + 26] - CD1[r - s + 27]) / CD1[r - s + 27]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance26[s] = MathAbs((CD1[r - s + 27] - CD1[r - s + 28]) / CD1[r - s + 28]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance27[s] = MathAbs((CD1[r - s + 28] - CD1[r - s + 29]) / CD1[r - s + 29]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance28[s] = MathAbs((CD1[r - s + 29] - CD1[r - s + 30]) / CD1[r - s + 30]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance29[s] = MathAbs((CD1[r - s + 30] - CD1[r - s + 31]) / CD1[r - s + 31]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance30[s] = MathAbs((CD1[r - s + 31] - CD1[r - s + 32]) / CD1[r - s + 32]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
      variance31[s] = MathAbs((CD1[r - s + 32] - CD1[r - s + 33]) / CD1[r - s + 33]); //(72 - 72 + 12) = 12 - (72 - 72 + 13) = 13  ///  @1 83 - 84 /// = D1 - 11
   }
   
   for (int s = 72; s >= 1; s--)
   {
         sda += (variancea[s] * s / totalobs * 100) / 72;
         sd0 += (variance0[s] * s / totalobs * 100) / 72;
         sd[1] += (variance1[s] * s / totalobs * 100) / 72;
         sd[2] += (variance2[s] * s / totalobs * 100) / 72;
         sd[3] += (variance3[s] * s / totalobs * 100) / 72;
         sd[4] += (variance4[s] * s / totalobs * 100) / 72;
         sd[5] += (variance5[s] * s / totalobs * 100) / 72;
         sd[6] += (variance6[s] * s / totalobs * 100) / 72;
         sd[7] += (variance7[s] * s / totalobs * 100) / 72;
         sd[8] += (variance8[s] * s / totalobs * 100) / 72;
         sd[9] += (variance9[s] * s / totalobs * 100) / 72;
         sd[10] += (variance10[s] * s / totalobs * 100) / 72;
         sd[11] += (variance11[s] * s / totalobs * 100) / 72;
         sd[12] += (variance12[s] * s / totalobs * 100) / 72;
         sd[13] += (variance13[s] * s / totalobs * 100) / 72;
         sd[14] += (variance14[s] * s / totalobs * 100) / 72;
         sd[15] += (variance15[s] * s / totalobs * 100) / 72;
         sd[16] += (variance16[s] * s / totalobs * 100) / 72;
         sd[17] += (variance17[s] * s / totalobs * 100) / 72;
         sd[18] += (variance18[s] * s / totalobs * 100) / 72;
         sd[19] += (variance19[s] * s / totalobs * 100) / 72;
         sd[20] += (variance20[s] * s / totalobs * 100) / 72;
         sd[21] += (variance21[s] * s / totalobs * 100) / 72;
         sd[22] += (variance22[s] * s / totalobs * 100) / 72;
         sd[23] += (variance23[s] * s / totalobs * 100) / 72;
         sd[24] += (variance24[s] * s / totalobs * 100) / 72;
         sd[25] += (variance25[s] * s / totalobs * 100) / 72;
         sd[26] += (variance26[s] * s / totalobs * 100) / 72;
         sd[27] += (variance27[s] * s / totalobs * 100) / 72;
         sd[28] += (variance28[s] * s / totalobs * 100) / 72;
         sd[29] += (variance29[s] * s / totalobs * 100) / 72;
         sd[30] += (variance30[s] * s / totalobs * 100) / 72;
         sd[31] += (variance31[s] * s / totalobs * 100) / 72;
   }
   
   for (int m = 30; m >= 1; m--)
   {
      //D1 - m
      {
      obname = Name + "upperExtoo" + IntegerToString(m); RecMake(obname, CD1[m + 1] + sd[m] * CD1[m + 1], CD1[m + 1] + 0.5 * sd[m] * CD1[m + 1], 0, 0, 0, C'18,60,6', "(0.5-1) sd[" + IntegerToString(m) + "] = " + DoubleToString(sd[m] * 100, 4));
         ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, m));
         ObjectSetInteger(0, obname, OBJPROP_TIME, 1, iTime(_Symbol, PERIOD_D1, m - 1));
         ObjectSetInteger(0, obname, OBJPROP_FILL, true);
      }
      obname = Name + "lowerExtoo" + IntegerToString(m); RecMake(obname, CD1[m + 1] - sd[m] * CD1[m + 1], CD1[m + 1] - 0.5 * sd[m] * CD1[m + 1], 0, 0, 0, C'18,60,6', "(0.5-1) sd[" + IntegerToString(m) + "] = " + DoubleToString(sd[m] * 100, 4));
         ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, m));
         ObjectSetInteger(0, obname, OBJPROP_TIME, 1, iTime(_Symbol, PERIOD_D1, m - 1));
         ObjectSetInteger(0, obname, OBJPROP_FILL, true);
      obname = Name + "upperExto1o" + IntegerToString(m); RecMake(obname, CD1[m + 1] + 1.5 * sd[m] * CD1[m + 1], CD1[m + 1] + sd[m] * CD1[m + 1], 0, 0, 0, C'58,19,7', "(1-1.5) sd[" + IntegerToString(m) + "] = " + DoubleToString(sd[m] * 100, 4));
         ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, m));
         ObjectSetInteger(0, obname, OBJPROP_TIME, 1, iTime(_Symbol, PERIOD_D1, m - 1));
      obname = Name + "lowerExto1o" + IntegerToString(m); RecMake(obname, CD1[m + 1] - 1.5 * sd[m] * CD1[m + 1], CD1[m + 1] - sd[m] * CD1[m + 1], 0, 0, 0, C'58,19,7', "(1-1.5) sd[" + IntegerToString(m) + "] = " + DoubleToString(sd[m] * 100, 4));
         ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, m));
         ObjectSetInteger(0, obname, OBJPROP_TIME, 1, iTime(_Symbol, PERIOD_D1, m - 1));
      obname = Name + "upperExto2o" + IntegerToString(m); RecMake(obname, CD1[m + 1] + 2 * sd[m] * CD1[m + 1], CD1[m + 1] + 1.5 * sd[m] * CD1[m + 1], 0, 0, 0, C'52,63,0', "(1.5-2) sd[" + IntegerToString(m) + "] = " + DoubleToString(sd[m] * 100, 4));
         ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, m));
         ObjectSetInteger(0, obname, OBJPROP_TIME, 1, iTime(_Symbol, PERIOD_D1, m - 1));
      obname = Name + "lowerExto2o" + IntegerToString(m); RecMake(obname, CD1[m + 1] - 2 * sd[m] * CD1[m + 1], CD1[m + 1] - 1.5 * sd[m] * CD1[m + 1], 0, 0, 0, C'52,63,0', "(1.5-2) sd[" + IntegerToString(m) + "] = " + DoubleToString(sd[m] * 100, 4));
         ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, m));
         ObjectSetInteger(0, obname, OBJPROP_TIME, 1, iTime(_Symbol, PERIOD_D1, m - 1));
      obname = Name + "upperExto3o" + IntegerToString(m); RecMake(obname, CD1[m + 1] + 2 * sd[m] * CD1[m + 1], CD1[m + 1] + 3 * sd[m] * CD1[m + 1], 0, 0, 0, C'60,55,40', "(2-3) sd[" + IntegerToString(m) + "] = " + DoubleToString(sd[m] * 100, 4));
         ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, m));
         ObjectSetInteger(0, obname, OBJPROP_TIME, 1, iTime(_Symbol, PERIOD_D1, m - 1));
      obname = Name + "lowerExto3o" + IntegerToString(m); RecMake(obname, CD1[m + 1] - 2 * sd[m] * CD1[m + 1], CD1[m + 1] - 3 * sd[m] * CD1[m + 1], 0, 0, 0, C'60,55,40', "(2-3) sd[" + IntegerToString(m) + "] = " + DoubleToString(sd[m] * 100, 4));
         ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, m));
         ObjectSetInteger(0, obname, OBJPROP_TIME, 1, iTime(_Symbol, PERIOD_D1, m - 1));
      obname = Name + "lowerExto3o" + IntegerToString(m) + "l"; objtrend2(obname, CD1[m + 1], CD1[m + 1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, m), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, m - 1), false), 0, 2, 0, clrWhite, "sd[" + IntegerToString(m) + "] = " + DoubleToString(sd[m] * 100, 4));
      obname = Name + "lowerExto3o" + IntegerToString(m) + "lu"; objtrend2(obname, CD1[m + 1] + 0.1 * sd[m] * CD1[m + 1], CD1[m + 1] + 0.1 * sd[m] * CD1[m + 1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, m), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, m - 1), false), 0, 2, 0, clrGray, "sd[" + IntegerToString(m) + "] = " + DoubleToString(sd[m] * 100, 4));
      obname = Name + "lowerExto3o" + IntegerToString(m) + "ld"; objtrend2(obname, CD1[m + 1] - 0.1 * sd[m] * CD1[m + 1], CD1[m + 1] - 0.1 * sd[m] * CD1[m + 1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, m), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, m - 1), false), 0, 2, 0, clrGray, "sd[" + IntegerToString(m) + "] = " + DoubleToString(sd[m] * 100, 4));
      obname = Name + "lowerEXsign" + IntegerToString(m); Texter(obname, CD1[m + 1] - 1.5 * sd[m] * CD1[m + 1], iTime(_Symbol, PERIOD_D1, m), "0", clrBlack);
         if (sd[m] > sd[m + 1]) ObjectSetString(0, obname, OBJPROP_TEXT, ">");
         if (sd[m] < sd[m + 1]) ObjectSetString(0, obname, OBJPROP_TEXT, "<");
   }
   
	datetime Trime[];
	ArrayResize(Trime, iBars(_Symbol, PERIOD_CURRENT));
	CopyTime(_Symbol, PERIOD_CURRENT, 0, iBars(_Symbol, PERIOD_CURRENT), Trime);
	ArraySetAsSeries(Trime, true);
	
   //D1 - 0
   obname = Name + "upperExt"; RecMake(obname, CD1[1] + sd0 * CD1[1], CD1[1] + 0.5 * sd0 * CD1[1], 0, 0, 0, clrGreen, "(0.5-1) sd0 = " + DoubleToString(sd0 * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, 0));
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] - 10800);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 60200);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 84900);
   obname = Name + "lowerExt"; RecMake(obname, CD1[1] - sd0 * CD1[1], CD1[1] - 0.5 * sd0 * CD1[1], 0, 0, 0, clrGreen, "(0.5-1) sd0 = " + DoubleToString(sd0 * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, 0));
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] - 10800);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 60200);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 84900);
   obname = Name + "upperExt1"; RecMake(obname, CD1[1] + 1.5 * sd0 * CD1[1], CD1[1] + sd0 * CD1[1], 0, 0, 0, C'58,19,7', "(1-1.5) sd0 = " + DoubleToString(sd0 * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, 0));
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] - 10800);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 60200);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 84900);
   obname = Name + "lowerExt1"; RecMake(obname, CD1[1] - 1.5 * sd0 * CD1[1], CD1[1] - sd0 * CD1[1], 0, 0, 0, C'58,19,7', "(1-1.5) sd0 = " + DoubleToString(sd0 * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, 0));
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] - 10800);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 60200);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 84900);
   obname = Name + "upperExt2"; RecMake(obname, CD1[1] + 2 * sd0 * CD1[1], CD1[1] + 1.5 * sd0 * CD1[1], 0, 0, 0, C'52,63,0', "(1.5-2) sd0 = " + DoubleToString(sd0 * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, 0));
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] - 10800);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 60200);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 84900);
   obname = Name + "lowerExt2"; RecMake(obname, CD1[1] - 2 * sd0 * CD1[1], CD1[1] - 1.5 * sd0 * CD1[1], 0, 0, 0, C'52,63,0', "(1.5-2) sd0 = " + DoubleToString(sd0 * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, 0));
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] - 10800);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 60200);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 84900);
   obname = Name + "upperExt3"; RecMake(obname, CD1[1] + 2 * sd0 * CD1[1], CD1[1] + 3 * sd0 * CD1[1], 0, 0, 0, C'60,55,40', "(2-3) sd0 = " + DoubleToString(sd0 * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, 0));
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] - 10800);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 60200);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 84900);
   obname = Name + "lowerExt3"; RecMake(obname, CD1[1] - 2 * sd0 * CD1[1], CD1[1] - 3 * sd0 * CD1[1], 0, 0, 0, C'60,55,40', "(2-3) sd0 = " + DoubleToString(sd0 * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, 0));
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] - 10800);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 60200);
      //ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 84900);
   obname = Name + "lowerExt3l"; objtrend2(obname, CD1[1], CD1[1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 2, 0, clrWhite, "sd0 = " + DoubleToString(sd0 * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] - 10800);
   obname = Name + "lowerExt3lu"; objtrend2(obname, CD1[1] + 0.1 * sd0 * CD1[1], CD1[1] + 0.1 * sd0 * CD1[1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 2, 0, clrGray, "sd0 = " + DoubleToString(sd0 * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] - 10800);
   obname = Name + "lowerExt3ld"; objtrend2(obname, CD1[1] - 0.1 * sd0 * CD1[1], CD1[1] - 0.1 * sd0 * CD1[1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 2, 0, clrGray, "sd0 = " + DoubleToString(sd0 * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] - 10800);
   obname = Name + "lowerEXsign"; Texter(obname, CD1[1] - 1.5 * sd0 * CD1[1], 0, "0", clrBlack);
      ObjectSetInteger(0, obname, OBJPROP_TIME, iTime(_Symbol, PERIOD_D1, 0));
      //ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 60200);
      if (sd0 > sd[1]) ObjectSetString(0, obname, OBJPROP_TEXT, ">");
      if (sd0 < sd[1]) ObjectSetString(0, obname, OBJPROP_TEXT, "<");
      
   //D1 - advance
   obname = Name + "upperExtn"; RecMake(obname, CD1[0] + sda * CD1[0], CD1[0] + 0.5 * sda * CD1[0], 0, 0, 0, C'18,60,6', "(0.5-1) sda = " + DoubleToString(sda * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 87660);
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 95700);
   obname = Name + "lowerExtn"; RecMake(obname, CD1[0] - sda * CD1[0], CD1[0] - 0.5 * sda * CD1[0], 0, 0, 0, C'18,60,6', "(0.5-1) sda = " + DoubleToString(sda * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 87660);
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 95700);
   obname = Name + "upperExtn1"; RecMake(obname, CD1[0] + 1.5 * sda * CD1[0], CD1[0] + sda * CD1[0], 0, 0, 0, C'58,19,7', "(1-1.5) sda = " + DoubleToString(sda * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 87660);
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 95700);
   obname = Name + "lowerExtn1"; RecMake(obname, CD1[0] - 1.5 * sda * CD1[0], CD1[0] - sda * CD1[0], 0, 0, 0, C'58,19,7', "(1-1.5) sda = " + DoubleToString(sda * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 87660);
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 95700);
   obname = Name + "upperExtn2"; RecMake(obname, CD1[0] + 2 * sda * CD1[0], CD1[0] + 1.5 * sda * CD1[0], 0, 0, 0, C'52,63,0', "(1.5-2) sda = " + DoubleToString(sda * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 87660);
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 95700);
   obname = Name + "lowerExtn2"; RecMake(obname, CD1[0] - 2 * sda * CD1[0], CD1[0] - 1.5 * sda * CD1[0], 0, 0, 0, C'52,63,0', "(1.5-2) sda = " + DoubleToString(sda * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 87660);
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 95700);
   obname = Name + "upperExtn3"; RecMake(obname, CD1[0] + 2 * sda * CD1[0], CD1[0] + 3 * sda * CD1[0], 0, 0, 0, C'60,55,40', "(2-3) sda = " + DoubleToString(sda * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 87660);
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 95700);
   obname = Name + "lowerExtn3"; RecMake(obname, CD1[0] - 2 * sda * CD1[0], CD1[0] - 3 * sda * CD1[0], 0, 0, 0, C'60,55,40', "(2-3) sda = " + DoubleToString(sda * 100, 4));
      ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 87660);
      ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Trime[0] + 95700);
   obname = Name + "lowerEXsigna"; Texter(obname, CD1[0] - 1.5 * sda * CD1[0], 0, "0", clrBlack);
      ObjectSetInteger(0, obname, OBJPROP_TIME, Trime[0] + 87660);
      if (sda > sd0) ObjectSetString(0, obname, OBJPROP_TEXT, ">");
      if (sda < sd0) ObjectSetString(0, obname, OBJPROP_TEXT, "<");
   }

//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name,
               double pr1,
               double pr2,
               int t1,
               int t2,
               int t3,
               int wi,
               int st,
               color col,
               string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	datetime Trime[];
	ArrayResize(Trime, iBars(_Symbol, PERIOD_CURRENT));
	CopyTime(_Symbol, PERIOD_CURRENT, 0, iBars(_Symbol, PERIOD_CURRENT), Trime);
	ArraySetAsSeries(Trime, true);
   
	ObjectSetInteger(0, name, OBJPROP_TIME, Trime[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Trime[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
	ObjectSetInteger(0, name, OBJPROP_STYLE, st);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
	ObjectSetInteger(0, name, OBJPROP_RAY, false);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToString(pr1, _Digits) + " Date: " + TimeToString(Trime[t1], TIME_DATE));
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	//ObjectSetText(name, label, FSize, "Arial", FCol);
	ObjectSetString(0, name, OBJPROP_TEXT, label);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name,
             const double pr1,
             const double pr2,
             const int t1,
             const int t2,
             const int t3,
             const color BCol,
             string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	datetime Trime[];
	ArrayResize(Trime, iBars(_Symbol, PERIOD_CURRENT));
	CopyTime(_Symbol, PERIOD_CURRENT, 0, iBars(_Symbol, PERIOD_CURRENT), Trime);
	ArraySetAsSeries(Trime, true);
		
	ObjectSetInteger(0, name, OBJPROP_TIME, Trime[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Trime[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_FILL, true);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

 //+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name,
            const double x,
            const datetime y,
            const string text,
            const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	datetime Trime[];
	ArrayResize(Trime, iBars(_Symbol, PERIOD_CURRENT));
	CopyTime(_Symbol, PERIOD_CURRENT, 0, iBars(_Symbol, PERIOD_CURRENT), Trime);
	ArraySetAsSeries(Trime, true);
	
	ObjectSetDouble(0, name, OBJPROP_PRICE, x);
	ObjectSetInteger(0, name, OBJPROP_TIME, y);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	//ObjectSetString(0, name, OBJPROP_TOOLTIP, "Price: " + DoubleToStr(x, _Digits));
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToString(x, _Digits));
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
}
//+------------------------------------------------------------------+  
   