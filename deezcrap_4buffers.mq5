//+------------------------------------------------------------------+
//|                                                     deezcrap.mq5 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright   "Copyright 2017, MetaQuotes Software Corp."
#property link        "https://www.mql5.com"
#property version     "1.00"
#property strict
#property indicator_separate_window

//––– Four separate plots like MT4
#property indicator_plots     4
#property indicator_buffers   6

// Plot 1: Blue line
#property indicator_label1    "Blue"
#property indicator_type1     DRAW_LINE
#property indicator_color1    clrBlue
#property indicator_width1    3

// Plot 2: Red line  
#property indicator_label2    "Red"
#property indicator_type2     DRAW_LINE
#property indicator_color2    clrRed
#property indicator_width2    3

// Plot 3: Green line
#property indicator_label3    "Green" 
#property indicator_type3     DRAW_LINE
#property indicator_color3    clrGreen
#property indicator_width3    3

// Plot 4: Orange line
#property indicator_label4    "Orange"
#property indicator_type4     DRAW_LINE
#property indicator_color4    clrOrange
#property indicator_width4    3

// Buffers (like MT4)
double  ValueBuffer[];   // the "black" baseline for calculations
double  ema120[];        // smoothed line for calculations
double  BlueBuffer[];    // Blue line plot
double  RedBuffer[];     // Red line plot  
double  GreenBuffer[];   // Green line plot
double  OrangeBuffer[];  // Orange line plot

//+------------------------------------------------------------------+
//| Initialization                                                   |
//+------------------------------------------------------------------+
int OnInit()
{
  // bind our buffers like MT4
  SetIndexBuffer(0, BlueBuffer, INDICATOR_DATA);
  SetIndexBuffer(1, RedBuffer, INDICATOR_DATA);
  SetIndexBuffer(2, GreenBuffer, INDICATOR_DATA);
  SetIndexBuffer(3, OrangeBuffer, INDICATOR_DATA);
  SetIndexBuffer(4, ValueBuffer, INDICATOR_DATA);    // calculation buffer
  SetIndexBuffer(5, ema120, INDICATOR_DATA);         // calculation buffer

  // Set empty values for all plot buffers (like MT4)
  PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
  PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
  PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, EMPTY_VALUE);
  PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, EMPTY_VALUE);

  return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Calculation                                                      |
//+------------------------------------------------------------------+
int OnCalculate(const int      rates_total,
                const int      prev_calculated,
                const datetime &time[],
                const double   &open[],
                const double   &high[],
                const double   &low[],
                const double   &close[],
                const long     &tick_volume[],
                const long     &volume[],
                const int      &spread[])
{
  if(rates_total < 127)                // not enough bars yet
     return(0);

  //––– make sure everything is in series orientation
  ArraySetAsSeries(high,true);
  ArraySetAsSeries(low, true);
  ArraySetAsSeries(close,true);
  ArraySetAsSeries(ValueBuffer,true);
  ArraySetAsSeries(BlueBuffer,true);
  ArraySetAsSeries(RedBuffer,true);
  ArraySetAsSeries(GreenBuffer,true);
  ArraySetAsSeries(OrangeBuffer,true);
  ArraySetAsSeries(ema120, true);

  //––– tiny working arrays for our pivots
  double up[],dn[],md[],hup[],ldn[];
  ArrayResize(up, rates_total);
  ArrayResize(dn, rates_total);
  ArrayResize(md, rates_total);
  ArrayResize(hup,rates_total);
  ArrayResize(ldn,rates_total);
  ArraySetAsSeries(up,true);
  ArraySetAsSeries(dn,true);
  ArraySetAsSeries(md,true);
  ArraySetAsSeries(hup,true);
  ArraySetAsSeries(ldn,true);

  //––– the earliest bar we can calculate is rates_total–127
  int firstBar = rates_total - 127;
  if(firstBar < 0) firstBar = 0;

  //––– seed the baseline at bar (firstBar+1)==0.0 on a full redraw
  if(prev_calculated == 0 && firstBar+1 < rates_total)
     ValueBuffer[firstBar+1] = 0.0;

  //––– if we're just updating, start at prev_calculated–1
  int start = (prev_calculated > 0 ? prev_calculated-1 : firstBar);

  //––– main loop: build from oldest→newest
  for(int i = start; i >= 0; i--)
  {
    // Skip if not enough bars for Donchian calculation
    if(i + 26 >= rates_total) {
      ValueBuffer[i] = 0.0;
      BlueBuffer[i] = EMPTY_VALUE;
      RedBuffer[i] = EMPTY_VALUE;
      GreenBuffer[i] = EMPTY_VALUE;
      OrangeBuffer[i] = EMPTY_VALUE;
      ema120[i] = 0.0;
      continue;
    }
    
    // Find highest/lowest over 20 bars, offset by 6 (manually to avoid array errors)
    double highest = high[i+6];
    double lowest = low[i+6];
    for(int j = i+6; j < i+26 && j < rates_total; j++) {
      if(high[j] > highest) highest = high[j];
      if(low[j] < lowest) lowest = low[j];
    }
    
    up[i] = highest;
    dn[i] = lowest;
    double dist = up[i] - dn[i];
    hup[i] = up[i] - 0.236*dist;
    ldn[i] = dn[i] + 0.236*dist;
    md[i]  = dn[i] + 0.5*dist;

    // the "black" stepping line
    if(i < rates_total-1) {
      if(close[i] > md[i])
         ValueBuffer[i] = ValueBuffer[i+1] + 0.001;
      else
         ValueBuffer[i] = ValueBuffer[i+1] - 0.001;
    } else {
      ValueBuffer[i] = 0.0;
    }

    // Calculate smoothed line first
    ema120[i] = iMAOnArray(ValueBuffer, rates_total, 6, 0, 3, i); // 3 = LWMA mode
    
    // Initialize all buffers to EMPTY_VALUE (no line)
    BlueBuffer[i] = EMPTY_VALUE;
    RedBuffer[i] = EMPTY_VALUE;
    GreenBuffer[i] = EMPTY_VALUE;
    OrangeBuffer[i] = EMPTY_VALUE;

    // Apply colors exactly like MT4 - only show line when condition is met
    if(close[i] > md[i])     BlueBuffer[i] = ema120[i];    // Blue when above middle
    if(close[i] > hup[i])    GreenBuffer[i] = ema120[i];   // Green when above upper
    if(close[i] < md[i])     RedBuffer[i] = ema120[i];     // Red when below middle
    if(close[i] < ldn[i])    OrangeBuffer[i] = ema120[i];  // Orange when below lower
  }

  //––– clear out anything newer than firstBar (optional)
  for(int j = rates_total-1; j > firstBar; j--)
  {
    ValueBuffer[j] = 0.0;
    BlueBuffer[j] = EMPTY_VALUE;
    RedBuffer[j] = EMPTY_VALUE;
    GreenBuffer[j] = EMPTY_VALUE;
    OrangeBuffer[j] = EMPTY_VALUE;
    ema120[j] = 0.0;
  }

  return(rates_total);
}

//+------------------------------------------------------------------+
//|  iMAOnArray for MQL5 — supports SMA, EMA, SMMA, LWMA              |
//+------------------------------------------------------------------+
double iMAOnArray(
   const double &array[],       // your source series (must be ArraySetAsSeries(...,true))
   int            arraySize,   // typically rates_total
   int            period,      // MA period (e.g. 6)
   int            ma_shift,    // MA shift
   int            method,      // 0=SMA, 1=EMA, 2=SMMA, 3=LWMA
   int            index        // the bar index you want (0=current, 1=previous, etc)
   )
{
   // compute the "start" bar for the window
   int start = index + ma_shift;
   if(start < 0 || start + period - 1 >= arraySize)
      return 0.0;  // not enough data

   switch(method)
   {
      //--- Simple MA
      case 0: // SMA
      {
         double sum = 0.0;
         for(int i = start; i < start + period; i++)
            sum += array[i];
         return sum / period;
      }

      //--- Exponential MA
      case 1: // EMA
      {
         double alpha = 2.0 / (period + 1);
         int   seedPos = start + period - 1;
         // seed with a SMA of the first 'period' bars
         double ema = 0.0;
         for(int i = start; i <= seedPos; i++)
            ema += array[i];
         ema /= period;
         // then recurse backwards down to 'start'
         for(int i = seedPos - 1; i >= start; i--)
            ema = alpha * array[i] + (1 - alpha) * ema;
         return ema;
      }

      //--- Smoothed MA
      case 2: // SMMA
      {
         // first point = SMA of the first 'period' bars
         double smma = 0.0;
         for(int i = start; i < start + period; i++)
            smma += array[i];
         smma /= period;
         // then apply the standard SMMA formula forward
         for(int i = start + period; i <= index + ma_shift; i++)
            smma = ((smma * (period - 1)) + array[i]) / period;
         return smma;
      }

      //--- Linear-Weighted MA
      case 3: // LWMA
      {
         double sumWeights = 0.0, sumVals = 0.0;
         for(int k = 0; k < period; k++)
         {
            double w = double(k + 1);
            sumWeights += w;
            sumVals    += array[start + k] * w;
         }
         return sumVals / sumWeights;
      }

      default:
         return 0.0;
   }
}
