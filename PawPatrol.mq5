//+------------------------------------------------------------------+
//|                                           PawPatrolDSP.mq4       |
//|                                           Copyright 2017, Sakis  |
//|                                                                  |
//+------------------------------------------------------------------+

// Version History
// ---------------
// v1.0 

#property strict
#property indicator_separate_window
#define Name WindowExpertName()
#property indicator_buffers 4
#property indicator_color1 clrWhite
#property indicator_color2 clrBlack
#property indicator_color3 clrRed
#property indicator_color4 clrBlue

input int d = 48; //PawSize Black
input int e = 24; //PawSize White
input bool draw = true; //Enable Drawings
bool Alerts=false;
bool pushn=true;
int period = 500;

double SIG[], LIG[];
double DAMA[], HAMA[];
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
	IndicatorDigits(1);
	IndicatorShortName(Name + " " + IntegerToString(d));
	
	if(IsTesting()) period=Bars-24;
	
	ObjectsDeleteAll(0, Name+"Arr");
	ObjectsDeleteAll(0, Name+"LinU");
	ObjectsDeleteAll(0, Name+"LinD");
	ObjectsDeleteAll(0, Name+"Pric");
	
	ObjectCreate(0, Name + "LINE0", OBJ_HLINE, ChartWindowFind(0, Name + " " + IntegerToString(d)), Time[0], 0);
	ObjectSet(Name + "LINE0", OBJPROP_PRICE1, 0);
	ObjectSet(Name + "LINE0", OBJPROP_COLOR, Black);
	ObjectSet(Name + "LINE0", OBJPROP_STYLE, STYLE_SOLID);
	ObjectSet(Name + "LINE0", OBJPROP_WIDTH, 2);
	ObjectSet(Name + "LINE0", OBJPROP_BACK, true);

	ObjectCreate(0, Name + "LINE1", OBJ_HLINE, ChartWindowFind(0, Name + " " + IntegerToString(d)), Time[0], 0.5);
	ObjectSet(Name + "LINE1", OBJPROP_PRICE1, 0.5);
	ObjectSet(Name + "LINE1", OBJPROP_COLOR, SeaGreen);
	ObjectSet(Name + "LINE1", OBJPROP_STYLE, STYLE_DOT);
	ObjectSet(Name + "LINE1", OBJPROP_WIDTH, 1);
	ObjectSet(Name + "LINE1", OBJPROP_BACK, true);

	ObjectCreate(0, Name + "LINE2", OBJ_HLINE, ChartWindowFind(0, Name + " " + IntegerToString(d)), Time[0], 1);
	ObjectSet(Name + "LINE2", OBJPROP_PRICE1, 1);
	ObjectSet(Name + "LINE2", OBJPROP_COLOR, Blue);
	ObjectSet(Name + "LINE2", OBJPROP_STYLE, STYLE_DOT);
	ObjectSet(Name + "LINE2", OBJPROP_WIDTH, 1);
	ObjectSet(Name + "LINE2", OBJPROP_BACK, true);

	ObjectCreate(0, Name + "LINE3", OBJ_HLINE, ChartWindowFind(0, Name + " " + IntegerToString(d)), Time[0], 1.5);
	ObjectSet(Name + "LINE3", OBJPROP_PRICE1, 1.5);
	ObjectSet(Name + "LINE3", OBJPROP_COLOR, Blue);
	ObjectSet(Name + "LINE3", OBJPROP_STYLE, STYLE_DOT);
	ObjectSet(Name + "LINE3", OBJPROP_WIDTH, 1);
	ObjectSet(Name + "LINE3", OBJPROP_BACK, true);

	ObjectCreate(0, Name + "LINE4", OBJ_HLINE, ChartWindowFind(0, Name + " " + IntegerToString(d)), Time[0], -0.5);
	ObjectSet(Name + "LINE4", OBJPROP_PRICE1, -0.5);
	ObjectSet(Name + "LINE4", OBJPROP_COLOR, DarkOrchid);
	ObjectSet(Name + "LINE4", OBJPROP_STYLE, STYLE_DOT);
	ObjectSet(Name + "LINE4", OBJPROP_WIDTH, 1);
	ObjectSet(Name + "LINE4", OBJPROP_BACK, true);

	ObjectCreate(0, Name + "LINE5", OBJ_HLINE, ChartWindowFind(0, Name + " " + IntegerToString(d)), Time[0], -1);
	ObjectSet(Name + "LINE5", OBJPROP_PRICE1, -1);
	ObjectSet(Name + "LINE5", OBJPROP_COLOR, Red);
	ObjectSet(Name + "LINE5", OBJPROP_STYLE, STYLE_DOT);
	ObjectSet(Name + "LINE5", OBJPROP_WIDTH, 1);
	ObjectSet(Name + "LINE5", OBJPROP_BACK, true);

	ObjectCreate(0, Name + "LINE6", OBJ_HLINE, ChartWindowFind(0, Name + " " + IntegerToString(d)), Time[0], -1.5);
	ObjectSet(Name + "LINE6", OBJPROP_PRICE1, -1.5);
	ObjectSet(Name + "LINE6", OBJPROP_COLOR, Red);
	ObjectSet(Name + "LINE6", OBJPROP_STYLE, STYLE_DOT);
	ObjectSet(Name + "LINE6", OBJPROP_WIDTH, 1);
	ObjectSet(Name + "LINE6", OBJPROP_BACK, true);

	SetIndexStyle(1, DRAW_LINE, 1, 2);
	SetIndexBuffer(1, SIG);
	SetIndexStyle(0, DRAW_LINE, 1, 2);
	SetIndexBuffer(0, LIG);
	SetIndexStyle(2, DRAW_LINE, 1, 2);
	SetIndexBuffer(2, DAMA);
	SetIndexStyle(3, DRAW_LINE, 1, 2);
	SetIndexBuffer(3, HAMA);
	
	objArrow(Name + "PricD",0,0,0,6,clrRed,2);
	objArrow(Name + "PricU",0,0,0,6,clrBlue,2);
	//---
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || IsTesting())
		if (!IsTesting())
		{
			DeleteObjects();
		}
	Comment(""); // Cleanup
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	//---
	bool new_5m_check = false;
	static datetime start_5m_time = 0;
	if (start_5m_time < iTime(NULL, PERIOD_M1, 0))
	{
		new_5m_check = true;
		start_5m_time = iTime(NULL, PERIOD_M1, 0);
	}
	if (new_5m_check)
	{
		blingbling();
		blingbling2();
		for (int i = 0; i < Bars - 1; i++) {
			HAMA[i] = iMAOnArray(SIG, 0, 20, 0, MODE_SMA, i);
			DAMA[i] = iMAOnArray(LIG, 0, 20, 0, MODE_SMA, i);
			if(HAMA[i]==EMPTY_VALUE) HAMA[i]=0;
			if(DAMA[i]==EMPTY_VALUE) DAMA[i]=0;
		}
		//Print("check");
		/*if((SIG[2]==-1 && SIG[1]==1) || (SIG[3]==-1 && SIG[2]==0 && SIG[1]==1) || (SIG[4]==-1 && SIG[3]==0 && SIG[2]==0 && SIG[1]==1)){
			Alert("Check " + Symbol() + "Up");
		}
		if((SIG[2]==1 && SIG[1]==-1) || (SIG[3]==1 && SIG[2]==0 && SIG[1]==-1) || (SIG[4]==1 && SIG[3]==0 && SIG[2]==0 && SIG[1]==-1)){
			Alert("Check " + Symbol() + "Down");
		}*/
	new_5m_check = false;
	}
	
	if(draw){
	Alerts=false;
	bool new_al_check = false;
	static datetime start_al_time = 0;
	if (start_al_time < iTime(NULL, PERIOD_CURRENT, 0))
	{
		new_al_check = true;
		start_al_time = iTime(NULL, PERIOD_CURRENT, 0);
	}
	if (new_al_check)
	{
		Alerts=true;
		Draw();
		new_al_check = false;
	}
	}
	//--- return value of prev_calculated for next call
	return(rates_total);
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	for (int i = ObjectsTotal() - 1; i >= 0; i--)
	{
		string ObName = ObjectName(i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(ObName);
			ObjectsDeleteAll(0, Name+"Arr");
		}
	}
}
//+------------------------------------------------------------------+

//+MAIN--------------------------------------------------------------+
void blingbling() {
	int counted_bars = IndicatorCounted();
	int limit = Bars - counted_bars;
	if (counted_bars > 0) limit++;

	for (int i = 0; i < limit - 1; i++)
	{
		if ((iClose(Symbol(), PERIOD_CURRENT, i) > iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) > iHigh(Symbol(), PERIOD_CURRENT, i + d)) && (iLow(Symbol(), PERIOD_CURRENT, i) > iLow(Symbol(), PERIOD_CURRENT, i + d)))
			SIG[i] = 2;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) > iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) < iHigh(Symbol(), PERIOD_CURRENT, i + d)) && (iLow(Symbol(), PERIOD_CURRENT, i) > iLow(Symbol(), PERIOD_CURRENT, i + d)))
			SIG[i] = 1.5;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) > iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) > iHigh(Symbol(), PERIOD_CURRENT, i + d)) && (iLow(Symbol(), PERIOD_CURRENT, i) < iLow(Symbol(), PERIOD_CURRENT, i + d)))
			SIG[i] = 1;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) > iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) < iHigh(Symbol(), PERIOD_CURRENT, i + d)) && (iLow(Symbol(), PERIOD_CURRENT, i) < iLow(Symbol(), PERIOD_CURRENT, i + d)))
			SIG[i] = 0.5;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) < iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) > iHigh(Symbol(), PERIOD_CURRENT, i + d)) && (iLow(Symbol(), PERIOD_CURRENT, i) > iLow(Symbol(), PERIOD_CURRENT, i + d)))
			SIG[i] = -0.5;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) < iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) > iHigh(Symbol(), PERIOD_CURRENT, i + d)) && (iLow(Symbol(), PERIOD_CURRENT, i) < iLow(Symbol(), PERIOD_CURRENT, i + d)))
			SIG[i] = -1;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) < iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) < iHigh(Symbol(), PERIOD_CURRENT, i + d)) && (iLow(Symbol(), PERIOD_CURRENT, i) > iLow(Symbol(), PERIOD_CURRENT, i + d)))
			SIG[i] = -1.5;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) < iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) < iHigh(Symbol(), PERIOD_CURRENT, i + d)) && (iLow(Symbol(), PERIOD_CURRENT, i) < iLow(Symbol(), PERIOD_CURRENT, i + d)))
			SIG[i] = -2;
		else SIG[i] = 0;
	}
	/* previous
		for (int n = 1; n <= d; n++) {
				if ((iClose(Symbol(), PERIOD_CURRENT, i) > iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iLow(Symbol(), PERIOD_CURRENT, i) > iLow(Symbol(), PERIOD_CURRENT, i + n)))
					SIG[i] = 1;
				else if ((iClose(Symbol(), PERIOD_CURRENT, i) > iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) < iHigh(Symbol(), PERIOD_CURRENT, i + n)))
					SIG[i] = 0.5;
				else if ((iClose(Symbol(), PERIOD_CURRENT, i) < iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iLow(Symbol(), PERIOD_CURRENT, i) > iLow(Symbol(), PERIOD_CURRENT, i + n)))
					SIG[i] = -0.5;
				else if ((iClose(Symbol(), PERIOD_CURRENT, i) < iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) < iHigh(Symbol(), PERIOD_CURRENT, i + n)))
					SIG[i] = -1;
				else SIG[i] = 0;
	*/
	//ArrayCopy(AMA,SIG,0,0,0);
	//ArraySetAsSeries(AMA,true);
}
//+------------------------------------------------------------------+

//+MAIN2-------------------------------------------------------------+
void blingbling2() {
	int counted_bars = IndicatorCounted();
	int limit = Bars - counted_bars;
	if (counted_bars > 0) limit++;

	for (int i = 0; i < limit - 1; i++)
	{
		if ((iClose(Symbol(), PERIOD_CURRENT, i) > iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) > iHigh(Symbol(), PERIOD_CURRENT, i + e)) && (iLow(Symbol(), PERIOD_CURRENT, i) > iLow(Symbol(), PERIOD_CURRENT, i + e)))
			LIG[i] = 2;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) > iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) < iHigh(Symbol(), PERIOD_CURRENT, i + e)) && (iLow(Symbol(), PERIOD_CURRENT, i) > iLow(Symbol(), PERIOD_CURRENT, i + e)))
			LIG[i] = 1.5;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) > iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) > iHigh(Symbol(), PERIOD_CURRENT, i + e)) && (iLow(Symbol(), PERIOD_CURRENT, i) < iLow(Symbol(), PERIOD_CURRENT, i + e)))
			LIG[i] = 1;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) > iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) < iHigh(Symbol(), PERIOD_CURRENT, i + e)) && (iLow(Symbol(), PERIOD_CURRENT, i) < iLow(Symbol(), PERIOD_CURRENT, i + e)))
			LIG[i] = 0.5;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) < iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) > iHigh(Symbol(), PERIOD_CURRENT, i + e)) && (iLow(Symbol(), PERIOD_CURRENT, i) > iLow(Symbol(), PERIOD_CURRENT, i + e)))
			LIG[i] = -0.5;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) < iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) > iHigh(Symbol(), PERIOD_CURRENT, i + e)) && (iLow(Symbol(), PERIOD_CURRENT, i) < iLow(Symbol(), PERIOD_CURRENT, i + e)))
			LIG[i] = -1;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) < iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) < iHigh(Symbol(), PERIOD_CURRENT, i + e)) && (iLow(Symbol(), PERIOD_CURRENT, i) > iLow(Symbol(), PERIOD_CURRENT, i + e)))
			LIG[i] = -1.5;
		else if ((iClose(Symbol(), PERIOD_CURRENT, i) < iClose(Symbol(), PERIOD_CURRENT, i + 1)) && (iHigh(Symbol(), PERIOD_CURRENT, i) < iHigh(Symbol(), PERIOD_CURRENT, i + e)) && (iLow(Symbol(), PERIOD_CURRENT, i) < iLow(Symbol(), PERIOD_CURRENT, i + e)))
			LIG[i] = -2;
		else LIG[i] = 0;
	}
	/*
		for (int n = 1; n <= e; n++) {
	*/
}
//+------------------------------------------------------------------+

//+DRAWINGS----------------------------------------------------------+
void Draw() {
	bool upal=false;
	bool dnal=false;
	static double pL;
	static double pH;
	static datetime tL;
	static datetime tH;
	
	if ((period+24) > Bars) period = (Bars-24);
	
	for (int i = period; i >= 2; i--) {
		int time;
		if(i<23) time=0; else time=i-23;
		if ( ((HAMA[i] > 0) && (HAMA[i+1]>DAMA[i+1]) && (HAMA[i]>DAMA[i]) && (iHigh(Symbol(),PERIOD_CURRENT,i)>iHigh(Symbol(),PERIOD_CURRENT,iHighest(Symbol(),PERIOD_CURRENT,MODE_HIGH,23,i+1)))) || ((HAMA[i] > 0) && (HAMA[i+2]<=DAMA[i+2]) && (HAMA[i+1]==DAMA[i+1]) && (HAMA[i]==DAMA[i]) && (iHigh(Symbol(),PERIOD_CURRENT,i)>iHigh(Symbol(),PERIOD_CURRENT,iHighest(Symbol(),PERIOD_CURRENT,MODE_HIGH,23,i+1)))) ) {
			double p1=iLow(Symbol(),PERIOD_CURRENT,iHighest(Symbol(),PERIOD_CURRENT,MODE_HIGH,23,i+1));
			datetime t1=Time[iHighest(Symbol(),PERIOD_CURRENT,MODE_HIGH,23,i+1)];
			datetime t2=Time[time];
			objLineD(Name + "LinD" + IntegerToString(i), t1, p1, t2, p1, clrRed);
			objArrow(Name + "Arr" + IntegerToString(i), 0, iTime(Symbol(), PERIOD_CURRENT, i), iHigh(Symbol(), PERIOD_CURRENT, i) + 130 * Point, 174, clrRed, 0);
			pL=iLow(Symbol(),PERIOD_CURRENT,iHighest(Symbol(),PERIOD_CURRENT,MODE_HIGH,23,i+1)); tL=iTime(Symbol(),PERIOD_CURRENT,i);
		}
		if ( ((HAMA[i] < 0) && (HAMA[i+1]<DAMA[i+1]) && (HAMA[i]<DAMA[i]) && (iLow(Symbol(),PERIOD_CURRENT,i)<iLow(Symbol(),PERIOD_CURRENT,iLowest(Symbol(),PERIOD_CURRENT,MODE_LOW,23,i+1)))) || ((HAMA[i] < 0) && (HAMA[i+2]>=DAMA[i+2]) && (HAMA[i+1]==DAMA[i+1]) && (HAMA[i]==DAMA[i]) && (iLow(Symbol(),PERIOD_CURRENT,i)<iLow(Symbol(),PERIOD_CURRENT,iLowest(Symbol(),PERIOD_CURRENT,MODE_LOW,23,i+1)))) ) {
			double p1=iHigh(Symbol(),PERIOD_CURRENT,iLowest(Symbol(),PERIOD_CURRENT,MODE_LOW,23,i+1));
			datetime t1=Time[iLowest(Symbol(),PERIOD_CURRENT,MODE_LOW,23,i+1)];
			datetime t2=Time[time];
			objLineU(Name + "LinU" + IntegerToString(i), t1, p1, t2, p1, clrBlue);
			objArrow(Name + "Arr" + IntegerToString(i), 0, iTime(Symbol(), PERIOD_CURRENT, i), iLow(Symbol(), PERIOD_CURRENT, i) - 50 * Point, 174, clrBlue, 0);
			pH=iHigh(Symbol(),PERIOD_CURRENT,iLowest(Symbol(),PERIOD_CURRENT,MODE_LOW,23,i+1)); tH=iTime(Symbol(),PERIOD_CURRENT,i);
		}
	}
	
		int time=0;
		if ( ((HAMA[1] > 0) && (HAMA[2]>DAMA[2]) && (HAMA[1]>DAMA[1]) && (iHigh(Symbol(),PERIOD_CURRENT,1)>iHigh(Symbol(),PERIOD_CURRENT,iHighest(Symbol(),PERIOD_CURRENT,MODE_HIGH,23,2)))) || ((HAMA[1] > 0) && (HAMA[3]<=DAMA[3]) && (HAMA[2]==DAMA[2]) && (HAMA[1]==DAMA[1]) && (iHigh(Symbol(),PERIOD_CURRENT,1)>iHigh(Symbol(),PERIOD_CURRENT,iHighest(Symbol(),PERIOD_CURRENT,MODE_HIGH,23,2)))) ) {
			double p1=iLow(Symbol(),PERIOD_CURRENT,iHighest(Symbol(),PERIOD_CURRENT,MODE_HIGH,23,2));
			datetime t1=Time[iHighest(Symbol(),PERIOD_CURRENT,MODE_HIGH,23,2)];
			datetime t2=Time[time];
			objLineD(Name + "LinD" + IntegerToString(1), t1, p1, t2, p1, clrRed);
			objArrow(Name + "Arr" + IntegerToString(1), 0, iTime(Symbol(), PERIOD_CURRENT, 1), iHigh(Symbol(), PERIOD_CURRENT, 1) + 130 * Point, 174, clrMaroon, 0);
			dnal=true;
		}
		if ( ((HAMA[1] < 0) && (HAMA[2]<DAMA[2]) && (HAMA[1]<DAMA[1]) && (iLow(Symbol(),PERIOD_CURRENT,1)<iLow(Symbol(),PERIOD_CURRENT,iLowest(Symbol(),PERIOD_CURRENT,MODE_LOW,23,2)))) || ((HAMA[1] < 0) && (HAMA[3]>=DAMA[3]) && (HAMA[2]==DAMA[2]) && (HAMA[1]==DAMA[1]) && (iLow(Symbol(),PERIOD_CURRENT,1)<iLow(Symbol(),PERIOD_CURRENT,iLowest(Symbol(),PERIOD_CURRENT,MODE_LOW,23,2)))) ) {
			double p1=iHigh(Symbol(),PERIOD_CURRENT,iLowest(Symbol(),PERIOD_CURRENT,MODE_LOW,23,2));
			datetime t1=Time[iLowest(Symbol(),PERIOD_CURRENT,MODE_LOW,23,2)];
			datetime t2=Time[time];
			objLineU(Name + "LinU" + IntegerToString(1), t1, p1, t2, p1, clrBlue);
			objArrow(Name + "Arr" + IntegerToString(1), 0, iTime(Symbol(), PERIOD_CURRENT, 1), iLow(Symbol(), PERIOD_CURRENT, 1) - 50 * Point, 174, clrNavy, 0);
			upal=true;
		}
	string aldn = "PawPatrol " + string(PERIOD_CURRENT) + " : Check "+Symbol()+" possible down at "+DoubleToStr((iLow(Symbol(),PERIOD_CURRENT,iHighest(Symbol(),PERIOD_CURRENT,MODE_HIGH,23,2))),Digits);
	string alup = "PawPatrol " + string(PERIOD_CURRENT) + " : Check "+Symbol()+" possible up at "+DoubleToStr((iHigh(Symbol(),PERIOD_CURRENT,iLowest(Symbol(),PERIOD_CURRENT,MODE_LOW,23,2))),Digits);
	
	if(Alerts && dnal) { Alert(aldn); if(pushn) SendNotification(aldn); objArrow(Name + "PricD", 0, Time[0], iLow(Symbol(),PERIOD_CURRENT,iHighest(Symbol(),PERIOD_CURRENT,MODE_HIGH,23,2)), SYMBOL_RIGHTPRICE, clrMaroon, 2); pL=iLow(Symbol(),PERIOD_CURRENT,iHighest(Symbol(),PERIOD_CURRENT,MODE_HIGH,23,2)); tL=iTime(Symbol(),PERIOD_CURRENT,1); }
	ObjectMove(Name+"PricD",0,Time[0],pL);
	if(Alerts && upal) { Alert(alup); if(pushn) SendNotification(alup); objArrow(Name + "PricU", 0, Time[0], iHigh(Symbol(),PERIOD_CURRENT,iLowest(Symbol(),PERIOD_CURRENT,MODE_LOW,23,2)), SYMBOL_RIGHTPRICE, clrNavy, 2); pH=iHigh(Symbol(),PERIOD_CURRENT,iLowest(Symbol(),PERIOD_CURRENT,MODE_LOW,23,2)); tH=iTime(Symbol(),PERIOD_CURRENT,1); }
	ObjectMove(Name+"PricU",0,Time[0],pH);
	//if(tH>tL) ObjectDelete(Name+"PricD"); else if(tL>tH) ObjectDelete(Name+"PricU"); // keep only the latest on chart?
}
//+------------------------------------------------------------------+

//+ARROW FUNCTION----------------------------------------------------+
void objArrow(string sName, int win, datetime t, double p, int arrow, color col, int width)
{
	ObjectCreate(sName, OBJ_ARROW, win, 0, 0);
	ObjectSet(sName, OBJPROP_TIME1, t);
	ObjectSet(sName, OBJPROP_PRICE1, p);
	ObjectSet(sName, OBJPROP_ARROWCODE, arrow);
	ObjectSet(sName, OBJPROP_COLOR, col);
	ObjectSet(sName, OBJPROP_WIDTH, width);
}
//+------------------------------------------------------------------+

//+LINE UP FUNCTION--------------------------------------------------+
void objLineU(string sName,datetime ts,double ps,datetime te,double pe,color col,
             int win=0,int width=0,int style=STYLE_DOT,bool bBack=false,bool bRay=false)
  {
   ObjectCreate(sName,OBJ_TREND,win,0,0);
   ObjectSet(sName,OBJPROP_TIME1,ts);
   ObjectSet(sName,OBJPROP_PRICE1,ps);
   ObjectSet(sName,OBJPROP_TIME2,te);
   ObjectSet(sName,OBJPROP_PRICE2,pe);
   ObjectSet(sName,OBJPROP_COLOR,col);
   ObjectSet(sName,OBJPROP_WIDTH,width);
   ObjectSet(sName,OBJPROP_STYLE,style);
   ObjectSet(sName,OBJPROP_BACK,bBack);
   ObjectSet(sName,OBJPROP_RAY,bRay);
  }
//+------------------------------------------------------------------+

//+LINE DN FUNCTION--------------------------------------------------+
void objLineD(string sName,datetime ts,double ps,datetime te,double pe,color col,
             int win=0,int width=0,int style=STYLE_DOT,bool bBack=false,bool bRay=false)
  {
   ObjectCreate(sName,OBJ_TREND,win,0,0);
   ObjectSet(sName,OBJPROP_TIME1,ts);
   ObjectSet(sName,OBJPROP_PRICE1,ps);
   ObjectSet(sName,OBJPROP_TIME2,te);
   ObjectSet(sName,OBJPROP_PRICE2,pe);
   ObjectSet(sName,OBJPROP_COLOR,col);
   ObjectSet(sName,OBJPROP_WIDTH,width);
   ObjectSet(sName,OBJPROP_STYLE,style);
   ObjectSet(sName,OBJPROP_BACK,bBack);
   ObjectSet(sName,OBJPROP_RAY,bRay);
  }
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function 
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed"; break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed"; break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed"; break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed"; break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled"; break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart"; break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart"; break;
	default:text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+