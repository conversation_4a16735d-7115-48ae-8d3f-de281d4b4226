//+------------------------------------------------------------------+
//|                                                      vwaprth.mq5 |
//|                        Copyright 2019, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2019, MetaQuotes Software Corp."
#property link "https://www.mql5.com"
#property version "1.00"
#property indicator_chart_window
#property indicator_buffers 5
#property indicator_plots 5
//VWAP
#property indicator_type1 DRAW_LINE
#property indicator_color1 clrBlue
#property indicator_style1 STYLE_SOLID
#property indicator_width1 2
#property indicator_label1 "iVWAP"
//VWAP SD POS1
#property indicator_type2 DRAW_LINE
#property indicator_color2 clrSilver
#property indicator_style2 STYLE_DOT
#property indicator_label2 "iVWAP SDPos1"
//VWAP SD NEG 1
#property indicator_type3 DRAW_LINE
#property indicator_color3 clrSilver
#property indicator_style3 STYLE_DOT
#property indicator_label3 "iVWAP SDNeg1"
//VWAP SD POS 2
#property indicator_type4 DRAW_LINE
#property indicator_color4 clrSilver
#property indicator_style4 STYLE_DOT
#property indicator_label4 "iVWAP SDPos2"
//VWAP SD NEG 2
#property indicator_type5 DRAW_LINE
#property indicator_color5 clrSilver
#property indicator_style5 STYLE_DOT
#property indicator_label5 "iVWAP SDNeg2"

double ivwap[], iSDP[], iSDN[], iSDP2[], iSDN2[];
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
	//--- indicator buffers mapping
	SetIndexBuffer(0, ivwap, INDICATOR_DATA);
	SetIndexBuffer(1, iSDP, INDICATOR_DATA);
	SetIndexBuffer(2, iSDN, INDICATOR_DATA);
	SetIndexBuffer(3, iSDP2, INDICATOR_DATA);
	SetIndexBuffer(4, iSDN2, INDICATOR_DATA);
	PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(4, PLOT_EMPTY_VALUE, EMPTY_VALUE);

	if (!checktime("16:30:00", "22:59:59"))
	{
		PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(3, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(4, PLOT_DRAW_TYPE, DRAW_NONE);
	}
	//---
	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	//---
	bool new_2m_check = false;
	static datetime start_2m_time = 0;
	if (start_2m_time < iTime(NULL, PERIOD_M1, 0))
	{
		new_2m_check = true;
		start_2m_time = iTime(NULL, PERIOD_M1, 0);
	}
	if (new_2m_check)
	{
		if (ChartPeriod(0) <= 16385 && checktime("16:30:00", "22:59:59"))
		{
			//uint start1 = GetTickCount();
			//Print("DO");
			vwaprth();
		}
		else
		{
			ArrayInitialize(ivwap, EMPTY_VALUE);
			ArrayInitialize(iSDP, EMPTY_VALUE);
			ArrayInitialize(iSDN, EMPTY_VALUE);
			ArrayInitialize(iSDP2, EMPTY_VALUE);
			ArrayInitialize(iSDN2, EMPTY_VALUE);
		}
		new_2m_check = false;
	}
	//--- return value of prev_calculated for next call
	return (rates_total);
}
//+------------------------------------------------------------------+

//+VWAP--------------------------------------------------------------+
void vwaprth()
{
	datetime bar = StringToTime("16:30");
	int start = iBarShift(_Symbol, PERIOD_CURRENT, bar, false) - 1;
	//Print(bar + " " + start);

	long tickvol[];
	ArraySetAsSeries(tickvol, true);
	if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFD || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFDINDEX)
	{
		if (CopyTickVolume(_Symbol, PERIOD_CURRENT, 0, start + 1, tickvol) <= 0)
		{
			Print(GetLastError());
			return;
		}
	}
	else if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_EXCH_FUTURES || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_EXCH_FUTURES_FORTS)
	{
		if (CopyRealVolume(_Symbol, PERIOD_CURRENT, 0, start + 1, tickvol) <= 0)
		{
			Print(GetLastError());
			return;
		}
	}
	double close[];
	ArraySetAsSeries(close, true);
	CopyClose(_Symbol, PERIOD_CURRENT, 0, start + 1, close);
	double high[];
	ArraySetAsSeries(high, true);
	CopyHigh(_Symbol, PERIOD_CURRENT, 0, start + 1, high);
	double low[];
	ArraySetAsSeries(low, true);
	CopyLow(_Symbol, PERIOD_CURRENT, 0, start + 1, low);
	datetime time[];
	ArraySetAsSeries(time, true);
	CopyTime(_Symbol, PERIOD_CURRENT, 0, start + 1, time);

	ArraySetAsSeries(ivwap, true);
	ArrayInitialize(ivwap, EMPTY_VALUE);
	ArraySetAsSeries(iSDP, true);
	ArrayInitialize(iSDP, EMPTY_VALUE);
	ArraySetAsSeries(iSDN, true);
	ArrayInitialize(iSDN, EMPTY_VALUE);
	ArraySetAsSeries(iSDP2, true);
	ArrayInitialize(iSDP2, EMPTY_VALUE);
	ArraySetAsSeries(iSDN2, true);
	ArrayInitialize(iSDN2, EMPTY_VALUE);
	//ArraySetAsSeries(SDP3, true);
	//ArrayInitialize(SDP3, EMPTY_VALUE);
	//ArraySetAsSeries(SDN3, true);
	//ArrayInitialize(SDN3, EMPTY_VALUE);

	datetime newsession = 0;
	double totvol = 0;
	double pervol = 0;
	double SD = 0;

	ivwap[start] = (high[start] + low[start] + close[start]) / 3;
	for (int x = start - 1; x >= 0; x--)
	{
		totvol += (double)tickvol[x];
		pervol += (double)tickvol[x] * (high[x] + close[x] + low[x]) / 3;
		if (totvol != 0)
		{
			ivwap[x] = pervol / totvol;
		}

		if (ChartPeriod() <= 30)
		{
			SD = 0;
			for (int k = start; k >= x; k--)
			{
				double avg = (close[k] + high[k] + low[k]) / 3;
				double diff = avg - ivwap[x];
				if (totvol != 0)
					SD += (tickvol[k] / totvol) * (diff * diff);
			}

			SD = MathSqrt(SD);
			iSDP[x] = ivwap[x] + SD;
			iSDN[x] = ivwap[x] - SD;
			iSDP2[x] = iSDP[x] + SD;
			iSDN2[x] = iSDN[x] - SD;
		}
	}
	ArrayResize(high, 0);
	ArrayResize(low, 0);
	ArrayResize(close, 0);
	ArrayResize(time, 0);
	ArrayResize(tickvol, 0);
	//Print(ivwap[0]);
}
//+------------------------------------------------------------------+

bool checktime(string starttime, string endtime)
{
	string dt = TimeToString(TimeCurrent());
	string DTstr = TimeToString(TimeCurrent(), TIME_DATE);
	string start = DTstr + " " + starttime;
	string end = DTstr + " " + endtime;
	StringToTime(start);
	StringToTime(end);
	StringToTime(dt);

	if (start < end)
		if (dt >= start && dt < end)
			return (true);
	if (start >= end)
		if (dt >= start || dt < end)
			return (true);
	return (false);
}