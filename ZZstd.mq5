//+------------------------------------------------------------------+
//|                                       ZigZagStdDevRectangles.mq5 |
//|                              Converted from MQL4 to MQL5         |
//+------------------------------------------------------------------+
#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

//--- plot ZigZag High
#property indicator_label1  "ZigZag High"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  Blue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2
//--- plot ZigZag Low
#property indicator_label2  "ZigZag Low"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  Red
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2

//--- input parameters
input int ZigZagDepth = 8;
input int ZigZagDeviation = 5;
input int ZigZagBackstep = 3;
input int SwingPointsToAnalyze = 30;
input bool NormalStd = true;
double StdDevMultiplier1 = 1.0;
double StdDevMultiplier2 = 2.0;
input color HighRectColor = clrLightGreen;
input color LowRectColor = clrPink;
input color SwingLineColor = clrYellow;

int ZZDepth;
double ZigZagBufferHigh[];
double ZigZagBufferLow[];

int storedBarIndicesHigh[];
int storedBarIndicesLow[];

//+------------------------------------------------------------------+
int OnInit()
{
   SetIndexBuffer(0, ZigZagBufferHigh, INDICATOR_DATA);
   PlotIndexSetInteger(0, PLOT_ARROW, 159);
   SetIndexBuffer(1, ZigZagBufferLow, INDICATOR_DATA);
   PlotIndexSetInteger(1, PLOT_ARROW, 159);

   if (Period() >= PERIOD_H1) ZZDepth = 12;
   else ZZDepth = 8;

   if (NormalStd)
   {
      StdDevMultiplier1 = 1.0;
      StdDevMultiplier2 = 2.0;
   }
   else
   {
      StdDevMultiplier1 = 1.229;
      StdDevMultiplier2 = 1.771;
   }
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   static datetime lastRun = 0;
   if (TimeCurrent() - lastRun < 120) return (rates_total);
   lastRun = TimeCurrent();

   ArrayInitialize(ZigZagBufferHigh, EMPTY_VALUE);
   ArrayInitialize(ZigZagBufferLow, EMPTY_VALUE);

   double zigzagHigh[];
   double zigzagLow[];
   ArrayResize(zigzagHigh, SwingPointsToAnalyze);
   ArrayResize(zigzagLow, SwingPointsToAnalyze);
   ArrayInitialize(zigzagHigh, 0.0);
   ArrayInitialize(zigzagLow, 0.0);

   ArrayResize(storedBarIndicesHigh, SwingPointsToAnalyze);
   ArrayResize(storedBarIndicesLow, SwingPointsToAnalyze);

   int validHighs = 0, validLows = 0;
   int maxBarsToScan = 1000;
   int zzz = iCustom(_Symbol, PERIOD_CURRENT, "Examples\\ZigZag", ZigZagDeviation, ZigZagBackstep);
   for (int i = 0; i < maxBarsToScan && (validHighs < SwingPointsToAnalyze || validLows < SwingPointsToAnalyze); i++)

   for (int i = 0; i < maxBarsToScan && (validHighs < SwingPointsToAnalyze || validLows < SwingPointsToAnalyze); i++)
   {
      double zz = iCustom(_Symbol, _Period, "Examples\\ZigZag", ZZDepth, ZigZagDeviation, ZigZagBackstep, 0, i);

      if (zz != 0.0 && zz != EMPTY_VALUE)
      {
         if (MathAbs(zz - high[i]) < _Point && validHighs < SwingPointsToAnalyze)
         {
            zigzagHigh[validHighs] = zz;
            storedBarIndicesHigh[validHighs] = i;
            ZigZagBufferHigh[i] = zz;
            validHighs++;
         }
         else if (MathAbs(zz - low[i]) < _Point && validLows < SwingPointsToAnalyze)
         {
            zigzagLow[validLows] = zz;
            storedBarIndicesLow[validLows] = i;
            ZigZagBufferLow[i] = zz;
            validLows++;
         }
      }
   }

   if (validHighs == 0 && validLows == 0) return (rates_total);

   ArrayResize(storedBarIndicesHigh, validHighs);
   ArrayResize(storedBarIndicesLow, validLows);
   ArrayResize(zigzagHigh, validHighs);
   ArrayResize(zigzagLow, validLows);

   ObjectsDeleteAll(0, "ZZRect_");
   ObjectsDeleteAll(0, "ZZLine_");
   ObjectsDeleteAll(0, "ZZLevel229_");
   ObjectsDeleteAll(0, "ZZLevel771_");

   for (int i = 1; i < MathMin(validHighs, validLows); i++)
   {
      int idx1 = storedBarIndicesLow[i - 1];
      int idx2 = storedBarIndicesHigh[i];
      if (idx1 >= rates_total || idx2 >= rates_total) continue;

      double price1 = zigzagLow[i - 1];
      double price2 = zigzagHigh[i];
      double swing = price2 - price1;

      double level229 = price1 + swing * 0.229;
      double level771 = price1 + swing * 0.771;

      string name229 = "ZZLevel229_" + IntegerToString(i);
      string name771 = "ZZLevel771_" + IntegerToString(i);

      datetime hitTime229 = time[0];
      datetime hitTime771 = time[0];
      bool hit229 = false;
      bool hit771 = false;

      for (int j = idx2 - 1; j >= 0; j--)
      {
         if (!hit229 && high[j] >= level229)
         {
            hitTime229 = time[j];
            hit229 = true;
         }
         if (!hit771 && high[j] >= level771)
         {
            hitTime771 = time[j];
            hit771 = true;
         }
         if (hit229 && hit771) break;
      }

      ObjectCreate(0, name229, OBJ_TREND, 0, time[idx1], level229, hit229 ? hitTime229 : time[0], level229);
      ObjectSetInteger(0, name229, OBJPROP_COLOR, SwingLineColor);
      ObjectSetInteger(0, name229, OBJPROP_WIDTH, hit229 ? 3 : 1);
      ObjectSetInteger(0, name229, OBJPROP_STYLE, hit229 ? STYLE_SOLID : STYLE_DASH);
      ObjectSetInteger(0, name229, OBJPROP_RAY, false);

      ObjectCreate(0, name771, OBJ_TREND, 0, time[idx1], level771, hit771 ? hitTime771 : time[0], level771);
      ObjectSetInteger(0, name771, OBJPROP_COLOR, SwingLineColor);
      ObjectSetInteger(0, name771, OBJPROP_WIDTH, hit771 ? 3 : 1);
      ObjectSetInteger(0, name771, OBJPROP_STYLE, hit771 ? STYLE_SOLID : STYLE_DASH);
      ObjectSetInteger(0, name771, OBJPROP_RAY, false);
   }

   return (rates_total);
}

//+------------------------------------------------------------------+
double CalculateStdDev(int startBar, int endBar, const double &high[], const double &low[])
{
   int count = MathAbs(startBar - endBar);
   if (count < 2) return 0;

   double sum = 0, sumSq = 0;
   int valid = 0;

   for (int i = MathMin(startBar, endBar); i <= MathMax(startBar, endBar); i++)
   {
      double mid = (high[i] + low[i]) / 2;
      if (mid <= 0) continue;
      sum += mid;
      sumSq += mid * mid;
      valid++;
   }

   if (valid < 2) return 0;

   double mean = sum / valid;
   double variance = (sumSq / valid) - (mean * mean);
   return MathSqrt(MathAbs(variance));
}

//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   ObjectsDeleteAll(0, "ZZRect_");
   ObjectsDeleteAll(0, "ZZLine_");
   ObjectsDeleteAll(0, "ZZLevel229_");
   ObjectsDeleteAll(0, "ZZLevel771_");
}
