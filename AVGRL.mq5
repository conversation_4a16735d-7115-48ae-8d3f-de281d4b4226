//+------------------------------------------------------------------+
//|                                           Picture of Power.mq5   |
//|                                           Copyright 2019, Sakis  |
//|                                                                  |
//+------------------------------------------------------------------+

// Version History
// ---------------
// v1.0 First version (<PERSON>)

#property strict
#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"
#define Name MQLInfoString(MQL_PROGRAM_NAME) + "U"

#property indicator_buffers 0
#property indicator_plots 0

//--- bars minimum for calculation
#define DATA_LIMIT 200

int periods = 200;

int MA200s, MA20s;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
	IndicatorSetString(INDICATOR_SHORTNAME, "AVGRL");
	//---
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-init function								 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || MQLInfoInteger(MQL_TESTER))
		if (!MQLInfoInteger(MQL_TESTER))
		{
			DeleteObjects();
		}
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	//ObjectsDeleteAll(0, Name);
	for (int i = ObjectsTotal(0, 0) - 1; i >= 0; i--)
	{
		string ObName = ObjectName(0, i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(0, ObName);
		}
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	//--- check for rates total
	if (rates_total < DATA_LIMIT) return(0); // not enough bars for calculation

	//if (BarsCalculated(MA200s) < 200)
		//return(0);

	bool new_1m_check = false;
	static datetime start_1m_time = 0;
	if (start_1m_time < iTime(NULL, PERIOD_CURRENT, 0))
	{
		new_1m_check = true;
		start_1m_time = iTime(NULL, PERIOD_CURRENT, 0);
	}
	if (new_1m_check)
	{
		AVG20s();
		//flip2();
		//Print("Did I run?");
		new_1m_check = false;
	}

	//--- return value of prev_calculated for next call
	return(rates_total);
}
//+------------------------------------------------------------------+

//+SUP/RES FUNCTION--------------------------------------------------+
void AVG20s() {
	if (iBars(_Symbol, PERIOD_CURRENT) < periods || periods == 0) periods = iBars(_Symbol, PERIOD_CURRENT) - 1;
	
   double prevdayhig[]; ArrayInitialize(prevdayhig, 0.0); ArrayResize(prevdayhig, periods + 1); ArraySetAsSeries(prevdayhig, true);
   double prevdaylow[]; ArrayInitialize(prevdaylow, 0.0); ArrayResize(prevdaylow, periods + 1); ArraySetAsSeries(prevdaylow, true);
   double prevdayope[]; ArrayInitialize(prevdayope, 0.0); ArrayResize(prevdayope, periods + 1); ArraySetAsSeries(prevdayope, true);
   double prevdayclo[]; ArrayInitialize(prevdayclo, 0.0); ArrayResize(prevdayclo, periods + 1); ArraySetAsSeries(prevdayclo, true);
   
	if (CopyHigh(_Symbol, PERIOD_CURRENT, 0, periods + 1, prevdayhig) < 0) { Print("Fail to get previous days highs: " + IntegerToString(GetLastError())); return; }
	if (CopyLow(_Symbol, PERIOD_CURRENT, 0, periods + 1, prevdaylow) < 0) { Print("Fail to get previous days lows: " + IntegerToString(GetLastError())); return; }
	if (CopyClose(_Symbol, PERIOD_CURRENT, 0, periods + 1, prevdayclo) < 0) { Print("Fail to get previous days closes: " + IntegerToString(GetLastError())); return; }
	if (CopyOpen(_Symbol, PERIOD_CURRENT, 0, periods + 1, prevdayope) < 0) { Print("Fail to get previous days opens: " + IntegerToString(GetLastError())); return; }
	
	double firstav1 = (prevdayhig[periods] - prevdaylow[periods]) / 2;
	
	double firstav1001 = (prevdayhig[periods/2] - prevdaylow[periods/2]) / 2;
	double firstav501 = (prevdayhig[periods/4] - prevdaylow[periods/4]) / 2;
	double firstav201 = (prevdayhig[periods/10] - prevdaylow[periods/10]) / 2;
	
	//200
	double avg[]; ArrayInitialize(avg, 0.0); ArrayResize(avg, periods + 1); //ArraySetAsSeries(avg, true);
	//100
	double avg100[]; ArrayInitialize(avg100, 0.0); ArrayResize(avg100, periods + 1); //ArraySetAsSeries(avg, true);
	//50
	double avg50[]; ArrayInitialize(avg50, 0.0); ArrayResize(avg50, periods + 1); //ArraySetAsSeries(avg, true);
	//20
	double avg20[]; ArrayInitialize(avg20, 0.0); ArrayResize(avg20, periods + 1); //ArraySetAsSeries(avg, true);
		
	for (int x = periods; x >= 1; x--) {
	   avg[x] = (prevdayhig[x] - prevdaylow[x]) / 2;
	   }
	double xinput1 = 0.0;
	for (int x = periods; x >= 1; x--) {
	   xinput1 += avg[x];
	   }
	double xavg1 = xinput1 / (periods);
	double sfix1 = 0.0;
	double spax1 = 0.0;
	
	double skix1[]; ArrayInitialize(skix1, 0.0); ArrayResize(skix1, periods + 1);
	
	for (int x = periods; x >= 1; x--) {
	   if (prevdayclo[x] > prevdayope[x]) sfix1 += xavg1; else sfix1 -= xavg1;
	   }
   
   for (int x = periods; x >= 1; x--) {
      if (prevdayclo[x] > prevdayope[x]) { spax1 += prevdayope[x] + xavg1; } else spax1 += prevdayope[x] - xavg1;
      if (prevdayclo[x] > prevdayope[x]) { skix1[x] = prevdayope[x] + xavg1; } else skix1[x] = prevdayope[x] - xavg1;
      }
   
   double mediana1 = 0;
   for (int x = periods; x >= 1; x--) {
      mediana1 += skix1[x] / (double)periods;
      }
   double firstS1b = 0;
   for (int x = periods; x >= 1; x--) {
      firstS1b += MathPow(((prevdayhig[x] + prevdaylow[x])/2) - mediana1, 2);
      }
   double firstS1 = MathSqrt(firstS1b/periods);
   
   double finspax1 = spax1 / periods;
   
   //100
	for (int x = periods/2; x >= 1; x--) {
	   avg100[x] = (prevdayhig[x] - prevdaylow[x]) / 2;
	   }
	double xinput2 = 0.0;
	for (int x = periods/2; x >= 1; x--) {
	   xinput2 += avg100[x];
	   }
	double xavg2 = xinput2 / (periods/2);
	double sfix2 = 0.0;
	double spax2 = 0.0;
	
	double skix2[]; ArrayInitialize(skix2, 0.0); ArrayResize(skix2, periods + 1);
	
	for (int x = periods/2; x >= 1; x--) {
	   if (prevdayclo[x] > prevdayope[x]) sfix2 += xavg2; else sfix2 -= xavg2;
	   }
   
   for (int x = periods/2; x >= 1; x--) {
      if (prevdayclo[x] > prevdayope[x]) { spax2 += prevdayope[x] + xavg2; } else spax2 += prevdayope[x] - xavg2;
      if (prevdayclo[x] > prevdayope[x]) { skix2[x] = prevdayope[x] + xavg2; } else skix2[x] = prevdayope[x] - xavg2;
      }
   
   double mediana2 = 0;
   for (int x = periods/2; x >= 1; x--) {
      mediana2 += skix2[x] / (double)(periods/2);
      }
   double firstS2b = 0;
   for (int x = periods/2; x >= 1; x--) {
      firstS2b += MathPow(((prevdayhig[x] + prevdaylow[x])/2) - mediana2, 2);
      }
   double firstS2 = MathSqrt(firstS2b/(periods/2));
   
	double finspax2 = spax2 / (periods/2);
	
	//50
	for (int x = periods/4; x >= 1; x--) {
	   avg50[x] = (prevdayhig[x] - prevdaylow[x]) / 2;
	   }
	double xinput3 = 0.0;
	for (int x = periods/4; x >= 1; x--) {
	   xinput3 += avg50[x];
	   }
	double xavg3 = xinput3 / (periods/4);
	double sfix3 = 0.0;
	double spax3 = 0.0;
	
	double skix3[]; ArrayInitialize(skix3, 0.0); ArrayResize(skix3, periods + 1);
	
	for (int x = periods/4; x >= 1; x--) {
	   if (prevdayclo[x] > prevdayope[x]) sfix3 += xavg3; else sfix3 -= xavg3;
	   }

   for (int x = periods/4; x >= 1; x--) {
      if (prevdayclo[x] > prevdayope[x]) { spax3 += prevdayope[x] + xavg3; } else spax3 += prevdayope[x] - xavg3;
      if (prevdayclo[x] > prevdayope[x]) { skix3[x] = prevdayope[x] + xavg3; } else skix3[x] = prevdayope[x] - xavg3;
      }
   
   double mediana3 = 0;
   for (int x = periods/4; x >= 1; x--) {
      mediana3 += skix3[x] / (double)(periods/4);
      }
   double firstS3b = 0;
   for (int x = periods/4; x >= 1; x--) {
      firstS3b += MathPow(((prevdayhig[x] + prevdaylow[x])/2) - mediana3, 2);
      }
   double firstS3 = MathSqrt(firstS3b/(periods/4));
   
	double finspax3 = spax3 / (periods/4);
	
	//20
	for (int x = periods/10; x >= 1; x--) {
	   avg20[x] = (prevdayhig[x] - prevdaylow[x]) / 2;
	   }
	double xinput4 = 0.0;
	for (int x = periods/10; x >= 1; x--) {
	   xinput4 += avg20[x];
	   }
	double xavg4 = xinput4 / (periods/10);
	double sfix4 = 0.0;
	double spax4 = 0.0;
	
	double skix4[]; ArrayInitialize(skix4, 0.0); ArrayResize(skix4, periods + 1);
	
	for (int x = periods/10; x >= 1; x--) {
	   if (prevdayclo[x] > prevdayope[x]) sfix4 += xavg4; else sfix4 -= xavg4;
	   }   

   for (int x = periods/10; x >= 1; x--) {
      if (prevdayclo[x] > prevdayope[x]) { spax4 += prevdayope[x] + xavg4; } else spax4 += prevdayope[x] - xavg4;
      if (prevdayclo[x] > prevdayope[x]) { skix4[x] = prevdayope[x] + xavg4; } else skix4[x] = prevdayope[x] - xavg4;
      }
   
   double mediana4 = 0;
   for (int x = periods/10; x >= 1; x--) {
      mediana4 += skix4[x] / (double)(periods/10);
      }
   double firstS4b = 0;
   for (int x = periods/10; x >= 1; x--) {
      firstS4b += MathPow(((prevdayhig[x] + prevdaylow[x])/2) - mediana4, 2);
      }
   double firstS4 = MathSqrt(firstS4b/(periods/10));
   
	double finspax4 = spax4 / (periods/10);
		
	string obname;
	//200
	obname = Name + " finx1u"; objtrend2(obname, finspax1, finspax1 + firstS1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods), false), 0, 0, 2, STYLE_SOLID, clrWhite, "Finx1");
	if (SymbolInfoDouble(_Symbol, SYMBOL_BID) > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " finx1d"; objtrend2(obname, finspax1, finspax1 - firstS1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods), false), 0, 0, 2, STYLE_SOLID, clrRed, "Finx1");
	if (SymbolInfoDouble(_Symbol, SYMBOL_BID) > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	//100
	obname = Name + " finx2u"; objtrend2(obname, finspax2, finspax2 + firstS2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/2), false), 0, 0, 2, STYLE_SOLID, clrWhite, "Finx1");
	if (finspax2 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " finx2d"; objtrend2(obname, finspax2, finspax2 - firstS2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/2), false), 0, 0, 2, STYLE_SOLID, clrRed, "Finx1");
	if (finspax2 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	//50
	obname = Name + " finx3u"; objtrend2(obname, finspax3, finspax3 + firstS3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/4), false), 0, 0, 2, STYLE_SOLID, clrWhite, "Finx1");
	if (finspax3 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " finx3d"; objtrend2(obname, finspax3, finspax3 - firstS3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/4), false), 0, 0, 2, STYLE_SOLID, clrRed, "Finx1");
	if (finspax3 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	//20
	obname = Name + " finx4u"; objtrend2(obname, finspax4, finspax4 + firstS4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/10), false), 0, 0, 2, STYLE_SOLID, clrWhite, "Finx1");
	if (finspax4 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " finx4d"; objtrend2(obname, finspax4, finspax4 - firstS4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/10), false), 0, 0, 2, STYLE_SOLID, clrRed, "Finx1");
	if (finspax4 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	
	//200-100 combo
	obname = Name + " fpax200100mu"; objtrend2(obname, finspax1, finspax2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/2), false), 0, 2, STYLE_SOLID, clrMagenta, "Finx1"); ObjectSetInteger(0, obname, OBJPROP_RAY_RIGHT, true);
	if (finspax2 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	obname = Name + " fpax10050mu"; objtrend2(obname, finspax2, finspax3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/4), false), 0, 2, STYLE_SOLID, clrMagenta, "Finx1"); ObjectSetInteger(0, obname, OBJPROP_RAY_RIGHT, true);
	if (finspax3 > finspax2) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	obname = Name + " fpax5020mu"; objtrend2(obname, finspax3, finspax4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/10), false), 0, 2, STYLE_SOLID, clrMagenta, "Finx1"); ObjectSetInteger(0, obname, OBJPROP_RAY_RIGHT, true);
	if (finspax4 > finspax3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	
	//obname = Name + " fpax200mu"; objtrend2(obname, finspax1, finspax1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods), false), 0, 0, 2, STYLE_SOLID, clrMagenta, "Finx1");
	//obname = Name + " fpax100mu"; objtrend2(obname, finspax2, finspax2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/2), false), 0, 0, 2, STYLE_SOLID, clrMagenta, "Finx2");
	//obname = Name + " fpax50mu"; objtrend2(obname, finspax3, finspax3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/4), false), 0, 0, 2, STYLE_SOLID, clrMagenta, "Finx3");
	//obname = Name + " fpax20mu"; objtrend2(obname, finspax4, finspax4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/10), false), 0, 0, 2, STYLE_SOLID, clrMagenta, "Finx4");
	obname = Name + " finxmid"; objtrend2(obname, prevdaylow[0] + finspax1, prevdaylow[0] + finspax1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods), false), 0, 0, 3, STYLE_SOLID, clrSienna, "Finx1");
}	
//+------------------------------------------------------------------+

//+SUP/RES FUNCTION--------------------------------------------------+
void AVG20c() {
	if (iBars(_Symbol, PERIOD_D1) < periods || periods == 0) periods = iBars(_Symbol, PERIOD_D1) - 1;
	
   double prevdayhig[]; ArrayInitialize(prevdayhig, 0.0); ArrayResize(prevdayhig, periods + 1); ArraySetAsSeries(prevdayhig, true);
   double prevdaylow[]; ArrayInitialize(prevdaylow, 0.0); ArrayResize(prevdaylow, periods + 1); ArraySetAsSeries(prevdaylow, true);
   double prevdayope[]; ArrayInitialize(prevdayope, 0.0); ArrayResize(prevdayope, periods + 1); ArraySetAsSeries(prevdayope, true);
   double prevdayclo[]; ArrayInitialize(prevdayclo, 0.0); ArrayResize(prevdayclo, periods + 1); ArraySetAsSeries(prevdayclo, true);
   
	if (CopyHigh(_Symbol, PERIOD_D1, 0, periods + 1, prevdayhig) < 0) { Print("Fail to get previous days highs: " + IntegerToString(GetLastError())); return; }
	if (CopyLow(_Symbol, PERIOD_D1, 0, periods + 1, prevdaylow) < 0) { Print("Fail to get previous days lows: " + IntegerToString(GetLastError())); return; }
	if (CopyClose(_Symbol, PERIOD_D1, 0, periods + 1, prevdayclo) < 0) { Print("Fail to get previous days closes: " + IntegerToString(GetLastError())); return; }
	if (CopyOpen(_Symbol, PERIOD_D1, 0, periods + 1, prevdayope) < 0) { Print("Fail to get previous days opens: " + IntegerToString(GetLastError())); return; }
	
	double firstav1 = (prevdayhig[periods] - prevdaylow[periods]) / 2;
	
	double firstav1001 = (prevdayhig[periods/2] - prevdaylow[periods/2]) / 2;
	double firstav501 = (prevdayhig[periods/4] - prevdaylow[periods/4]) / 2;
	double firstav201 = (prevdayhig[periods/10] - prevdaylow[periods/10]) / 2;
	
	//200
	double avg[]; ArrayInitialize(avg, 0.0); ArrayResize(avg, periods + 1); //ArraySetAsSeries(avg, true);
	//100
	double avg100[]; ArrayInitialize(avg100, 0.0); ArrayResize(avg100, periods + 1); //ArraySetAsSeries(avg, true);
	//50
	double avg50[]; ArrayInitialize(avg50, 0.0); ArrayResize(avg50, periods + 1); //ArraySetAsSeries(avg, true);
	//20
	double avg20[]; ArrayInitialize(avg20, 0.0); ArrayResize(avg20, periods + 1); //ArraySetAsSeries(avg, true);
		
	for (int x = periods; x >= 1; x--) {
	   avg[x] = (prevdayhig[x] - prevdaylow[x]) / 2;
	   }
	double xinput1 = 0.0;
	for (int x = periods; x >= 1; x--) {
	   xinput1 += avg[x];
	   }
	double xavg1 = xinput1 / (periods);
	double sfix1 = 0.0;
	double spax1 = 0.0;
	
	double skix1[]; ArrayInitialize(skix1, 0.0); ArrayResize(skix1, periods + 1);
	
	for (int x = periods; x >= 1; x--) {
	   if (prevdayclo[x] > prevdayope[x]) sfix1 += xavg1; else sfix1 -= xavg1;
	   }
   
   for (int x = periods; x >= 1; x--) {
      if (prevdayclo[x] > prevdayope[x]) { spax1 += prevdayope[x] + xavg1; } else spax1 += prevdayope[x] - xavg1;
      if (prevdayclo[x] > prevdayope[x]) { skix1[x] = prevdayope[x] + xavg1; } else skix1[x] = prevdayope[x] - xavg1;
      }
   
   double mediana1 = 0;
   for (int x = periods; x >= 1; x--) {
      mediana1 += skix1[x] / (double)periods;
      }
   double firstS1b = 0;
   for (int x = periods; x >= 1; x--) {
      firstS1b += MathPow(prevdayope[x] - mediana1, 2);
      }
   double firstS1 = MathSqrt(firstS1b/periods);
   
   double finspax1 = spax1 / periods;
   
   //100
	for (int x = periods/2; x >= 1; x--) {
	   avg100[x] = (prevdayhig[x] - prevdaylow[x]) / 2;
	   }
	double xinput2 = 0.0;
	for (int x = periods/2; x >= 1; x--) {
	   xinput2 += avg100[x];
	   }
	double xavg2 = xinput2 / (periods/2);
	double sfix2 = 0.0;
	double spax2 = 0.0;
	
	double skix2[]; ArrayInitialize(skix2, 0.0); ArrayResize(skix2, periods + 1);
	
	for (int x = periods/2; x >= 1; x--) {
	   if (prevdayclo[x] > prevdayope[x]) sfix2 += xavg2; else sfix2 -= xavg2;
	   }
   
   for (int x = periods/2; x >= 1; x--) {
      if (prevdayclo[x] > prevdayope[x]) { spax2 += prevdayope[x] + xavg2; } else spax2 += prevdayope[x] - xavg2;
      if (prevdayclo[x] > prevdayope[x]) { skix2[x] = prevdayope[x] + xavg2; } else skix2[x] = prevdayope[x] - xavg2;
      }
   
   double mediana2 = 0;
   for (int x = periods/2; x >= 1; x--) {
      mediana2 += skix2[x] / (double)(periods/2);
      }
   double firstS2b = 0;
   for (int x = periods/2; x >= 1; x--) {
      firstS2b += MathPow(prevdayope[x] - mediana2, 2);
      }
   double firstS2 = MathSqrt(firstS2b/(periods/2));
   
	double finspax2 = spax2 / (periods/2);
	
	//50
	for (int x = periods/4; x >= 1; x--) {
	   avg50[x] = (prevdayhig[x] - prevdaylow[x]) / 2;
	   }
	double xinput3 = 0.0;
	for (int x = periods/4; x >= 1; x--) {
	   xinput3 += avg50[x];
	   }
	double xavg3 = xinput3 / (periods/4);
	double sfix3 = 0.0;
	double spax3 = 0.0;
	
	double skix3[]; ArrayInitialize(skix3, 0.0); ArrayResize(skix3, periods + 1);
	
	for (int x = periods/4; x >= 1; x--) {
	   if (prevdayclo[x] > prevdayope[x]) sfix3 += xavg3; else sfix3 -= xavg3;
	   }

   for (int x = periods/4; x >= 1; x--) {
      if (prevdayclo[x] > prevdayope[x]) { spax3 += prevdayope[x] + xavg3; } else spax3 += prevdayope[x] - xavg3;
      if (prevdayclo[x] > prevdayope[x]) { skix3[x] = prevdayope[x] + xavg3; } else skix3[x] = prevdayope[x] - xavg3;
      }
   
   double mediana3 = 0;
   for (int x = periods/4; x >= 1; x--) {
      mediana3 += skix3[x] / (double)(periods/4);
      }
   double firstS3b = 0;
   for (int x = periods/4; x >= 1; x--) {
      firstS3b += MathPow(prevdayope[x] - mediana3, 2);
      }
   double firstS3 = MathSqrt(firstS3b/(periods/4));
   
	double finspax3 = spax3 / (periods/4);
	
	//20
	for (int x = periods/10; x >= 1; x--) {
	   avg20[x] = (prevdayhig[x] - prevdaylow[x]) / 2;
	   }
	double xinput4 = 0.0;
	for (int x = periods/10; x >= 1; x--) {
	   xinput4 += avg20[x];
	   }
	double xavg4 = xinput4 / (periods/10);
	double sfix4 = 0.0;
	double spax4 = 0.0;
	
	double skix4[]; ArrayInitialize(skix4, 0.0); ArrayResize(skix4, periods + 1);
	
	for (int x = periods/10; x >= 1; x--) {
	   if (prevdayclo[x] > prevdayope[x]) sfix4 += xavg4; else sfix4 -= xavg4;
	   }   

   for (int x = periods/10; x >= 1; x--) {
      if (prevdayclo[x] > prevdayope[x]) { spax4 += prevdayope[x] + xavg4; } else spax4 += prevdayope[x] - xavg4;
      if (prevdayclo[x] > prevdayope[x]) { skix4[x] = prevdayope[x] + xavg4; } else skix4[x] = prevdayope[x] - xavg4;
      }
   
   double mediana4 = 0;
   for (int x = periods/10; x >= 1; x--) {
      mediana4 += skix4[x] / (double)(periods/10);
      }
   double firstS4b = 0;
   for (int x = periods/10; x >= 1; x--) {
      firstS4b += MathPow(prevdayope[x] - mediana4, 2);
      }
   double firstS4 = MathSqrt(firstS4b/(periods/10));
   
	double finspax4 = spax4 / (periods/10);
		
	string obname;
	//200
	obname = Name + " finx1u"; objtrend2(obname, finspax1, finspax1 + firstS1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, periods), false), 0, 0, 2, STYLE_SOLID, clrWhite, "Finx1");
	if (SymbolInfoDouble(_Symbol, SYMBOL_BID) > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " finx1d"; objtrend2(obname, finspax1, finspax1 - firstS1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, periods), false), 0, 0, 2, STYLE_SOLID, clrRed, "Finx1");
	if (SymbolInfoDouble(_Symbol, SYMBOL_BID) > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	//100
	obname = Name + " finx2u"; objtrend2(obname, finspax2, finspax2 + firstS2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, periods/2), false), 0, 0, 2, STYLE_SOLID, clrWhite, "Finx1");
	if (finspax2 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " finx2d"; objtrend2(obname, finspax2, finspax2 - firstS2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, periods/2), false), 0, 0, 2, STYLE_SOLID, clrRed, "Finx1");
	if (finspax2 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	//50
	obname = Name + " finx3u"; objtrend2(obname, finspax3, finspax3 + firstS3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, periods/4), false), 0, 0, 2, STYLE_SOLID, clrWhite, "Finx1");
	if (finspax3 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " finx3d"; objtrend2(obname, finspax3, finspax3 - firstS3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, periods/4), false), 0, 0, 2, STYLE_SOLID, clrRed, "Finx1");
	if (finspax3 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	//20
	obname = Name + " finx4u"; objtrend2(obname, finspax4, finspax4 + firstS4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, periods/10), false), 0, 0, 2, STYLE_SOLID, clrWhite, "Finx1");
	if (finspax4 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " finx4d"; objtrend2(obname, finspax4, finspax4 - firstS4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, periods/10), false), 0, 0, 2, STYLE_SOLID, clrRed, "Finx1");
	if (finspax4 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	
	//200-100 combo
	obname = Name + " fpax200100mu"; objtrend2(obname, finspax1, finspax2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, periods), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/2), false), 0, 2, STYLE_SOLID, clrMagenta, "Finx1"); ObjectSetInteger(0, obname, OBJPROP_RAY_RIGHT, true);
	if (finspax2 > finspax1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	obname = Name + " fpax10050mu"; objtrend2(obname, finspax2, finspax3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, periods/2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/4), false), 0, 2, STYLE_SOLID, clrMagenta, "Finx1"); ObjectSetInteger(0, obname, OBJPROP_RAY_RIGHT, true);
	if (finspax3 > finspax2) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	obname = Name + " fpax5020mu"; objtrend2(obname, finspax3, finspax4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, periods/4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/10), false), 0, 2, STYLE_SOLID, clrMagenta, "Finx1"); ObjectSetInteger(0, obname, OBJPROP_RAY_RIGHT, true);
	if (finspax4 > finspax3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	
	//obname = Name + " fpax200mu"; objtrend2(obname, finspax1, finspax1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods), false), 0, 0, 2, STYLE_SOLID, clrMagenta, "Finx1");
	//obname = Name + " fpax100mu"; objtrend2(obname, finspax2, finspax2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/2), false), 0, 0, 2, STYLE_SOLID, clrMagenta, "Finx2");
	//obname = Name + " fpax50mu"; objtrend2(obname, finspax3, finspax3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/4), false), 0, 0, 2, STYLE_SOLID, clrMagenta, "Finx3");
	//obname = Name + " fpax20mu"; objtrend2(obname, finspax4, finspax4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, periods/10), false), 0, 0, 2, STYLE_SOLID, clrMagenta, "Finx4");
	obname = Name + " finxmid"; objtrend2(obname, prevdaylow[0] + firstS1, prevdaylow[0] + firstS1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, periods), false), 0, 0, 3, STYLE_SOLID, clrSienna, "Finx1");
}	
//+------------------------------------------------------------------+

//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett) {
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Time[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
	ObjectSetInteger(0, name, OBJPROP_STYLE, st);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
	ObjectSetInteger(0, name, OBJPROP_RAY, false);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToString(pr1, _Digits) + " Date: " + TimeToString(Time[t1], TIME_DATE));
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
	const int corner,
	const int x,
	const int y,
	const string label,
	const int FSize,
	const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetString(0, name, OBJPROP_TEXT, label);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+OBJECTSETTEXT MQL4------------------------------------------------+
bool ObjectSetTextMQL4(string name,
	string text,
	int font_size,
	string font = "",
	color text_color = CLR_NONE)
{
	int tmpObjType = (int)ObjectGetInteger(0, name, OBJPROP_TYPE);
	if (tmpObjType != OBJ_LABEL && tmpObjType != OBJ_TEXT) return(false);
	if (StringLen(text) > 0 && font_size > 0)
	{
		if (ObjectSetString(0, name, OBJPROP_TEXT, text) == true
			&& ObjectSetInteger(0, name, OBJPROP_FONTSIZE, font_size) == true)
		{
			if ((StringLen(font) > 0)
				&& ObjectSetString(0, name, OBJPROP_FONT, font) == false)
				return(false);
			if (text_color > 0
				&& ObjectSetInteger(0, name, OBJPROP_COLOR, text_color) == false)
				return(false);
			return(true);
		}
		return(false);
	}
	return(false);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function 
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed"; break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed"; break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed"; break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed"; break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled"; break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart"; break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart"; break;
	default:text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+