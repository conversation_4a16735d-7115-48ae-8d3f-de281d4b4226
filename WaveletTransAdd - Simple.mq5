#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict

#property indicator_buffers 8
#property indicator_plots 2

#property indicator_type1   DRAW_LINE
#property indicator_color1  clrPeru
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrPeru
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2

#define Name MQLInfoString(MQL_PROGRAM_NAME) + "U"

int periods = 3000; //Bars to calculate for (0 = all)
int pr = 10; //Smoothing period
int Sampling_Period = 4 * pr; // Sampling period for 3.0 (must be at least 4* smoothing)

double dippe[], kippe[];
double ma3[], ma4[];
double ma36[], ma36l[], ma36h[], atrix[];

double Pip;

int ma36data, ma36hdata, ma36ldata, atrdays;

//+INIT FUNCTION-----------------------------------------------------+
int OnInit()
{
	IndicatorSetString(INDICATOR_SHORTNAME,"WaveletsOC-S");
	IndicatorSetInteger(INDICATOR_DIGITS,_Digits);

	SetIndexBuffer(0, ma3, INDICATOR_DATA);
	SetIndexBuffer(1, ma4, INDICATOR_DATA);
	SetIndexBuffer(2, dippe, INDICATOR_CALCULATIONS);
	SetIndexBuffer(3, kippe, INDICATOR_CALCULATIONS);
	SetIndexBuffer(4, ma36, INDICATOR_CALCULATIONS);
	SetIndexBuffer(5, ma36h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(6, ma36l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(7, atrix, INDICATOR_CALCULATIONS);
	
	Pip = (_Point * MathPow(10, MathMod(_Digits, 2)));

	ma36data = iMA(_Symbol, PERIOD_CURRENT, pr, 0, MODE_EMA, PRICE_TYPICAL);
	ma36hdata = iMA(_Symbol, PERIOD_CURRENT, pr, 0, MODE_EMA, PRICE_HIGH);
	ma36ldata = iMA(_Symbol, PERIOD_CURRENT, pr, 0, MODE_EMA, PRICE_LOW);
	atrdays = iATR(_Symbol, PERIOD_CURRENT, pr);

	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+CALCULATE---------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	datetime expiry = D'2019.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("WaveletTransformAddition expired on " + TimeToString(expiry, TIME_DATE) + ", contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true) {		
		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, ChartPeriod(), 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, ChartPeriod(), 0);
		}
		if (new_1m_check)
		{
			bulak();
			new_1m_check = false;
		}
	}
	return(rates_total);
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION-----------------------------------------------------+
void bulak() {
   int bars = Bars(_Symbol, PERIOD_CURRENT);
	if (bars < periods + pr || periods == 0) periods = bars - 1 - pr;

 	ArraySetAsSeries(ma36, true); ArraySetAsSeries(ma36h, true); ArraySetAsSeries(ma36l, true); ArraySetAsSeries(atrix, true);
	if (CopyBuffer(ma36data, 0, 0, periods, ma36) <= 0) Print("Getting EMA10 failed! Error",GetLastError());
	if (CopyBuffer(ma36hdata, 0, 0, periods, ma36h) <= 0) Print("Getting EMA10H failed! Error",GetLastError());
	if (CopyBuffer(ma36ldata, 0, 0, periods, ma36l) <= 0) Print("Getting EMA10L failed! Error",GetLastError());
	if (CopyBuffer(atrdays, 0, 0, periods, atrix) <= 0) Print("Getting ATR failed! Error",GetLastError());
	   
	{ // MAIN CALCS	   
		double volful[];
		double volp[], voln[];
		double cvolp[], cvoln[];
		ArrayResize(volful, periods + 1 + pr);
		ArrayInitialize(volful, 0);
		ArrayResize(volp, periods + 1 + pr);
		ArrayInitialize(volp, 0);
		ArrayResize(voln, periods + 1 + pr);
		ArrayInitialize(voln, 0);
		ArrayResize(cvolp, periods + 1 + pr);
		ArrayInitialize(cvolp, 0);
		ArrayResize(cvoln, periods + 1 + pr);
		ArrayInitialize(cvoln, 0);
				
		for (int x = periods; x >= 0; x--) {
			for (int i = pr + x; i > x; i--) {
				volful[x] += (double)iVolume(_Symbol, PERIOD_CURRENT, i);
				if (iClose(_Symbol, PERIOD_CURRENT, i) > iOpen(_Symbol, PERIOD_CURRENT, i)) volp[x] += (double)iVolume(_Symbol, PERIOD_CURRENT, i);
				if (iOpen(_Symbol, PERIOD_CURRENT, i) > iClose(_Symbol, PERIOD_CURRENT, i)) voln[x] += (double)iVolume(_Symbol, PERIOD_CURRENT, i);
			}
			cvolp[x] = volp[x] / volful[x];
			cvoln[x] = voln[x] / volful[x];
		}

		for (int x = periods; x >= 0; x--) {
			dippe[x] = iHigh(_Symbol, PERIOD_CURRENT, x + 1) + atrix[x + 1] + cvolp[x] * atrix[x + 1];
			kippe[x] = iLow(_Symbol, PERIOD_CURRENT, x + 1) - atrix[x + 1] - cvoln[x] * atrix[x + 1];
		}
	}
	
	{ // YELLOW LINES
		double MA1[]; ArrayResize(MA1, periods + 1);
		ArrayInitialize(MA1, 0);
		double MA2[]; ArrayResize(MA2, periods + 1);
		ArrayInitialize(MA2, 0);
		double MA3[]; ArrayResize(MA3, periods + 1);
		ArrayInitialize(MA3, 0);
		double MA4[]; ArrayResize(MA4, periods + 1);
		ArrayInitialize(MA4, 0);

		ArraySetAsSeries(MA1, true);
		ArraySetAsSeries(MA3, true);
		ArraySetAsSeries(MA2, true);
		ArraySetAsSeries(MA4, true);

		for (int i = periods; i >= 0; i--) {
			MA1[i] = iMAOnArrayMQL4(dippe, 0, Sampling_Period, 0, MODE_EMA, i);
			MA3[i] = iMAOnArrayMQL4(kippe, 0, Sampling_Period, 0, MODE_EMA, i);
		}

		for (int i = periods; i >= 0; i--) {
			MA2[i] = iMAOnArrayMQL4(MA1, 0, pr, 0, MODE_EMA, i);
			MA4[i] = iMAOnArrayMQL4(MA3, 0, pr, 0, MODE_EMA, i);
		}

		double Lambda = 1.0 * Sampling_Period / (1.0 * pr);
		double Alpha = Lambda * (Sampling_Period - 1) / (Sampling_Period - Lambda);
		
		ArraySetAsSeries(ma3, true); ArraySetAsSeries(ma4, true);
		for (int i = periods; i >= 0; i--) {
			ma3[i] = (Alpha + 1) * MA1[i] - Alpha * MA2[i];
			ma4[i] = (Alpha + 1) * MA3[i] - Alpha * MA4[i];
		}
	}

	{ // SHOW k - d PRICE
		string obname;
		obname = Name + "d"; ArrowPrice(obname, ma3[0], 0 * Period() * 60, clrPeru); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_BACK, false); ObjectSetString(0, obname, OBJPROP_TOOLTIP, "Upper Wavelet Price: " + DoubleToString(ma3[0], _Digits));
		obname = Name + "k"; ArrowPrice(obname, ma4[0], 0 * Period() * 60, clrPeru); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_BACK, false); ObjectSetString(0, obname, OBJPROP_TOOLTIP, "Lower Wavelet Price: " + DoubleToString(ma4[0], _Digits));
		//obname = Name + "r"; ArrowPrice(obname, rippe[0], 0 * Period() * 60, clrIndigo); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_BACK, false);
	}
	//end of midma3
}
//+------------------------------------------------------------------+

//+ARROWPRICE--------------------------------------------------------+
void ArrowPrice(const string name, const double x, const datetime y, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW_RIGHT_PRICE, 0, 0, 0))
		{
			Print("error: can't create arrow_right_price! code #", GetLastError());
		}
   datetime Time[];
   int count = Bars(_Symbol, PERIOD_CURRENT);   // number of elements to copy
   ArraySetAsSeries(Time,true);
   CopyTime(_Symbol,_Period,0,count,Time);
   
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE, x);
	ObjectSetInteger(0, name, OBJPROP_TIME, Time[0] + y);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
}
//+------------------------------------------------------------------+

double iMAOnArrayMQL4(double &array[],
                      int total,
                      int period,
                      int ma_shift,
                      int ma_method,
                      int shift)
  {
   double buf[],arr[];
   if(total==0) total=ArraySize(array);
   if(total>0 && total<=period) return(0);
   if(shift>total-period-ma_shift) return(0);
   switch(ma_method)
     {
      case MODE_SMA :
        {
         total=ArrayCopy(arr,array,0,shift+ma_shift,period);
         if(ArrayResize(buf,total)<0) return(0);
         double sum=0;
         int    i,pos=total-1;
         for(i=1;i<period;i++,pos--)
            sum+=arr[pos];
         while(pos>=0)
           {
            sum+=arr[pos];
            buf[pos]=sum/period;
            sum-=arr[pos+period-1];
            pos--;
           }
         return(buf[0]);
        }
      case MODE_EMA :
        {
         if(ArrayResize(buf,total)<0) return(0);
         double per=2.0/(period+1);
         int    pos=total-2;
         while(pos>=0)
           {
            if(pos==total-2) buf[pos+1]=array[pos+1];
            buf[pos]=array[pos]*per+buf[pos+1]*(1-per);
            pos--;
           }
         return(buf[shift+ma_shift]);
        }
      case MODE_SMMA :
        {
         if(ArrayResize(buf,total)<0) return(0);
         double sum=0;
         int    i,k,pos;
         pos=total-period;
         while(pos>=0)
           {
            if(pos==total-period)
              {
               for(i=0,k=pos;i<period;i++,k++)
                 {
                  sum+=array[k];
                  buf[k]=0;
                 }
              }
            else sum=buf[pos+1]*(period-1)+array[pos];
            buf[pos]=sum/period;
            pos--;
           }
         return(buf[shift+ma_shift]);
        }
      case MODE_LWMA :
        {
         if(ArrayResize(buf,total)<0) return(0);
         double sum=0.0,lsum=0.0;
         double price;
         int    i,weight=0,pos=total-1;
         for(i=1;i<=period;i++,pos--)
           {
            price=array[pos];
            sum+=price*i;
            lsum+=price;
            weight+=i;
           }
         pos++;
         i=pos+period;
         while(pos>=0)
           {
            buf[pos]=sum/weight;
            if(pos==0) break;
            pos--;
            i--;
            price=array[pos];
            sum=sum-lsum+price*period;
            lsum-=array[i];
            lsum+=price;
           }
         return(buf[shift+ma_shift]);
        }
      default: return(0);
     }
   return(0);
  }