//+------------------------------------------------------------------+
//|                                                         xmas.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
//--- input parameters

#define Name MQLInfoString(MQL_PROGRAM_NAME)
#property indicator_buffers 6
#property indicator_plots 6

input int shift = 0;          //Shift all back X bars
input ENUM_TIMEFRAMES perioda = PERIOD_CURRENT;

//for sds
double mixa[];                //outer rim up
double mixb[];                //outer rim dn

//for cs
input string suffix = "";     //Broker pair suffix i.e. .r .raw etc

//dannys
int timesb = 72;              //LR channel Period (long)
int timesc = 24;              //LR channel period (short)
int timesa = 120;             //LR channel Period (longer)

//LR variables
double trophya[];             //LR long high channel
double trophyb[];             //LR long low channel
//double trigga[];              //LR short high channel
//double triggb[];              //LR short low channel
double trophyc[];             //LR longer high channel
double trophyd[];             //LR longer low channel
/*
double movoulaki[];
double prasinaki[];
*/

//Indicator settings
//mixa
#property indicator_type1 DRAW_NONE
#property indicator_style1 STYLE_SOLID
#property indicator_color1 clrBlack
#property indicator_width1 1

//mixb
#property indicator_type2 DRAW_NONE
#property indicator_style2 STYLE_SOLID
#property indicator_color2 clrBlack
#property indicator_width2 1

//trophya
#property indicator_type3 DRAW_LINE
#property indicator_style3 STYLE_SOLID
#property indicator_color3 clrGoldenrod
#property indicator_width3 3

//triggb
#property indicator_type4 DRAW_NONE
#property indicator_style4 STYLE_SOLID
#property indicator_color4 clrRed
#property indicator_width4 1

//trophyc
#property indicator_type5 DRAW_LINE
#property indicator_style5 STYLE_SOLID
#property indicator_color5 clrGoldenrod
#property indicator_width5 2

//trophyd
#property indicator_type6 DRAW_LINE
#property indicator_style6 STYLE_SOLID
#property indicator_color6 clrYellow
#property indicator_width6 2

/*
//movoulaki
#property indicator_type7 DRAW_NONE

//prasinaki
#property indicator_type8 DRAW_NONE
*/
int atr21;
double iatr21[];
/*
int rsi14;
double rsi[];
*/
//#include <MovingAverages.mqh>

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping

   DeleteObjects();

   SetIndexBuffer(0, mixa, INDICATOR_DATA); // outer std band
   SetIndexBuffer(1, mixb, INDICATOR_DATA); // outer std band
   SetIndexBuffer(2, trophya, INDICATOR_DATA);
   SetIndexBuffer(3, trophyb, INDICATOR_DATA);
   //SetIndexBuffer(4, trigga, INDICATOR_DATA);
   //SetIndexBuffer(5, triggb, INDICATOR_DATA);
   SetIndexBuffer(4, trophyc, INDICATOR_DATA);
   SetIndexBuffer(5, trophyd, INDICATOR_DATA);
   //SetIndexBuffer(8, movoulaki, INDICATOR_DATA);
   //SetIndexBuffer(9, prasinaki, INDICATOR_DATA);
	
	atr21 = iATR(_Symbol, PERIOD_CURRENT, 21);
	//rsi14 = iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
   
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, PERIOD_CURRENT, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, PERIOD_CURRENT, 0);
		}
		if (new_1m_check)
		{
         tsap();
			new_1m_check = false;
		}
		/*
		bool new_ctf_check = false;
		static datetime start_ctf_time = 0;
		if (start_ctf_time < iTime(_Symbol, PERIOD_M30, 0))
		{
			new_ctf_check = true;
			start_ctf_time = iTime(_Symbol, PERIOD_M30, 0);
		}
		if (new_ctf_check)
		{
			ObjectsDeleteAll(0, Name + " y");
			//if (SymbolInfoDouble(_Symbol, SYMBOL_BID) > trophyc[0] && SymbolInfoDouble(_Symbol, SYMBOL_BID) > trophya[0]) { Alert(_Symbol + " too high " + EnumToString(ChartPeriod())); SendNotification(_Symbol + " too high " + EnumToString(ChartPeriod())); }
			//if (SymbolInfoDouble(_Symbol, SYMBOL_BID) < trophyd[0] && SymbolInfoDouble(_Symbol, SYMBOL_BID) < trophyb[0]) { Alert(_Symbol + " too low " + EnumToString(ChartPeriod())); SendNotification(_Symbol + " too low " + EnumToString(ChartPeriod())); }
			//if (SymbolInfoDouble(_Symbol, SYMBOL_BID) > trigga[0]) { Alert(_Symbol + " momentum up " + EnumToString(ChartPeriod())); SendNotification(_Symbol + " momentum up " + EnumToString(ChartPeriod())); }
			//if (SymbolInfoDouble(_Symbol, SYMBOL_BID) < triggb[0]) { Alert(_Symbol + " momentum down " + EnumToString(ChartPeriod())); SendNotification(_Symbol + " momentum down " + EnumToString(ChartPeriod())); }
			new_ctf_check = false;
		}
		*/
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+

//MAIN PROGRAM
//+MAIN FUNCTION-----------------------------------------------------+
void tsap(){
   
   /*
   double high[];
   ArrayResize(high, iBars(_Symbol, perioda));
   CopyHigh(_Symbol, perioda, 0, iBars(_Symbol, perioda), high);
   ArraySetAsSeries(high, true);
   
   double low[];
   ArrayResize(low, iBars(_Symbol, perioda));
   CopyLow(_Symbol, perioda, 0, iBars(_Symbol, perioda), low);
   ArraySetAsSeries(low, true);
   
   double cc[];
   ArrayResize(cc, iBars(_Symbol, perioda));
   CopyClose(_Symbol, perioda, 0, iBars(_Symbol, perioda), cc);
   ArraySetAsSeries(cc, true);
   */
   
   double oo[];
   ArrayResize(oo, iBars(_Symbol, perioda));
   CopyOpen(_Symbol, perioda, 0, iBars(_Symbol, perioda), oo);
   ArraySetAsSeries(oo, true);   
         
   //ArrayResize(rsi, iBars(_Symbol, perioda));
   //ArraySetAsSeries(rsi, true);
   ArrayResize(iatr21, iBars(_Symbol, perioda));
   ArraySetAsSeries(iatr21, true);
   //CopyBuffer(rsi14, 0, 0, iBars(_Symbol, perioda), rsi);
   CopyBuffer(atr21, 0, 0, iBars(_Symbol, perioda), iatr21);
   
   ArraySetAsSeries(mixa, true);
   ArraySetAsSeries(mixb, true);
   ArraySetAsSeries(trophya, true);
   ArraySetAsSeries(trophyb, true);
   //ArraySetAsSeries(trigga, true);
   //ArraySetAsSeries(triggb, true);
   ArraySetAsSeries(trophyc, true);
   ArraySetAsSeries(trophyd, true);
   //ArraySetAsSeries(movoulaki, true);
   //ArraySetAsSeries(prasinaki, true);
   
   ArrayInitialize(mixa, EMPTY_VALUE);
   ArrayInitialize(mixb, EMPTY_VALUE);
   ArrayInitialize(trophya, EMPTY_VALUE);
   ArrayInitialize(trophyb, EMPTY_VALUE);
   //ArrayInitialize(trigga, EMPTY_VALUE);
   //ArrayInitialize(triggb, EMPTY_VALUE);
   ArrayInitialize(trophyc, EMPTY_VALUE);
   ArrayInitialize(trophyd, EMPTY_VALUE);
   //ArrayInitialize(movoulaki, EMPTY_VALUE);
   //ArrayInitialize(prasinaki, EMPTY_VALUE);
   
   //LR variables   
   
   for (int y = iBars(_Symbol, perioda) - 21; y >= 0 + shift; y--)
   {      
      //Own system sd's and bands buffer fill
      //MathSqrt(startt * 0.5 * 0.5) * startadr;
      mixa[y] = oo[y] + MathSqrt(21 * 0.5 * 0.5) * iatr21[y];
      mixb[y] = oo[y] - MathSqrt(21 * 0.5 * 0.5) * iatr21[y];
   }
   {
      //ArrayInitialize(trophya, EMPTY_VALUE);
      //ArrayInitialize(trophyb, EMPTY_VALUE);
      //ArrayInitialize(trophyc, EMPTY_VALUE);
      //ArrayInitialize(trophyd, EMPTY_VALUE);
      //ArrayInitialize(trigga, EMPTY_VALUE);
      //ArrayInitialize(triggb, EMPTY_VALUE);
   
      //LR regression lines on adr bands
      {
         double a = 0.0, b = 0.0, c = 0.0;
         double isumx = 0.0, isumy = 0.0;
         double isumxy = 0.0, isumx2 = 0.0;
         double h = 0.0, l = 0.0;
         //if (timesb < iBars(_Symbol, perioda) - 21) timesb = iBars(_Symbol, perioda) - 21;
         for (int j = 0 + shift; j < (shift + timesb); j++)
         //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
         {
            //Print(ArraySize(mixa));
            isumy += mixa[j];
            isumxy += mixa[j] * j;
            isumx += j;
            isumx2 += j * j;
         }
         
         c = isumx2 * timesb - isumx * isumx;
         a = (isumxy * timesb - isumx * isumy) / c;
         b = (isumy - isumx * a) / timesb;
         
         for (int j = 0 + shift; j < (shift + timesb); j++)
         //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
         {
            //trophymid1[j] = a * j + b + range;
            trophya[j] = a * j + b;
            //trophymid2[j] = a * j + b - range;
         }
      }
      {
         double a = 0.0, b = 0.0, c = 0.0;
         double isumx = 0.0, isumy = 0.0;
         double isumxy = 0.0, isumx2 = 0.0;
         double h = 0.0, l = 0.0;
         for (int j = 0 + shift; j < (shift + timesb); j++)
         //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
         {
            isumy += mixb[j];
            isumxy += mixb[j] * j;
            isumx += j;
            isumx2 += j * j;
         }
         
         c = isumx2 * timesb - isumx * isumx;
         a = (isumxy * timesb - isumx * isumy) / c;
         b = (isumy - isumx * a) / timesb;
         
         for (int j = 0 + shift; j < (shift + timesb); j++)
         //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
         {
            //trophymid1[j] = a * j + b + range;
            trophyb[j] = a * j + b;
            //trophymid2[j] = a * j + b - range;
         }
      }
      {
         double a = 0.0, b = 0.0, c = 0.0;
         double isumx = 0.0, isumy = 0.0;
         double isumxy = 0.0, isumx2 = 0.0;
         double h = 0.0, l = 0.0;
         
         for (int j = 0 + shift; j < (shift + timesa); j++)
         //for (int j = (shift + timesa - 1); j >= 0 + shift; j--)
         {
            isumy += mixa[j];
            isumxy += mixa[j] * j;
            isumx += j;
            isumx2 += j * j;
         }
         
         c = isumx2 * timesa - isumx * isumx;
         a = (isumxy * timesa - isumx * isumy) / c;
         b = (isumy - isumx * a) / timesa;
         
         for (int j = 0 + shift; j < (shift + timesa); j++)
         //for (int j = (shift + timesa - 1); j >= 0 + shift; j--)
         {
            //trophymid1[j] = a * j + b + range;
            trophyc[j] = a * j + b;
            //trophymid2[j] = a * j + b - range;
         }
      }
      {
         double a = 0.0, b = 0.0, c = 0.0;
         double isumx = 0.0, isumy = 0.0;
         double isumxy = 0.0, isumx2 = 0.0;
         double h = 0.0, l = 0.0;
         
         for (int j = 0 + shift; j < (shift + timesa); j++)
         //for (int j = (shift + timesa - 1); j >= 0 + shift; j--)
         {
            isumy += mixb[j];
            isumxy += mixb[j] * j;
            isumx += j;
            isumx2 += j * j;
         }
         
         c = isumx2 * timesa - isumx * isumx;
         a = (isumxy * timesa - isumx * isumy) / c;
         b = (isumy - isumx * a) / timesa;
         
         for (int j = 0 + shift; j < (shift + timesa); j++)
         //for (int j = (shift + timesa - 1); j >= 0 + shift; j--)
         {
            //trophymid1[j] = a * j + b + range;
            trophyd[j] = a * j + b;
            //trophymid2[j] = a * j + b - range;
         }
      }/*
      {
         double a = 0.0, b = 0.0, c = 0.0;
         double isumx = 0.0, isumy = 0.0;
         double isumxy = 0.0, isumx2 = 0.0;
         double h = 0.0, l = 0.0;
         
         for (int j = 0 + shift; j < (shift + timesc); j++)
         //for (int j = (shift + timesc - 1); j >= 0 + shift; j--)
         {
            isumy += mixa[j];
            isumxy += mixa[j] * j;
            isumx += j;
            isumx2 += j * j;
         }
         
         c = isumx2 * timesc - isumx * isumx;
         a = (isumxy * timesc - isumx * isumy) / c;
         b = (isumy - isumx * a) / timesc;
         
         for (int j = 0 + shift; j < (shift + timesc); j++)
         //for (int j = (shift + timesc - 1); j >= 0 + shift; j--)
         {
            //trophymid1[j] = a * j + b + range;
            trigga[j] = a * j + b;
            //trophymid2[j] = a * j + b - range;
         }
      }
      {
         double a = 0.0, b = 0.0, c = 0.0;
         double isumx = 0.0, isumy = 0.0;
         double isumxy = 0.0, isumx2 = 0.0;
         double h = 0.0, l = 0.0;
         
         for (int j = 0 + shift; j < (shift + timesc); j++)
         //for (int j = (shift + timesc - 1); j >= 0 + shift; j--)
         {
            isumy += mixb[j];
            isumxy += mixb[j] * j;
            isumx += j;
            isumx2 += j * j;
         }
         
         c = isumx2 * timesc - isumx * isumx;
         a = (isumxy * timesc - isumx * isumy) / c;
         b = (isumy - isumx * a) / timesc;
         
         for (int j = 0 + shift; j < (shift + timesc); j++)
         //for (int j = (shift + timesc - 1); j >= 0 + shift; j--)
         {
            //trophymid1[j] = a * j + b + range;
            triggb[j] = a * j + b;
            //trophymid2[j] = a * j + b - range;
         }
      }*/
   }
   /*
   if (trophya[0] > trophya[timesb - 1] && trophyb[0] > trophyb[timesb - 1] && trophyc[0] > trophyc[timesa - 1] && trophyd[0] > trophyd[timesa - 1]) movoulaki[0] = 1; else movoulaki[0] = 0;
   if (trophya[0] < trophya[timesb - 1] && trophyb[0] < trophyb[timesb - 1] && trophyc[0] < trophyc[timesa - 1] && trophyd[0] < trophyd[timesa - 1]) prasinaki[0] = 1; else prasinaki[0] = 0;
   
   string obname;
   if (movoulaki[0] == 1)
   { obname = Name + "AR"; LabelMake(obname, 3, 40, 20, "UP", 16, clrGreen); }
   if (prasinaki[0] == 1)
   { obname = Name + "AR"; LabelMake(obname, 3, 40, 20, "DN", 16, clrMagenta); }
   if (movoulaki[0] != 1 && prasinaki[0] != 1)
   { obname = Name + "AR"; LabelMake(obname, 3, 40, 20, "SD", 16, clrWhite); }
   */
   //Print(movoulaki[0] + " " + prasinaki[0]);
}
//+------------------------------------------------------------------+
/*
//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	//ObjectSetText(name, label, FSize, "Arial", FCol);
	ObjectSetString(0, name, OBJPROP_TEXT, label);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+
*/
//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	DeleteObjects();
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	for (int i = ObjectsTotal(0) - 1; i >= 0; i--)
	{
		string ObName = ObjectName(0, i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(0, ObName);
		}
	}
}
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+
/*
double iMAOnArray(double &array[],
                      int total,
                      int period,
                      int ma_shift,
                      int shifta)
  {
   double buf[],arr[];
   if(total==0) total=ArraySize(array);
   if(total>0 && total<=period) return(0);
   if(shifta>total-period-ma_shift) return(0);
   
      if(ArrayResize(buf,total)<0) return(0);
      double sum=0.0,lsum=0.0;
      double price;
      int    i,weight=0,pos=total-1;
      for(i=1;i<=period;i++,pos--)
        {
         price=array[pos];
         sum+=price*i;
         lsum+=price;
         weight+=i;
        }
      pos++;
      i=pos+period;
      while(pos>=0)
        {
         buf[pos]=sum/weight;
         if(pos==0) break;
         pos--;
         i--;
         price=array[pos];
         sum=sum-lsum+price*period;
         lsum-=array[i];
         lsum+=price;
        }
   return(buf[shifta+ma_shift]);
   }
   */