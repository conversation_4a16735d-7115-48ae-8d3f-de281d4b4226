//+------------------------------------------------------------------+
//|                                                      newplay.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
#property indicator_buffers 8
#property indicator_plots 0
#define Name MQLInfoString(MQL_PROGRAM_NAME)
double buysig[], sellsig[];
double buyprice[], buytp[], buysl[];
double sellprice[], selltp[], sellsl[];

int bad;
int bad5;
int bad15;
int bad200;
//int bad75;

int bad50, bad100;
input int period = 600;
int periods;
int badupper, badlower;

input double multiplier = 1; //TP RR multiplier
//input int checkback = 5; //Check back X bars for high/low
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   SetIndexBuffer(0, buysig);
   PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_NONE);
   SetIndexBuffer(1, sellsig); 
   PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_NONE);  
   SetIndexBuffer(2, buyprice);
   PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_NONE);
   SetIndexBuffer(3, buysl);  
   PlotIndexSetInteger(3, PLOT_DRAW_TYPE, DRAW_NONE);
   SetIndexBuffer(4, buytp);
   PlotIndexSetInteger(4, PLOT_DRAW_TYPE, DRAW_NONE);
   SetIndexBuffer(5, sellprice);
   PlotIndexSetInteger(5, PLOT_DRAW_TYPE, DRAW_NONE);  
   SetIndexBuffer(6, sellsl);
   PlotIndexSetInteger(6, PLOT_DRAW_TYPE, DRAW_NONE);
   SetIndexBuffer(7, selltp);
   PlotIndexSetInteger(7, PLOT_DRAW_TYPE, DRAW_NONE);
   
   periods = period;
   
   bad = iMA(_Symbol, PERIOD_CURRENT, 10, 0, MODE_EMA, PRICE_CLOSE);
   bad5 = iMA(_Symbol, PERIOD_M5, 10, 0, MODE_EMA, PRICE_CLOSE);
   bad15 = iMA(_Symbol, PERIOD_M15, 10, 0, MODE_EMA, PRICE_CLOSE);
   bad200 = iMA(_Symbol, PERIOD_CURRENT, 200, 0, MODE_EMA, PRICE_CLOSE);
   //bad75 = iMA(_Symbol, PERIOD_CURRENT, 75, 0, MODE_EMA, PRICE_CLOSE);
   bad50 = iMA(_Symbol, PERIOD_CURRENT, 50, 0, MODE_EMA, PRICE_CLOSE);
   bad100 = iMA(_Symbol, PERIOD_CURRENT, 100, 0, MODE_EMA, PRICE_CLOSE);
   
   badupper = iBands(_Symbol, PERIOD_CURRENT, 20, 0, 2, PRICE_CLOSE);
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   bool new_ctf_check = false;
   static datetime start_ctf_time = 0;
   if (start_ctf_time < iTime(_Symbol, PERIOD_CURRENT, 0))
   {
      new_ctf_check = true;
      start_ctf_time = iTime(_Symbol, PERIOD_CURRENT, 0);
   }
   if (new_ctf_check)
   {
      if (ChartPeriod() == 1) big();
      //if (ChartPeriod() == 5) bigger();
      //if (ChartPeriod() == 15) biggest();     
      //if (ChartPeriod() == 60) biggestA();    
      if (ChartPeriod() > 1) biggestALL(); 
      new_ctf_check = false;
   }
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+

void big(){
   if (periods > Bars(_Symbol, PERIOD_CURRENT)) periods = Bars(_Symbol, PERIOD_CURRENT) - 112;
   
   double close1[];
   ArraySetAsSeries(close1, true);
   //ArrayResize(close1, periods + 108);
   CopyClose(_Symbol, PERIOD_M1, 0, periods + 108, close1);
   double open1[];
   ArraySetAsSeries(open1, true);
   //ArrayResize(open1, periods + 108);
   CopyOpen(_Symbol, PERIOD_M1, 0, periods + 108, open1);
   double high1[];
   ArraySetAsSeries(high1, true);
   //ArrayResize(high1, periods + 108);
   CopyHigh(_Symbol, PERIOD_M1, 0, periods + 108, high1);
   double low1[];
   ArraySetAsSeries(low1, true);
   //ArrayResize(low1, periods + 108);
   CopyLow(_Symbol, PERIOD_M1, 0, periods + 108, low1);
   double ema10a[];
   ArraySetAsSeries(ema10a, true);
   //ArrayResize(ema10a, periods + 109);
   CopyBuffer(bad, 0, 0, periods + 109, ema10a);
      
   int m1countl[], m1counts[];
   ArrayResize(m1countl, periods + 108);
   ArrayResize(m1counts, periods + 108);
   ArrayInitialize(m1countl, 0);
   ArrayInitialize(m1counts, 0);
   //100/50/25 = 110 + 55 + 28 - 11 + 6 + 4
   for (int y = periods; y >= 1; y--){
      for (int x = 107; x >= 8; x--){
         if (close1[x + y] > ema10a[x + y]) m1countl[y]++;
         if (close1[x + y] < ema10a[x + y]) m1counts[y]++;
      }
   }
      
   int sm1countl[], sm1counts[], sm5countl[], sm5counts[], sm15countl[], sm15counts[];
   ArrayResize(sm1countl, periods + 8);
   ArrayResize(sm1counts, periods + 8);
   ArrayResize(sm5countl, periods + 6);
   ArrayResize(sm5counts, periods + 6);
   ArrayResize(sm15countl, periods + 4);
   ArrayResize(sm15counts, periods + 4);
   ArrayInitialize(sm1countl, 0);
   ArrayInitialize(sm1counts, 0);
   ArrayInitialize(sm5countl, 0);
   ArrayInitialize(sm5counts, 0);
   ArrayInitialize(sm15countl, 0);
   ArrayInitialize(sm15counts, 0);
   
   double ema10a5[];
   //ArrayResize(ema10a5, periods + 6);
   double ema10a15[];
   //ArrayResize(ema10a15, periods + 6);
   CopyBuffer(bad5, 0, 0, periods + 6, ema10a5);
   CopyBuffer(bad15, 0, 0, periods + 6, ema10a15);
   
   
   for (int y = periods; y >= 1; y--){
      for (int x = 7; x >= 1; x--){
         if (open1[x + y] > ema10a[x + y]) sm1countl[y]++;
         if (open1[x + y] < ema10a[x + y]) sm1counts[y]++;
      }
   }
   
   for (int y = periods; y >= 1; y--){
      for (int x = 5; x >= 1; x--){
         if (iOpen(_Symbol, PERIOD_M5, x + y) > ema10a5[x + y]) sm5countl[y]++;
         if (iOpen(_Symbol, PERIOD_M5, x + y) < ema10a5[x + y]) sm5counts[y]++;
      }
   }
   
   for (int y = periods; y >= 1; y--){
      for (int x = 3; x >= 1; x--){
         if (iOpen(_Symbol, PERIOD_M15, x + y) > ema10a15[x + y]) sm15countl[y]++;
         if (iOpen(_Symbol, PERIOD_M15, x + y) < ema10a15[x + y]) sm15counts[y]++;
      }
   }
   
   string obname;
   
   /*
   double mosxup[];
   double mosxdn[];
   double tosxup[];
   double tosxdn[];
   ArrayResize(mosxup, periods + 1);
   ArrayResize(tosxup, periods + 1);
   ArrayResize(mosxdn, periods + 1);
   ArrayResize(tosxdn, periods + 1);
   */
   
   double ema200[], ema50[], ema100[], ema75[];
   ArraySetAsSeries(ema200, true);
   ArraySetAsSeries(ema50, true);
   ArraySetAsSeries(ema100, true);
   ArraySetAsSeries(ema75, true);
   //ArrayResize(ema200, periods + 2);
   //ArrayResize(ema50, periods + 2);
   //ArrayResize(ema100, periods + 2);
   ArrayResize(ema75, periods + 2);
   CopyBuffer(bad200, 0, 0, periods + 1, ema200);
   CopyBuffer(bad50, 0, 0, periods + 1, ema50);
   CopyBuffer(bad100, 0, 0, periods + 1, ema100);
   for (int x = periods; x >= 1; x--){
      ema75[x] = (ema100[x] + ema50[x]) / 2;
      }
   double upper[], lower[];
   ArraySetAsSeries(upper, true);
   ArraySetAsSeries(lower, true);
   //ArrayResize(upper, periods + 3);
   //ArrayResize(lower, periods + 3);
   CopyBuffer(badupper, 1, 0, periods + 2, upper);
   CopyBuffer(badupper, 2, 0, periods + 2, lower);   
   
   if (ChartPeriod() == 1){
      for (int y = periods; y >= 1; y--){
         if (m1countl[y] < 70 && sm1countl[y] == 7 && sm5countl[y] >= 1 && sm15countl[y] >= 1 && close1[y] > ema200[y] && close1[y] > ema75[y] / 2) 
         { obname = Name + " ArrU " + IntegerToString(y); burnarr(obname, low1[y], y, 233, clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); } //mosxup[y] = 1; else mosxup[y] = 0;
         if (m1countl[y] > 60 && low1[y] < lower[y] && close1[y] > ema200[y] && close1[y] > ema75[y] / 2) 
         { obname = Name + " ArrU " + IntegerToString(y); burnarr(obname, low1[y], y, 233, clrGreen); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); } //tosxup[y] = 1; else tosxup[y] = 0;
         if (m1counts[y] < 70 && sm1counts[y] == 7 && sm5counts[y] >= 1 && sm15counts[y] >= 1 && close1[y] < ema200[y] && close1[y] < ema75[y] / 2) 
         { obname = Name + " ArrD " + IntegerToString(y); burnarr(obname, high1[y], y, 234, clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); } //mosxdn[y] = 1; else mosxdn[y] = 0;
         if (m1counts[y] > 60 && high1[y] > upper[y] && close1[y] < ema200[y] && close1[y] < ema75[y] / 2) 
         { obname = Name + " ArrD " + IntegerToString(y); burnarr(obname, high1[y], y, 234, clrMagenta); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); } //tosxdn[y] = 1; else tosxdn[y] = 0;
         if (m1countl[y] > 60 && (MathAbs(close1[y] - open1[y])) < 0.5 * (high1[y] - low1[y]) && high1[y] > upper[y]) 
         { obname = Name + " ArroD " + IntegerToString(y); burnarr(obname, high1[y], y, 234, clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
         if (m1counts[y] > 60 && (MathAbs(open1[y] - close1[y])) < 0.5 * (high1[y] - low1[y]) && low1[y] < lower[y]) 
         { obname = Name + " ArroU " + IntegerToString(y); burnarr(obname, low1[y], y, 233, clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
      }
   }
   
   /*
   for (int x = periods; x >= 1; x--){
      for (int y = 7; y >= 1; y--){
         if (mosxup[x + y] == 1) mosxup[x] = 0;
         if (tosxup[x + y] == 1) tosxup[x] = 0;
         if (mosxdn[x + y] == 1) mosxdn[x] = 0;
         if (tosxdn[x + y] == 1) tosxdn[x] = 0;
      }
   }
   
   if (ChartPeriod() == 1){
      for (int y = periods; y >= 1; y--){
         if (mosxup[y] == 1)
            { obname = Name + " ArrU " + IntegerToString(y); burnarr(obname, low1[y], y, 233, clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
         if (tosxup[y] == 1)
            { obname = Name + " ArrU " + IntegerToString(y); burnarr(obname, low1[y], y, 233, clrGreen); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
         if (mosxdn[y] == 1)
            { obname = Name + " ArrD " + IntegerToString(y); burnarr(obname, high1[y], y, 234, clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
         if (tosxdn[y] == 1)
            { obname = Name + " ArrD " + IntegerToString(y); burnarr(obname, high1[y], y, 234, clrMagenta); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
      }
   }
   */
      
   ArrayFree(close1); ArrayFree(open1);
   ArrayFree(high1); ArrayFree(low1);
   ArrayFree(m1countl); ArrayFree(m1counts);
   ArrayFree(sm1countl); ArrayFree(sm1counts);
   ArrayFree(sm5countl); ArrayFree(sm5counts);
   ArrayFree(sm15countl); ArrayFree(sm15counts);   
}
//+------------------------------------------------------------------+

/*
void bigger(){
   int periods = 600;
   if (periods > Bars) periods = Bars - 57;

   double close5[];
   ArraySetAsSeries(close5, true);
   ArrayResize(close5, periods + 56);
   CopyClose(_Symbol, PERIOD_M5, 0, periods + 56, close5);
   double open5[];
   ArraySetAsSeries(open5, true);
   ArrayResize(open5, periods + 56);
   CopyOpen(_Symbol, PERIOD_M5, 0, periods + 56, open5);
   double high5[];
   ArraySetAsSeries(high5, true);
   ArrayResize(high5, periods + 56);
   CopyHigh(_Symbol, PERIOD_M5, 0, periods + 56, high5);
   double low5[];
   ArraySetAsSeries(low5, true);
   ArrayResize(low5, periods + 56);
   CopyLow(_Symbol, PERIOD_M5, 0, periods + 56, low5);
   
   double lower5[], higher5[];
   double lower20[], lower40[];
   double higher20[], higher40[];
   double highline[], lowline[];
   ArrayResize(lower5, periods + 1);
   ArrayResize(lower20, periods + 1);
   ArrayResize(lower40, periods + 1);
   ArrayResize(higher5, periods + 1);
   ArrayResize(higher20, periods + 1);
   ArrayResize(higher40, periods + 1);
   ArrayResize(lowline, periods + 1);
   ArrayResize(highline, periods + 1);
   
   for (int y = periods-144; y >= 1; y--){
      lower5[y] = low5[iLowest(_Symbol, PERIOD_M5, MODE_LOW, 10, y)];
      lower20[y] = low5[iLowest(_Symbol, PERIOD_M5, MODE_LOW, 20, y)];
      lower40[y] = low5[iLowest(_Symbol, PERIOD_M5, MODE_LOW, 50, y)];
      higher5[y] = high5[iHighest(_Symbol, PERIOD_M5, MODE_HIGH, 10, y)];
      higher20[y] = high5[iHighest(_Symbol, PERIOD_M5, MODE_HIGH, 20, y)];
      higher40[y] = high5[iHighest(_Symbol, PERIOD_M5, MODE_HIGH, 50, y)];
      if (iMA(_Symbol, PERIOD_M5, 200, 0, MODE_EMA, PRICE_CLOSE, y) < iMA(_Symbol, PERIOD_M5, 300, 0, MODE_EMA, PRICE_CLOSE, y)) lowline[y] = iMA(_Symbol, PERIOD_M5, 200, 0, MODE_EMA, PRICE_CLOSE, y); else lowline[y] = iMA(_Symbol, PERIOD_M5, 300, 0, MODE_EMA, PRICE_CLOSE, y);
      if (iMA(_Symbol, PERIOD_M5, 200, 0, MODE_EMA, PRICE_CLOSE, y) > iMA(_Symbol, PERIOD_M5, 300, 0, MODE_EMA, PRICE_CLOSE, y)) highline[y] = iMA(_Symbol, PERIOD_M5, 200, 0, MODE_EMA, PRICE_CLOSE, y); else highline[y] = iMA(_Symbol, PERIOD_M5, 300, 0, MODE_EMA, PRICE_CLOSE, y);
   }
   
   bool moodup[], mooddn[];
   ArrayResize(moodup, periods + 1);
   ArrayResize(mooddn, periods + 1);
   
   for (int y = periods; y>=1; y--){
      if(lower5[y] > (higher40[y] + lower40[y])/2 - (higher40[y] - lower40[y]) * 0.25) moodup[y] = 1; else moodup[y] = 0;
      if(higher5[y] < (higher40[y] + lower40[y])/2 + (higher40[y] - lower40[y]) * 0.25) mooddn[y] = 1; else mooddn[y] = 0;
   }
   
   
   double adr21x[];
   ArrayResize(adr21x, iBars(_Symbol, PERIOD_D1));
   
   int r = 5;
   for (int x = iBars(_Symbol, PERIOD_D1) - 6; x >= 0; x--){
      while (r >= 1){
      adr21x[x] += (iHigh(_Symbol, PERIOD_D1, x + r) - iLow(_Symbol, PERIOD_D1, x + r)) / 5;
      r--;
      }
      r = 5;
   }
   
   double adr21toC[];
   ArrayResize(adr21toC, periods + 1);
   
   for (int x = periods; x >= 1; x--){
      adr21toC[x] = adr21x[iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_M5, x), false)];
   }
      
   //Print(adr21x[0] + " " + adr21x[1] + " " + adr21x[2]);
   //Print(adr21toC[0] + " " + adr21toC[1] + " " + adr21toC[2] + " " + adr21toC[289] + " " + adr21toC[600]);
   
   bool move[];
   ArrayResize(move, periods + 1);
   
   for (int x = periods; x >= 1; x--){
      if (higher40[x] - lower40[x] > 0.20 * adr21toC[x]) move[x] = 1; else move[x] = 0;
   }    
   
   int m5countl[], m5counts[];
   
   ArrayResize(m5countl, periods + 56);
   ArrayResize(m5counts, periods + 56);
   ArrayInitialize(m5countl, 0);
   ArrayInitialize(m5counts, 0);
   
   for (int y = periods; y >= 1; y--){
      for (int x = 55; x >= 6; x--){
         if (close5[x + y] > iMA(_Symbol, PERIOD_M5, 10, 0, MODE_EMA, PRICE_CLOSE, x + y) && moodup[y]) m5countl[y]++;
         if (close5[x + y] < iMA(_Symbol, PERIOD_M5, 10, 0, MODE_EMA, PRICE_CLOSE, x + y) && mooddn[y]) m5counts[y]++;
      }
   }
   
   int sm5countl[], sm5counts[];
   ArrayResize(sm5countl, periods + 8);
   ArrayResize(sm5counts, periods + 8);
   ArrayInitialize(sm5countl, 0);
   ArrayInitialize(sm5counts, 0);
   
   for (int y = periods; y >= 1; y--){
      for (int x = 7; x >= 1; x--){
         if (open5[x + y] > iMA(_Symbol, PERIOD_M5, 10, 0, MODE_EMA, PRICE_CLOSE, x + y) && moodup[y]) sm5countl[y]++;
         if (open5[x + y] < iMA(_Symbol, PERIOD_M5, 10, 0, MODE_EMA, PRICE_CLOSE, x + y) && mooddn[y]) sm5counts[y]++;
      }
   }   
      
   int sm15countl[], sm15counts[];
   ArrayResize(sm15countl, periods + 4);
   ArrayResize(sm15counts, periods + 4);
   ArrayInitialize(sm15countl, 0);
   ArrayInitialize(sm15counts, 0);
      
   for (int y = periods; y >= 1; y--){
      for (int x = 3; x >= 1; x--){
         if (iOpen(_Symbol, PERIOD_M15, x + y) > iMA(_Symbol, PERIOD_M15, 10, 0, MODE_EMA, PRICE_CLOSE, x + y)) sm15countl[y]++;
         if (iOpen(_Symbol, PERIOD_M15, x + y) < iMA(_Symbol, PERIOD_M15, 10, 0, MODE_EMA, PRICE_CLOSE, x + y)) sm15counts[y]++;
      }
   }

   string obname;
   
   double mosxup[];
   double mosxdn[];
   double tosxup[];
   double tosxdn[];
   ArrayResize(mosxup, periods + 1);
   ArrayResize(tosxup, periods + 1);
   ArrayResize(mosxdn, periods + 1);
   ArrayResize(tosxdn, periods + 1);
   
   if (ChartPeriod() == 5){
      for (int y = periods; y >= 1; y--){
         if (m5countl[y] < 35 && sm5countl[y] == 7 && sm15countl[y] >= 1 && close5[y] > iMA(_Symbol, PERIOD_M5, 250, 0, MODE_EMA, PRICE_CLOSE, y) && close5[y] > (iMA(_Symbol, PERIOD_M5, 50, 0, MODE_EMA, PRICE_CLOSE, y) + iMA(_Symbol, PERIOD_M5, 100, 0, MODE_EMA, PRICE_CLOSE, y)) / 2) 
         { obname = Name + " ArrU " + IntegerToString(y); burnarr(obname, low5[y], y, 233, clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }//mosxup[y] = 1; } else mosxup[y] = 0;
         if (m5countl[y] > 30 && low5[y] < iBands(_Symbol, PERIOD_M5, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, y) && close5[y] > iMA(_Symbol, PERIOD_M5, 250, 0, MODE_EMA, PRICE_CLOSE, y) && close5[y] > (iMA(_Symbol, PERIOD_M5, 50, 0, MODE_EMA, PRICE_CLOSE, y) + iMA(_Symbol, PERIOD_M5, 100, 0, MODE_EMA, PRICE_CLOSE, y) / 2))
         { obname = Name + " ArrU " + IntegerToString(y); burnarr(obname, low5[y], y, 233, clrGreen); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }//tosxup[y] = 1; } else tosxup[y] = 0;    
         if (m5counts[y] < 35 && sm5counts[y] == 7 && sm15counts[y] >= 1 && close5[y] < iMA(_Symbol, PERIOD_M5, 250, 0, MODE_EMA, PRICE_CLOSE, y) && close5[y] < (iMA(_Symbol, PERIOD_M5, 50, 0, MODE_EMA, PRICE_CLOSE, y) + iMA(_Symbol, PERIOD_M5, 100, 0, MODE_EMA, PRICE_CLOSE, y)) / 2) 
         { obname = Name + " ArrD " + IntegerToString(y); burnarr(obname, high5[y], y, 234, clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }//mosxdn[y] = 1; } else mosxdn[y] = 0;
         if (m5counts[y] > 30 && high5[y] > iBands(_Symbol, PERIOD_M5, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, y) && close5[y] < iMA(_Symbol, PERIOD_M5, 250, 0, MODE_EMA, PRICE_CLOSE, y) && close5[y] < (iMA(_Symbol, PERIOD_M5, 50, 0, MODE_EMA, PRICE_CLOSE, y) + iMA(_Symbol, PERIOD_M5, 100, 0, MODE_EMA, PRICE_CLOSE, y) / 2)) 
         { obname = Name + " ArrD " + IntegerToString(y); burnarr(obname, high5[y], y, 234, clrMagenta); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }//tosxdn[y] = 1; } else tosxdn[y] = 0;
      }
   }
   
   ArrayFree(close5); ArrayFree(open5);
   ArrayFree(high5); ArrayFree(low5);
   ArrayFree(m5countl); ArrayFree(m5counts);
   ArrayFree(sm5countl); ArrayFree(sm5counts);
   ArrayFree(sm15countl); ArrayFree(sm15counts);
   
}
*/

/*
void biggest(){
   int periods = 300;
   if (periods > Bars) periods = Bars - 56;
   
   double close15[];
   ArraySetAsSeries(close15, true);
   ArrayResize(close15, periods + 56);
   CopyClose(_Symbol, PERIOD_M15, 0, periods + 56, close15);
   double open15[];
   ArraySetAsSeries(open15, true);
   ArrayResize(open15, periods + 56);
   CopyOpen(_Symbol, PERIOD_M15, 0, periods + 56, open15);
   double high15[];
   ArraySetAsSeries(high15, true);
   ArrayResize(high15, periods + 56);
   CopyHigh(_Symbol, PERIOD_M15, 0, periods + 56, high15);
   double low15[];
   ArraySetAsSeries(low15, true);
   ArrayResize(low15, periods + 56);
   CopyLow(_Symbol, PERIOD_M15, 0, periods + 56, low15);
   
   int m15countl[], m15counts[];
   
   ArrayResize(m15countl, periods + 56);
   ArrayResize(m15counts, periods + 56);
   
   ArrayInitialize(m15countl, 0);
   ArrayInitialize(m15counts, 0);
   
   for (int y = periods; y >= 1; y--){
      for (int x = 55; x >= 6; x--){
         if (close15[x + y] > iMA(_Symbol, PERIOD_M15, 10, 0, MODE_EMA, PRICE_CLOSE, x + y)) m15countl[y]++;
         if (close15[x + y] < iMA(_Symbol, PERIOD_M15, 10, 0, MODE_EMA, PRICE_CLOSE, x + y)) m15counts[y]++;
      }
   }
      
   int sm15countl[], sm15counts[];
   ArrayResize(sm15countl, periods + 8);
   ArrayResize(sm15counts, periods + 8);
   ArrayInitialize(sm15countl, 0);
   ArrayInitialize(sm15counts, 0);
      
   for (int y = periods; y >= 1; y--){
      for (int x = 7; x >= 1; x--){
         if (open15[x + y] > iMA(_Symbol, PERIOD_M15, 10, 0, MODE_EMA, PRICE_CLOSE, x + y)) sm15countl[y]++;
         if (open15[x + y] < iMA(_Symbol, PERIOD_M15, 10, 0, MODE_EMA, PRICE_CLOSE, x + y)) sm15counts[y]++;
      }
   }

   string obname;
   
   double mosxup[];
   double mosxdn[];
   double tosxup[];
   double tosxdn[];
   ArrayResize(mosxup, periods + 1);
   ArrayResize(tosxup, periods + 1);
   ArrayResize(mosxdn, periods + 1);
   ArrayResize(tosxdn, periods + 1);
   
   if (ChartPeriod() == 15){
      for (int y = periods; y >= 1; y--){
         if (m15countl[y] < 35 && sm15countl[y] == 7 && close15[y] > iMA(_Symbol, PERIOD_M15, 250, 0, MODE_EMA, PRICE_CLOSE, y) && close15[y] > iMA(_Symbol, PERIOD_M15, 75, 0, MODE_EMA, PRICE_CLOSE, y)) 
         { obname = Name + " ArrU " + IntegerToString(y); burnarr(obname, low15[y], y, 233, clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }//mosxup[y] = 1; } else mosxup[y] = 0;
         if (m15countl[y] > 30 && low15[y] < iBands(_Symbol, PERIOD_M15, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, y) && close15[y] > iMA(_Symbol, PERIOD_M15, 250, 0, MODE_EMA, PRICE_CLOSE, y) && close15[y] > iMA(_Symbol, PERIOD_M15, 75, 0, MODE_EMA, PRICE_CLOSE, y)) 
         { obname = Name + " ArrU " + IntegerToString(y); burnarr(obname, low15[y], y, 233, clrGreen); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }//tosxup[y] = 1; } else tosxup[y] = 0;    
         if (m15counts[y] < 35 && sm15counts[y] == 7 && close15[y] < iMA(_Symbol, PERIOD_M15, 250, 0, MODE_EMA, PRICE_CLOSE, y) && close15[y] < iMA(_Symbol, PERIOD_M15, 75, 0, MODE_EMA, PRICE_CLOSE, y)) 
         { obname = Name + " ArrD " + IntegerToString(y); burnarr(obname, high15[y], y, 234, clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }//mosxdn[y] = 1; } else mosxdn[y] = 0;
         if (m15counts[y] > 30 && high15[y] > iBands(_Symbol, PERIOD_M15, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, y) && close15[y] < iMA(_Symbol, PERIOD_M15, 250, 0, MODE_EMA, PRICE_CLOSE, y) && close15[y] < iMA(_Symbol, PERIOD_M15, 75, 0, MODE_EMA, PRICE_CLOSE, y)) 
         { obname = Name + " ArrD " + IntegerToString(y); burnarr(obname, high15[y], y, 234, clrMagenta); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }//tosxdn[y] = 1; } else tosxdn[y] = 0;
      }
   }
   
   ArrayFree(close15); ArrayFree(open15);
   ArrayFree(high15); ArrayFree(low15);
   ArrayFree(m15countl); ArrayFree(m15counts);
   ArrayFree(sm15countl); ArrayFree(sm15counts);
}

/*
void biggestA(){
   int periods = 300;
   if (periods > Bars) periods = Bars - 56;
   
   double close60[];
   ArraySetAsSeries(close60, true);
   ArrayResize(close60, periods + 56);
   CopyClose(_Symbol, PERIOD_H1, 0, periods + 56, close60);
   double open60[];
   ArraySetAsSeries(open60, true);
   ArrayResize(open60, periods + 56);
   CopyOpen(_Symbol, PERIOD_H1, 0, periods + 56, open60);
   double high60[];
   ArraySetAsSeries(high60, true);
   ArrayResize(high60, periods + 56);
   CopyHigh(_Symbol, PERIOD_H1, 0, periods + 56, high60);
   double low60[];
   ArraySetAsSeries(low60, true);
   ArrayResize(low60, periods + 56);
   CopyLow(_Symbol, PERIOD_H1, 0, periods + 56, low60);
   
   int m60countl[], m60counts[];
   
   ArrayResize(m60countl, periods + 56);
   ArrayResize(m60counts, periods + 56);
   
   ArrayInitialize(m60countl, 0);
   ArrayInitialize(m60counts, 0);
   
   for (int y = periods; y >= 1; y--){
      for (int x = 55; x >= 6; x--){
         if (close60[x + y] > iMA(_Symbol, PERIOD_H1, 10, 0, MODE_EMA, PRICE_CLOSE, x + y)) m60countl[y]++;
         if (close60[x + y] < iMA(_Symbol, PERIOD_H1, 10, 0, MODE_EMA, PRICE_CLOSE, x + y)) m60counts[y]++;
      }
   }
      
   int sm60countl[], sm60counts[];
   ArrayResize(sm60countl, periods + 8);
   ArrayResize(sm60counts, periods + 8);
   ArrayInitialize(sm60countl, 0);
   ArrayInitialize(sm60counts, 0);
      
   for (int y = periods; y >= 1; y--){
      for (int x = 7; x >= 1; x--){
         if (open60[x + y] > iMA(_Symbol, PERIOD_H1, 10, 0, MODE_EMA, PRICE_CLOSE, x + y)) sm60countl[y]++;
         if (open60[x + y] < iMA(_Symbol, PERIOD_H1, 10, 0, MODE_EMA, PRICE_CLOSE, x + y)) sm60counts[y]++;
      }
   }

   string obname;
   
   double mosxup[];
   double mosxdn[];
   double tosxup[];
   double tosxdn[];
   ArrayResize(mosxup, periods + 1);
   ArrayResize(tosxup, periods + 1);
   ArrayResize(mosxdn, periods + 1);
   ArrayResize(tosxdn, periods + 1);
   
   if (ChartPeriod() == 60){
      for (int y = periods; y >= 1; y--){
         if (m60countl[y] < 35 && sm60countl[y] == 7 && close60[y] > iMA(_Symbol, PERIOD_H1, 250, 0, MODE_EMA, PRICE_CLOSE, y) && close60[y] > iMA(_Symbol, PERIOD_H1, 75, 0, MODE_EMA, PRICE_CLOSE, y)) 
         { obname = Name + " ArrU " + IntegerToString(y); burnarr(obname, low60[y], y, 233, clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }//mosxup[y] = 1; } else mosxup[y] = 0;
         if (m60countl[y] > 30 && low60[y] < iBands(_Symbol, PERIOD_H1, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, y) && close60[y] > iMA(_Symbol, PERIOD_H1, 250, 0, MODE_EMA, PRICE_CLOSE, y) && close60[y] > iMA(_Symbol, PERIOD_H1, 75, 0, MODE_EMA, PRICE_CLOSE, y)) 
         { obname = Name + " ArrU " + IntegerToString(y); burnarr(obname, low60[y], y, 233, clrGreen); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }//tosxup[y] = 1; } else tosxup[y] = 0;    
         if (m60counts[y] < 35 && sm60counts[y] == 7 && close60[y] < iMA(_Symbol, PERIOD_H1, 250, 0, MODE_EMA, PRICE_CLOSE, y) && close60[y] < iMA(_Symbol, PERIOD_H1, 75, 0, MODE_EMA, PRICE_CLOSE, y)) 
         { obname = Name + " ArrD " + IntegerToString(y); burnarr(obname, high60[y], y, 234, clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }//mosxdn[y] = 1; } else mosxdn[y] = 0;
         if (m60counts[y] > 30 && high60[y] > iBands(_Symbol, PERIOD_H1, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, y) && close60[y] < iMA(_Symbol, PERIOD_H1, 250, 0, MODE_EMA, PRICE_CLOSE, y) && close60[y] < iMA(_Symbol, PERIOD_H1, 75, 0, MODE_EMA, PRICE_CLOSE, y)) 
         { obname = Name + " ArrD " + IntegerToString(y); burnarr(obname, high60[y], y, 234, clrMagenta); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }//tosxdn[y] = 1; } else tosxdn[y] = 0;
      }
   }
   
   ArrayFree(close60); ArrayFree(open60);
   ArrayFree(high60); ArrayFree(low60);
   ArrayFree(m60countl); ArrayFree(m60counts);
   ArrayFree(sm60countl); ArrayFree(sm60counts);
}
*/


void biggestALL(){
   if (periods > Bars(_Symbol, PERIOD_CURRENT)) periods = Bars(_Symbol, PERIOD_CURRENT) - 56;
   
   double close60[];
   ArraySetAsSeries(close60, true);
   //ArrayResize(close60, periods + 56);
   CopyClose(_Symbol, PERIOD_CURRENT, 0, periods + 56, close60);
   double open60[];
   ArraySetAsSeries(open60, true);
   //ArrayResize(open60, periods + 56);
   CopyOpen(_Symbol, PERIOD_CURRENT, 0, periods + 56, open60);
   double high60[];
   ArraySetAsSeries(high60, true);
   //ArrayResize(high60, periods + 56);
   CopyHigh(_Symbol, PERIOD_CURRENT, 0, periods + 56, high60);
   double low60[];
   ArraySetAsSeries(low60, true);
   //ArrayResize(low60, periods + 56);
   CopyLow(_Symbol, PERIOD_CURRENT, 0, periods + 56, low60);
   double ema10a[];
   ArraySetAsSeries(ema10a, true);
   //ArrayResize(ema10a, periods + 109);
   CopyBuffer(bad, 0, 0, periods + 109, ema10a);
   
   int m60countl[], m60counts[];
   
   ArrayResize(m60countl, periods + 56);
   ArrayResize(m60counts, periods + 56);
   
   ArrayInitialize(m60countl, 0);
   ArrayInitialize(m60counts, 0);
   
   for (int y = periods; y >= 1; y--){
      for (int x = 55; x >= 6; x--){
         if (close60[x + y] > ema10a[x + y]) m60countl[y]++;
         if (close60[x + y] < ema10a[x + y]) m60counts[y]++;
      }
   }
      
   int sm60countl[], sm60counts[];
   ArrayResize(sm60countl, periods + 8);
   ArrayResize(sm60counts, periods + 8);
   ArrayInitialize(sm60countl, 0);
   ArrayInitialize(sm60counts, 0);
      
   for (int y = periods; y >= 1; y--){
      for (int x = 7; x >= 1; x--){
         if (open60[x + y] > ema10a[x + y]) sm60countl[y]++;
         if (open60[x + y] < ema10a[x + y]) sm60counts[y]++;
      }
   }
   /*
   
   double adr21[], adr61[];
   ArrayResize(adr21, periods + 21);
   ArrayResize(adr61, periods + 61);
   
   for (int x = periods - 61; x >= 1; x--){
      adr21[x] = high60[iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 21, 1)] - low60[iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 21, 1)];
      adr61[x] = high60[iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 61, 1)] - low60[iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 61, 1)];
   }
   
   bool checkadr[];
   ArrayResize(checkadr, periods + 1);
   
   for (int x = periods; x >= 1; x--){
      if (adr21[x] > 0.45 * adr61[x]) checkadr[x] = true; else checkadr[x] = false;
   }
   */
   /*
   double emadist20[], emadist40[];
   ArrayResize(emadist20, periods + 21);
   ArrayResize(emadist40, periods + 41);
   
   for (int x = periods; x >= 1; x--){
      for (int y = 20; y >= 1; y--){
         emadist20[x] += MathAbs((iMA(_Symbol, PERIOD_CURRENT, 50, 0, MODE_EMA, PRICE_CLOSE, x + y) + iMA(_Symbol, PERIOD_CURRENT, 100, 0, MODE_EMA, PRICE_CLOSE, x + y)) / 2 - iMA(_Symbol, PERIOD_CURRENT, 250, 0, MODE_EMA, PRICE_CLOSE, x + y)) / 20;
      }
      for (int y = 40; y >= 1; y--){
         emadist40[x] += MathAbs((iMA(_Symbol, PERIOD_CURRENT, 50, 0, MODE_EMA, PRICE_CLOSE, x + y) + iMA(_Symbol, PERIOD_CURRENT, 100, 0, MODE_EMA, PRICE_CLOSE, x + y)) / 2 - iMA(_Symbol, PERIOD_CURRENT, 250, 0, MODE_EMA, PRICE_CLOSE, x + y)) / 40;
      }
   }
   bool checkadr[];
   ArrayResize(checkadr, periods + 1);
   
   for (int x = periods; x >= 1; x--){
      if (emadist20[x] > 0.8 * emadist40[x]) checkadr[x] = true; else checkadr[x] = false;
   }
   */   

   string obname;
   
   double mosxup[];
   double mosxdn[];
   double tosxup[];
   double tosxdn[];
   ArrayResize(mosxup, periods + 1);
   ArrayResize(tosxup, periods + 1);
   ArrayResize(mosxdn, periods + 1);
   ArrayResize(tosxdn, periods + 1);
   
   double ema200[], ema50[], ema100[], ema75[];
   ArraySetAsSeries(ema200, true);
   ArraySetAsSeries(ema50, true);
   ArraySetAsSeries(ema100, true);
   ArraySetAsSeries(ema75, true);
   //ArrayResize(ema200, periods + 2);
   //ArrayResize(ema50, periods + 2);
   //ArrayResize(ema100, periods + 2);
   ArrayResize(ema75, periods + 2);
   CopyBuffer(bad200, 0, 0, periods + 1, ema200);
   CopyBuffer(bad50, 0, 0, periods + 1, ema50);
   CopyBuffer(bad100, 0, 0, periods + 1, ema100);
   for (int x = periods; x >= 1; x--){
      ema75[x] = (ema100[x] + ema50[x]) / 2;
      }
   double upper[], lower[];
   ArraySetAsSeries(upper, true);
   ArraySetAsSeries(lower, true);
   //ArrayResize(upper, periods + 3);
   //ArrayResize(lower, periods + 3);
   CopyBuffer(badupper, 1, 0, periods + 2, upper);
   CopyBuffer(badupper, 2, 0, periods + 2, lower);
      
   if (ChartPeriod() > 1){
      for (int y = periods; y >= 1; y--){
         if (m60countl[y] < 35 && sm60countl[y] <= 3 && close60[y] > ema75[y] && !(high60[y] > ema75[y] && low60[y] < ema75[y])) 
         { obname = Name + " ArrU " + IntegerToString(y); burnarr(obname, low60[y], y, 233, clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); mosxup[y] = 1; } else mosxup[y] = 0;
         if (m60countl[y] > 30 && low60[y] < lower[y] && close60[y] > ema75[y]) 
         { obname = Name + " ArrU " + IntegerToString(y); burnarr(obname, low60[y], y, 233, clrGreen); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); tosxup[y] = 1; } else tosxup[y] = 0;    
         if (m60counts[y] < 35 && sm60counts[y] <= 3 && close60[y] < ema75[y] && !(high60[y] > ema75[y] && low60[y] <ema75[y])) 
         { obname = Name + " ArrD " + IntegerToString(y); burnarr(obname, high60[y], y, 234, clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); mosxdn[y] = 1; } else mosxdn[y] = 0;
         if (m60counts[y] > 30 && high60[y] > upper[y] && close60[y] < ema75[y]) 
         { obname = Name + " ArrD " + IntegerToString(y); burnarr(obname, high60[y], y, 234, clrMagenta); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); tosxdn[y] = 1; } else tosxdn[y] = 0;
         if (m60countl[y] > 30 && (MathAbs(close60[y] - open60[y])) < 0.5 * (high60[y] - low60[y]) && high60[y] > upper[y]) 
         { obname = Name + " ArroD " + IntegerToString(y); burnarr(obname, high60[y], y, 234, clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
         if (m60counts[y] > 30 && (MathAbs(open60[y] - close60[y])) < 0.5 * (high60[y] - low60[y]) && low60[y] < lower[y]) 
         { obname = Name + " ArroU " + IntegerToString(y); burnarr(obname, low60[y], y, 233, clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
         //if (_Symbol == "AUDCAD" && y < 230 && y > 220) Print(y + " low: " + low60[y] + " lowb: " + lower[y]);
         if (m60counts[y] > 30 && (close60[y + 1] < open60[y + 1] && close60[y] > open60[y + 1]) && low60[y] < lower[y] && low60[y + 1] < lower[y + 1]) 
         { obname = Name + " ArroU " + IntegerToString(y); burnarr(obname, low60[y], y, 233, clrAqua); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
         if (m60countl[y] > 30 && (close60[y + 1] > open60[y + 1] && close60[y] < open60[y + 1]) && high60[y] > upper[y] && high60[y + 1] > upper[y + 1]) 
         { obname = Name + " ArroD " + IntegerToString(y); burnarr(obname, high60[y], y, 234, clrAqua); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
      }
   }
   
   if (mosxup[1] == 1) Alert("Possible buy " + _Symbol + " " + TimeToString(TimeCurrent(), TIME_MINUTES));
   if (tosxup[1] == 1) Alert("Possible rev buy " + _Symbol + " " + TimeToString(TimeCurrent(), TIME_MINUTES));
   if (mosxdn[1] == 1) Alert("Possible sell " + _Symbol + " " + TimeToString(TimeCurrent(), TIME_MINUTES));
   if (tosxdn[1] == 1) Alert("Possible rev sell " + _Symbol + " " + TimeToString(TimeCurrent(), TIME_MINUTES));
   
   ArrayFree(close60); ArrayFree(open60);
   ArrayFree(high60); ArrayFree(low60);
   ArrayFree(m60countl); ArrayFree(m60counts);
   ArrayFree(sm60countl); ArrayFree(sm60counts);
}

//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
   datetime Time[];
   ArraySetAsSeries(Time, true);
   CopyTime(_Symbol, PERIOD_CURRENT, 0, periods, Time);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 2, Time[t2] + 60* _Period * t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 2, pr2);
	ObjectSetInteger(0, name, OBJPROP_STYLE, st);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
	ObjectSetInteger(0, name, OBJPROP_RAY, false);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToString(pr1, _Digits) + " Date: " + TimeToString(Time[t1], TIME_DATE));
}
//+------------------------------------------------------------------+

//+CREATE ARROWS-----------------------------------------------------+
void burnarr(string name, double p, int t, int arrow, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
   datetime Time[];
   ArraySetAsSeries(Time, true);
   CopyTime(_Symbol, PERIOD_CURRENT, 0, periods + 1, Time);
	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t]);
	ObjectSetDouble(0, name, OBJPROP_PRICE, p);
	ObjectSetInteger(0, name, OBJPROP_ARROWCODE, arrow);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
}
//+------------------------------------------------------------------+

/*
//+CREATE ARROWS-----------------------------------------------------+
void burnarr(string name, double p, int t, int arrow, color col)
{

   datetime Time[];
   int counta = Bars(_Symbol, PERIOD_CURRENT);   // number of elements to copy
   ArraySetAsSeries(Time,true);
   CopyTime(_Symbol,_Period,0,counta,Time);

	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	ObjectSetInteger(0,name,OBJPROP_TIME,Time[t]);
	ObjectSetDouble(0,name,OBJPROP_PRICE,p);
	ObjectSetInteger(0,name,OBJPROP_ARROWCODE,arrow);
	ObjectSetInteger(0,name,OBJPROP_COLOR,col);
	ObjectSetInteger(0,name,OBJPROP_WIDTH,0);
	ObjectSetInteger(0,name,OBJPROP_BACK,false);
}
//+------------------------------------------------------------------+

*/

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
double PipsToPoints(double pips)
{
  //------------------------------------------------------------------
  if (_Digits==5 || _Digits==3) pips *= 10;
  return (pips * _Point);
  //------------------------------------------------------------------
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
double PointsToPips(double points)
{
  //------------------------------------------------------------------
  if (_Digits==5 || _Digits==3) points /= 10;
  return (points / _Point);
  //------------------------------------------------------------------
}
//+------------------------------------------------------------------+