#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict
#property indicator_buffers 21
#property indicator_plots 7

#property indicator_type4   DRAW_HISTOGRAM2
#property indicator_color4  C'164,183,219'
#property indicator_style4  STYLE_SOLID
#property indicator_width4  1
#property indicator_type5   DRAW_HISTOGRAM2
#property indicator_color5  C'255,111,131'
#property indicator_style5  STYLE_SOLID
#property indicator_width5  1

#define Name MQLInfoString(MQL_PROGRAM_NAME) + "U"

input int period1 = 20; // 1st Period
input int period2 = 50; // 2nd Period
input int period3 = 100; // 3rd Period
input int period4 = 200; // 4th Period

input ENUM_MA_METHOD mamode = MODE_LWMA; // MA Mode

double ma8h[], ma8l[];
double ma13h[], ma13l[];
double ma21h[], ma21l[];
double ma34h[], ma34l[];
double ma44h[], ma44l[];
double ma55h[], ma55l[];
double ma45h[], ma45l[];
double ma54[], ma54a[], ma54b[];
double ma53[], ma52[];
double D1[], H4[];

double W1H, W1M, W1L, D1H, D1M, D1L, H1H, H1M, H1L;

int m8h, m8l, m13h, m13l, m21h, m21l, m34h, m34l, m44h, m44l, m55h, m55l;

//TradeInfo
double point;
int PipAdjust, NrOfDigits;

//Handles for MTF
int N8hh1, N8lh1, N13hh1, N13lh1, N21hh1, N21lh1, N34hh1, N34lh1;
int N8hh4, N8lh4, N13hh4, N13lh4, N21hh4, N21lh4, N34hh4, N34lh4;
int N8hd1, N8ld1, N13hd1, N13ld1, N21hd1, N21ld1, N34hd1, N34ld1;
int N8hw1, N8lw1, N13hw1, N13lw1, N21hw1, N21lw1, N34hw1, N34lw1;

bool timerwait = false;

//+INIT FUNCTION-----------------------------------------------------+
int OnInit()
{
	IndicatorSetString(INDICATOR_SHORTNAME, "Rips");
	IndicatorSetInteger(INDICATOR_DIGITS,_Digits);

	SetIndexBuffer(0, ma54, INDICATOR_DATA);
	PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_LINE);
	PlotIndexSetInteger(0, PLOT_LINE_WIDTH, 3);
	PlotIndexSetInteger(0, PLOT_LINE_COLOR, clrLimeGreen);
	PlotIndexSetString(0, PLOT_LABEL, "Mid");

	SetIndexBuffer(1, ma45h, INDICATOR_DATA);
	PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_LINE);
	PlotIndexSetInteger(1, PLOT_LINE_WIDTH, 4);
	PlotIndexSetInteger(1, PLOT_LINE_COLOR, clrSeaGreen);
	PlotIndexSetString(1, PLOT_LABEL, "Upper");
	
	SetIndexBuffer(2, ma45l, INDICATOR_DATA);
	PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_LINE);
	PlotIndexSetInteger(2, PLOT_LINE_WIDTH, 4);
	PlotIndexSetInteger(2, PLOT_LINE_COLOR, clrSeaGreen);
	PlotIndexSetString(2, PLOT_LABEL, "Lower");	
	
	SetIndexBuffer(3, ma53, INDICATOR_DATA);
   //PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_HISTOGRAM);
	//PlotIndexSetInteger(3, PLOT_LINE_WIDTH, 10);
	//PlotIndexSetInteger(0, PLOT_LINE_COLOR, C'164,183,219');
	//PlotIndexSetString(0, PLOT_LABEL, "");
	
	SetIndexBuffer(4, ma54a, INDICATOR_DATA);
	//PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_HISTOGRAM);
	//PlotIndexSetInteger(4, PLOT_LINE_WIDTH, 10);
	//PlotIndexSetInteger(1, PLOT_LINE_COLOR, C'164,183,219');
	//PlotIndexSetString(1, PLOT_LABEL, "");
	
	SetIndexBuffer(5, ma52, INDICATOR_DATA);
	//PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_HISTOGRAM);
	//PlotIndexSetInteger(5, PLOT_LINE_WIDTH, 10);
	//PlotIndexSetInteger(2, PLOT_LINE_COLOR, C'255,111,131');
	//PlotIndexSetString(2, PLOT_LABEL, "");
	
	SetIndexBuffer(6, ma54b, INDICATOR_DATA);
	//PlotIndexSetInteger(3, PLOT_DRAW_TYPE, DRAW_HISTOGRAM);
	//PlotIndexSetInteger(6, PLOT_LINE_WIDTH, 10);
	//PlotIndexSetInteger(3, PLOT_LINE_COLOR, C'255,111,131');
	//PlotIndexSetString(3, PLOT_LABEL, "");
	
	SetIndexBuffer(7, ma8h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(8, ma8l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(9, ma13h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(10, ma13l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(11, ma21h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(12, ma21l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(13, ma34h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(14, ma34l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(15, ma44h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(16, ma44l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(17, ma55h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(18, ma55l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(19, D1, INDICATOR_CALCULATIONS);
	SetIndexBuffer(20, H4, INDICATOR_CALCULATIONS);
	
	//CURRENT
	m8h = iMA(_Symbol, PERIOD_CURRENT, period1, 0, mamode, PRICE_HIGH);
	m13h = iMA(_Symbol, PERIOD_CURRENT, period2, 0, mamode, PRICE_HIGH);
	m21h = iMA(_Symbol, PERIOD_CURRENT, period3, 0, mamode, PRICE_HIGH);
	m34h = iMA(_Symbol, PERIOD_CURRENT, period4, 0, mamode, PRICE_HIGH);
	m8l = iMA(_Symbol, PERIOD_CURRENT, period1, 0, mamode, PRICE_LOW);
	m13l = iMA(_Symbol, PERIOD_CURRENT, period2, 0, mamode, PRICE_LOW);
	m21l = iMA(_Symbol, PERIOD_CURRENT, period3, 0, mamode, PRICE_LOW);
	m34l = iMA(_Symbol, PERIOD_CURRENT, period4, 0, mamode, PRICE_LOW);
	//H1
	N8hh1 = iMA(_Symbol, PERIOD_H1, period1, 0, mamode, PRICE_HIGH);
	N8lh1 = iMA(_Symbol, PERIOD_H1, period1, 0, mamode, PRICE_LOW);
	N13hh1 = iMA(_Symbol, PERIOD_H1, period2, 0, mamode, PRICE_HIGH);
	N13lh1 = iMA(_Symbol, PERIOD_H1, period2, 0, mamode, PRICE_LOW);
	N21hh1 = iMA(_Symbol, PERIOD_H1, period3, 0, mamode, PRICE_HIGH);
	N21lh1 = iMA(_Symbol, PERIOD_H1, period3, 0, mamode, PRICE_LOW);
	N34hh1 = iMA(_Symbol, PERIOD_H1, period4, 0, mamode, PRICE_HIGH);
	N34lh1 = iMA(_Symbol, PERIOD_H1, period4, 0, mamode, PRICE_LOW);	
	//H4
	N8hh4 = iMA(_Symbol, PERIOD_H4, period1, 0, mamode, PRICE_HIGH);
	N8lh4 = iMA(_Symbol, PERIOD_H4, period1, 0, mamode, PRICE_LOW);
	N13hh4 = iMA(_Symbol, PERIOD_H4, period2, 0, mamode, PRICE_HIGH);
	N13lh4 = iMA(_Symbol, PERIOD_H4, period2, 0, mamode, PRICE_LOW);
	N21hh4 = iMA(_Symbol, PERIOD_H4, period3, 0, mamode, PRICE_HIGH);
	N21lh4 = iMA(_Symbol, PERIOD_H4, period3, 0, mamode, PRICE_LOW);
	N34hh4 = iMA(_Symbol, PERIOD_H4, period4, 0, mamode, PRICE_HIGH);
	N34lh4 = iMA(_Symbol, PERIOD_H4, period4, 0, mamode, PRICE_LOW);
	//D1
	N8hd1 = iMA(_Symbol, PERIOD_D1, period1, 0, mamode, PRICE_HIGH);
	N8ld1 = iMA(_Symbol, PERIOD_D1, period1, 0, mamode, PRICE_LOW);
	N13hd1 = iMA(_Symbol, PERIOD_D1, period2, 0, mamode, PRICE_HIGH);
	N13ld1 = iMA(_Symbol, PERIOD_D1, period2, 0, mamode, PRICE_LOW);
	N21hd1 = iMA(_Symbol, PERIOD_D1, period3, 0, mamode, PRICE_HIGH);
	N21ld1 = iMA(_Symbol, PERIOD_D1, period3, 0, mamode, PRICE_LOW);
	N34hd1 = iMA(_Symbol, PERIOD_D1, period4, 0, mamode, PRICE_HIGH);
	N34ld1 = iMA(_Symbol, PERIOD_D1, period4, 0, mamode, PRICE_LOW);
	//W1
	N8hw1 = iMA(_Symbol, PERIOD_W1, period1, 0, mamode, PRICE_HIGH);
	N8lw1 = iMA(_Symbol, PERIOD_W1, period1, 0, mamode, PRICE_LOW);
	N13hw1 = iMA(_Symbol, PERIOD_W1, period2, 0, mamode, PRICE_HIGH);
	N13lw1 = iMA(_Symbol, PERIOD_W1, period2, 0, mamode, PRICE_LOW);
	N21hw1 = iMA(_Symbol, PERIOD_W1, period3, 0, mamode, PRICE_HIGH);
	N21lw1 = iMA(_Symbol, PERIOD_W1, period3, 0, mamode, PRICE_LOW);
	N34hw1 = iMA(_Symbol, PERIOD_W1, period4, 0, mamode, PRICE_HIGH);
	N34lw1 = iMA(_Symbol, PERIOD_W1, period4, 0, mamode, PRICE_LOW);

   EventSetMillisecondTimer(1);

	string obname;
	obname = Name + " Dist1"; LabelMake(obname, 3, 105, 85, "W1", 9, clrNavy);
	obname = Name + " Dist2"; LabelMake(obname, 3, 80, 85, "D1", 9, clrDeepSkyBlue);
	obname = Name + " Dist3"; LabelMake(obname, 3, 55, 85, "H4", 9, clrGray);
	obname = Name + " Dist4"; LabelMake(obname, 3, 30, 85, "H1", 9, clrSeaGreen);
	int p = 15;
	string buls[4] = { "H", "UM", "LM", "L" };
	for (int i = 4; i >= 1; i--) {
		obname = Name + IntegerToString(i) + " L"; LabelMake(obname, 3, 135, 85 + i * p, buls[i - 1], 9, clrBlack);
		obname = Name + IntegerToString(i) + " W1"; LabelMake(obname, 3, 105, 85 + i * p, " ", 9, clrBlack);
		obname = Name + IntegerToString(i) + " D1"; LabelMake(obname, 3, 80, 85 + i * p, " ", 9, clrBlack);
		obname = Name + IntegerToString(i) + " H4"; LabelMake(obname, 3, 55, 85 + i * p, " ", 9, clrBlack);
		obname = Name + IntegerToString(i) + " H1"; LabelMake(obname, 3, 30, 85 + i * p, " ", 9, clrBlack);
	}

	obname = Name + " BuyPos"; LabelMake(obname, 3, 135, 170, " ", 8, clrBlue);
	obname = Name + " SellPos"; LabelMake(obname, 3, 135, 180, " ", 8, clrRed);
	obname = Name + " AvgPos"; LabelMake(obname, 3, 135, 190, " ", 8, clrWhite);


	NrOfDigits = _Digits;
	if (NrOfDigits == 5 || NrOfDigits == 3) PipAdjust = 10;
	else
		if (NrOfDigits == 4 || NrOfDigits == 2) PipAdjust = 1;
	point = _Point * PipAdjust;
   
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

void OnTimer(){
   EventKillTimer();
   timerwait = true;
   }

//+CALCULATE---------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
   if(!timerwait) { return rates_total; }
	bool new_2m_check = false;
	static datetime start_2m_time = 0;
	if (start_2m_time < iTime(NULL, PERIOD_CURRENT, 0))
	{
		new_2m_check = true;
		start_2m_time = iTime(NULL, PERIOD_CURRENT, 0);
	}
	if (new_2m_check)
	{
		Bingo();
		new_2m_check = false;
	}
	bool new_4h_check = false;
	static datetime start_4h_time = 0;
	if (start_4h_time < iTime(NULL, PERIOD_H1, 0))
	{
		new_4h_check = true;
		start_4h_time = iTime(NULL, PERIOD_H1, 0);
	}
	if (new_4h_check)
	{
		BingoH1();
		BingoH();
		BingoD();
		BingoW();
		marks();
		new_4h_check = false;
	}
   /*
	if (trades_on_symbol(_Symbol)) TradeInfo();
	else if (!trades_on_symbol(_Symbol) && ObjectFind(0, Name + "Average_Price_Line_" + _Symbol) == 0) ObjectDelete(0, Name + "Average_Price_Line_" + _Symbol);
   */
   
	return(rates_total);
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION-----------------------------------------------------+
void Bingo() {
   int periods = Bars(_Symbol, PERIOD_CURRENT) - 1;
   
   ArraySetAsSeries(ma8h, true); ArraySetAsSeries(ma8l, true);
   ArraySetAsSeries(ma13h, true); ArraySetAsSeries(ma13l, true);
   ArraySetAsSeries(ma21h, true); ArraySetAsSeries(ma21l, true);
   ArraySetAsSeries(ma34h, true); ArraySetAsSeries(ma34l, true);
   if(CopyBuffer(m8h, 0, 0, periods, ma8h) <= 0 ||
      CopyBuffer(m8l, 0, 0, periods, ma8l) <= 0)
      Print("Failed to copy " + IntegerToString(period1) + "MA. ", GetLastError());
   if(CopyBuffer(m13h, 0, 0, periods, ma13h) <= 0 ||
      CopyBuffer(m13l, 0, 0, periods, ma13l) <= 0)
      Print("Failed to copy " + IntegerToString(period2) + "MA. ", GetLastError());
   if(CopyBuffer(m21h, 0, 0, periods, ma21h) <= 0 ||
      CopyBuffer(m21l, 0, 0, periods, ma21l) <= 0)
      Print("Failed to copy " + IntegerToString(period3) + "MA. ", GetLastError());
   if(CopyBuffer(m34h, 0, 0, periods, ma34h) <= 0 ||
      CopyBuffer(m34l, 0, 0, periods, ma34l) <= 0)
      Print("Failed to copy " + IntegerToString(period4) + "MA. ", GetLastError());
      
   ArraySetAsSeries(ma44h, true); ArraySetAsSeries(ma44l, true);
   ArraySetAsSeries(ma55h, true); ArraySetAsSeries(ma55l, true);
	for (int x = periods; x >= 0; x--) {
		ma44h[x] = MathMax(ma8h[x], ma13h[x]);
		ma55h[x] = MathMax(ma21h[x], ma34h[x]);
		ma44l[x] = MathMin(ma8l[x], ma13l[x]);
		ma55l[x] = MathMin(ma21l[x], ma34l[x]);
	}
   
   ArraySetAsSeries(ma45h, true); ArraySetAsSeries(ma45l, true);
	for (int x = periods; x >= 0; x--) {
		ma45h[x] = MathMax(ma44h[x], ma55h[x]);
		ma45l[x] = MathMin(ma44l[x], ma55l[x]);
	}

   ArraySetAsSeries(ma54, true); ArraySetAsSeries(ma54a, true); ArraySetAsSeries(ma54b, true); ArraySetAsSeries(ma53, true); ArraySetAsSeries(ma52, true);
   ArrayInitialize(ma54, 0); ArrayInitialize(ma54a, 0); ArrayInitialize(ma54b, 0); ArrayInitialize(ma53, 0); ArrayInitialize(ma52, 0);
	for (int x = periods - 1; x >= 0; x--) {
		ma54[x] = (ma45h[x] + ma45l[x]) / 2;
		ma54a[x] = ma54[x]; ma54b[x] = ma54[x];
		ma53[x] = (ma54[x] + iHigh(_Symbol, PERIOD_CURRENT, x + 1)) / 2;
		ma52[x] = (ma54[x] + iLow(_Symbol, PERIOD_CURRENT, x + 1)) / 2;
		if (ma53[x] < ma54[x]) ma53[x] = ma54[x];
		if (ma52[x] > ma54[x]) ma52[x] = ma54[x];
	}
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION-----------------------------------------------------+
void BingoH1() {
	double maN8h[1], maN8l[1], maN13h[1], maN13l[1], maN21h[1], maN21l[1], maN34h[1], maN34l[1];

   ArrayInitialize(maN8h, 0); ArrayInitialize(maN8l, 0); ArrayInitialize(maN13h, 0); ArrayInitialize(maN13l, 0); ArrayInitialize(maN21h, 0); ArrayInitialize(maN21l, 0); ArrayInitialize(maN34h, 0); ArrayInitialize(maN34l, 0);

   if(CopyBuffer(N8hh1, 0, 0, 1, maN8h) <= 0 ||
      CopyBuffer(N8lh1, 0, 0, 1, maN8l) <= 0)
      Print("Failed to copy " + IntegerToString(period1) + "MA H1. ", GetLastError());
   if(CopyBuffer(N13hh1, 0, 0, 1, maN13h) <= 0 ||
      CopyBuffer(N13lh1, 0, 0, 1, maN13l) <= 0)
      Print("Failed to copy " + IntegerToString(period2) + "MA H1. ", GetLastError());
   if(CopyBuffer(N21hh1, 0, 0, 1, maN21h) <= 0 ||
      CopyBuffer(N21lh1, 0, 0, 1, maN21l) <= 0)
      Print("Failed to copy " + IntegerToString(period3) + "MA H1. ", GetLastError());
   if(CopyBuffer(N34hh1, 0, 0, 1, maN34h) <= 0 ||
      CopyBuffer(N34lh1, 0, 0, 1, maN34l) <= 0)
      Print("Failed to copy " + IntegerToString(period4) + "MA H1. ", GetLastError());
   
	double maN44h, maN55h, maN44l, maN55l;

	maN44h = MathMax(maN8h[0], maN13h[0]);
	maN55h = MathMax(maN21h[0], maN34h[0]);
	maN44l = MathMin(maN8l[0], maN13l[0]);
	maN55l = MathMin(maN21l[0], maN34l[0]);

	double maN45h, maN45l;

	maN45h = MathMax(maN44h, maN55h);
	maN45l = MathMin(maN44l, maN55l);

	double maN54, maN53, maN52;

	maN54 = (maN45h + maN45l) / 2;
	maN53 = (maN54 + iHigh(_Symbol, PERIOD_H1, 1)) / 2;
	maN52 = (maN54 + iLow(_Symbol, PERIOD_H1, 1)) / 2;
	if (maN53 < maN54) maN53 = maN54;
	if (maN52 > maN54) maN52 = maN54;

	if ((iClose(_Symbol, PERIOD_H1, 0) > maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(1) + " H1", CharToString(174), 12, "Wingdings", clrBlue); else ObjectSetTextMQL4(Name + IntegerToString(1) + " H1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H1, 0) > maN54) && (iClose(_Symbol, PERIOD_H1, 0) < maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(2) + " H1", CharToString(233), 12, "Wingdings", clrSteelBlue); else ObjectSetTextMQL4(Name + IntegerToString(2) + " H1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H1, 0) < maN54) && (iClose(_Symbol, PERIOD_H1, 0) > maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(3) + " H1", CharToString(234), 12, "Wingdings", clrIndianRed); else ObjectSetTextMQL4(Name + IntegerToString(3) + " H1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H1, 0) < maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(4) + " H1", CharToString(174), 12, "Wingdings", clrRed); else ObjectSetTextMQL4(Name + IntegerToString(4) + " H1", " ", 9, "Arial", clrBlack);

	for (int x = 1; x <= 4; x++) {
		ObjectSetString(0, Name + IntegerToString(x) + " H1", OBJPROP_TOOLTIP, "H: " + DoubleToString(maN45h, _Digits) + " / M: " + DoubleToString(maN54, _Digits) + " / L: " + DoubleToString(maN45l, _Digits));
	}
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION-----------------------------------------------------+
void BingoH() {
	double maN8h[1], maN8l[1], maN13h[1], maN13l[1], maN21h[1], maN21l[1], maN34h[1], maN34l[1];

   ArrayInitialize(maN8h, 0); ArrayInitialize(maN8l, 0); ArrayInitialize(maN13h, 0); ArrayInitialize(maN13l, 0); ArrayInitialize(maN21h, 0); ArrayInitialize(maN21l, 0); ArrayInitialize(maN34h, 0); ArrayInitialize(maN34l, 0);

   if(CopyBuffer(N8hh4, 0, 0, 1, maN8h) <= 0 ||
      CopyBuffer(N8lh4, 0, 0, 1, maN8l) <= 0)
      Print("Failed to copy " + IntegerToString(period1) + "MA H4. ", GetLastError());
   if(CopyBuffer(N13hh4, 0, 0, 1, maN13h) <= 0 ||
      CopyBuffer(N13lh4, 0, 0, 1, maN13l) <= 0)
      Print("Failed to copy " + IntegerToString(period2) + "MA H4. ", GetLastError());
   if(CopyBuffer(N21hh4, 0, 0, 1, maN21h) <= 0 ||
      CopyBuffer(N21lh4, 0, 0, 1, maN21l) <= 0)
      Print("Failed to copy " + IntegerToString(period3) + "MA H4. ", GetLastError());
   if(CopyBuffer(N34hh4, 0, 0, 1, maN34h) <= 0 ||
      CopyBuffer(N34lh4, 0, 0, 1, maN34l) <= 0)
      Print("Failed to copy " + IntegerToString(period4) + "MA H4. ", GetLastError());
   
	double maN44h, maN55h, maN44l, maN55l;

	maN44h = MathMax(maN8h[0], maN13h[0]);
	maN55h = MathMax(maN21h[0], maN34h[0]);
	maN44l = MathMin(maN8l[0], maN13l[0]);
	maN55l = MathMin(maN21l[0], maN34l[0]);

	double maN45h, maN45l;

	maN45h = MathMax(maN44h, maN55h);
	maN45l = MathMin(maN44l, maN55l);

	double maN54, maN53, maN52;

	maN54 = (maN45h + maN45l) / 2;
	maN53 = (maN54 + iHigh(_Symbol, PERIOD_H4, 1)) / 2;
	maN52 = (maN54 + iLow(_Symbol, PERIOD_H4, 1)) / 2;
	if (maN53 < maN54) maN53 = maN54;
	if (maN52 > maN54) maN52 = maN54;

	if ((iClose(_Symbol, PERIOD_H4, 0) > maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(1) + " H4", CharToString(174), 12, "Wingdings", clrBlue); else ObjectSetTextMQL4(Name + IntegerToString(1) + " H4", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H4, 0) > maN54) && (iClose(_Symbol, PERIOD_H4, 0) < maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(2) + " H4", CharToString(233), 12, "Wingdings", clrSteelBlue); else ObjectSetTextMQL4(Name + IntegerToString(2) + " H4", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H4, 0) < maN54) && (iClose(_Symbol, PERIOD_H4, 0) > maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(3) + " H4", CharToString(234), 12, "Wingdings", clrIndianRed); else ObjectSetTextMQL4(Name + IntegerToString(3) + " H4", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H4, 0) < maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(4) + " H4", CharToString(174), 12, "Wingdings", clrRed); else ObjectSetTextMQL4(Name + IntegerToString(4) + " H4", " ", 9, "Arial", clrBlack);

	if (iClose(_Symbol, PERIOD_H4, 0) > maN54) H4[0] = 1; else H4[0] = 0;

	for (int x = 1; x <= 4; x++) {
		ObjectSetString(0, Name + IntegerToString(x) + " H4", OBJPROP_TOOLTIP, "H: " + DoubleToString(maN45h, _Digits) + " / M: " + DoubleToString(maN54, _Digits) + " / L: " + DoubleToString(maN45l, _Digits));
	}

	H1H = maN45h; H1M = maN54; H1L = maN45l;
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION-----------------------------------------------------+
void BingoD() {
	double maN8h[1], maN8l[1], maN13h[1], maN13l[1], maN21h[1], maN21l[1], maN34h[1], maN34l[1];

   ArrayInitialize(maN8h, 0); ArrayInitialize(maN8l, 0); ArrayInitialize(maN13h, 0); ArrayInitialize(maN13l, 0); ArrayInitialize(maN21h, 0); ArrayInitialize(maN21l, 0); ArrayInitialize(maN34h, 0); ArrayInitialize(maN34l, 0);

   if(CopyBuffer(N8hd1, 0, 0, 1, maN8h) <= 0 ||
      CopyBuffer(N8ld1, 0, 0, 1, maN8l) <= 0)
      Print("Failed to copy " + IntegerToString(period1) + "MA D1. ", GetLastError());
   if(CopyBuffer(N13hd1, 0, 0, 1, maN13h) <= 0 ||
      CopyBuffer(N13ld1, 0, 0, 1, maN13l) <= 0)
      Print("Failed to copy " + IntegerToString(period2) + "MA D1. ", GetLastError());
   if(CopyBuffer(N21hd1, 0, 0, 1, maN21h) <= 0 ||
      CopyBuffer(N21ld1, 0, 0, 1, maN21l) <= 0)
      Print("Failed to copy " + IntegerToString(period3) + "MA D1. ", GetLastError());
   if(CopyBuffer(N34hd1, 0, 0, 1, maN34h) <= 0 ||
      CopyBuffer(N34ld1, 0, 0, 1, maN34l) <= 0)
      Print("Failed to copy " + IntegerToString(period4) + "MA D1. ", GetLastError());
   
	double maN44h, maN55h, maN44l, maN55l;

	maN44h = MathMax(maN8h[0], maN13h[0]);
	maN55h = MathMax(maN21h[0], maN34h[0]);
	maN44l = MathMin(maN8l[0], maN13l[0]);
	maN55l = MathMin(maN21l[0], maN34l[0]);

	double maN45h, maN45l;

	maN45h = MathMax(maN44h, maN55h);
	maN45l = MathMin(maN44l, maN55l);

	double maN54, maN53, maN52;

	maN54 = (maN45h + maN45l) / 2;
	maN53 = (maN54 + iHigh(_Symbol, PERIOD_D1, 1)) / 2;
	maN52 = (maN54 + iLow(_Symbol, PERIOD_D1, 1)) / 2;
	if (maN53 < maN54) maN53 = maN54;
	if (maN52 > maN54) maN52 = maN54;

	if ((iClose(_Symbol, PERIOD_D1, 0) > maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(1) + " D1", CharToString(174), 12, "Wingdings", clrBlue); else ObjectSetTextMQL4(Name + IntegerToString(1) + " D1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_D1, 0) > maN54) && (iClose(_Symbol, PERIOD_D1, 0) < maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(2) + " D1", CharToString(233), 12, "Wingdings", clrSteelBlue); else ObjectSetTextMQL4(Name + IntegerToString(2) + " D1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_D1, 0) < maN54) && (iClose(_Symbol, PERIOD_D1, 0) > maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(3) + " D1", CharToString(234), 12, "Wingdings", clrIndianRed); else ObjectSetTextMQL4(Name + IntegerToString(3) + " D1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_D1, 0) < maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(4) + " D1", CharToString(174), 12, "Wingdings", clrRed); else ObjectSetTextMQL4(Name + IntegerToString(4) + " D1", " ", 9, "Arial", clrBlack);

	if (iClose(_Symbol, PERIOD_D1, 0) > maN54) D1[0] = 1; else D1[0] = 0;

	for (int x = 1; x <= 4; x++) {
		ObjectSetString(0, Name + IntegerToString(x) + " D1", OBJPROP_TOOLTIP, "H: " + DoubleToString(maN45h, _Digits) + " / M: " + DoubleToString(maN54, _Digits) + " / L: " + DoubleToString(maN45l, _Digits));
	}

	D1H = maN45h; D1M = maN54; D1L = maN45l;
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION-----------------------------------------------------+
void BingoW() {
	double maN8h[1], maN8l[1], maN13h[1], maN13l[1], maN21h[1], maN21l[1], maN34h[1], maN34l[1];

   ArrayInitialize(maN8h, 0); ArrayInitialize(maN8l, 0); ArrayInitialize(maN13h, 0); ArrayInitialize(maN13l, 0); ArrayInitialize(maN21h, 0); ArrayInitialize(maN21l, 0); ArrayInitialize(maN34h, 0); ArrayInitialize(maN34l, 0);

   if(CopyBuffer(N8hw1, 0, 0, 1, maN8h) <= 0 ||
      CopyBuffer(N8lw1, 0, 0, 1, maN8l) <= 0)
      Print("Failed to copy " + IntegerToString(period1) + "MA W1. ", GetLastError());
   if(CopyBuffer(N13hw1, 0, 0, 1, maN13h) <= 0 ||
      CopyBuffer(N13lw1, 0, 0, 1, maN13l) <= 0)
      Print("Failed to copy " + IntegerToString(period2) + "MA W1. ", GetLastError());
   if(CopyBuffer(N21hw1, 0, 0, 1, maN21h) <= 0 ||
      CopyBuffer(N21lw1, 0, 0, 1, maN21l) <= 0)
      Print("Failed to copy " + IntegerToString(period3) + "MA W1. ", GetLastError());
   if(CopyBuffer(N34hw1, 0, 0, 1, maN34h) <= 0 ||
      CopyBuffer(N34lw1, 0, 0, 1, maN34l) <= 0)
      Print("Failed to copy " + IntegerToString(period4) + "MA W1. ", GetLastError());
   
	double maN44h, maN55h, maN44l, maN55l;

	maN44h = MathMax(maN8h[0], maN13h[0]);
	maN55h = MathMax(maN21h[0], maN34h[0]);
	maN44l = MathMin(maN8l[0], maN13l[0]);
	maN55l = MathMin(maN21l[0], maN34l[0]);

	double maN45h, maN45l;

	maN45h = MathMax(maN44h, maN55h);
	maN45l = MathMin(maN44l, maN55l);

	double maN54, maN53, maN52;

	maN54 = (maN45h + maN45l) / 2;
	maN53 = (maN54 + iHigh(_Symbol, PERIOD_W1, 1)) / 2;
	maN52 = (maN54 + iLow(_Symbol, PERIOD_W1, 1)) / 2;
	if (maN53 < maN54) maN53 = maN54;
	if (maN52 > maN54) maN52 = maN54;

	if ((iClose(_Symbol, PERIOD_W1, 0) > maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(1) + " W1", CharToString(174), 12, "Wingdings", clrBlue); else ObjectSetTextMQL4(Name + IntegerToString(1) + " W1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_W1, 0) > maN54) && (iClose(_Symbol, PERIOD_W1, 0) < maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(2) + " W1", CharToString(233), 12, "Wingdings", clrSteelBlue); else ObjectSetTextMQL4(Name + IntegerToString(2) + " W1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_W1, 0) < maN54) && (iClose(_Symbol, PERIOD_W1, 0) > maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(3) + " W1", CharToString(234), 12, "Wingdings", clrIndianRed); else ObjectSetTextMQL4(Name + IntegerToString(3) + " W1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_W1, 0) < maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(4) + " W1", CharToString(174), 12, "Wingdings", clrRed); else ObjectSetTextMQL4(Name + IntegerToString(4) + " W1", " ", 9, "Arial", clrBlack);

	for (int x = 1; x <= 4; x++) {
		ObjectSetString(0, Name + IntegerToString(x) + " W1", OBJPROP_TOOLTIP, "H: " + DoubleToString(maN45h, _Digits) + " / M: " + DoubleToString(maN54, _Digits) + " / L: " + DoubleToString(maN45l, _Digits));
	}

	W1H = maN45h; W1M = maN54; W1L = maN45l;
}
//+------------------------------------------------------------------+

//+MARKS ON CHART----------------------------------------------------+
void marks() {
	string obname;
	//Weekly
	obname = Name + "ArrW1Up";
	ArrowPrice(obname, W1H, iTime(_Symbol,_Period,0) + 5 * Period(), clrNavy);
	//if(Bid < W1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "W1 High Wave " + DoubleToString(W1H, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 3);
	obname = Name + "ArrW1Mid";
	ArrowPrice(obname, W1M, iTime(_Symbol,_Period,0) + 5 * Period(), clrNavy);
	//if(Bid < W1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "W1 Mid Wave " + DoubleToString(W1M, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 4);
	obname = Name + "ArrW1Dn";
	ArrowPrice(obname, W1L, iTime(_Symbol,_Period,0) + 5 * Period(), clrNavy);
	//if(Bid < W1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "W1 Low Wave " + DoubleToString(W1L, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 3);

	//Daily
	obname = Name + "ArrD1Up";
	ArrowPrice(obname, D1H, iTime(_Symbol,_Period,0) + 5 * Period(), clrDeepSkyBlue);
	//if(Bid < D1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "D1 High Wave " + DoubleToString(D1H, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
	obname = Name + "ArrD1Mid";
	ArrowPrice(obname, D1M, iTime(_Symbol,_Period,0) + 5 * Period(), clrDeepSkyBlue);
	//if(Bid < D1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "D1 Mid Wave " + DoubleToString(D1M, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 3);
	obname = Name + "ArrD1Dn";
	ArrowPrice(obname, D1L, iTime(_Symbol,_Period,0) + 5 * Period(), clrDeepSkyBlue);
	//if(Bid < D1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "D1 Low Wave " + DoubleToString(D1L, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);

	//H4
	obname = Name + "ArrH1Up";
	ArrowPrice(obname, H1H, iTime(_Symbol,_Period,0) + 5 * Period(), clrGray);
	//if(Bid < H1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "H4 High Wave " + DoubleToString(H1H, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
	obname = Name + "ArrH1Mid";
	ArrowPrice(obname, H1M, iTime(_Symbol,_Period,0) + 5 * Period(), clrGray);
	//if(Bid < H1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "H4 Mid Wave " + DoubleToString(H1M, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
	obname = Name + "ArrH1Dn";
	ArrowPrice(obname, H1L, iTime(_Symbol,_Period,0) + 5 * Period(), clrGray);
	//if(Bid < H1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "H4 Low Wave " + DoubleToString(H1L, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
}
//+------------------------------------------------------------------+
/*
//+------------------------------------------------------------------+
void TradeInfo()
{
	int Total_Buy_Trades = 0;
	double Total_Buy_Size = 0, Total_Buy_Price = 0, Buy_Profit = 0;

	int Total_Sell_Trades = 0;
	double Total_Sell_Size = 0, Total_Sell_Price = 0, Sell_Profit = 0;

	int Net_Trades = 0;
	double Net_Lots = 0, Net_Result = 0;

	double Average_Price = 0, distance = 0;
	double Pip_Value = MarketInfo(Symbol(), MODE_TICKVALUE)*PipAdjust;
	double Pip_Size = MarketInfo(Symbol(), MODE_TICKSIZE)*PipAdjust;

	int total = OrdersTotal();

	for (int i = 0; i < total; i++)
	{
		int ord = OrderSelect(i, SELECT_BY_POS, MODE_TRADES);
		{
			if (OrderType() == OP_BUY && OrderSymbol() == Symbol())
			{
				Total_Buy_Trades++;
				Total_Buy_Price += OrderOpenPrice()*OrderLots();
				Total_Buy_Size += OrderLots();
				Buy_Profit += OrderProfit() + OrderSwap() + OrderCommission();
			}
			if (OrderType() == OP_SELL && OrderSymbol() == Symbol())
			{
				Total_Sell_Trades++;
				Total_Sell_Size += OrderLots();
				Total_Sell_Price += OrderOpenPrice()*OrderLots();
				Sell_Profit += OrderProfit() + OrderSwap() + OrderCommission();
			}
		}
	}
	if (Total_Buy_Price > 0)
	{
		Total_Buy_Price /= Total_Buy_Size;
	}
	if (Total_Sell_Price > 0)
	{
		Total_Sell_Price /= Total_Sell_Size;
	}

	Net_Trades = Total_Buy_Trades + Total_Sell_Trades;
	Net_Lots = Total_Buy_Size - Total_Sell_Size;
	Net_Result = Buy_Profit + Sell_Profit;

	if (Net_Trades > 0 && Net_Lots != 0)
	{
		distance = (Net_Result / (MathAbs(Net_Lots*MarketInfo(Symbol(), MODE_TICKVALUE)))*MarketInfo(Symbol(), MODE_TICKSIZE));
		if (Net_Lots > 0)
		{
			Average_Price = Bid - distance;
		}
		if (Net_Lots < 0)
		{
			Average_Price = Ask + distance;
		}
	}
	if (Net_Trades > 0 && Net_Lots == 0)
	{
		distance = (Net_Result / ((MarketInfo(Symbol(), MODE_TICKVALUE)))*MarketInfo(Symbol(), MODE_TICKSIZE));
		Average_Price = NormalizeDouble(Bid - distance, _Digits);
	}

	color cl = clrBlue;
	if (Net_Lots < 0) cl = clrRed;
	if (Net_Lots == 0) cl = clrWhite;

	if (Average_Price != 0 && ObjectFind(Name + "Average_Price_Line_" + _Symbol) < 0) {
		ObjectCreate(Name + "Average_Price_Line_" + _Symbol, OBJ_HLINE, 0, 0, Average_Price);
		ObjectSet(Name + "Average_Price_Line_" + _Symbol, OBJPROP_WIDTH, 2);
	}

	if (Average_Price != 0) {
		ObjectSet(Name + "Average_Price_Line_" + _Symbol, OBJPROP_PRICE1, Average_Price);
		ObjectSet(Name + "Average_Price_Line_" + _Symbol, OBJPROP_COLOR, cl);
		if (Total_Buy_Trades >= 1) ObjectSetTextMQL4(Name + " BuyPos", "Buy: " + DoubleToString(Bid - distance, _Digits) + " / " + DoubleToString(Total_Buy_Size, 2) + " / " + DoubleToString(Buy_Profit, 2), 7, "Arial", clrBlue);
		if (Total_Sell_Trades >= 1) ObjectSetTextMQL4(Name + " SellPos", "Sell: " + DoubleToString(Ask + distance, _Digits) + " / " + DoubleToString(Total_Sell_Size, 2) + " / " + DoubleToString(Sell_Profit, 2), 7, "Arial", clrRed);
		if (Total_Sell_Trades >= 1 && Total_Buy_Trades >= 1) ObjectSetTextMQL4(Name + " AvgPos", "Avg: " + DoubleToString(Average_Price, _Digits) + " / " + DoubleToString(Net_Lots, 2) + " / " + DoubleToString(Net_Result, 2), 7, "Arial", clrBlack);
	}
	else { ObjectSetTextMQL4(Name + " BuyPos", "", 7, "Arial", clrBlue); ObjectSetTextMQL4(Name + " SellPos", "", 7, "Arial", clrRed); ObjectSetTextMQL4(Name + " AvgPos", "", 7, "Arial", clrRed); ObjectDelete(Name + "Average_Price_Line_" + _Symbol); }
}
//+------------------------------------------------------------------+
*/

//+ARROWPRICE--------------------------------------------------------+
void ArrowPrice(const string name, const double x, const datetime y, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW_RIGHT_PRICE, 0, 0, 0))
		{
			Print("error: can't create arrow_right_price! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE, x);
	ObjectSetInteger(0, name, OBJPROP_TIME, y);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "SL Price: " + DoubleToString(x, _Digits));
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
	const int corner,
	const int x,
	const int y,
	const string label,
	const int FSize,
	const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetTextMQL4(name, label, FSize, "Arial", FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

/*
//+TRADE CHECK-------------------------------------------------------+
bool trades_on_symbol(string symbol)
{
	for (int i = OrdersTotal() - 1; OrderSelect(i, SELECT_BY_POS); i--)
		if (OrderSymbol() == symbol && OrderType() < 2)
			return true;
	return false;
}
//+------------------------------------------------------------------+
*/


bool ObjectSetTextMQL4(string name,
                       string text,
                       int font_size,
                       string font="",
                       color text_color=CLR_NONE)
  {
   int tmpObjType=(int)ObjectGetInteger(0,name,OBJPROP_TYPE);
   if(tmpObjType!=OBJ_LABEL && tmpObjType!=OBJ_TEXT) return(false);
   if(StringLen(text)>0 && font_size>0)
     {
      if(ObjectSetString(0,name,OBJPROP_TEXT,text)==true
         && ObjectSetInteger(0,name,OBJPROP_FONTSIZE,font_size)==true)
        {
         if((StringLen(font)>0)
            && ObjectSetString(0,name,OBJPROP_FONT,font)==false)
            return(false);
         if(text_color>-1
            && ObjectSetInteger(0,name,OBJPROP_COLOR,text_color)==false)
            return(false);
         return(true);
        }
      return(false);
     }
   return(false);
  }
  