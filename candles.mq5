//+------------------------------------------------------------------+
//|  DEEZNUTS_ColorBars.mq5                                          |
//|  MQL5 version with color bars                                    |
//|  Green bars when (FastMA-SlowMA) > 0, Red bars when ≤ 0         |
//+------------------------------------------------------------------+
#property copyright "Converted to MQL5"
#property link      ""
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 5
#property indicator_plots   1

//--- plot ColorBars
#property indicator_label1  "DEEZNUTS"
#property indicator_type1   DRAW_COLOR_CANDLES
#property indicator_color1  clrGreen,clrRed
#property indicator_style1  STYLE_SOLID
#property indicator_width1  1

//─── user inputs ────────────────────────────────────────────────────
input int FastLength   = 1;     // Fast MA period (≥1)
input int SlowLength   = 5;     // Slow MA period (> FastLength)
input int SmoothLength = 3;     // Wilder smoothing of (fast-slow); 1 = off
input ENUM_MA_METHOD MAMethod = MODE_EMA;  // MA Method

//─── indicator buffers ─────────────────────────────────────────────
double OpenBuffer[];
double HighBuffer[];
double LowBuffer[];
double CloseBuffer[];
double ColorBuffer[];

//─── work arrays ───────────────────────────────────────────────────
double rawOsc[];
int fastMA_handle;
int slowMA_handle;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- indicator buffers mapping
   SetIndexBuffer(0, OpenBuffer);
   SetIndexBuffer(1, HighBuffer);
   SetIndexBuffer(2, LowBuffer);
   SetIndexBuffer(3, CloseBuffer);
   SetIndexBuffer(4, ColorBuffer, INDICATOR_COLOR_INDEX);

   //--- set drawing style
   PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_COLOR_CANDLES);
   PlotIndexSetInteger(0, PLOT_COLOR_INDEXES, 2);
   PlotIndexSetInteger(0, PLOT_LINE_COLOR, 0, clrDodgerBlue);
   PlotIndexSetInteger(0, PLOT_LINE_COLOR, 1, clrViolet);

   //--- create MA handles
   fastMA_handle = iMA(_Symbol, _Period, FastLength, 0, MAMethod, PRICE_OPEN);
   slowMA_handle = iMA(_Symbol, _Period, SlowLength, 0, MAMethod, PRICE_OPEN);

   if(fastMA_handle == INVALID_HANDLE || slowMA_handle == INVALID_HANDLE)
   {
      Print("Error creating MA handles");
      return(INIT_FAILED);
   }

   //--- allocate work array
   ArrayResize(rawOsc, 1000);

   //--- set indicator name
   string short_name = StringFormat("DEEZNUTS ColorBars (%d,%d,%d)",
                                   FastLength, SlowLength, SmoothLength);
   IndicatorSetString(INDICATOR_SHORTNAME, short_name);

   //--- set digits
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits);

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- release handles
   if(fastMA_handle != INVALID_HANDLE)
      IndicatorRelease(fastMA_handle);
   if(slowMA_handle != INVALID_HANDLE)
      IndicatorRelease(slowMA_handle);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   //--- check for minimum bars
   if(rates_total < SlowLength + SmoothLength + 5)
      return(0);
   
   //--- determine starting position
   int start = (prev_calculated == 0) ? SlowLength + SmoothLength : prev_calculated - 1;

   //--- resize work array if needed
   if(ArraySize(rawOsc) < rates_total)
   {
      ArrayResize(rawOsc, rates_total);
   }

   //--- get MA data
   double fastMA[], slowMA[];

   int copied1 = CopyBuffer(fastMA_handle, 0, 0, rates_total, fastMA);
   int copied2 = CopyBuffer(slowMA_handle, 0, 0, rates_total, slowMA);

   if(copied1 <= 0 || copied2 <= 0)
      return(0);

   //--- main calculation loop
   for(int i = start; i < rates_total; i++)
   {
      //── 1. Calculate raw oscillator
      rawOsc[i] = fastMA[i] - slowMA[i];

      //── 2. Optional Wilder smoothing (SMMA)
      double osc;
      if(SmoothLength <= 1)
      {
         osc = rawOsc[i];
      }
      else
      {
         // Calculate simple moving average for smoothing
         if(i < SmoothLength - 1)
         {
            osc = rawOsc[i]; // Not enough data for smoothing
         }
         else
         {
            double sum = 0;
            for(int j = 0; j < SmoothLength; j++)
               sum += rawOsc[i - j];
            osc = sum / SmoothLength;
         }
      }

      //── 3. Set candle data and color based on oscillator
      OpenBuffer[i] = open[i];
      HighBuffer[i] = high[i];
      LowBuffer[i] = low[i];
      CloseBuffer[i] = close[i];

      // Color logic: Green (0) when osc > 0, Red (1) when osc <= 0
      if(osc > 0.0)
         ColorBuffer[i] = 0;  // Green
      else
         ColorBuffer[i] = 1;  // Red
   }
   
   //--- return value of prev_calculated for next call
   return(rates_total);
}
//+------------------------------------------------------------------+
