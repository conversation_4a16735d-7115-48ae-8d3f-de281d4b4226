//+------------------------------------------------------------------+
//|                                     ET Dots Only (MQL5)         |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "1.01"


#property indicator_chart_window
#property indicator_buffers 1
#property indicator_plots   1

#property indicator_type1 DRAW_NONE
#property indicator_style1 STYLE_SOLID
#property indicator_width1 5
#property indicator_color1 clrAqua

//--- Indicator buffers
double midBuffer[];    // calculation buffer for Donchian midline

//+------------------------------------------------------------------+
int OnInit()
  {
   //--- Bind calculation buffer for Donchian midline
   SetIndexBuffer(0, midBuffer, INDICATOR_DATA);

   return(INIT_SUCCEEDED);
  }

void OnDeinit(const int reason)
{
ObjectsDeleteAll(0, "ETA");
}
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long   &tick_volume[],
                const long   &volume[],
                const int    &spread[])
  {
   //--- First loop: Calculate all Donchian midlines
   bool ArrowUp[];
   bool ArrowDown[];
   ArrayResize(ArrowUp, 525);
   ArrayResize(ArrowDown, 525);
   ArrayInitialize(ArrowUp, false);
   ArrayInitialize(ArrowDown, false);
   ArraySetAsSeries(midBuffer, true);
   ArraySetAsSeries(ArrowUp, true);
   ArraySetAsSeries(ArrowDown, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   
   static datetime checkt = 0;
   bool check = false;
   if (checkt < iTime(_Symbol, PERIOD_CURRENT, 0))
   {
      checkt = iTime(_Symbol, PERIOD_CURRENT, 0);
      check = true;
   }
   if (check)
   {
      for(int i = 521; i >= 0; i--)
      {
         midBuffer[i] = iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 20, i + 6)) + 0.5 * (iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 20, i + 6)) - iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 20, i + 6)));
        
         if (iOpen(_Symbol, PERIOD_CURRENT, i + 1) < midBuffer[i + 1] && iOpen(_Symbol, PERIOD_CURRENT, i) > midBuffer[i])
         ArrowUp[i] = true;
         if (iOpen(_Symbol, PERIOD_CURRENT, i + 2) < midBuffer[i + 2] && iOpen(_Symbol, PERIOD_CURRENT, i + 1) > midBuffer[i + 1] && iOpen(_Symbol, PERIOD_CURRENT, i) > midBuffer[i])
         ArrowUp[i] = true;
         if (iOpen(_Symbol, PERIOD_CURRENT, i + 1) > midBuffer[i + 1] && iOpen(_Symbol, PERIOD_CURRENT, i) < midBuffer[i])
         ArrowDown[i] = true;
         if (iOpen(_Symbol, PERIOD_CURRENT, i + 2) > midBuffer[i + 2] && iOpen(_Symbol, PERIOD_CURRENT, i + 1) < midBuffer[i + 1] && iOpen(_Symbol, PERIOD_CURRENT, i) < midBuffer[i])
         ArrowDown[i] = true;
      }
      
      for(int i = 521; i >= 0; i--)
        {
            //--- Plot arrows
            if(ArrowUp[i] == true)
            {
               string obname = "ETArrU" + IntegerToString(i);
               burnarr(obname, low[i] - 50 * _Point, 108, i, clrBlue);
            }
            if(ArrowDown[i] == true)
            {
               string obname = "ETArrD" + IntegerToString(i);
               burnarr(obname, high[i] + 50 * _Point, 108, i, clrRed);
            }
        }
   }
   
   return(rates_total);
  }
//+------------------------------------------------------------------+

//+ARROW CREATE------------------------------------------------------+
void burnarr(string name, double p, int arrow, int t, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
   	datetime Time[];
   	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
   	ArraySetAsSeries(Time, true);
   	CopyTime(_Symbol, _Period, 0, counta, Time);
	ObjectSetInteger(0, name, OBJPROP_TIME, 0, Time[t]);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 0, p);
	ObjectSetInteger(0, name, OBJPROP_ARROWCODE, arrow);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 0);
}
//+------------------------------------------------------------------+