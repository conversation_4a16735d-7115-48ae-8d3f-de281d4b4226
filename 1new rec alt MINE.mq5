#property copyright "sakisf"
#property link      "none"
#property version   "1.00"
#property indicator_plots   1
#property strict
#property indicator_chart_window
#property indicator_buffers 3

//Daily Pips

input string Dail = ""; // Daily inputs pips

input int TradeZoneS = -1; // Daily TZ L
input int TradeZoneE = 1; // Daily TZ H
input int LowP = -2; // High % Area L
input int HighP = 2; // High % Area H
input int Lt30pctZL = -7; // Less than 30% Zone L
input int Lt30pctZH = 7; // Less than 30% Zone H
int LowExtremeS = LowP-1; // Low Extreme Start
input int LowExtremeE = -15; // Low Extreme End 
input int LowSL = -18; // Low Max SL
int HighExtremeS = HighP+1; // High Extreme Start
input int HighExtremeE = 15; // High Extreme End
input int HighSL = 18; // High Max SL
input double SDd = 1; // SD Day down
input double SDu = 1; // SD Day up

/*
//Daily Percentage

input string aDail = ""; // Daily inputs perc

input int aTradeZoneS = -1; // Daily TZ L
input int aTradeZoneE = 1; // Daily TZ H
input int aLowP = -2; // High % Area L
input int aHighP = 2; // High % Area H
input int aLt30pctZL = -7; // Less than 30% Zone L
input int aLt30pctZH = 7; // Less than 30% Zone H
int aLowExtremeS = aLowP-1; // Low Extreme Start
input int aLowExtremeE = -15; // Low Extreme End 
input int aLowSL = -18; // Low Max SL
int aHighExtremeS = aHighP+1; // High Extreme Start
input int aHighExtremeE = 15; // High Extreme End
input int aHighSL = 18; // High Max SL
*/

//Weekly Pips

input string Weekl = ""; // Weekly inputs pips

input int TradeZoneWS = -2; // Weekly TZ L
input int TradeZoneWE = 2; // Weekly TZ H
input int LowWP = -5; // W High % Area L
input int HighWP = 5; // W High % Area H
input int Lt30pctZWL = -7; // Less than 30% W Zone L
input int Lt30pctZWH = 7; // Less than 30% W Zone H
int LowExtremeWS = LowWP-1; // W Low Extreme Start
input int LowExtremeWE = -12; // W Low Extreme End 
int HighExtremeWS = HighWP+1; // W High Extreme Start
input int HighExtremeWE = 12; // W High Extreme End
input double SDWd = 1; // SD Week down
input double SDWu = 1; // SD Week up

/*
//Weekly Percentage

input string aWeekl = ""; // Weekly inputs perc

input int aTradeZoneWS = -2; // Weekly TZ L
input int aTradeZoneWE = 2; // Weekly TZ H
input int aLowWP = -5; // W High % Area L
input int aHighWP = 5; // W High % Area H
input int aLt30pctZWL = -7; // Less than 30% W Zone L
input int aLt30pctZWH = 7; // Less than 30% W Zone H
int aLowExtremeWS = LowWP-1; // W Low Extreme Start
input int aLowExtremeWE = -12; // W Low Extreme End 
int aHighExtremeWS = HighWP+1; // W High Extreme Start
input int aHighExtremeWE = 12; // W High Extreme End
*/

//Additional Options

input string Options = ""; //Additional Options

input int CheckSeconds=5;// Re-check Mid Every x Seconds

//ENUM_MQL_INFO_STRING Name=MQLInfoString(MQL_PROGRAM_NAME);
string Name=MQLInfoString(MQL_PROGRAM_NAME);

string sym1 = Symbol();
string sym2 = StringSubstr(sym1,0,2);

//Colors

input string DayCol = ""; // Daily Colors
input color MidColor = clrGoldenrod; // Daily / Weekly Mid Color
input color DTZColor = clrAqua; // Daily Trade Zone Color
input color MZColor = clrRed; // Daily Main Zone Color
input color Z3Color = clrPurple; // Daily 30% Zones
input color ExtremesColor = clrYellow; // Daily Extreme Zones Color
input color SLZoneColor = clrRed; // Daily SL Zones Color
input color SDColor = clrGold; // Daily SD Color

input string WeekCol = ""; // Weekly Colors
input color WTZColor = clrPink; // Weekly Trade Zone Color
input color WMZColor = clrOrange; // Weekly Main Zone Color
input color WZ3Color = clrPurple; // Weekly 30% Zones
input color WExtremesColor = clrGold; // Weekly Extreme Zones Color
color FibsColor = clrMagenta; // Fib Color
color FibsColorEx = clrBlack; // Fib Extension Color

//Day Pips Variables

double DayMid;
double TradeZoneStart;
double TradeZoneEnd;
double HighPctAreaLow;
double HighPctAreaHigh;
double LessThan30PctZoneLow;
double LessThan30PctZoneHigh;
double LowExtremeStart;
double LowExtremeEnd;
double HighExtremeStart;
double HighExtremeEnd;
double MaxSLLow;
double MaxSLHigh;

/*
//Day Perc Variables

double aDayMid;
double aTradeZoneStart;
double aTradeZoneEnd;
double aHighPctAreaLow;
double aHighPctAreaHigh;
double aLessThan30PctZoneLow;
double aLessThan30PctZoneHigh;
double aLowExtremeStart;
double aLowExtremeEnd;
double aHighExtremeStart;
double aHighExtremeEnd;
double aMaxSLLow;
double aMaxSLHigh;
*/

//Week Pips variables

double WeekMid;
double WeekMida;
double WeekMidb;
double WeekMidaa;
double WeekMidbb;
double WeekMidaaa;
double WeekMidbbb;
double WeekMidaaaa;
double WeekMidbbbb;
double WeekMidaaaaa;
double WeekMidbbbbb;
double WeekMidc;
double WeekMidd;
double WeekTradeZoneStart;
double WeekTradeZoneEnd;
double WeekHighPctAreaLow;
double WeekHighPctAreaHigh;
double WeekLessThan30PctZoneLow;
double WeekLessThan30PctZoneHigh;
double WeekLowExtremeStart;
double WeekLowExtremeEnd;
double WeekHighExtremeStart;
double WeekHighExtremeEnd;

/*
//Week Perc Variables

double aWeekMid;
double aWeekTradeZoneStart;
double aWeekTradeZoneEnd;
double aWeekHighPctAreaLow;
double aWeekHighPctAreaHigh;
double aWeekLessThan30PctZoneLow;
double aWeekLessThan30PctZoneHigh;
double aWeekLowExtremeStart;
double aWeekLowExtremeEnd;
double aWeekHighExtremeStart;
double aWeekHighExtremeEnd;
*/

//Pivots

double Pivot;
double WeekPivot;

//Alerts

input bool ALERT        = true        ; // Popup Alert 
input bool pushm = true; // Push Notifications

bool ALessThan30PctZoneLow      = true;
bool ALessThan30PctZoneHigh     = true;
bool ALowExtremeStart           = true;
bool AHighExtremeStart          = true;
bool AMaxSLLow                  = true;
bool AMaxSLHigh                 = true;
bool ASDdn                      = true;
bool ASDup                      = true;
bool AWeekLessThan30PctZoneLow  = true;
bool AWeekLessThan30PctZoneHigh = true;
bool AWeekLowExtremeStart       = true;
bool AWeekHighExtremeStart      = true;
bool AWeekHighPctAreaLow        = true;
bool AWeekHighPctAreaHigh       = true;
bool AWSDdn                      = true;
bool AWSDup                      = true;

//Screenshots

string DateTimeReformat(string dat_0) {
   string dat_8;
   string dat_ret_16 = "";
   dat_0 = " " + dat_0;
   int dat_len_24 = StringLen(dat_0);
   for (int dat_28 = 0; dat_28 < dat_len_24; dat_28++) {
      dat_8 = StringSetCharacter(dat_8, 0, StringGetCharacter(dat_0, dat_28));
      if (dat_8 != ":" && dat_8 != " " && dat_8 != ".") dat_ret_16 = dat_ret_16 + dat_8;
   }
   return (dat_ret_16);
} 

string PeriodDesc(int TF_0) {
   switch (TF_0) {
   case 1:
      return ("M1");
   case 5:
      return ("M5");
   case 15:
      return ("M15");
   case 30:
      return ("M30");
   case 60:
      return ("H1");
   case 240:
      return ("H4");
   case 1440:
      return ("D1");
   case 10080:
      return ("W1");
   case 43200:
      return ("MN");
   }
   return ("Unknown TF");
}

string SavedChartsFolder = "charts screenshot/sphot/";

string pretxt_40 = SavedChartsFolder + Symbol() + "_" + PeriodDesc(Period()) + "_" + DateTimeReformat(TimeToString(TimeCurrent(),TIME_DATE)) + "-" + DateTimeReformat(TimeToString(TimeCurrent(),TIME_MINUTES|TIME_SECONDS));

//Current day variables

double CurrentMid, CurrentMida, CurrentMidb;

/*
//ADR Variables

int adr1,adr5,adr10,adr20;
double PipValue;
double DailyPips;
string text;
*/
ENUM_TIMEFRAMES TFMigrate(int tf)
{
   switch(tf)
   {
      case 0: return(PERIOD_CURRENT);
      case 1: return(PERIOD_M1);
      case 5: return(PERIOD_M5);
      case 15: return(PERIOD_M15);
      case 30: return(PERIOD_M30);
      case 60: return(PERIOD_H1);
      case 240: return(PERIOD_H4);
      case 1440: return(PERIOD_D1);
      case 10080: return(PERIOD_W1);
      case 43200: return(PERIOD_MN1);
      
      case 2: return(PERIOD_M2);
      case 3: return(PERIOD_M3);
      case 4: return(PERIOD_M4);      
      case 6: return(PERIOD_M6);
      case 10: return(PERIOD_M10);
      case 12: return(PERIOD_M12);
      case 16385: return(PERIOD_H1);
      case 16386: return(PERIOD_H2);
      case 16387: return(PERIOD_H3);
      case 16388: return(PERIOD_H4);
      case 16390: return(PERIOD_H6);
      case 16392: return(PERIOD_H8);
      case 16396: return(PERIOD_H12);
      case 16408: return(PERIOD_D1);
      case 32769: return(PERIOD_W1);
      case 49153: return(PERIOD_MN1);      

      default: return(PERIOD_CURRENT);
   }
}

double iOpen(string symbol,int tf,int index)
{   
   if(index < 0) return(-1);
   double Arr[];
   ENUM_TIMEFRAMES timeframe=TFMigrate(tf);
   if(CopyOpen(symbol,timeframe, index, 1, Arr)>0) return(Arr[0]);
   else return(-1);
}
double iLow(string symbol,int tf,int index)
{
   if(index < 0) return(-1);
   double Arr[];
   ENUM_TIMEFRAMES timeframe=TFMigrate(tf);
   if(CopyLow(symbol,timeframe, index, 1, Arr)>0) return(Arr[0]);
   else return(-1);
}
double iHigh(string symbol,int tf,int index)
{
   if(index < 0) return(-1);
   double Arr[];
   ENUM_TIMEFRAMES timeframe=TFMigrate(tf);
   if(CopyHigh(symbol,timeframe, index, 1, Arr)>0) return(Arr[0]);
   else return(-1);
}
double iClose(string symbol,int tf,int index)
{
   if(index < 0) return(-1);
   double Arr[];
   ENUM_TIMEFRAMES timeframe=TFMigrate(tf);
   if(CopyClose(symbol,timeframe, index, 1, Arr)>0) return(Arr[0]);
   else return(-1);
}
datetime iTime(string symbol,int tf,int index)
{
   if(index < 0) return(-1);
   ENUM_TIMEFRAMES timeframe=TFMigrate(tf);
   datetime Arr[];
   if(CopyTime(symbol, timeframe, index, 1, Arr)>0) return(Arr[0]);
   else return(-1);
}
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
   CalculateCurrentDayMid();
   CalculateDayPositions();
//   CalculateaDayPositions();
   CalculateWeekPositions();
//   InitADR();
//   CalcADR();
   DeleteObjects();
   IndicatorSetInteger(INDICATOR_DIGITS,_Digits);
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
//   if(reason!=3 || IsTesting())

//   if(!IsTesting())
//     {
      DeleteObjects();
//     }
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   
   static datetime start_day_time=0;
   datetime now_day_time=iTime(Symbol(),PERIOD_D1,0);
   bool new_check=false;
//   bool adr_check=false;
   static int check_count=0;
//   static int adr_check_count=0;
   if(start_day_time!=now_day_time)
     {
      start_day_time=now_day_time;
      check_count=0;
        ALessThan30PctZoneLow      = true;
        ALessThan30PctZoneHigh     = true;
        ALowExtremeStart           = true;
        AHighExtremeStart          = true;
        AMaxSLLow                  = true;
        AMaxSLHigh                 = true;
        ASDdn                      = true;
        ASDup                      = true;
        AWeekLessThan30PctZoneLow  = true;
        AWeekLessThan30PctZoneHigh = true;
        AWeekLowExtremeStart       = true;
        AWeekHighExtremeStart      = true;
        AWeekHighPctAreaLow        = true;
        AWeekHighPctAreaHigh       = true;
        AWSDdn                     = true;
        AWSDup                     = true;
     }
   
   static datetime recheck_time=0;
   if(check_count<30)
     {
      new_check=true;
      check_count++;
      recheck_time=TimeCurrent();
     }
   if(TimeCurrent()>=recheck_time+(CheckSeconds*1))
     {
      new_check=true;
      recheck_time=TimeCurrent();
     }
   if(new_check)
     {
      CalculateCurrentDayMid();
      BuildCurrentDayMid();
     }

/*
   static datetime adr_recheck_time=0;
   if(adr_check_count<30)
     {
      adr_check=true;
      adr_check_count++;
      adr_recheck_time=TimeCurrent();
     }
   if(TimeCurrent()>=adr_recheck_time+1)
     {
      adr_check=true;
      adr_recheck_time=TimeCurrent();
     }
   if(adr_check)
     {
      BuildADR();
     }
*/

   static datetime bar_time=0;
   if(bar_time!=time[0])
     {
      bar_time=time[0];
      CalculateDayPositions();
//      CalculateaDayPositions();
      BuildDayObjects();
//      BuildaDayObjects();
      CalculateWeekPositions();
      BuildWeekObjects();
      CalculateCurrentDayMid();
      BuildCurrentDayMid();
/*
      InitADR();
      CalcADR();
      BuildADR();
*/
     }
     
   string DD30 = ("Pips "+Symbol() + " = DAILY DOWN < 30%" + " @ " + DoubleToString(LessThan30PctZoneLow,_Digits));
   string DU30 = ("Pips "+Symbol() + " = DAILY UP < 30%" + " @ " + DoubleToString(LessThan30PctZoneHigh,_Digits));
   string WD30 = ("Pips "+Symbol() + " = WEEKLY DOWN < 30%" + " @ " + DoubleToString(WeekLessThan30PctZoneLow,_Digits));
   string WU30 = ("Pips "+Symbol() + " = WEEKLY UP < 30%" + " @ " + DoubleToString(WeekLessThan30PctZoneHigh,_Digits));
   string DDES = ("Pips "+Symbol() + " = DAILY DOWN Extreme Start" + " @ " + DoubleToString(LowExtremeStart,_Digits));
   string DUES = ("Pips "+Symbol() + " = DAILY UP Extreme Start" + " @ " + DoubleToString(HighExtremeStart,_Digits));
   string WDES = ("Pips "+Symbol() + " = WEEKLY DOWN Extreme Start" + " @ " + DoubleToString(WeekLowExtremeStart,_Digits));
   string WUES = ("Pips "+Symbol() + " = WEEKLY UP Extreme Start" + " @ " + DoubleToString(WeekHighExtremeStart,_Digits));
   string DDSL = ("Pips "+Symbol() + " = DAILY DOWN SL" + " @ " + DoubleToString(MaxSLLow,_Digits));
   string DUSL = ("Pips "+Symbol() + " = DAILY UP SL" + " @ " + DoubleToString(MaxSLHigh,_Digits));
   string WDHA = ("Pips "+Symbol() + " = WEEKLY DOWN High % Area" + " @ " + DoubleToString(WeekHighPctAreaLow,_Digits));
   string WUHA = ("Pips "+Symbol() + " = WEEKLY UP High % Area" + " @ " + DoubleToString(WeekHighPctAreaHigh,_Digits));
   string SDDN = ("Pips "+Symbol() + " = DAILY SD DOWN " + " @ " + DoubleToString(SDd,_Digits));
   string SDUP = ("Pips "+Symbol() + " = DAILY SD UP " + " @ " + DoubleToString(SDu,_Digits));
   string SDWD = ("Pips "+Symbol() + " = WEEKLY SD DOWN " + " @ " + DoubleToString(SDWd,_Digits));
   string SDWU = ("Pips "+Symbol() + " = WEEKLY SD UP " + " @ " + DoubleToString(SDWu,_Digits));
      
     if (ALERT==true)
      {
      if (ALessThan30PctZoneLow==true && close[0] < LessThan30PctZoneLow && open[0]>=LessThan30PctZoneLow)
         {
         Alert(DD30);
         if(pushm)
         {
            SendNotification(DD30);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          ALessThan30PctZoneLow= false;
         }
      if (ALessThan30PctZoneHigh==true && close[0] > LessThan30PctZoneHigh && open[0]<=LessThan30PctZoneHigh)
         {
         Alert(DU30);
         if(pushm)
         {
            SendNotification(DU30);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          ALessThan30PctZoneHigh= false;
         }
      if (ALowExtremeStart==true && close[0] < LowExtremeStart && open[0]>=LowExtremeStart)
         {
          Alert(DDES);
         if(pushm)
         {
            SendNotification(DDES);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          ALowExtremeStart= false;
         }
      if (AHighExtremeStart==true && close[0] > HighExtremeStart && open[0]<=HighExtremeStart)
         {
         Alert(DUES);
         if(pushm)
         {
            SendNotification(DUES);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          AHighExtremeStart= false;
         }
      if (AMaxSLLow==true && close[0] > MaxSLLow && open[0]<=MaxSLLow)
         {
         Alert(DDSL);
         if(pushm)
         {
            SendNotification(DDSL);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          AMaxSLLow= false;
         }
      if (AMaxSLHigh==true && close[0] > MaxSLHigh && open[0]<=MaxSLHigh)
         {
         Alert(DUSL);
         if(pushm)
         {
            SendNotification(DUSL);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          AMaxSLHigh= false;
         }
      if (ASDdn==true && close[0] > SDd && open[0]<=SDd)
         {
         Alert(SDDN);
         if(pushm)
         {
            SendNotification(SDDN);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          ASDdn= false;
         }
      if (ASDup==true && close[0] > SDu && open[0]<=SDu)
         {
         Alert(SDUP);
         if(pushm)
         {
            SendNotification(SDUP);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          ASDup= false;
         }
      if (AWeekLessThan30PctZoneLow==true && close[0] < WeekLessThan30PctZoneLow && open[0]>=WeekLessThan30PctZoneLow)
         {
         Alert(WD30);
         if(pushm)
         {
            SendNotification(WD30);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          AWeekLessThan30PctZoneLow= false;
         }
      if (AWeekLessThan30PctZoneHigh==true && close[0] > WeekLessThan30PctZoneHigh && open[0]<=WeekLessThan30PctZoneHigh)
         {
         Alert(WU30);
         if(pushm)
         {
            SendNotification(WU30);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
         AWeekLessThan30PctZoneHigh = false;
         }
      if (AWeekLowExtremeStart==true && close[0] < WeekLowExtremeStart && open[0]>=WeekLowExtremeStart)
         {
         Alert(WDES);
         if(pushm)
         {
            SendNotification(WDES);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          AWeekLowExtremeStart= false;
         }
      if (AWeekHighExtremeStart==true && close[0] > WeekHighExtremeStart && open[0]<=WeekHighExtremeStart)
         {
         Alert(WUES);
         if(pushm)
         {
            SendNotification(WUES);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          AWeekHighExtremeStart= false;
         }
      if (AWeekHighPctAreaLow==true && close[0] > WeekHighPctAreaLow && open[0]<=WeekHighPctAreaLow)
         {
         Alert(WDHA);
         if(pushm)
         {
            SendNotification(WDHA);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          AWeekHighPctAreaLow= false;
         }
      if (AWeekHighPctAreaHigh==true && close[0] > WeekHighPctAreaHigh && open[0]<=WeekHighPctAreaHigh)
         {
         Alert(WUHA);
         if(pushm)
         {
            SendNotification(WUHA);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          AWeekHighPctAreaHigh= false;
         }
      if (AWSDdn==true && close[0] > SDWd && open[0]<=SDWd)
         {
         Alert(SDWD);
         if(pushm)
         {
            SendNotification(SDWD);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          AWSDdn= false;
         }
      if (AWSDup==true && close[0] > SDWu && open[0]<=SDWu)
         {
         Alert(SDWU);
         if(pushm)
         {
            SendNotification(SDWU);
         }
         ChartScreenShot(0, pretxt_40 + ".png",1920, 1080, ALIGN_RIGHT);
         PlaySound("news.wav");
          AWSDup= false;
         }
      
      
      }
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+
void DeleteObjects()
  {
   for(int i=ObjectsTotal(0,-1)-1;i>=0;i--)
     {
      string ObName=ObjectName(0,i);
      if(StringFind(ObName,Name,0)!=-1)
        {
         ObjectDelete(0,ObName);
        }
     }
     
/*
     if (ObjectFind("xADR0") == 0 )          ObjectDelete("xADR0");// only delete it if it exists
     if (ObjectFind("xADR1") == 0 )          ObjectDelete("xADR1");
     if (ObjectFind("xADR2") == 0 )          ObjectDelete("xADR2");   
     if (ObjectFind("xADR3") == 0 )          ObjectDelete("xADR3");
     if (ObjectFind("xADR4") == 0 )          ObjectDelete("xADR4"); 
     if (ObjectFind("xADR5") == 0 )          ObjectDelete("xADR5");                    
     if (ObjectFind("DailyPipsObj2") == 0 )  ObjectDelete("DailyPipsObj2");  
     if (ObjectFind("ADR5_High_obj") == 0 )  ObjectDelete("ADR5_High_obj");
     if (ObjectFind("ADR5_Low_obj") == 0 )   ObjectDelete("ADR5_Low_obj");
     if (ObjectFind("ADR10_High_obj") == 0 )  ObjectDelete("ADR10_High_obj");
     if (ObjectFind("ADR10_Low_obj") == 0 )   ObjectDelete("ADR10_Low_obj");
     if (ObjectFind("ADR20_High_obj") == 0 )  ObjectDelete("ADR20_High_obj");
     if (ObjectFind("ADR20_Low_obj") == 0 )   ObjectDelete("ADR20_Low_obj");    
*/

  }
//+------------------------------------------------------------------+
void CalculateDayPositions()
  {
   double PreviousDayHigh = iHigh(NULL,PERIOD_D1,1); //iMA(NULL,PERIOD_D1,1,0,MODE_SMA,PRICE_HIGH,1);
   double PreviousDayLow = iLow(NULL,PERIOD_D1,1); //iMA(NULL,PERIOD_D1,1,0,MODE_SMA,PRICE_LOW,1);
   double PreviousDayClose = iClose(NULL,PERIOD_D1,1); //iMA(NULL,PERIOD_D1,1,0,MODE_SMA,PRICE_LOW,1);
   double PreviousDayRange = PreviousDayHigh-PreviousDayLow;
   Pivot = (PreviousDayHigh+PreviousDayLow+PreviousDayClose)/3;
   DayMid = PreviousDayLow+(PreviousDayRange/2);
   double DayStep;//=PreviousDayRange/10;
if (_Digits ==4 || _Digits == 5)
{
   DayStep = 0.001;
}
else
{if (_Digits == 3)

   DayStep = 0.1;
else
{if (sym2 == "EP" || sym2 == "EN")

   DayStep = 2.5;
else
{if (_Digits == 7)

   DayStep = 0.015625;
else
{if (_Digits == 6)
   
   DayStep = 0.03125;
else
{if (sym2 == "YM")
   
   DayStep = 10;
   }}}}}
 
   TradeZoneStart = DayMid + (DayStep * TradeZoneS);
   TradeZoneEnd = DayMid + (DayStep * TradeZoneE);
   
   HighPctAreaLow = DayMid + (DayStep * LowP);
   HighPctAreaHigh = DayMid + (DayStep * HighP);
   
   LessThan30PctZoneLow = DayMid + (DayStep * Lt30pctZL);
   LessThan30PctZoneHigh = DayMid + (DayStep * Lt30pctZH);
   
   LowExtremeStart = DayMid + (DayStep * LowExtremeS);
   LowExtremeEnd = DayMid + (DayStep * LowExtremeE);
   
   HighExtremeStart = DayMid + (DayStep * HighExtremeS);
   HighExtremeEnd = DayMid + (DayStep * HighExtremeE);
   
   MaxSLLow = DayMid + (DayStep * LowSL);
   MaxSLHigh = DayMid + (DayStep * HighSL);
   
  }
//+------------------------------------------------------------------+
/*
void CalculateaDayPositions()
  {
   double aPreviousDayHigh = iHigh(NULL,PERIOD_D1,1); //iMA(NULL,PERIOD_D1,1,0,MODE_SMA,PRICE_HIGH,1);
   double aPreviousDayLow = iLow(NULL,PERIOD_D1,1); //iMA(NULL,PERIOD_D1,1,0,MODE_SMA,PRICE_LOW,1);
   double aPreviousDayRange = aPreviousDayHigh-aPreviousDayLow;
   aDayMid = aPreviousDayLow+(aPreviousDayRange/2);
   double aDayStep = aPreviousDayRange/10;
   
   aTradeZoneStart = aDayMid + (aDayStep * aTradeZoneS);
   aTradeZoneEnd = aDayMid + (aDayStep * aTradeZoneE);
   
   aHighPctAreaLow = aDayMid + (aDayStep * aLowP);
   aHighPctAreaHigh = aDayMid + (aDayStep * aHighP);
   
   aLessThan30PctZoneLow = aDayMid + (aDayStep * aLt30pctZL);
   aLessThan30PctZoneHigh = aDayMid + (aDayStep * aLt30pctZH);
   
   aLowExtremeStart = aDayMid + (aDayStep * aLowExtremeS);
   aLowExtremeEnd = aDayMid + (aDayStep * aLowExtremeE);
   
   aHighExtremeStart = aDayMid + (aDayStep * aHighExtremeS);
   aHighExtremeEnd = aDayMid + (aDayStep * aHighExtremeE);
   
   aMaxSLLow = aDayMid + (aDayStep * aLowSL);
   aMaxSLHigh = aDayMid + (aDayStep * aHighSL);

  }
*/
//+------------------------------------------------------------------+
void CalculateWeekPositions()
  {
   double PreviousWeekHigh = iHigh(NULL,PERIOD_W1,1); //iMA(NULL,PERIOD_W1,1,0,MODE_SMA,PRICE_HIGH,1);
   double PreviousWeekLow = iLow(NULL,PERIOD_W1,1); //iMA(NULL,PERIOD_W1,1,0,MODE_SMA,PRICE_LOW,1);
   double PreviousWeekClose = iClose(NULL,PERIOD_W1,1); //iMA(NULL,PERIOD_W1,1,0,MODE_SMA,PRICE_LOW,1);
   double PreviousWeekRange = PreviousWeekHigh-PreviousWeekLow;
   WeekMid = PreviousWeekLow+(PreviousWeekRange/2);
   WeekMida = PreviousWeekLow+(PreviousWeekRange)*0.72;
   WeekMidb = PreviousWeekHigh-(PreviousWeekRange)*0.72;
   WeekMidaa = PreviousWeekLow+(PreviousWeekRange)*0.82;
   WeekMidbb = PreviousWeekHigh-(PreviousWeekRange)*0.82;
   WeekMidaaa = PreviousWeekLow+(PreviousWeekRange)*1.146;
   WeekMidbbb = PreviousWeekHigh-(PreviousWeekRange)*1.146;
   WeekMidaaaa = PreviousWeekLow+(PreviousWeekRange)*1.382;
   WeekMidbbbb = PreviousWeekHigh-(PreviousWeekRange)*1.382;   
   WeekMidaaaaa = PreviousWeekLow+(PreviousWeekRange)*1.618;
   WeekMidbbbbb = PreviousWeekHigh-(PreviousWeekRange)*1.618;  
   WeekMidc = PreviousWeekLow+(PreviousWeekRange)*2;
   WeekMidd = PreviousWeekHigh-(PreviousWeekRange)*2;  
   WeekPivot = (PreviousWeekHigh+PreviousWeekLow+PreviousWeekClose)/3;
   double WeekStep;//=PreviousWeekRange/10;
if (_Digits ==4 || _Digits == 5)
{
   WeekStep = 0.001;
}
else
{if (_Digits == 3)

   WeekStep = 0.1;
else
{if (_Digits == 2)

   WeekStep = 2.5;
else
{if (_Digits == 7)

   WeekStep = 0.03125;
else
{if (_Digits == 6)
   
   WeekStep = 0.0625;
else
{if (_Digits == 0)
   
   WeekStep = 10;
   }}}}}
  
   WeekTradeZoneStart = WeekMid + (WeekStep * TradeZoneWS);
   WeekTradeZoneEnd = WeekMid + (WeekStep * TradeZoneWE);
   
   WeekHighPctAreaLow = WeekMid + (WeekStep * LowWP);
   WeekHighPctAreaHigh = WeekMid + (WeekStep * HighWP);
   
   WeekLessThan30PctZoneLow = WeekMid + (WeekStep * Lt30pctZWL);
   WeekLessThan30PctZoneHigh = WeekMid + (WeekStep * Lt30pctZWH);
   
   WeekLowExtremeStart = WeekMid + (WeekStep * LowExtremeWS);
   WeekLowExtremeEnd = WeekMid + (WeekStep * LowExtremeWE);
   
   WeekHighExtremeStart = WeekMid + (WeekStep * HighExtremeWS);
   WeekHighExtremeEnd = WeekMid + (WeekStep * HighExtremeWE);
  }
//+------------------------------------------------------------------+
void CalculateCurrentDayMid()
  {
   double CurrentHigh = iHigh(NULL,PERIOD_D1,0);
   double CurrentLow = iLow(NULL,PERIOD_D1,0);
   double CurrentRange = CurrentHigh-CurrentLow;
   CurrentMid = CurrentLow + (CurrentRange/2); //iMA(NULL,PERIOD_D1,1,0,MODE_SMA,PRICE_MEDIAN,0);
   CurrentMida = CurrentLow + (CurrentRange/2)*1.618;
   CurrentMidb = CurrentHigh - (CurrentRange/2)*1.618;
  }
//+------------------------------------------------------------------+
void BuildDayObjects()
  {
   datetime time1d = iTime(NULL,PERIOD_D1,0)+60*5;
   datetime time2d = iTime(NULL,PERIOD_D1,0)+60*60*23+60*59;

   string obname;
   
      if (TradeZoneS != TradeZoneE) 
      {
      obname=Name+" "+"TZ";
         ObjectDelete(0,obname);
         ObjectCreate(0,obname,OBJ_RECTANGLE,0,time1d,TradeZoneStart,time2d,TradeZoneEnd);
         ObjectSetInteger(0,obname,OBJPROP_COLOR,DTZColor);
         ObjectSetInteger(0,obname,OBJPROP_BACK,true);
         ObjectSetInteger(0,obname,OBJPROP_FILL,true);
      }
      
      else
      
      {
      if (TradeZoneS == TradeZoneE) 
      {
      obname=Name+" "+"TZ";
         ObjectDelete(0,obname);
         ObjectCreate(0,obname,OBJ_TREND,0,time1d,TradeZoneStart,time2d,TradeZoneEnd);
         ObjectSetInteger(0,obname,OBJPROP_COLOR,DTZColor);
         ObjectSetInteger(0,obname,OBJPROP_BACK,true);
         ObjectSetInteger(0,obname,OBJPROP_RAY,false);
         ObjectSetInteger(0,obname,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obname,OBJPROP_WIDTH,5);
      }
      }
      
      obname=Name+" "+"Pivot";
         ObjectDelete(0,obname);
         ObjectCreate(0,obname,OBJ_TREND,0,time1d,Pivot,time2d,Pivot);
         ObjectSetInteger(0,obname,OBJPROP_COLOR,Gray);
         ObjectSetInteger(0,obname,OBJPROP_BACK,true);
         ObjectSetInteger(0,obname,OBJPROP_RAY,false);
         ObjectSetInteger(0,obname,OBJPROP_STYLE,STYLE_DOT);
         ObjectSetInteger(0,obname,OBJPROP_WIDTH,1);
      
      obname=Name+" "+"1xM";
         ObjectDelete(0,obname);
         ObjectCreate(0,obname,OBJ_TREND,0,time1d,DayMid,time2d,DayMid);
         ObjectSetInteger(0,obname,OBJPROP_COLOR,MidColor);
         ObjectSetInteger(0,obname,OBJPROP_BACK,true);
         ObjectSetInteger(0,obname,OBJPROP_RAY,false);
         ObjectSetInteger(0,obname,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obname,OBJPROP_WIDTH,2);
         
      obname=Name+" "+"SDdown";
         ObjectDelete(0,obname);
         ObjectCreate(0,obname,OBJ_TREND,0,time1d,SDd,time2d,SDd);
         ObjectSetInteger(0,obname,OBJPROP_COLOR,SDColor);
         ObjectSetInteger(0,obname,OBJPROP_BACK,true);
         ObjectSetInteger(0,obname,OBJPROP_RAY,false);
         ObjectSetInteger(0,obname,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obname,OBJPROP_WIDTH,3);
         
      obname=Name+" "+"SDup";
         ObjectDelete(0,obname);
         ObjectCreate(0,obname,OBJ_TREND,0,time1d,SDu,time2d,SDu);
         ObjectSetInteger(0,obname,OBJPROP_COLOR,SDColor);
         ObjectSetInteger(0,obname,OBJPROP_BACK,true);
         ObjectSetInteger(0,obname,OBJPROP_RAY,false);
         ObjectSetInteger(0,obname,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obname,OBJPROP_WIDTH,3);
      
      obname=Name+" "+"HPA";
         ObjectDelete(0,obname);
         ObjectCreate(0,obname,OBJ_RECTANGLE,0,time1d,HighPctAreaLow,time2d,HighPctAreaHigh);
         ObjectSetInteger(0,obname,OBJPROP_COLOR,MZColor);
         ObjectSetInteger(0,obname,OBJPROP_BACK,false);
      
      obname=Name+" "+"LEx";
         ObjectDelete(0,obname);
         ObjectCreate(0,obname,OBJ_RECTANGLE,0,time1d,LowExtremeStart,time2d,LowExtremeEnd);
         ObjectSetInteger(0,obname,OBJPROP_COLOR,ExtremesColor);
         ObjectSetInteger(0,obname,OBJPROP_BACK,true);
         ObjectSetInteger(0,obname,OBJPROP_FILL,true);
      
      obname=Name+" "+"HEx";
         ObjectDelete(0,obname);
         ObjectCreate(0,obname,OBJ_RECTANGLE,0,time1d,HighExtremeStart,time2d,HighExtremeEnd);
         ObjectSetInteger(0,obname,OBJPROP_COLOR,ExtremesColor);
         ObjectSetInteger(0,obname,OBJPROP_BACK,true);
         ObjectSetInteger(0,obname,OBJPROP_FILL,true);
         ObjectSetInteger(0,obname,OBJPROP_FILL,true);
         
      obname=Name+" "+"L3p";
         ObjectDelete(0,obname);
         ObjectCreate(0,obname,OBJ_TREND,0,time1d,LessThan30PctZoneLow,time2d,LessThan30PctZoneLow);
         ObjectSetInteger(0,obname,OBJPROP_COLOR,Z3Color);
         ObjectSetInteger(0,obname,OBJPROP_BACK,true);
         ObjectSetInteger(0,obname,OBJPROP_RAY,false);
         ObjectSetInteger(0,obname,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obname,OBJPROP_WIDTH,3);
         
      obname=Name+" "+"H3p";
         ObjectDelete(0,obname);
         ObjectCreate(0,obname,OBJ_TREND,0,time1d,LessThan30PctZoneHigh,time2d,LessThan30PctZoneHigh);
         ObjectSetInteger(0,obname,OBJPROP_COLOR,Z3Color);
         ObjectSetInteger(0,obname,OBJPROP_BACK,true);
         ObjectSetInteger(0,obname,OBJPROP_RAY,false);
         ObjectSetInteger(0,obname,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obname,OBJPROP_WIDTH,3);

      obname=Name+" "+"SLL";
         ObjectDelete(0,obname);
         ObjectCreate(0,obname,OBJ_TREND,0,time1d,MaxSLLow,time2d,MaxSLLow);
         ObjectSetInteger(0,obname,OBJPROP_COLOR,SLZoneColor);
         ObjectSetInteger(0,obname,OBJPROP_BACK,true);
         ObjectSetInteger(0,obname,OBJPROP_RAY,false);
         ObjectSetInteger(0,obname,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obname,OBJPROP_WIDTH,3);
         
      obname=Name+" "+"SLH";
         ObjectDelete(0,obname);
         ObjectCreate(0,obname,OBJ_TREND,0,time1d,MaxSLHigh,time2d,MaxSLHigh);
         ObjectSetInteger(0,obname,OBJPROP_COLOR,SLZoneColor);
         ObjectSetInteger(0,obname,OBJPROP_BACK,true);
         ObjectSetInteger(0,obname,OBJPROP_RAY,false);
         ObjectSetInteger(0,obname,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obname,OBJPROP_WIDTH,3);               
  }
//+------------------------------------------------------------------+
/*
void BuildaDayObjects()
  {
   datetime time1d = iTime(NULL,PERIOD_D1,0)+60*60*12+60*5;
   datetime time2d = iTime(NULL,PERIOD_D1,0)+60*60*23+60*59;

   string obnamea;
   
      if (aTradeZoneS != aTradeZoneE) 
      {
      obnamea=Name+" "+"aTZ";
         ObjectDelete(0,obnamea);
         ObjectCreate(0,obnamea,OBJ_RECTANGLE,0,time1d,aTradeZoneStart,time2d,aTradeZoneEnd);
         ObjectSetInteger(0,obnamea,OBJPROP_COLOR,DTZColor);
         ObjectSetInteger(0,obnamea,OBJPROP_BACK,true);
      }
      
      else
      
      {
      if (aTradeZoneS == aTradeZoneE) 
      {
      obnamea=Name+" "+"aTZ";
         ObjectDelete(0,obnamea);
         ObjectCreate(0,obnamea,OBJ_TREND,0,time1d,aTradeZoneStart,time2d,aTradeZoneEnd);
         ObjectSetInteger(0,obnamea,OBJPROP_COLOR,DTZColor);
         ObjectSetInteger(0,obnamea,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamea,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamea,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamea,OBJPROP_WIDTH,5);
      }
      }
      
      obnamea=Name+" "+"aPivot";
         ObjectDelete(0,obnamea);
         ObjectCreate(0,obnamea,OBJ_TREND,0,time1d,Pivot,time2d,Pivot);
         ObjectSetInteger(0,obnamea,OBJPROP_COLOR,Gray);
         ObjectSetInteger(0,obnamea,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamea,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamea,OBJPROP_STYLE,STYLE_DOT);
         ObjectSetInteger(0,obnamea,OBJPROP_WIDTH,1);
      
      obnamea=Name+" "+"a1xM";
         ObjectDelete(0,obnamea);
         ObjectCreate(0,obnamea,OBJ_TREND,0,time1d,aDayMid,time2d,aDayMid);
         ObjectSetInteger(0,obnamea,OBJPROP_COLOR,MidColor);
         ObjectSetInteger(0,obnamea,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamea,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamea,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamea,OBJPROP_WIDTH,2);
      
      obnamea=Name+" "+"aHPA";
         ObjectDelete(0,obnamea);
         ObjectCreate(0,obnamea,OBJ_RECTANGLE,0,time1d,aHighPctAreaLow,time2d,aHighPctAreaHigh);
         ObjectSetInteger(0,obnamea,OBJPROP_COLOR,MZColor);
         ObjectSetInteger(0,obnamea,OBJPROP_BACK,false);
      
      obnamea=Name+" "+"aLEx";
         ObjectDelete(0,obnamea);
         ObjectCreate(0,obnamea,OBJ_RECTANGLE,0,time1d,aLowExtremeStart,time2d,aLowExtremeEnd);
         ObjectSetInteger(0,obnamea,OBJPROP_COLOR,ExtremesColor);
         ObjectSetInteger(0,obnamea,OBJPROP_BACK,true);
      
      obnamea=Name+" "+"aHEx";
         ObjectDelete(0,obnamea);
         ObjectCreate(0,obnamea,OBJ_RECTANGLE,0,time1d,aHighExtremeStart,time2d,aHighExtremeEnd);
         ObjectSetInteger(0,obnamea,OBJPROP_COLOR,ExtremesColor);
         ObjectSetInteger(0,obnamea,OBJPROP_BACK,true);
         
      obnamea=Name+" "+"aL3p";
         ObjectDelete(0,obnamea);
         ObjectCreate(0,obnamea,OBJ_TREND,0,time1d,aLessThan30PctZoneLow,time2d,aLessThan30PctZoneLow);
         ObjectSetInteger(0,obnamea,OBJPROP_COLOR,Z3Color);
         ObjectSetInteger(0,obnamea,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamea,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamea,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamea,OBJPROP_WIDTH,3);
         
      obnamea=Name+" "+"aH3p";
         ObjectDelete(0,obnamea);
         ObjectCreate(0,obnamea,OBJ_TREND,0,time1d,aLessThan30PctZoneHigh,time2d,aLessThan30PctZoneHigh);
         ObjectSetInteger(0,obnamea,OBJPROP_COLOR,Z3Color);
         ObjectSetInteger(0,obnamea,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamea,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamea,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamea,OBJPROP_WIDTH,3);

      obnamea=Name+" "+"aSLL";
         ObjectDelete(0,obnamea);
         ObjectCreate(0,obnamea,OBJ_TREND,0,time1d,aMaxSLLow,time2d,aMaxSLLow);
         ObjectSetInteger(0,obnamea,OBJPROP_COLOR,SLZoneColor);
         ObjectSetInteger(0,obnamea,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamea,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamea,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamea,OBJPROP_WIDTH,3);
         
      obnamea=Name+" "+"aSLH";
         ObjectDelete(0,obnamea);
         ObjectCreate(0,obnamea,OBJ_TREND,0,time1d,aMaxSLHigh,time2d,aMaxSLHigh);
         ObjectSetInteger(0,obnamea,OBJPROP_COLOR,SLZoneColor);
         ObjectSetInteger(0,obnamea,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamea,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamea,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamea,OBJPROP_WIDTH,3);               
  }
*/
//+------------------------------------------------------------------+
void BuildWeekObjects()
  {
   datetime time1w = iTime(NULL,PERIOD_W1,0)+60*60*24;
   datetime time2w = iTime(NULL,PERIOD_W1,0)+60*60*24*6;

   string obnamew;
   
      obnamew=Name+" "+"WTZ";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_RECTANGLE,0,time1w,WeekTradeZoneStart,time2w,WeekTradeZoneEnd);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,WTZColor);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,5);
         ObjectSetInteger(0,obnamew,OBJPROP_FILL,true);
         
      obnamew=Name+" "+"1xWP";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekPivot,time2w,WeekPivot);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,Gray);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_DOT);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,1);
         
      obnamew=Name+" "+"1xWM";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekMid,time2w,WeekMid);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,MidColor);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,2);
         
      obnamew=Name+" "+"W SDdown";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,SDWd,time2w,SDWd);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,SDColor);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,5);
         
      obnamew=Name+" "+"W SDup";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,SDWu,time2w,SDWu);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,SDColor);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,5);

      obnamew=Name+" "+"1xWM+72";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekMida,time2w,WeekMida);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,FibsColor);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_DASH);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,1);

      obnamew=Name+" "+"1xWM-72";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekMidb,time2w,WeekMidb);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,FibsColor);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_DASH);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,1);

      obnamew=Name+" "+"1xWM+82";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekMidaa,time2w,WeekMidaa);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,FibsColor);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_DASH);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,1);

      obnamew=Name+" "+"1xWM-82";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekMidbb,time2w,WeekMidbb);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,FibsColor);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_DASH);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,1);
         
      obnamew=Name+" "+"1xWM+114.6";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekMidaaa,time2w,WeekMidaaa);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,FibsColorEx);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_DASH);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,1);

      obnamew=Name+" "+"1xWM-114.6";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekMidbbb,time2w,WeekMidbbb);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,FibsColorEx);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_DASH);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,1);

      obnamew=Name+" "+"1xWM+138.2";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekMidaaaa,time2w,WeekMidaaaa);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,FibsColorEx);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_DASH);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,1);

      obnamew=Name+" "+"1xWM-138.2";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekMidbbbb,time2w,WeekMidbbbb);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,FibsColorEx);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_DASH);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,1);
         
      obnamew=Name+" "+"1xWM+161.8";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekMidaaaaa,time2w,WeekMidaaaaa);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,FibsColorEx);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_DASH);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,1);

      obnamew=Name+" "+"1xWM-161.8";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekMidbbbbb,time2w,WeekMidbbbbb);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,FibsColorEx);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_DASH);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,1);
         
      obnamew=Name+" "+"1xWM+200";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekMidc,time2w,WeekMidc);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,FibsColorEx);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_DASH);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,1);

      obnamew=Name+" "+"1xWM-200";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekMidd,time2w,WeekMidd);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,FibsColorEx);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_DASH);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,1);
         
      obnamew=Name+" "+"WHPA";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_RECTANGLE,0,time1w,WeekHighPctAreaLow,time2w,WeekHighPctAreaHigh);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,WMZColor);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,false);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,4);
      
      obnamew=Name+" "+"WLEx";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_RECTANGLE,0,time1w,WeekLowExtremeStart,time2w,WeekLowExtremeEnd);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,WExtremesColor);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,false);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,4);
      
      obnamew=Name+" "+"WHEx";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_RECTANGLE,0,time1w,WeekHighExtremeStart,time2w,WeekHighExtremeEnd);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,WExtremesColor);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,false);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,4);
         
      obnamew=Name+" "+"WL3p";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekLessThan30PctZoneLow,time2w,WeekLessThan30PctZoneLow);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,WZ3Color);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,5);
         
      obnamew=Name+" "+"WH3p";
         ObjectDelete(0,obnamew);
         ObjectCreate(0,obnamew,OBJ_TREND,0,time1w,WeekLessThan30PctZoneHigh,time2w,WeekLessThan30PctZoneHigh);
         ObjectSetInteger(0,obnamew,OBJPROP_COLOR,WZ3Color);
         ObjectSetInteger(0,obnamew,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamew,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamew,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamew,OBJPROP_WIDTH,5);          
  }
//+------------------------------------------------------------------+
void BuildCurrentDayMid()
  {
   datetime time1c = iTime(NULL,PERIOD_D1,0)+60*60*24;
   datetime time2c = iTime(NULL,PERIOD_D1,0)+60*45*24*2;
   
   string obnamec;
   
      obnamec=Name+" "+"CM";
      
         ObjectDelete(0,obnamec);
         ObjectCreate(0,obnamec,OBJ_TREND,0,time1c,CurrentMid,time2c,CurrentMid);
         ObjectSetInteger(0,obnamec,OBJPROP_COLOR,SteelBlue);
         ObjectSetInteger(0,obnamec,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamec,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamec,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamec,OBJPROP_WIDTH,3);
//         ObjectSet(obnamec,OBJPROP_PRICE1,CurrentMid);
//         ObjectSet(obnamec,OBJPROP_PRICE2,CurrentMid);
         
      obnamec=Name+" "+"CM+61.8";
      
         ObjectDelete(0,obnamec);
         ObjectCreate(0,obnamec,OBJ_TREND,0,time1c,CurrentMida,time2c,CurrentMida);
         ObjectSetInteger(0,obnamec,OBJPROP_COLOR,SteelBlue);
         ObjectSetInteger(0,obnamec,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamec,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamec,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamec,OBJPROP_WIDTH,3);
//         ObjectSet(obnamec,OBJPROP_PRICE1,CurrentMida);
//         ObjectSet(obnamec,OBJPROP_PRICE2,CurrentMida);
      
      obnamec=Name+" "+"CM-61.8";
      
         ObjectDelete(0,obnamec);
         ObjectCreate(0,obnamec,OBJ_TREND,0,time1c,CurrentMidb,time2c,CurrentMidb);
         ObjectSetInteger(0,obnamec,OBJPROP_COLOR,SteelBlue);
         ObjectSetInteger(0,obnamec,OBJPROP_BACK,true);
         ObjectSetInteger(0,obnamec,OBJPROP_RAY,false);
         ObjectSetInteger(0,obnamec,OBJPROP_STYLE,STYLE_SOLID);
         ObjectSetInteger(0,obnamec,OBJPROP_WIDTH,3);
//         ObjectSet(obnamec,OBJPROP_PRICE1,CurrentMidb);
//         ObjectSet(obnamec,OBJPROP_PRICE2,CurrentMidb);
   }
//+------------------------------------------------------------------+  
/*
void CalcADR()
  {
   
  double d1,d2,d3,d4,d5,d6,d7,d8,d9,d10,d11,d12,d13,d14,d15,d16,d17,d18,d19,d20;

  if (_Digits == 4 || _Digits == 5)
  {
  PipValue = 0.0001;
  }
  else
  {if (_Digits == 2 || _Digits == 3)
  PipValue = 0.01;
  }

  d20=(iHigh(NULL,PERIOD_D1,20)-iLow(NULL,PERIOD_D1,20))/PipValue;
  d19=(iHigh(NULL,PERIOD_D1,19)-iLow(NULL,PERIOD_D1,19))/PipValue;
  d18=(iHigh(NULL,PERIOD_D1,18)-iLow(NULL,PERIOD_D1,18))/PipValue;
  d17=(iHigh(NULL,PERIOD_D1,17)-iLow(NULL,PERIOD_D1,17))/PipValue;
  d16=(iHigh(NULL,PERIOD_D1,16)-iLow(NULL,PERIOD_D1,16))/PipValue;
  d15=(iHigh(NULL,PERIOD_D1,15)-iLow(NULL,PERIOD_D1,15))/PipValue;
  d14=(iHigh(NULL,PERIOD_D1,14)-iLow(NULL,PERIOD_D1,14))/PipValue;
  d13=(iHigh(NULL,PERIOD_D1,13)-iLow(NULL,PERIOD_D1,13))/PipValue;
  d12=(iHigh(NULL,PERIOD_D1,12)-iLow(NULL,PERIOD_D1,12))/PipValue;
  d11=(iHigh(NULL,PERIOD_D1,11)-iLow(NULL,PERIOD_D1,11))/PipValue;
  d10=(iHigh(NULL,PERIOD_D1,10)-iLow(NULL,PERIOD_D1,10))/PipValue;
  d9=(iHigh(NULL,PERIOD_D1,9)-iLow(NULL,PERIOD_D1,9))/PipValue;
  d8=(iHigh(NULL,PERIOD_D1,8)-iLow(NULL,PERIOD_D1,8))/PipValue;
  d7=(iHigh(NULL,PERIOD_D1,7)-iLow(NULL,PERIOD_D1,7))/PipValue;
  d6=(iHigh(NULL,PERIOD_D1,6)-iLow(NULL,PERIOD_D1,6))/PipValue;
  d5=(iHigh(NULL,PERIOD_D1,5)-iLow(NULL,PERIOD_D1,5))/PipValue;
  d4=(iHigh(NULL,PERIOD_D1,4)-iLow(NULL,PERIOD_D1,4))/PipValue;
  d3=(iHigh(NULL,PERIOD_D1,3)-iLow(NULL,PERIOD_D1,3))/PipValue;
  d2=(iHigh(NULL,PERIOD_D1,2)-iLow(NULL,PERIOD_D1,2))/PipValue;
  d1=(iHigh(NULL,PERIOD_D1,1)-iLow(NULL,PERIOD_D1,1))/PipValue;
   
  adr1 = MathRound(d1);
  adr5 = MathRound(d1 + d2 + d3 + d4 + d5)/5;
  adr10 = MathRound(d1 + d2 + d3 + d4 + d5 + d6 + d7 + d8 + d9 + d10)/10;
  adr20 = MathRound(d1 + d2 + d3 + d4 + d5 + d6 + d7 + d8 + d9 + d10 + d11 + d12 + d13 + d14 + d15 + d16 + d17 + d18 + d19 + d20)/20;
   
  text="ADR(20) = "+DoubleToString(adr20,0);
  ObjectSetText("xADR0",text,8,"Arial Black",Blue);      // $$$ "ADR20" Label 
   
  text="ADR(10) = "+DoubleToString(adr10,0);
  ObjectSetText("xADR1",text,8,"Arial Black",Blue);      // $$$ "ADR10" Label    
   
  text="ADR(5)   = "+DoubleToString(adr5,0);
  ObjectSetText("xADR2",text,8,"Arial Black",Blue);      // $$$ "ADR5" Label
   
  }
//+------------------------------------------------------------------+  
void InitADR()
  {
     ObjectDelete("xADR0");
     ObjectCreate("xADR0", OBJ_LABEL, 0, 0, 0);          // Average Daily Range ("ADR20" Label)
     ObjectSet("xADR0", OBJPROP_CORNER, 0);
     ObjectSet("xADR0", OBJPROP_XDISTANCE, 10);
     ObjectSet("xADR0", OBJPROP_YDISTANCE, 20);   
     
     ObjectDelete("xADR1");
     ObjectCreate("xADR1", OBJ_LABEL, 0, 0, 0);          // Average Daily Range ("ADR10" Label)
     ObjectSet("xADR1", OBJPROP_CORNER, 0);
     ObjectSet("xADR1", OBJPROP_XDISTANCE, 10);
     ObjectSet("xADR1", OBJPROP_YDISTANCE, 35);  
     
     ObjectDelete("xADR2");
     ObjectCreate("xADR2", OBJ_LABEL, 0, 0, 0);          // Average Daily Range ("ADR5" Label)
     ObjectSet("xADR2", OBJPROP_CORNER, 0);
     ObjectSet("xADR2", OBJPROP_XDISTANCE, 10);
     ObjectSet("xADR2", OBJPROP_YDISTANCE, 50);
     
     ObjectDelete("xADR3");
     ObjectCreate("xADR3", OBJ_LABEL, 0, 0, 0);          // "Today's Range" Label
     ObjectSet("xADR3", OBJPROP_CORNER, 0);
     ObjectSet("xADR3", OBJPROP_XDISTANCE, 10);
     ObjectSet("xADR3", OBJPROP_YDISTANCE, 65); 
     
     ObjectDelete("xADR4");
     ObjectCreate("xADR4", OBJ_LABEL, 0, 0, 0);
     ObjectSet("xADR4", OBJPROP_CORNER, 0);              // "ADR Top" Label
     ObjectSet("xADR4", OBJPROP_XDISTANCE, 10);
     ObjectSet("xADR4", OBJPROP_YDISTANCE, 80);
     
     ObjectDelete("xADR5");
     ObjectCreate("xADR5", OBJ_LABEL, 0, 0, 0);          // "ADR Bottom" Label
     ObjectSet("xADR5", OBJPROP_CORNER, 0);
     ObjectSet("xADR5", OBJPROP_XDISTANCE, 10);
     ObjectSet("xADR5", OBJPROP_YDISTANCE, 95);
     
     ObjectDelete("DailyPipsObj2");
     ObjectCreate("DailyPipsObj2", OBJ_LABEL, 0, 0, 0);
     ObjectSet("DailyPipsObj2", OBJPROP_CORNER, 0);      // Daily Pips
     ObjectSet("DailyPipsObj2", OBJPROP_XDISTANCE, 10);
     ObjectSet("DailyPipsObj2", OBJPROP_YDISTANCE, 120);
     
   // $$$ On Chart ADR Price Label Display     
     ObjectDelete("ADR5_High_Obj");
     ObjectCreate("ADR5_High_obj" ,OBJ_ARROW,0,Time[0],0);  //Get this to calc price and plot 
     ObjectSet("ADR5_High_obj",OBJPROP_ARROWCODE,SYMBOL_RIGHTPRICE); 
     ObjectSet("ADR5_High_obj" ,OBJPROP_COLOR,Crimson);
     ObjectSet("ADR5_High_obj" ,OBJPROP_WIDTH,1);	
     
     ObjectDelete("ADR5_Low_Obj");
     ObjectCreate("ADR5_Low_obj",OBJ_ARROW,0,Time[0],0);  //Get this to calc price and plot  
     ObjectSet("ADR5_Low_obj",OBJPROP_ARROWCODE,SYMBOL_RIGHTPRICE); 
     ObjectSet("ADR5_Low_obj" ,OBJPROP_COLOR,DarkGreen);
     ObjectSet("ADR5_Low_obj" ,OBJPROP_WIDTH,1);	
     
     ObjectDelete("ADR10_High_Obj");
     ObjectCreate("ADR10_High_obj" ,OBJ_ARROW,0,Time[0],0);  //Get this to calc price and plot 
     ObjectSet("ADR10_High_obj",OBJPROP_ARROWCODE,SYMBOL_RIGHTPRICE); 
     ObjectSet("ADR10_High_obj" ,OBJPROP_COLOR,Red);
     ObjectSet("ADR10_High_obj" ,OBJPROP_WIDTH,1);	
     
     ObjectDelete("ADR10_Low_Obj");
     ObjectCreate("ADR10_Low_obj",OBJ_ARROW,0,Time[0],0);  //Get this to calc price and plot  
     ObjectSet("ADR10_Low_obj",OBJPROP_ARROWCODE,SYMBOL_RIGHTPRICE); 
     ObjectSet("ADR10_Low_obj" ,OBJPROP_COLOR,LimeGreen);
     ObjectSet("ADR10_Low_obj" ,OBJPROP_WIDTH,1);	   
     
     ObjectDelete("ADR20_High_Obj");
     ObjectCreate("ADR20_High_obj" ,OBJ_ARROW,0,Time[0],0);  //Get this to calc price and plot 
     ObjectSet("ADR20_High_obj",OBJPROP_ARROWCODE,SYMBOL_RIGHTPRICE); 
     ObjectSet("ADR20_High_obj" ,OBJPROP_COLOR,Orange);
     ObjectSet("ADR20_High_obj" ,OBJPROP_WIDTH,1);	
      
     ObjectDelete("ADR20_Low_Obj");
     ObjectCreate("ADR20_Low_obj",OBJ_ARROW,0,Time[0],0);  //Get this to calc price and plot  
     ObjectSet("ADR20_Low_obj",OBJPROP_ARROWCODE,SYMBOL_RIGHTPRICE); 
     ObjectSet("ADR20_Low_obj" ,OBJPROP_COLOR,YellowGreen);
     ObjectSet("ADR20_Low_obj" ,OBJPROP_WIDTH,1);
  } 
//+------------------------------------------------------------------+  
void BuildADR()
  {
  
  double TodaysLow  =  iLow (Symbol(),PERIOD_D1,0);
  double TodaysHigh =  iHigh(Symbol(),PERIOD_D1,0);
  
  double TodaysRange =  MathRound((TodaysHigh - TodaysLow)/PipValue );
  double RmUp         =  MathRound( adr5 - (Bid - TodaysLow)/PipValue );
  double RmDn         =  MathRound( adr5 - (TodaysHigh - Bid)/PipValue );
  
  double ADR5_High       = TodaysHigh + (adr5 - TodaysRange)*PipValue;
  double ADR5_Low        = TodaysLow - (adr5 - TodaysRange)*PipValue;
  
  //ADR10_High     =  TodaysLow  + (adr10 * PipValue);
  //ADR10_Low      =  TodaysHigh - (adr10 * PipValue); 
  double ADR10_High       = TodaysHigh + (adr10 - TodaysRange)*PipValue;
  double ADR10_Low        = TodaysLow - (adr10 - TodaysRange)*PipValue;
  
  //ADR20_High     =  TodaysLow  + (adr20 * PipValue);
  //ADR20_Low      =  TodaysHigh - (adr20 * PipValue);
  double ADR20_High       = TodaysHigh + (adr20 - TodaysRange)*PipValue;
  double ADR20_Low        = TodaysLow - (adr20 - TodaysRange)*PipValue;  
  
  datetime time1d = iTime(NULL,PERIOD_D1,0)+60*60*24;
  

  // $$$ DISPLAY OF PRICE LABELS ON THE CHART

  // $$$ Set ADR Price Label HIGH     
  ObjectSet("ADR5_High_obj"    ,OBJPROP_TIME1,time1d);  //Get this to calc price and plot 
  ObjectSet("ADR5_High_obj"    ,OBJPROP_PRICE1,ADR5_High);  //Get this to calc price and plot    
  ObjectSetText("ADR5_High_obj","ADR5 High=" + DoubleToString(ADR5_High,4),8,"Compact",Red);
  // $$$ Set ADR Price Label LOW     
  ObjectSet("ADR5_Low_obj"    ,OBJPROP_TIME1,time1d);  //Get this to calc price and plot 
  ObjectSet("ADR5_Low_obj"    ,OBJPROP_PRICE1,ADR5_Low);  //Get this to calc price and plot    
  ObjectSetText("ADR5_Low_obj","ADR5 Low="+DoubleToString(ADR5_Low,4),8,"Compact",Red);

  // $$$ Set ADR Price Label HIGH     
  ObjectSet("ADR10_High_obj"    ,OBJPROP_TIME1,time1d);  //Get this to calc price and plot 
  ObjectSet("ADR10_High_obj"    ,OBJPROP_PRICE1,ADR10_High);  //Get this to calc price and plot    
  ObjectSetText("ADR10_High_obj","ADR10 High=" + DoubleToString(ADR10_High,4),8,"Compact",Red);
  // $$$ Set ADR Price Label LOW     
  ObjectSet("ADR10_Low_obj"    ,OBJPROP_TIME1,time1d);  //Get this to calc price and plot 
  ObjectSet("ADR10_Low_obj"    ,OBJPROP_PRICE1,ADR10_Low);  //Get this to calc price and plot    
  ObjectSetText("ADR10_Low_obj","ADR10 Low="+DoubleToString(ADR10_Low,4),8,"Compact",Red);

  // $$$ Set ADR Price Label HIGH     
  ObjectSet("ADR20_High_obj"    ,OBJPROP_TIME1,time1d);  //Get this to calc price and plot 
  ObjectSet("ADR20_High_obj"    ,OBJPROP_PRICE1,ADR20_High);  //Get this to calc price and plot    
  ObjectSetText("ADR20_High_obj","ADR20 High=" + DoubleToString(ADR20_High,4),8,"Compact",Red);
  // $$$ Set ADR Price Label LOW     
  ObjectSet("ADR20_Low_obj"    ,OBJPROP_TIME1,time1d);  //Get this to calc price and plot 
  ObjectSet("ADR20_Low_obj"    ,OBJPROP_PRICE1,ADR20_Low);  //Get this to calc price and plot    
  ObjectSetText("ADR20_Low_obj","ADR20 Low="+DoubleToString(ADR20_Low,4),8,"Compact",Red);
   
  text         ="Today    = " + DoubleToString(TodaysRange,0);                       // "Today's Range" Label
  ObjectSetText("xADR3",text,8,"Arial Black", Blue);  
      
  text         ="ADR(5) Top@" + DoubleToString(ADR5_High,4)+" = "+ DoubleToString(RmUp,0) + " Pips Away";      // "ADR Top" Label
  if (RmUp<20)   ObjectSetText("xADR4",text,8, "Compact", Red);
  else   ObjectSetText("xADR4",text,8, "Arial Black", Green);
  
  text         ="ADR(5) Bottom@" + DoubleToString(ADR5_Low,4)+" = "+ DoubleToString(RmDn,0) + " Pips Away";    // "ADR Bottom" Label
  if (RmDn<20)   ObjectSetText("xADR5",text,8, "Compact", Red); 
  else ObjectSetText("xADR5",text,8, "Arial Black", Green); 

  if( iClose(Symbol(),PERIOD_D1,0 ) > iOpen(Symbol(),PERIOD_D1,0 )) DailyPips = (iClose(Symbol(),PERIOD_D1,0 ) - iOpen(Symbol(),PERIOD_D1,0 ) ) /PipValue;
  else if( iOpen(Symbol(),PERIOD_D1,0 ) > iClose(Symbol(),PERIOD_D1,0 )) DailyPips = (iOpen(Symbol(),PERIOD_D1,0 ) - iClose(Symbol(),PERIOD_D1,0 ) ) /PipValue;

  // $$$ Change Display of DailyPips by Daily Pip Range for the day.
  // $$$ YELLOW
  if (DailyPips < 20) 
  ObjectSetText ("DailyPipsObj2",DoubleToString( (iClose(Symbol(),PERIOD_D1,0 ) - iOpen(Symbol(),PERIOD_D1,0 ))/PipValue ,0),14,"Arial Black",Yellow);
  // $$$ RED
  else if (  
            (iOpen(Symbol(),PERIOD_D1,0 ) - iClose(Symbol(),PERIOD_D1,0 ))/PipValue >20 ) 
  ObjectSetText ("DailyPipsObj2",DoubleToString( (iClose(Symbol(),PERIOD_D1,0 ) - iOpen(Symbol(),PERIOD_D1,0 ))/PipValue ,0),14,"Arial Black",Red);
  // $$$ GREEN 
  else if (  
            (iClose(Symbol(),PERIOD_D1,0 ) - iOpen(Symbol(),PERIOD_D1,0 ))/PipValue >20 ) 	  

  ObjectSetText ("DailyPipsObj2",DoubleToString( (iClose(Symbol(),PERIOD_D1,0 ) - iOpen(Symbol(),PERIOD_D1,0 ))/PipValue ,0),14,"Arial Black",Green); 
  }
*/

//+------------------------------------------------------------------+  
