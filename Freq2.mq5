#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict
#property indicator_plots 0
#define Name MQLInfoString(MQL_PROGRAM_NAME)

input int nod1 = 250; //Number of days to draw (mid max 300)
int nod = nod1;
input string test = "";
bool showpast = false;

//+INIT FUNCTION-----------------------------------------------------+
int OnInit()
{
	if (nod > 300)
		nod = 300;
	if (iBars(_Symbol, PERIOD_D1) < nod)
		nod = iBars(_Symbol, PERIOD_D1) - 2;
	if (ChartPeriod() <= 16408)
	{
		for (int x = nod; x >= 1; x--)
		{
			datetime dt = iTime(_Symbol, PERIOD_D1, x);
			if (TimeDayOfWeek(dt) == FRIDAY || TimeDayOfWeek(dt) == SATURDAY)
				continue;
			buildd(x);
		}
	}
	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+CALCULATE---------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	datetime expiry = D'2022.06.30 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("Freq expired on " + TimeToString(expiry, TIME_DATE) + ", contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true && ChartPeriod() <= 16408)
	{

		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, PERIOD_D1, 0)) // && (TimeCurrent() >= (iTime(NULL, PERIOD_D1, 0) + 35) && TimeCurrent() <= (iTime(NULL, PERIOD_D1, 0) + 4020)))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, PERIOD_D1, 0);
		}
		if (new_1m_check)
		{
			//int start1 = GetTickCount();
			//int end1 = GetTickCount() - start1; Print (end1);
			//int start2 = GetTickCount();
			for (int x = nod; x >= 1; x--)
			{
				datetime dt = iTime(_Symbol, PERIOD_D1, x);
				if (TimeDayOfWeek(dt) == FRIDAY || TimeDayOfWeek(dt) == SATURDAY)
					continue;
				buildd(x);
			}
			//int end2 = GetTickCount() - start2; Print (end2);
			new_1m_check = false;
		}

		bool new_2m_check = false;
		static datetime start_2m_time = 0;
		if (start_2m_time < iTime(NULL, PERIOD_H1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_H1, 0) + 35) && TimeCurrent() <= (iTime(NULL, PERIOD_H1, 0) + 420)))
		{
			new_2m_check = true;
			start_2m_time = iTime(NULL, PERIOD_H1, 0);
		}
		if (new_2m_check)
		{
			for (int x = nod; x >= 1; x--)
			{
				datetime dt = iTime(_Symbol, PERIOD_D1, x);
				if (TimeDayOfWeek(dt) == FRIDAY || TimeDayOfWeek(dt) == SATURDAY)
					continue;
				buildd(x);
			}
			new_2m_check = false;
		}
	}
	return (rates_total);
}
//+------------------------------------------------------------------+

//+ChartEvent function-----------------------------------------------+
void OnChartEvent(const int id,
				  const long &lparam,
				  const double &dparam,
				  const string &sparam)
{
	//---
	{ //Switch TF lines on/off
		if (id == CHARTEVENT_KEYDOWN)
		{
			if (lparam == StringGetCharacter("S", 0) && showpast == false)
			{
				showpast = true;
				for (int x = nod; x >= 1; x--)
				{
					buildd(x);
				}
			}
			else if (lparam == StringGetCharacter("S", 0) && showpast == true)
			{
				showpast = false;
				for (int x = nod; x >= 1; x--)
				{
					buildd(x);
				}
			}
		}
	}
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+CREATE PAST & CURRENT SHAPES--------------------------------------+
void buildd(const int D)
{ //*****CH*****//
	string obname;

	double HD1[], LD1[], CD1[], OD1[];
	datetime TD1[];
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(OD1, true);
	ArraySetAsSeries(TD1, true);
	ArrayResize(HD1, nod + 2);
	ArrayResize(LD1, nod + 2);
	ArrayResize(CD1, nod + 2);
	ArrayResize(OD1, nod + 2);
	ArrayResize(TD1, nod + 2);
	CopyHigh(_Symbol, PERIOD_D1, 0, nod + 2, HD1);
	CopyLow(_Symbol, PERIOD_D1, 0, nod + 2, LD1);
	CopyClose(_Symbol, PERIOD_D1, 0, nod + 2, CD1);
	CopyOpen(_Symbol, PERIOD_D1, 0, nod + 2, OD1);
	CopyTime(_Symbol, PERIOD_D1, 0, nod + 2, TD1);

	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT); // number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	if (ChartPeriod() > 5 && D >= 1)
	{
		ObjectsDeleteAll(0, Name + "OlineCon-");
		ObjectsDeleteAll(0, Name + "Oline-");

		obname = Name + "OLine-" + IntegerToString(D);
		objtrend(obname, OD1[D], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrNONE, DoubleToString(OD1[D], _Digits));
		ObjectSetDouble(0, obname, OBJPROP_PRICE, 1, (HD1[D + 1] + LD1[D + 1]) / 2);
		ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false)]);
		ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
		if (HD1[D] < (HD1[D + 1] + LD1[D + 1]) / 2)
		{
			//ObjectDelete(obname);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOlive);
			ObjectSetDouble(0, obname, OBJPROP_PRICE, HD1[D]);
		}
		else if (LD1[D] > (HD1[D + 1] + LD1[D + 1]) / 2)
		{
			//ObjectDelete(obname);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOlive);
			ObjectSetDouble(0, obname, OBJPROP_PRICE, LD1[D]);
		}

		int t = 0, y = 0;

		if ((HD1[D] < (HD1[D + 1] + LD1[D + 1]) / 2) || (LD1[D] > (HD1[D + 1] + LD1[D + 1]) / 2))
		{
			obname = Name + "OLineCon-" + IntegerToString(D);
			objtrend(obname, (HD1[D + 1] + LD1[D + 1]) / 2, iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false), 4, clrGreenYellow, DoubleToString((HD1[D + 1] + LD1[D + 1]) / 2, _Digits) + " @ " + TimeToString(TD1[D], TIME_DATE));
			if ((HD1[D] < (HD1[D + 1] + LD1[D + 1]) / 2) && (HD1[iHighest(_Symbol, PERIOD_D1, MODE_HIGH, D, 1)] > ObjectGetDouble(0, obname, OBJPROP_PRICE, 1)))
			{
				for (int x = iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false); x >= 1; x--)
				{
					if (iHigh(_Symbol, PERIOD_CURRENT, x) < ObjectGetDouble(0, obname, OBJPROP_PRICE, 1))
						t++;
					if (iHigh(_Symbol, PERIOD_CURRENT, x) > ObjectGetDouble(0, obname, OBJPROP_PRICE, 1))
						break;
				}
				ObjectDelete(0, Name + "OLineCon-" + IntegerToString(D - 1));
				if (!showpast)
					ObjectDelete(0, obname);
				else if (showpast)
				{
					ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
					ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOlive);
					ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false) - t], false)]);
				}
			}
			if ((LD1[D] > (HD1[D + 1] + LD1[D + 1]) / 2) && (LD1[iLowest(_Symbol, PERIOD_D1, MODE_LOW, D, 1)] < ObjectGetDouble(0, obname, OBJPROP_PRICE, 1)))
			{
				for (int x = iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false); x >= 1; x--)
				{
					if (iLow(_Symbol, PERIOD_CURRENT, x) > ObjectGetDouble(0, obname, OBJPROP_PRICE, 1))
						y++;
					if (iLow(_Symbol, PERIOD_CURRENT, x) < ObjectGetDouble(0, obname, OBJPROP_PRICE, 1))
						break;
				}
				ObjectDelete(0, Name + "OLineCon-" + IntegerToString(D - 1));
				if (!showpast)
					ObjectDelete(0, obname);
				else if (showpast)
				{
					ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
					ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOlive);
					ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[iBarShift(_Symbol, PERIOD_CURRENT, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false) - y], false)]);
				}
			}
		}

		if (ObjectGetInteger(0, Name + "OLineCon-" + IntegerToString(D), OBJPROP_COLOR) == clrGreenYellow)
			ObjectSetInteger(0, Name + "OLine-" + IntegerToString(D), OBJPROP_COLOR, clrGreenYellow);
		if (!showpast && ObjectGetInteger(0, Name + "OLineCon-" + IntegerToString(D), OBJPROP_COLOR) != clrGreenYellow)
			ObjectDelete(0, Name + "OLine-" + IntegerToString(D));
	}
}
//+------------------------------------------------------------------+

//+TL CREATE---------------------------------------------------------+
void objtrend(string name, double pr1, int t, int pir, color col, string buls)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT); // number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Time[0] + pir * Period() / ChartPeriod());
	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr1);
	ObjectSetInteger(0, name, OBJPROP_STYLE, 0);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
	ObjectSetInteger(0, name, OBJPROP_RAY, false);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, buls);
}
//+------------------------------------------------------------------+

int TimeDayOfWeek(datetime date)
{
	MqlDateTime tm;
	TimeToStruct(date, tm);
	return (tm.day_of_week);
}