//+------------------------------------------------------------------+
//|                                                       testAA.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_separate_window

double bot1[];
double ima[];
#property indicator_buffers 3
#property indicator_color1 clrDodgerBlue
#property indicator_color2 clrOrange
int ima_handle;
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
	
	PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_HISTOGRAM);
	PlotIndexSetInteger(0, PLOT_LINE_WIDTH, 3);
	SetIndexBuffer(0, bot1);
	
	SetIndexBuffer(2, ima);
	
	//--- indicator buffers mapping
   IndicatorSetInteger(INDICATOR_LEVELS, 2);
   IndicatorSetInteger(INDICATOR_LEVELCOLOR, 0, clrBlack);
   IndicatorSetInteger(INDICATOR_LEVELSTYLE, 0, 0);
   IndicatorSetDouble(INDICATOR_LEVELVALUE, 0, 10);
   IndicatorSetInteger(INDICATOR_LEVELCOLOR, 1, clrBlack);
   IndicatorSetInteger(INDICATOR_LEVELSTYLE, 1, 0);
   IndicatorSetDouble(INDICATOR_LEVELVALUE, 1, -10);
   
   ima_handle = iMA(_Symbol, PERIOD_CURRENT, 10, 0, MODE_EMA, PRICE_CLOSE);
   
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
	bool checka = false;
	static datetime checka_time = 0;
	if (checka_time < iTime(_Symbol, PERIOD_M1, 0))
	{
	   checka_time = iTime(_Symbol, PERIOD_M1, 0);
	   checka = true;
	}
	if (checka)
	{
   	if(!CopyBuffer(ima_handle, 0, 0, 1000, ima)) Print("error");
   	
   	datetime newsession = 0;
   	double totvol = 0;
   	ArraySetAsSeries(bot1, true);
   	ArrayResize(bot1, 100000);
      
      int daymin = 0;
      if (ChartPeriod() <= 61)
   	   daymin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 5), false);
   	bot1[daymin] = 0;
   	for (int x = daymin; x >= 0; x--)
   	{
   		if (iClose(_Symbol, PERIOD_CURRENT, x) > iClose(_Symbol, PERIOD_CURRENT, x + 1) && (iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) > 0)) totvol += 1;
   		if (iClose(_Symbol, PERIOD_CURRENT, x) < iClose(_Symbol, PERIOD_CURRENT, x + 1) && (MathAbs(iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_CURRENT, x)) > MathAbs(iClose(_Symbol, PERIOD_CURRENT, x + 1) - iOpen(_Symbol, PERIOD_CURRENT, x + 1))) && (iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) > 0)) totvol += -0.5;
   		if (iClose(_Symbol, PERIOD_CURRENT, x) < iClose(_Symbol, PERIOD_CURRENT, x + 1) && (MathAbs(iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_CURRENT, x)) < MathAbs(iClose(_Symbol, PERIOD_CURRENT, x + 1) - iOpen(_Symbol, PERIOD_CURRENT, x + 1))) && (iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) > 0)) totvol += -0.25;
   
   		if (iClose(_Symbol, PERIOD_CURRENT, x) < iClose(_Symbol, PERIOD_CURRENT, x + 1) && (iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) < 0)) totvol += -1;
   		if (iClose(_Symbol, PERIOD_CURRENT, x) > iClose(_Symbol, PERIOD_CURRENT, x + 1) && (MathAbs(iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_CURRENT, x)) > MathAbs(iClose(_Symbol, PERIOD_CURRENT, x + 1) - iOpen(_Symbol, PERIOD_CURRENT, x + 1))) && (iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) < 0)) totvol += 0.5;
   		if (iClose(_Symbol, PERIOD_CURRENT, x) > iClose(_Symbol, PERIOD_CURRENT, x + 1) && (MathAbs(iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_CURRENT, x)) < MathAbs(iClose(_Symbol, PERIOD_CURRENT, x + 1) - iOpen(_Symbol, PERIOD_CURRENT, x + 1))) && (iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) < 0)) totvol += 0.25;
   		
   		if (iClose(_Symbol, PERIOD_CURRENT, x) - iHigh(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false) + 1) > 0) totvol += 2;
   		//if (iClose(_Symbol, PERIOD_CURRENT, x) - iHigh(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) < 0 && (iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) > 0)) totvol += -0.25;
   		if (iLow(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false) + 1) - iClose(_Symbol, PERIOD_CURRENT, x) > 0) totvol += -2;
   		//if (iLow(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) - iClose(_Symbol, PERIOD_CURRENT, x) < 0 && (iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) < 0)) totvol += 0.25;
   		if (iClose(_Symbol, PERIOD_CURRENT, x) > iClose(_Symbol, PERIOD_CURRENT, x + 1) && (iClose(_Symbol, PERIOD_CURRENT, x) > ima[x]) && (iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) < 0)) totvol += 0.5;
   		if (iClose(_Symbol, PERIOD_CURRENT, x) < iClose(_Symbol, PERIOD_CURRENT, x + 1) && (iClose(_Symbol, PERIOD_CURRENT, x) < ima[x]) && (iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) > 0)) totvol += -0.5;
   		
   		//if (iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) > 0 && iClose(_Symbol, PERIOD_CURRENT, x + 1) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) < 0) totvol += 5;
   		//if (iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) < 0 && iClose(_Symbol, PERIOD_CURRENT, x + 1) - iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, x), false)) > 0) totvol -= 5;
   		
   		if (TimeDay(time[x]) != TimeDay(newsession))
   		{
   			newsession = time[x];
   			totvol = 0;
   		}
   		if (totvol != 0)
   		{
   			bot1[x] = totvol;
   			//if (totvol < 0) bot2[x] = totvol;
   		}
   	}
		checka = false;
		}
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+

//+TIMEDAY / HOUR / DAYOFWEEK MQL4-----------------------------------+
int TimeDay(datetime date)
{
	MqlDateTime tm;
	TimeToStruct(date, tm);
	return (tm.day);
}