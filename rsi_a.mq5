//+------------------------------------------------------------------+
//|                                                        rsi_a.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_separate_window

#define DATA_LIMIT 1440

double rsi[], rsi_a[];
double hori1[], hori2[];
double action1[], action2[];
double rev1[], rev2[];
double touch1[], touch2[];
double bblow[], bbhigh[], ma200[];

input int show_days = 1;

#property indicator_minimum -50
#property indicator_maximum 50

#property indicator_buffers 20
#property indicator_plots 10

#property indicator_type1 DRAW_COLOR_HISTOGRAM
#property indicator_style1 STYLE_SOLID
#property indicator_width1 10
#property indicator_color1 clrBlue
#property indicator_label1 "hori1"
#property indicator_type2 DRAW_COLOR_HISTOGRAM 
#property indicator_style2 STYLE_SOLID
#property indicator_width2 10
#property indicator_color2 clrYellow
#property indicator_label2 "action1"
#property indicator_type3 DRAW_COLOR_HISTOGRAM 
#property indicator_style3 STYLE_SOLID
#property indicator_width3 10
#property indicator_color3 clrLime
#property indicator_label3 "rev1"
#property indicator_type4 DRAW_COLOR_HISTOGRAM 
#property indicator_style4 STYLE_SOLID
#property indicator_width4 10
#property indicator_color4 clrWhite
#property indicator_label4 "touch1"
#property indicator_type9 DRAW_NONE
#property indicator_color9 clrGreen
#property indicator_label9 "rsi"
#property indicator_type10 DRAW_NONE
#property indicator_color10 clrGreen
#property indicator_label10 "bblow"
#property indicator_type11 DRAW_NONE
#property indicator_color11 clrGreen
#property indicator_label11 "bbhigh"
#property indicator_type12 DRAW_NONE
#property indicator_color12 clrGreen
#property indicator_label12 "ma200"

int rsi_handle, rsi_a_handle, bb_handle, ma200_handle;
   
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping

	IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
	   
	SetIndexBuffer(0, hori1, INDICATOR_CALCULATIONS);
	//PlotIndexSetDouble(0, 1, 2)
		
	SetIndexBuffer(2, action1, INDICATOR_CALCULATIONS);
	
	SetIndexBuffer(4, rev1, INDICATOR_CALCULATIONS);
	
	SetIndexBuffer(6, touch1, INDICATOR_CALCULATIONS);

	SetIndexBuffer(8, rsi_a, INDICATOR_DATA);
	
	SetIndexBuffer(9, rsi, INDICATOR_CALCULATIONS);
	
	SetIndexBuffer(10, bblow, INDICATOR_CALCULATIONS);
	
	SetIndexBuffer(11, bbhigh, INDICATOR_CALCULATIONS);
	
	SetIndexBuffer(12, ma200, INDICATOR_CALCULATIONS);
	
	PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(4, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(5, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(6, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(7, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(8, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(9, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(10, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(11, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(12, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(13, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	
	//IndicatorSetDouble(INDICATOR_MINIMUM, -50);
	//IndicatorSetDouble(INDICATOR_MAXIMUM, 50);
	
	rsi_handle = iRSI(_Symbol, PERIOD_CURRENT, 8, PRICE_CLOSE);
	rsi_a_handle = iRSI(_Symbol, PERIOD_CURRENT, 8, PRICE_CLOSE) - 50;
	bb_handle = iBands(_Symbol, PERIOD_CURRENT, 20, 0, 2, PRICE_CLOSE);
	ma200_handle = iMA(_Symbol, PERIOD_CURRENT, 200, 0, MODE_EMA, PRICE_CLOSE);
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
	
	//--- check for rates total
	if (rates_total < DATA_LIMIT)
		return (0); // not enough bars for calculation
			//--- not all data may be calculated
	int calculated = BarsCalculated(rsi_handle);
	//Print(BarsCalculated(rsi_handle));
	//Print(rates_total);
	//Print(calculated + " " + rates_total);
	if (calculated < rates_total)
	{
		Print("Not all data of RSI is calculated (", calculated, "bars ). Error", GetLastError());
		return (0);
	}
	//--- we can copy not all data
	int to_copy;
	if (prev_calculated > rates_total || prev_calculated < 0)
		to_copy = rates_total;
	else
	{
		to_copy = rates_total - prev_calculated;
		if (prev_calculated > 0)
			to_copy++;
	}
	ArraySetAsSeries(rsi, true);
	ArraySetAsSeries(rsi_a, true);
	ArraySetAsSeries(bblow, true);
	ArraySetAsSeries(bbhigh, true);
	ArraySetAsSeries(ma200, true);
	
	ArraySetAsSeries(action1, true);
	ArraySetAsSeries(hori1, true);
	ArraySetAsSeries(rev1, true);
	ArraySetAsSeries(touch1, true);
		
	CopyBuffer(rsi_handle, 0, 0, to_copy, rsi);
	CopyBuffer(bb_handle, 2, 0, to_copy, bblow);
	CopyBuffer(bb_handle, 1, 0, to_copy, bbhigh);
	CopyBuffer(ma200_handle, 0, 0, to_copy, ma200);
	//CopyBuffer(rsi_a_handle, 0, 0, to_copy, rsi_a);
	//Print(rsi[0] + " " + rsi[1]);
	//Print(iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, show_days - 1), false));
	//Print(ma200[0] + " " + ma200[1]);
	for (int i = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false); i >= 0; i--)
	{
		rsi_a[i] = rsi[i] - 50.0;
		
		if (rsi[i] > 70 && iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0)) //iClose(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false)) > iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false))) //iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0))
		{
		   action1[i] = 50; //action2[i] = 0;
		}
		if (iHigh(_Symbol, PERIOD_CURRENT, i) > bbhigh[i] && iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0)) //iClose(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false)) > iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false))) //iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0))
		{
		   action1[i] = 50; //action2[i] = 0;
		}
		if (rsi[i] < 50 && iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0)) // iClose(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false)) > iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false))) //iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0))
		{
		   hori1[i] = 50; //hori2[i] = 0;
		}
		if (iLow(_Symbol, PERIOD_CURRENT, i) < bblow[i] && iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0)) //iClose(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false)) > iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false))) //iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0))
		{
		   rev1[i] = 25; //rev2[i] = 0;
		}
		if (((iLow(_Symbol, PERIOD_CURRENT, i) < ma200[i] && iHigh(_Symbol, PERIOD_CURRENT, i) > ma200[i]) || (iLow(_Symbol, PERIOD_CURRENT, i + 1) < ma200[i + 1] && iHigh(_Symbol, PERIOD_CURRENT, i + 1) > ma200[i + 1]) || (iLow(_Symbol, PERIOD_CURRENT, i + 2) < ma200[i + 2] && iHigh(_Symbol, PERIOD_CURRENT, i + 2) > ma200[i + 2]) || (iLow(_Symbol, PERIOD_CURRENT, i + 3) < ma200[i + 3] && iHigh(_Symbol, PERIOD_CURRENT, i + 3) > ma200[i + 3])) && iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0)) //iClose(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false)) > iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false))) //iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0))
		{
		   touch1[i] = 50; //touch2[i] = 0;
		}
		
		//
		if (rsi[i] < 30 && iClose(_Symbol, PERIOD_D1, 0) < iOpen(_Symbol, PERIOD_D1, 0)) //iClose(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false)) < iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false))) //iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0))
		{
		   action1[i] = -50; //action2[i] = -50;
		}
		if (iLow(_Symbol, PERIOD_CURRENT, i) < bblow[i] && iClose(_Symbol, PERIOD_D1, 0) < iOpen(_Symbol, PERIOD_D1, 0)) //iClose(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false)) < iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false))) //iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0))
		{
		   action1[i] = -50; //action2[i] = -50;
		}
		if (rsi[i] > 50 && iClose(_Symbol, PERIOD_D1, 0) < iOpen(_Symbol, PERIOD_D1, 0)) //iClose(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false)) < iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false))) //iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0))
		{
		   hori1[i] = -50; //hori2[i] = -50;
		}
		if (iHigh(_Symbol, PERIOD_CURRENT, i) > bbhigh[i] && iClose(_Symbol, PERIOD_D1, 0) < iOpen(_Symbol, PERIOD_D1, 0)) //iClose(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false)) < iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false))) //iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0))
		{
		   rev1[i] = -25; //rev2[i] = -25;
		}
		if (((iLow(_Symbol, PERIOD_CURRENT, i) < ma200[i] && iHigh(_Symbol, PERIOD_CURRENT, i) > ma200[i]) || (iLow(_Symbol, PERIOD_CURRENT, i + 1) < ma200[i + 1] && iHigh(_Symbol, PERIOD_CURRENT, i + 1) > ma200[i + 1]) || (iLow(_Symbol, PERIOD_CURRENT, i + 2) < ma200[i + 2] && iHigh(_Symbol, PERIOD_CURRENT, i + 2) > ma200[i + 2]) || (iLow(_Symbol, PERIOD_CURRENT, i + 3) < ma200[i + 3] && iHigh(_Symbol, PERIOD_CURRENT, i + 3) > ma200[i + 3])) && iClose(_Symbol, PERIOD_D1, 0) < iOpen(_Symbol, PERIOD_D1, 0)) //iClose(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false)) < iOpen(_Symbol, PERIOD_D1, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, PERIOD_CURRENT, i), false))) //iClose(_Symbol, PERIOD_D1, 0) > iOpen(_Symbol, PERIOD_D1, 0))
		{
		   touch1[i] = -50; //touch2[i] = -50;
		}
		//|| rsi[i] < 40) { hori1[i] = 40; hori2[i] = 60; }
   }
   
	//Print(rsi_a[0] + " " + rsi_a[1]);
   /*
	for (int i = 0; i < iBarShift(_Symbol, PERIOD_M5, iTime(_Symbol, PERIOD_D1, 0), false); i++)
	{
		if (iLow(_Symbol, PERIOD_M5, i) < iBands(_Symbol, PERIOD_M5, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, i) )//&& iClose(_Symbol, PERIOD_D1, 0) < iOpen(_Symbol, PERIOD_D1, 0))
		{
		   action1[i] = 30; action2[i] = 70;
		}
		if (iHigh(_Symbol, PERIOD_M5, i) > iBands(_Symbol, PERIOD_M5, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, i) )//&& iClose(_Symbol, PERIOD_D1, 0) < iOpen(_Symbol, PERIOD_D1, 0))
		{
		   rev1[i] = 30; rev2[i] = 50;
		}
   }
	for (int i = 0; i < iBarShift(_Symbol, PERIOD_H1, iTime(_Symbol, PERIOD_D1, 0), false); i++)
	{
		if (iLow(_Symbol, PERIOD_H1, i) < iBands(_Symbol, PERIOD_H1, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, i) )//&& iClose(_Symbol, PERIOD_D1, 0) < iOpen(_Symbol, PERIOD_D1, 0))
		{
		   action1[i] = 30; action2[i] = 70;
		}
		if (iHigh(_Symbol, PERIOD_H1, i) > iBands(_Symbol, PERIOD_H1, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, i) )//&& iClose(_Symbol, PERIOD_D1, 0) < iOpen(_Symbol, PERIOD_D1, 0))
		{
		   rev1[i] = 30; rev2[i] = 50;
		}
	}
	*/	   
	
	
   
			
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+
