//+------------------------------------------------------------------+
//|                                               fracscalp-lite.mq5 |
//|                                                        Sakis-Pit |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Sakis-Pit"
#property link      ""
#property version   "1.00"
#property indicator_chart_window
#property strict
#property indicator_buffers 2
#property indicator_color1 clrBlue
#property indicator_color2 clrRed
#define EMPTY -1

#define Name MQLInfoString(MQL_PROGRAM_NAME)

//+INPUTS------------------------------------------------------------+
extern int periods = 8000; // Candles back to check
input int drawlines = 8000; // Candles back to mark with trendlines
ENUM_TIMEFRAMES DROP_TF = PERIOD_CURRENT; // Check every X period
input double dist = 0.25; // Distance of wick vs previous body

enum boxshow {
	notshowbox = 0, // False
	yesshowbox = 1 // True
};
boxshow showbox = 1; // Show moveable rectangle with multi TF resists/supports

double finp[];
double finn[];

int m, n;
double bobbit[8], doddit[8];
double bobbit1[8], doddit1[8];
double bobbit2[8], doddit2[8];

double Pip;
//+------------------------------------------------------------------+

//+INIT--------------------------------------------------------------+
int OnInit()
{
	IndicatorSetString(INDICATOR_SHORTNAME,"FractalScalp");
	ObjectsDeleteAll(0, Name);
	PlotIndexSetInteger(0,PLOT_DRAW_TYPE,DRAW_NONE);
	PlotIndexSetInteger(0,PLOT_ARROW,233);
	SetIndexBuffer(0,finp,INDICATOR_DATA);
	PlotIndexSetInteger(1,PLOT_DRAW_TYPE,DRAW_NONE);
	PlotIndexSetInteger(1,PLOT_ARROW,234);
	SetIndexBuffer(1,finn,INDICATOR_DATA);
	
	datetime Time[]; 
   ArraySetAsSeries(Time,true);
	
	if (GlobalVariableCheck("ub" + _Symbol) == false) GlobalVariableSet("ub" + _Symbol, 1); //Show moveable rectangle with MTF information on next support/resist & stats
	showbox = (boxshow)GlobalVariableGet("ub" + _Symbol);
	
	if (SymbolInfoInteger(0, SYMBOL_TRADE_CALC_MODE) == 0 && _Digits >= 3 && (StringFind(StringSubstr(_Symbol, 0, 1), "X", 0) == -1) && (StringFind(StringSubstr(_Symbol, 0, 1), "D", 0) == -1)) Pip = (_Point * MathPow(10, MathMod(_Digits, 2))); 
	else if (_Digits == 3 && StringFind(StringSubstr(_Symbol, 0, 1), "X", 0) == 0) Pip = 0.1;
	//else if ((MarketInfo(_Symbol, MODE_MARGINCALCMODE) == 0 && _Digits == 2)) Pip = 1;
	else Pip = 1;
	
	//statd();

	{//Run functions to fill table if enabled in GV
		if (showbox == 1) {
			//bigtfnumbers();
			//reclabloc();
		}
	}
		
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN PROGRAM------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	datetime expiry = D'2021.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("FractalScalp expired on " + TimeToString(expiry, TIME_DATE) + ", contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true) {

		ObjectSetTextMQL4(Name + " Time", TimeToString(TimeCurrent(), TIME_MINUTES | TIME_SECONDS), 9, "Arial", clrBlack);
		ObjectSetTextMQL4(Name + " Spread", "Spr: " + DoubleToString((SymbolInfoDouble(_Symbol,SYMBOL_ASK) - SymbolInfoDouble(_Symbol,SYMBOL_BID)) / Pip, 2), 9, "Arial", clrBlack);

		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, DROP_TF, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, DROP_TF, 0);
		}
		if (new_1m_check)
		{
			//ObjectsDeleteAll(0, Name);
			ChartRedraw();
			checkpre();
			//ydayweek();
			new_1m_check = false;
		}
		
		bool new_1h_check = false;
		static datetime start_1h_time = 0;
		if (start_1h_time < iTime(NULL, PERIOD_M5, 0))
		{
			new_1h_check = true;
			start_1h_time = iTime(NULL, PERIOD_M5, 0);
		}
		if (new_1h_check)
		{
			if (showbox == 1) {
				//statd();
				//bigtfnumbers();
				//reclabloc();
			}
			GlobalVariablesFlush();
			new_1h_check = false;
		}
	}//YesStop (expiry) end
	return(rates_total);
}
//+------------------------------------------------------------------+

//+ChartEvent function-----------------------------------------------+
void OnChartEvent(const int id,
	const long &lparam,
	const double &dparam,
	const string &sparam)
{
	//---
	{//Redraw lines
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + " Redraw")
			{
				//ObjectsDeleteAll(0, Name);
				ChartRedraw(); checkpre(); ydayweek(); if (showbox == 1) { /*statd(); bigtfnumbers(); reclabloc();*/ }
			}
		}
	}
	{//MTF Box on/off
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if ((sparam == Name + " ShowBox") && ObjectGetInteger(0,Name + " ShowBox",OBJPROP_COLOR) == clrBlue)
			{
				GlobalVariableSet("ub" + _Symbol, 0); showbox = (boxshow)GlobalVariableGet("ub" + _Symbol); ObjectSetInteger(0,Name + " ShowBox",OBJPROP_COLOR,(int)clrRed);
				ObjectsDeleteAll(0, Name + "Rec");
			}
			else if ((sparam == Name + " ShowBox") && ObjectGetInteger(0,Name + " ShowBox",OBJPROP_COLOR) == clrRed)
			{
				GlobalVariableSet("ub" + _Symbol, 1); showbox = (boxshow)GlobalVariableGet("ub" + _Symbol); ObjectSetInteger(0,Name + " ShowBox",OBJPROP_COLOR,(int)clrBlue);
				/*statd(); bigtfnumbers(); reclabloc();*/
			}
		}
	}
	{//Move rectangle
		if (id == CHARTEVENT_OBJECT_DRAG)
		{
			if (sparam == Name + "Rec " + " MovRec")
			{
				GlobalVariableSet("um" + _Symbol, (int)ObjectGetInteger(0,Name + "Rec " + " MovRec",OBJPROP_XDISTANCE));
				GlobalVariableSet("un" + _Symbol, (int)ObjectGetInteger(0,Name + "Rec " + " MovRec",OBJPROP_YDISTANCE));
				GlobalVariablesFlush();
				/*statd(); bigtfnumbers(); reclabloc();*/
			}
		}
	}
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION FRACTALS--------------------------------------------+
void checkpre() {
	if (Bars(_Symbol,PERIOD_CURRENT) < periods) periods = Bars(_Symbol,PERIOD_CURRENT) - 1;
	string obname;
	double nexp[], prep[];
	double nexn[], pren[];
	ArrayResize(prep, periods + 2); ArrayResize(nexp, periods + 1);
	ArrayResize(pren, periods + 2); ArrayResize(nexn, periods + 1);
	ArrayInitialize(finp, EMPTY_VALUE); ArrayInitialize(finn, EMPTY_VALUE);

	for (int x = periods; x >= 1; x--) {
		if ((iClose(_Symbol, PERIOD_CURRENT, x + 1) - iOpen(_Symbol, PERIOD_CURRENT, x + 1)) > 0 && (iClose(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_CURRENT, x)) > 0)
		{
			prep[x] = iClose(_Symbol, PERIOD_CURRENT, x + 1) - iOpen(_Symbol, PERIOD_CURRENT, x + 1);
			nexp[x] = iOpen(_Symbol, PERIOD_CURRENT, x) - iLow(_Symbol, PERIOD_CURRENT, x);
			if (nexp[x] < dist*prep[x] && iOpen(_Symbol, PERIOD_CURRENT, x + 1) + 0.75 * prep[x] < iLow(_Symbol, PERIOD_CURRENT, x) && (iClose(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_CLOSE, x - 1, 1)) > iLow(_Symbol, PERIOD_CURRENT, x + 1))) finp[x] = iLow(_Symbol, PERIOD_CURRENT, x) - 40 * _Point;
			else finp[x] = EMPTY_VALUE;
		}
	}

	for (int x = periods; x >= 1; x--) {
		if ((iOpen(_Symbol, PERIOD_CURRENT, x + 1) - iClose(_Symbol, PERIOD_CURRENT, x + 1)) > 0 && (iOpen(_Symbol, PERIOD_CURRENT, x) - iClose(_Symbol, PERIOD_CURRENT, x)) > 0)
		{
			pren[x] = iOpen(_Symbol, PERIOD_CURRENT, x + 1) - iClose(_Symbol, PERIOD_CURRENT, x + 1);
			nexn[x] = iHigh(_Symbol, PERIOD_CURRENT, x) - iOpen(_Symbol, PERIOD_CURRENT, x);
			if (nexn[x] < dist*pren[x] && iOpen(_Symbol, PERIOD_CURRENT, x + 1) - 0.75 * pren[x] > iHigh(_Symbol, PERIOD_CURRENT, x) && (iClose(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_CLOSE, x - 1, 1)) < iHigh(_Symbol, PERIOD_CURRENT, x + 1))) finn[x] = iHigh(_Symbol, PERIOD_CURRENT, x) + 40 * _Point;
			else finn[x] = EMPTY_VALUE;
		}
	}

	for (int x = periods; x >= 1; x--) {
		string intrepl = "s" + TimeToString(iTime(_Symbol, PERIOD_CURRENT, x + 1), TIME_DATE | TIME_MINUTES);
		double LL = iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, x - 1, 1));

		if (finp[x] != EMPTY_VALUE && x < drawlines && (iClose(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_CLOSE, x - 1, 1)) >= iLow(_Symbol, PERIOD_CURRENT, x + 1))) {
			obname = Name + "BasLin" + intrepl; objtrend2(obname, iOpen(_Symbol, PERIOD_CURRENT, x + 1), iOpen(_Symbol, PERIOD_CURRENT, x + 1), x + 1, 0, 3 * Period() * 60, 3, 0, clrBlack, "OSup1");
			obname = Name + "Lin" + intrepl; objtrend2(obname, iLow(_Symbol, PERIOD_CURRENT, x + 1), iLow(_Symbol, PERIOD_CURRENT, x + 1), x + 1, 0, 3 * Period() * 60, 2, 0, clrBlack, "LSup2");
			obname = Name + "ConLin" + intrepl; objtrend2(obname, iLow(_Symbol, PERIOD_CURRENT, x + 1), iOpen(_Symbol, PERIOD_CURRENT, x + 1), 4, 0, 3 * Period() * 60, 2, 0, clrBlack, "SCon");
			obname = Name + "ArrBas" + intrepl; ArrowPrice(obname, iLow(_Symbol, PERIOD_CURRENT, x + 1), iOpen(_Symbol, PERIOD_CURRENT, x + 1), 4 * Period() * 60, clrBlack);
			if (iOpen(_Symbol, PERIOD_CURRENT, x + 1) >= LL) { ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_COLOR, clrWhite); ObjectSetInteger(0, Name + "Lin" + intrepl, OBJPROP_COLOR, clrWhite); ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_COLOR, clrWhite); ObjectSetInteger(0, Name + "ArrBas" + intrepl, OBJPROP_COLOR, clrWhite); ObjectSetInteger(0, Name + "ArrBas" + intrepl, OBJPROP_WIDTH, 1); }
		}
		else if (finp[x] == EMPTY_VALUE) { ObjectDelete(0, Name + "BasLin" + intrepl); ObjectDelete(0, Name + "Lin" + intrepl); ObjectDelete(0, Name + "ArrBas" + intrepl); ObjectDelete(0, Name + "ConLin" + intrepl); }
	}

	for (int x = periods; x >= 1; x--) {
		string intrepl = "b" + TimeToString(iTime(_Symbol, PERIOD_CURRENT, x + 1), TIME_DATE | TIME_MINUTES);
		double HH = iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, x - 1, 1));

		if (finn[x] != EMPTY_VALUE && x < drawlines && (iClose(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_CLOSE, x - 1, 1)) <= iHigh(_Symbol, PERIOD_CURRENT, x + 1))) {
			obname = Name + "BasLin" + intrepl; objtrend2(obname, iOpen(_Symbol, PERIOD_CURRENT, x + 1), iOpen(_Symbol, PERIOD_CURRENT, x + 1), x + 1, 0, 3 * Period() * 60, 3, 0, clrBlack, "ORes1");
			obname = Name + "Lin" + intrepl; objtrend2(obname, iHigh(_Symbol, PERIOD_CURRENT, x + 1), iHigh(_Symbol, PERIOD_CURRENT, x + 1), x + 1, 0, 3 * Period() * 60, 2, 0, clrBlack, "HRes2");
			obname = Name + "ConLin" + intrepl; objtrend2(obname, iHigh(_Symbol, PERIOD_CURRENT, x + 1), iOpen(_Symbol, PERIOD_CURRENT, x + 1), 4, 0, 3 * Period() * 60, 2, 0, clrBlack, "RCon");
			obname = Name + "ArrBas" + intrepl; ArrowPrice(obname, iHigh(_Symbol, PERIOD_CURRENT, x + 1), iOpen(_Symbol, PERIOD_CURRENT, x + 1), 4 * Period() * 60, clrBlack);
			if (iOpen(_Symbol, PERIOD_CURRENT, x + 1) <= HH) { ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_COLOR, clrWhite); ObjectSetInteger(0, Name + "Lin" + intrepl, OBJPROP_COLOR, clrWhite); ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_COLOR, clrWhite); ObjectSetInteger(0, Name + "ArrBas" + intrepl, OBJPROP_COLOR, clrWhite); ObjectSetInteger(0, Name + "ArrBas" + intrepl, OBJPROP_WIDTH, 1); }
		}
		else if (finn[x] == EMPTY_VALUE) { ObjectDelete(0, Name + "BasLin" + intrepl); ObjectDelete(0, Name + "Lin" + intrepl); ObjectDelete(0, Name + "ArrBas" + intrepl); ObjectDelete(0, Name + "ConLin" + intrepl); }
	}
}
//+------------------------------------------------------------------+

//+YDAY HIGH/LOW/OPEN/CLOSE------------------------------------------+
void ydayweek() {
   MqlDateTime tm;
   TimeCurrent(tm);
    
   datetime Time[]; 
   ArraySetAsSeries(Time,true);
	double o, h, l, c, oo, hh, ll, cc, ooo, hhh, lll, ccc;
	o = iOpen(_Symbol, PERIOD_D1, 1); oo = iOpen(_Symbol, PERIOD_W1, 1); ooo = iOpen(_Symbol, PERIOD_MN1, 1);
	h = iHigh(_Symbol, PERIOD_D1, 1); hh = iHigh(_Symbol, PERIOD_W1, 1); hhh = iHigh(_Symbol, PERIOD_MN1, 1);
	l = iLow(_Symbol, PERIOD_D1, 1); ll = iLow(_Symbol, PERIOD_W1, 1); lll = iLow(_Symbol, PERIOD_MN1, 1);
	c = iClose(_Symbol, PERIOD_D1, 1); cc = iClose(_Symbol, PERIOD_W1, 1); ccc = iClose(_Symbol, PERIOD_MN1, 1);

	int x = 24 + tm.hour;
	string obname;

	int od = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 1), false);
	int cd = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false);
	int ow = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 1), false);
	int cw = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 0), false);
	int om = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 1), false);
	int cm = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 0), false);

	if (ChartPeriod() >= 1 && ChartPeriod() <= 240) {
		if (ChartPeriod() <= 70) {
			obname = Name + " ydayO";
			objtrend2(obname, o, o, od, 0, 20 * Period() * 60, 1, 0, clrBlack, "Yesterday Open");
		}
		{ obname = Name + " ydayO F1"; Texter(obname, o, CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 20 * Period() * 60, "D-1 O", clrBlack); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		if (tm.day_of_week != 1) {
			if (ChartPeriod() <= 70) {
				obname = Name + " ydayC";
				objtrend2(obname, c, c, cd, 0, 20 * Period() * 60, 1, 0, clrBlack, "Yesterday Close");
			}
			{ obname = Name + " ydayC F1"; Texter(obname, c, CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 20 * Period() * 60, "D-1 C", clrBlack); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		}
		if (ChartPeriod() <= 70) {
			obname = Name + " ydayH";
			objtrend2(obname, h, h, od, 0, 20 * Period() * 60, 1, 0, clrWhite, "Yesterday High");
		}
		{ obname = Name + " ydayH F1"; Texter(obname, h, CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 20 * Period() * 60, "D-1 H", clrWhite); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		if (ChartPeriod() <= 70) {
			obname = Name + " ydayL";
			objtrend2(obname, l, l, od, 0, 20 * Period() * 60, 1, 0, clrWhite, "Yesterday Low");
		}
		{ obname = Name + " ydayL F1"; Texter(obname, l, CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 20 * Period() * 60, "D-1 L", clrWhite); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }

		obname = Name + " ymonthO";
		objtrend2(obname, ooo, ooo, om, 0, 27 * Period() * 60, 2, 0, clrRed, "Last Month Open");
		{ obname = Name + " ymonthO F1"; Texter(obname, ooo, CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 27 * Period() * 60, "M-1 O", clrRed); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " ymonthC";
		objtrend2(obname, ccc, ccc, cm, 0, 27 * Period() * 60, 2, 0, clrRed, "Last Month Close");
		{ obname = Name + " ymonthC F1"; Texter(obname, ccc, CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 27 * Period() * 60, "M-1 C", clrRed); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " ymonthH";
		objtrend2(obname, hhh, hhh, om, 0, 27 * Period() * 60, 2, 0, clrDarkOrange, "Last Month High");
		{ obname = Name + " ymonthH F1"; Texter(obname, hhh, CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 27 * Period() * 60, "M-1 H", clrDarkOrange); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " ymonthL";
		objtrend2(obname, lll, lll, om, 0, 27 * Period() * 60, 2, 0, clrDarkOrange, "Last Month Low");
		{ obname = Name + " ymonthL F1"; Texter(obname, lll, CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 27 * Period() * 60, "M-1 L", clrDarkOrange); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		
		obname = Name + " yweekO";
		objtrend2(obname, oo, oo, ow, 0, 27 * Period() * 60, 2, 0, clrDarkGoldenrod, "Last Week Open");
		{ obname = Name + " yweekO F1"; Texter(obname, oo, CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 27 * Period() * 60, "W-1 O", clrDarkGoldenrod); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " yweekC";
		objtrend2(obname, cc, cc, cw, 0, 27 * Period() * 60, 2, 0, clrDarkGoldenrod, "Last Week Close");
		{ obname = Name + " yweekC F1"; Texter(obname, cc, CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 27 * Period() * 60, "W-1 C", clrDarkGoldenrod); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " yweekH";
		objtrend2(obname, hh, hh, ow, 0, 27 * Period() * 60, 2, 0, clrYellow, "Last Week High");
		{ obname = Name + " yweekH F1"; Texter(obname, hh, CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 27 * Period() * 60, "W-1 H", clrYellow); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " yweekL";
		objtrend2(obname, ll, ll, ow, 0, 27 * Period() * 60, 2, 0, clrYellow, "Last Week Low");
		{ obname = Name + " yweekL F1"; Texter(obname, ll, CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 27 * Period() * 60, "W-1 L", clrYellow); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }

		obname = Name + " HOM";
		objtrend2(obname, iHigh(_Symbol, PERIOD_MN1, 0), iHigh(_Symbol, PERIOD_MN1, 0), cm, 0, 34 * Period() * 60, 1, 2, clrAqua, "Month High");
		{ obname = Name + " HOM F1"; Texter(obname, iHigh(_Symbol, PERIOD_MN1, 0), CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 34 * Period() * 60, "M H", clrAqua); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " LOM";
		objtrend2(obname, iLow(_Symbol, PERIOD_MN1, 0), iLow(_Symbol, PERIOD_MN1, 0), cm, 0, 34 * Period() * 60, 1, 2, clrAqua, "Month Low");
		{ obname = Name + " LOM F1"; Texter(obname, iLow(_Symbol, PERIOD_MN1, 0), CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 34 * Period() * 60, "M L", clrAqua); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " HOW";
		objtrend2(obname, iHigh(_Symbol, PERIOD_W1, 0), iHigh(_Symbol, PERIOD_W1, 0), cw, 0, 34 * Period() * 60, 1, 2, clrAqua, "Week High");
		{ obname = Name + " HOW F1"; Texter(obname, iHigh(_Symbol, PERIOD_W1, 0), CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 34 * Period() * 60, "W H", clrAqua); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " LOW";
		objtrend2(obname, iLow(_Symbol, PERIOD_W1, 0), iLow(_Symbol, PERIOD_W1, 0), cw, 0, 34 * Period() * 60, 1, 2, clrAqua, "Week Low");
		{ obname = Name + " LOW F1"; Texter(obname, iLow(_Symbol, PERIOD_W1, 0), CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 34 * Period() * 60, "W L", clrAqua); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " HOD";
		objtrend2(obname, iHigh(_Symbol, PERIOD_D1, 0), iHigh(_Symbol, PERIOD_D1, 0), cd, 0, 34 * Period() * 60, 1, 2, clrAqua, "Day High");
		{ obname = Name + " HOD F1"; Texter(obname, iHigh(_Symbol, PERIOD_D1, 0), CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 34 * Period() * 60, "D H", clrAqua); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " LOD";
		objtrend2(obname, iLow(_Symbol, PERIOD_D1, 0), iLow(_Symbol, PERIOD_D1, 0), cd, 0, 34 * Period() * 60, 1, 2, clrAqua, "Day Low");
		{ obname = Name + " LOD F1"; Texter(obname, iLow(_Symbol, PERIOD_D1, 0), CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + 34 * Period() * 60, "D L", clrAqua); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
	}
}
//+------------------------------------------------------------------+

/*
//+BIG TF LINES BOX--------------------------------------------------+
void bigtfnumbers() {
	static datetime start_mont_time = 0, start_week_time = 0, start_dail_time = 0, start_four_time = 0, start_hour_time = 0, start_thir_time = 0, start_fift_time = 0, start_five_time = 0;

	// { start_mont_time = 0; start_week_time = 0; start_dail_time = 0; start_four_time = 0; start_hour_time = 0; start_thir_time = 0; start_fift_time = 0; start_five_time = 0; }
	
	//static double monthhigh = 0, montllow = 0, monthhigh2 = 0, montllow2 = 0, monthhigh3 = 0, montllow3 = 0;
	//static double weekhhigh = 0, weekllow = 0, weekhhigh2 = 0, weekllow2 = 0, weekhhigh3 = 0, weekllow3 = 0;
	//static double dailhhigh = 0, dailllow = 0, dailhhigh2 = 0, dailllow2 = 0, dailhhigh3 = 0, dailllow3 = 0;
	//static double fourhhigh = 0, fourllow = 0, fourhhigh2 = 0, fourllow2 = 0, fourhhigh3 = 0, fourllow3 = 0;
	//static double hourhhigh = 0, hourllow = 0, hourhhigh2 = 0, hourllow2 = 0, hourhhigh3 = 0, hourllow3 = 0;
	//static double thirhhigh = 0, thirllow = 0, thirhhigh2 = 0, thirllow2 = 0, thirhhigh3 = 0, thirllow3 = 0;
	//static double fifthhigh = 0, fiftllow = 0, fifthhigh2 = 0, fiftllow2 = 0, fifthhigh3 = 0, fiftllow3 = 0;
	//static double fivehhigh = 0, fivellow = 0, fivehhigh2 = 0, fivellow2 = 0, fivehhigh3 = 0, fivellow3 = 0;
	
	// 1 Month
	bool new_mont_check = false;
	if (start_mont_time < iTime(NULL, PERIOD_MN1, 0))
	{
		new_mont_check = true;
		start_mont_time = iTime(NULL, PERIOD_MN1, 0);
	}
	if (new_mont_check)
	{
		double month[], montl[];
		double monco[], monol[], monoc[], monho[];
		ArrayResize(month, iBars(_Symbol, PERIOD_MN1)); ArrayResize(montl, iBars(_Symbol, PERIOD_MN1));
		ArrayResize(monco, iBars(_Symbol, PERIOD_MN1)); ArrayResize(monol, iBars(_Symbol, PERIOD_MN1)); ArrayResize(monoc, iBars(_Symbol, PERIOD_MN1)); ArrayResize(monho, iBars(_Symbol, PERIOD_MN1));
		for (int x = iBars(_Symbol, PERIOD_MN1) - 2; x >= 1; x--) {
			if ((iClose(_Symbol, PERIOD_MN1, x + 1) - iOpen(_Symbol, PERIOD_MN1, x + 1)) > 0 && (iClose(_Symbol, PERIOD_MN1, x) - iOpen(_Symbol, PERIOD_MN1, x)) > 0)
			{
				monco[x] = iClose(_Symbol, PERIOD_MN1, x + 1) - iOpen(_Symbol, PERIOD_MN1, x + 1);
				monol[x] = iOpen(_Symbol, PERIOD_MN1, x) - iLow(_Symbol, PERIOD_MN1, x);
				if (monol[x] < dist*monco[x] && iOpen(_Symbol, PERIOD_MN1, x + 1) + 0.75 * monco[x] < iLow(_Symbol, PERIOD_MN1, x) && iClose(_Symbol, PERIOD_MN1, iLowest(_Symbol, PERIOD_MN1, MODE_CLOSE, x - 1, 1)) > iLow(_Symbol, PERIOD_MN1, x + 1)) { month[x] = iLow(_Symbol, PERIOD_MN1, x + 1); }
				else { month[x] = EMPTY_VALUE; }
			}
		}
		for (int x = iBars(_Symbol, PERIOD_MN1) - 2; x >= 1; x--) {
			if ((iOpen(_Symbol, PERIOD_MN1, x + 1) - iClose(_Symbol, PERIOD_MN1, x + 1)) > 0 && (iOpen(_Symbol, PERIOD_MN1, x) - iClose(_Symbol, PERIOD_MN1, x)) > 0)
			{
				monoc[x] = iOpen(_Symbol, PERIOD_MN1, x + 1) - iClose(_Symbol, PERIOD_MN1, x + 1);
				monho[x] = iHigh(_Symbol, PERIOD_MN1, x) - iOpen(_Symbol, PERIOD_MN1, x);
				if (monho[x] < dist*monoc[x] && iOpen(_Symbol, PERIOD_MN1, x + 1) - 0.75 * monoc[x] > iHigh(_Symbol, PERIOD_MN1, x) && iClose(_Symbol, PERIOD_MN1, iHighest(_Symbol, PERIOD_MN1, MODE_CLOSE, x - 1, 1)) < iHigh(_Symbol, PERIOD_MN1, x + 1)) { montl[x] = iHigh(_Symbol, PERIOD_MN1, x + 1); }
				else { montl[x] = EMPTY_VALUE; }
			}
		}
		{//1 Month
			double newmonth[], newmontl[];
			ArrayResize(newmonth, iBars(_Symbol, PERIOD_MN1)); ArrayResize(newmontl, iBars(_Symbol, PERIOD_MN1));

			for (int y = ArraySize(month) - 1, z = EMPTY; y >= 0; y--) {
				if (month[y] != EMPTY_VALUE && month[y] > 0.00001) {
					++z; ArrayResize(newmonth, z + 1); newmonth[z] = month[y];
				}
			}
			for (int y = ArraySize(montl) - 1, z = EMPTY; y >= 0; y--) {
				if (montl[y] != EMPTY_VALUE && montl[y] > 0.00001) {
					++z; ArrayResize(newmontl, z + 1); newmontl[z] = montl[y];
				}
			}

			ArraySetAsSeries(newmonth,false); ArraySort(newmonth); ArraySort(newmontl);
			if (ArraySize(newmonth) >= 1) bobbit[0] = newmonth[0]; else bobbit[0] = 0;
			if (ArraySize(newmontl) >= 1) doddit[0] = newmontl[0]; else doddit[0] = 0;
			if (ArraySize(newmonth) >= 2) bobbit1[0] = newmonth[1]; else bobbit1[0] = 0;
			if (ArraySize(newmontl) >= 2) doddit1[0] = newmontl[1]; else doddit1[0] = 0;
			if (ArraySize(newmonth) >= 3) bobbit2[0] = newmonth[2]; else bobbit2[0] = 0;
			if (ArraySize(newmontl) >= 3) doddit2[0] = newmontl[2]; else doddit2[0] = 0;
		}
		new_mont_check = false;
	}

	// 1 Week
	bool new_week_check = false;
	if (start_week_time < iTime(NULL, PERIOD_W1, 0))
	{
		new_week_check = true;
		start_week_time = iTime(NULL, PERIOD_W1, 0);
	}
	if (new_week_check)
	{
		double weekh[], weekl[];
		double weeco[], weeol[], weeoc[], weeho[];
		ArrayResize(weekh, iBars(_Symbol, PERIOD_W1)); ArrayResize(weekl, iBars(_Symbol, PERIOD_W1));
		ArrayResize(weeco, iBars(_Symbol, PERIOD_W1)); ArrayResize(weeol, iBars(_Symbol, PERIOD_W1)); ArrayResize(weeoc, iBars(_Symbol, PERIOD_W1)); ArrayResize(weeho, iBars(_Symbol, PERIOD_W1));
		for (int x = iBars(_Symbol, PERIOD_W1) - 2; x >= 1; x--) {
			if ((iClose(_Symbol, PERIOD_W1, x + 1) - iOpen(_Symbol, PERIOD_W1, x + 1)) > 0 && (iClose(_Symbol, PERIOD_W1, x) - iOpen(_Symbol, PERIOD_W1, x)) > 0)
			{
				weeco[x] = iClose(_Symbol, PERIOD_W1, x + 1) - iOpen(_Symbol, PERIOD_W1, x + 1);
				weeol[x] = iOpen(_Symbol, PERIOD_W1, x) - iLow(_Symbol, PERIOD_W1, x);
				if (weeol[x] < dist*weeco[x] && iOpen(_Symbol, PERIOD_W1, x + 1) + 0.75 * weeco[x] < iLow(_Symbol, PERIOD_W1, x) && iClose(_Symbol, PERIOD_W1, iLowest(_Symbol, PERIOD_W1, MODE_CLOSE, x - 1, 1)) > iLow(_Symbol, PERIOD_W1, x + 1)) { weekh[x] = iLow(_Symbol, PERIOD_W1, x + 1); }
				else { weekh[x] = EMPTY_VALUE; }
			}
		}
		for (int x = iBars(_Symbol, PERIOD_W1) - 2; x >= 1; x--) {
			if ((iOpen(_Symbol, PERIOD_W1, x + 1) - iClose(_Symbol, PERIOD_W1, x + 1)) > 0 && (iOpen(_Symbol, PERIOD_W1, x) - iClose(_Symbol, PERIOD_W1, x)) > 0)
			{
				weeoc[x] = iOpen(_Symbol, PERIOD_W1, x + 1) - iClose(_Symbol, PERIOD_W1, x + 1);
				weeho[x] = iHigh(_Symbol, PERIOD_W1, x) - iOpen(_Symbol, PERIOD_W1, x);
				if (weeho[x] < dist*weeoc[x] && iOpen(_Symbol, PERIOD_W1, x + 1) - 0.75 * weeoc[x] > iHigh(_Symbol, PERIOD_W1, x) && iClose(_Symbol, PERIOD_W1, iHighest(_Symbol, PERIOD_W1, MODE_CLOSE, x - 1, 1)) < iHigh(_Symbol, PERIOD_W1, x + 1)) { weekl[x] = iHigh(_Symbol, PERIOD_W1, x + 1); }
				else { weekl[x] = EMPTY_VALUE; }
			}
		}
		{//1 Week
			double newweekh[], newweekl[];
			ArrayResize(newweekh, iBars(_Symbol, PERIOD_W1)); ArrayResize(newweekl, iBars(_Symbol, PERIOD_W1));

			for (int y = ArraySize(weekh) - 1, z = EMPTY; y >= 0; y--) {
				if (weekh[y] != EMPTY_VALUE && weekh[y] > 0.00001) {
					++z; ArrayResize(newweekh, z + 1); newweekh[z] = weekh[y];
				}
			}
			for (int y = ArraySize(weekl) - 1, z = EMPTY; y >= 0; y--) {
				if (weekl[y] != EMPTY_VALUE && weekl[y] > 0.00001) {
					++z; ArrayResize(newweekl, z + 1); newweekl[z] = weekl[y];
				}
			}

			ArraySetAsSeries(newweekh,false); ArraySort(newweekh); ArraySort(newweekl);
			if (ArraySize(newweekh) >= 1) bobbit[1] = newweekh[0]; else bobbit[1] = 0;
			if (ArraySize(newweekl) >= 1) doddit[1] = newweekl[0]; else doddit[1] = 0;
			if (ArraySize(newweekh) >= 2) bobbit1[1] = newweekh[1]; else bobbit1[1] = 0;
			if (ArraySize(newweekl) >= 2) doddit1[1] = newweekl[1]; else doddit1[1] = 0;
			if (ArraySize(newweekh) >= 3) bobbit2[1] = newweekh[2]; else bobbit2[1] = 0;
			if (ArraySize(newweekl) >= 3) doddit2[1] = newweekl[2]; else doddit2[1] = 0;
		}
		new_week_check = false;
	}

	// 1 Day
	bool new_dail_check = false;
	if (start_dail_time < iTime(NULL, PERIOD_D1, 0))
	{
		new_dail_check = true;
		start_dail_time = iTime(NULL, PERIOD_D1, 0);
	}
	if (new_dail_check)
	{
		double dailh[], daill[];
		double daico[], daiol[], daioc[], daiho[];
		ArrayResize(dailh, iBars(_Symbol, PERIOD_D1)); ArrayResize(daill, iBars(_Symbol, PERIOD_D1));
		ArrayResize(daico, iBars(_Symbol, PERIOD_D1)); ArrayResize(daiol, iBars(_Symbol, PERIOD_D1)); ArrayResize(daioc, iBars(_Symbol, PERIOD_D1)); ArrayResize(daiho, iBars(_Symbol, PERIOD_D1));
		for (int x = iBars(_Symbol, PERIOD_D1) - 2; x >= 1; x--) {
			if ((iClose(_Symbol, PERIOD_D1, x + 1) - iOpen(_Symbol, PERIOD_D1, x + 1)) > 0 && (iClose(_Symbol, PERIOD_D1, x) - iOpen(_Symbol, PERIOD_D1, x)) > 0)
			{
				daico[x] = iClose(_Symbol, PERIOD_D1, x + 1) - iOpen(_Symbol, PERIOD_D1, x + 1);
				daiol[x] = iOpen(_Symbol, PERIOD_D1, x) - iLow(_Symbol, PERIOD_D1, x);
				if (daiol[x] < dist*daico[x] && iOpen(_Symbol, PERIOD_D1, x + 1) + 0.75 * daico[x] < iLow(_Symbol, PERIOD_D1, x) && iClose(_Symbol, PERIOD_D1, iLowest(_Symbol, PERIOD_D1, MODE_CLOSE, x - 1, 1)) > iLow(_Symbol, PERIOD_D1, x + 1)) { dailh[x] = iLow(_Symbol, PERIOD_D1, x + 1); }
				else { dailh[x] = EMPTY_VALUE; }
			}
		}
		for (int x = iBars(_Symbol, PERIOD_D1) - 2; x >= 1; x--) {
			if ((iOpen(_Symbol, PERIOD_D1, x + 1) - iClose(_Symbol, PERIOD_D1, x + 1)) > 0 && (iOpen(_Symbol, PERIOD_D1, x) - iClose(_Symbol, PERIOD_D1, x)) > 0)
			{
				daioc[x] = iOpen(_Symbol, PERIOD_D1, x + 1) - iClose(_Symbol, PERIOD_D1, x + 1);
				daiho[x] = iHigh(_Symbol, PERIOD_D1, x) - iOpen(_Symbol, PERIOD_D1, x);
				if (daiho[x] < dist*daioc[x] && iOpen(_Symbol, PERIOD_D1, x + 1) - 0.75 * daioc[x] > iHigh(_Symbol, PERIOD_D1, x) && iClose(_Symbol, PERIOD_D1, iHighest(_Symbol, PERIOD_D1, MODE_CLOSE, x - 1, 1)) < iHigh(_Symbol, PERIOD_D1, x + 1)) { daill[x] = iHigh(_Symbol, PERIOD_D1, x + 1); }
				else { daill[x] = EMPTY_VALUE; }
			}
		}
		{//1 Day
			double newdailh[], newdaill[];
			ArrayResize(newdailh, iBars(_Symbol, PERIOD_D1)); ArrayResize(newdaill, iBars(_Symbol, PERIOD_D1));

			for (int y = ArraySize(dailh) - 1, z = EMPTY; y >= 0; y--) {
				if (dailh[y] != EMPTY_VALUE && dailh[y] > 0.00001) {
					++z; ArrayResize(newdailh, z + 1); newdailh[z] = dailh[y];
				}
			}
			for (int y = ArraySize(daill) - 1, z = EMPTY; y >= 0; y--) {
				if (daill[y] != EMPTY_VALUE && daill[y] > 0.00001) {
					++z; ArrayResize(newdaill, z + 1); newdaill[z] = daill[y];
				}
			}

			ArraySetAsSeries(newdailh,false); ArraySort(newdailh); ArraySort(newdaill);
			if (ArraySize(newdailh) >= 1) bobbit[2] = newdailh[0]; else bobbit[2] = 0;
			if (ArraySize(newdaill) >= 1) doddit[2] = newdaill[0]; else doddit[2] = 0;
			if (ArraySize(newdailh) >= 2) bobbit1[2] = newdailh[1]; else bobbit1[2] = 0;
			if (ArraySize(newdaill) >= 2) doddit1[2] = newdaill[1]; else doddit1[2] = 0;
			if (ArraySize(newdailh) >= 3) bobbit2[2] = newdailh[2]; else bobbit2[2] = 0;
			if (ArraySize(newdaill) >= 3) doddit2[2] = newdaill[2]; else doddit2[2] = 0;
		}
		new_dail_check = false;
	}

	// 4 Hours
	bool new_four_check = false;
	if (start_four_time < iTime(NULL, PERIOD_H4, 0))
	{
		new_four_check = true;
		start_four_time = iTime(NULL, PERIOD_H4, 0);
	}
	if (new_four_check)
	{
		double fourh[], fourl[];
		double fouco[], fouol[], fouoc[], fouho[];
		ArrayResize(fourh, iBars(_Symbol, PERIOD_H4)); ArrayResize(fourl, iBars(_Symbol, PERIOD_H4));
		ArrayResize(fouco, iBars(_Symbol, PERIOD_H4)); ArrayResize(fouol, iBars(_Symbol, PERIOD_H4)); ArrayResize(fouoc, iBars(_Symbol, PERIOD_H4)); ArrayResize(fouho, iBars(_Symbol, PERIOD_H4));
		for (int x = iBars(_Symbol, PERIOD_H4) - 2; x >= 1; x--) {
			if ((iClose(_Symbol, PERIOD_H4, x + 1) - iOpen(_Symbol, PERIOD_H4, x + 1)) > 0 && (iClose(_Symbol, PERIOD_H4, x) - iOpen(_Symbol, PERIOD_H4, x)) > 0)
			{
				fouco[x] = iClose(_Symbol, PERIOD_H4, x + 1) - iOpen(_Symbol, PERIOD_H4, x + 1);
				fouol[x] = iOpen(_Symbol, PERIOD_H4, x) - iLow(_Symbol, PERIOD_H4, x);
				if (fouol[x] < dist*fouco[x] && iOpen(_Symbol, PERIOD_H4, x + 1) + 0.75 * fouco[x] < iLow(_Symbol, PERIOD_H4, x) && iClose(_Symbol, PERIOD_H4, iLowest(_Symbol, PERIOD_H4, MODE_CLOSE, x - 1, 1)) > iLow(_Symbol, PERIOD_H4, x + 1)) { fourh[x] = iLow(_Symbol, PERIOD_H4, x + 1); }
				else { fourh[x] = EMPTY_VALUE; }
			}
		}
		for (int x = iBars(_Symbol, PERIOD_H4) - 2; x >= 1; x--) {
			if ((iOpen(_Symbol, PERIOD_H4, x + 1) - iClose(_Symbol, PERIOD_H4, x + 1)) > 0 && (iOpen(_Symbol, PERIOD_H4, x) - iClose(_Symbol, PERIOD_H4, x)) > 0)
			{
				fouoc[x] = iOpen(_Symbol, PERIOD_H4, x + 1) - iClose(_Symbol, PERIOD_H4, x + 1);
				fouho[x] = iHigh(_Symbol, PERIOD_H4, x) - iOpen(_Symbol, PERIOD_H4, x);
				if (fouho[x] < dist*fouoc[x] && iOpen(_Symbol, PERIOD_H4, x + 1) - 0.75 * fouoc[x] > iHigh(_Symbol, PERIOD_H4, x) && iClose(_Symbol, PERIOD_H4, iHighest(_Symbol, PERIOD_H4, MODE_CLOSE, x - 1, 1)) < iHigh(_Symbol, PERIOD_H4, x + 1)) { fourl[x] = iHigh(_Symbol, PERIOD_H4, x + 1); }
				else { fourl[x] = EMPTY_VALUE; }
			}
		}
		{//4 Hours
			double newfourh[], newfourl[];
			ArrayResize(newfourh, iBars(_Symbol, PERIOD_H4)); ArrayResize(newfourl, iBars(_Symbol, PERIOD_H4));

			for (int y = ArraySize(fourh) - 1, z = EMPTY; y >= 0; y--) {
				if (fourh[y] != EMPTY_VALUE && fourh[y] > 0.00001) {
					++z; ArrayResize(newfourh, z + 1); newfourh[z] = fourh[y];
				}
			}
			for (int y = ArraySize(fourl) - 1, z = EMPTY; y >= 0; y--) {
				if (fourl[y] != EMPTY_VALUE && fourl[y] > 0.00001) {
					++z; ArrayResize(newfourl, z + 1); newfourl[z] = fourl[y];
				}
			}

			ArraySetAsSeries(newfourh,false); ArraySort(newfourh); ArraySort(newfourl);
			if (ArraySize(newfourh) >= 1) bobbit[3] = newfourh[0]; else bobbit[3] = 0;
			if (ArraySize(newfourl) >= 1) doddit[3] = newfourl[0]; else doddit[3] = 0;
			if (ArraySize(newfourh) >= 2) bobbit1[3] = newfourh[1]; else bobbit1[3] = 0;
			if (ArraySize(newfourl) >= 2) doddit1[3] = newfourl[1]; else doddit1[3] = 0;
			if (ArraySize(newfourh) >= 3) bobbit2[3] = newfourh[2]; else bobbit2[3] = 0;
			if (ArraySize(newfourl) >= 3) doddit2[3] = newfourl[2]; else doddit2[3] = 0;
		}
		new_four_check = false;
	}

	// 1 Hour
	bool new_hour_check = false;
	if (start_hour_time < iTime(NULL, PERIOD_H1, 0))
	{
		new_hour_check = true;
		start_hour_time = iTime(NULL, PERIOD_H1, 0);
	}
	if (new_hour_check)
	{
		double hourh[], hourl[];
		double houco[], houol[], houoc[], houho[];
		ArrayResize(hourh, iBars(_Symbol, PERIOD_H1)); ArrayResize(hourl, iBars(_Symbol, PERIOD_H1));
		ArrayResize(houco, iBars(_Symbol, PERIOD_H1)); ArrayResize(houol, iBars(_Symbol, PERIOD_H1)); ArrayResize(houoc, iBars(_Symbol, PERIOD_H1)); ArrayResize(houho, iBars(_Symbol, PERIOD_H1));
		for (int x = iBars(_Symbol, PERIOD_H1) - 2; x >= 1; x--) {
			if ((iClose(_Symbol, PERIOD_H1, x + 1) - iOpen(_Symbol, PERIOD_H1, x + 1)) > 0 && (iClose(_Symbol, PERIOD_H1, x) - iOpen(_Symbol, PERIOD_H1, x)) > 0)
			{
				houco[x] = iClose(_Symbol, PERIOD_H1, x + 1) - iOpen(_Symbol, PERIOD_H1, x + 1);
				houol[x] = iOpen(_Symbol, PERIOD_H1, x) - iLow(_Symbol, PERIOD_H1, x);
				if (houol[x] < dist*houco[x] && iOpen(_Symbol, PERIOD_H1, x + 1) + 0.75 * houco[x] < iLow(_Symbol, PERIOD_H1, x) && iClose(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_CLOSE, x - 1, 1)) > iLow(_Symbol, PERIOD_H1, x + 1)) { hourh[x] = iLow(_Symbol, PERIOD_H1, x + 1); }
				else { hourh[x] = EMPTY_VALUE; }
			}
		}
		for (int x = iBars(_Symbol, PERIOD_H1) - 2; x >= 1; x--) {
			if ((iOpen(_Symbol, PERIOD_H1, x + 1) - iClose(_Symbol, PERIOD_H1, x + 1)) > 0 && (iOpen(_Symbol, PERIOD_H1, x) - iClose(_Symbol, PERIOD_H1, x)) > 0)
			{
				houoc[x] = iOpen(_Symbol, PERIOD_H1, x + 1) - iClose(_Symbol, PERIOD_H1, x + 1);
				houho[x] = iHigh(_Symbol, PERIOD_H1, x) - iOpen(_Symbol, PERIOD_H1, x);
				if (houho[x] < dist*houoc[x] && iOpen(_Symbol, PERIOD_H1, x + 1) - 0.75 * houoc[x] > iHigh(_Symbol, PERIOD_H1, x) && iClose(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_CLOSE, x - 1, 1)) < iHigh(_Symbol, PERIOD_H1, x + 1)) { hourl[x] = iHigh(_Symbol, PERIOD_H1, x + 1); }
				else { hourl[x] = EMPTY_VALUE; }
			}
		}
		{//1 Hour
			double newhourh[], newhourl[];
			ArrayResize(newhourh, iBars(_Symbol, PERIOD_H1)); ArrayResize(newhourl, iBars(_Symbol, PERIOD_H1));

			for (int y = ArraySize(hourh) - 1, z = EMPTY; y >= 0; y--) {
				if (hourh[y] != EMPTY_VALUE && hourh[y] > 0.00001) {
					++z; ArrayResize(newhourh, z + 1); newhourh[z] = hourh[y];
				}
			}
			for (int y = ArraySize(hourl) - 1, z = EMPTY; y >= 0; y--) {
				if (hourl[y] != EMPTY_VALUE && hourl[y] > 0.00001) {
					++z; ArrayResize(newhourl, z + 1); newhourl[z] = hourl[y];
				}
			}

			ArraySetAsSeries(newhourh,false); ArraySort(newhourh); ArraySort(newhourl);
			if (ArraySize(newhourh) >= 1) bobbit[4] = newhourh[0]; else bobbit[4] = 0;
			if (ArraySize(newhourl) >= 1) doddit[4] = newhourl[0]; else doddit[4] = 0;
			if (ArraySize(newhourh) >= 2) bobbit1[4] = newhourh[1]; else bobbit1[4] = 0;
			if (ArraySize(newhourl) >= 2) doddit1[4] = newhourl[1]; else doddit1[4] = 0;
			if (ArraySize(newhourh) >= 3) bobbit2[4] = newhourh[2]; else bobbit2[4] = 0;
			if (ArraySize(newhourl) >= 3) doddit2[4] = newhourl[2]; else doddit2[4] = 0;
		}
		new_hour_check = false;
	}

	// 30 Minutes
	bool new_thir_check = false;
	if (start_thir_time < iTime(NULL, PERIOD_M30, 0))
	{
		new_thir_check = true;
		start_thir_time = iTime(NULL, PERIOD_M30, 0);
	}
	if (new_thir_check)
	{
		double thirh[], thirl[];
		double thico[], thiol[], thioc[], thiho[];ArrayResize(thirh, iBars(_Symbol, PERIOD_M30)); ArrayResize(thirl, iBars(_Symbol, PERIOD_M30));
		ArrayResize(thico, iBars(_Symbol, PERIOD_M30)); ArrayResize(thiol, iBars(_Symbol, PERIOD_M30)); ArrayResize(thioc, iBars(_Symbol, PERIOD_M30)); ArrayResize(thiho, iBars(_Symbol, PERIOD_M30));
		for (int x = iBars(_Symbol, PERIOD_M30) - 2; x >= 1; x--) {
			if ((iClose(_Symbol, PERIOD_M30, x + 1) - iOpen(_Symbol, PERIOD_M30, x + 1)) > 0 && (iClose(_Symbol, PERIOD_M30, x) - iOpen(_Symbol, PERIOD_M30, x)) > 0)
			{
				thico[x] = iClose(_Symbol, PERIOD_M30, x + 1) - iOpen(_Symbol, PERIOD_M30, x + 1);
				thiol[x] = iOpen(_Symbol, PERIOD_M30, x) - iLow(_Symbol, PERIOD_M30, x);
				if (thiol[x] < dist*thico[x] && iOpen(_Symbol, PERIOD_M30, x + 1) + 0.75 * thico[x] < iLow(_Symbol, PERIOD_M30, x) && iClose(_Symbol, PERIOD_M30, iLowest(_Symbol, PERIOD_M30, MODE_CLOSE, x - 1, 1)) > iLow(_Symbol, PERIOD_M30, x + 1)) { thirh[x] = iLow(_Symbol, PERIOD_M30, x + 1); }
				else { thirh[x] = EMPTY_VALUE; }
			}
		}
		for (int x = iBars(_Symbol, PERIOD_M30) - 2; x >= 1; x--) {
			if ((iOpen(_Symbol, PERIOD_M30, x + 1) - iClose(_Symbol, PERIOD_M30, x + 1)) > 0 && (iOpen(_Symbol, PERIOD_M30, x) - iClose(_Symbol, PERIOD_M30, x)) > 0)
			{
				thioc[x] = iOpen(_Symbol, PERIOD_M30, x + 1) - iClose(_Symbol, PERIOD_M30, x + 1);
				thiho[x] = iHigh(_Symbol, PERIOD_M30, x) - iOpen(_Symbol, PERIOD_M30, x);
				if (thiho[x] < dist*thioc[x] && iOpen(_Symbol, PERIOD_M30, x + 1) - 0.75 * thioc[x] > iHigh(_Symbol, PERIOD_M30, x) && iClose(_Symbol, PERIOD_M30, iHighest(_Symbol, PERIOD_M30, MODE_CLOSE, x - 1, 1)) < iHigh(_Symbol, PERIOD_M30, x + 1)) { thirl[x] = iHigh(_Symbol, PERIOD_M30, x + 1); }
				else { thirl[x] = EMPTY_VALUE; }
			}
		}
		{//30 Minutes
			double newthirh[], newthirl[];
			ArrayResize(newthirh, iBars(_Symbol, PERIOD_M30)); ArrayResize(newthirl, iBars(_Symbol, PERIOD_M30));

			for (int y = ArraySize(thirh) - 1, z = EMPTY; y >= 0; y--) {
				if (thirh[y] != EMPTY_VALUE && thirh[y] > 0.00001) {
					++z; ArrayResize(newthirh, z + 1); newthirh[z] = thirh[y];
				}
			}
			for (int y = ArraySize(thirl) - 1, z = EMPTY; y >= 0; y--) {
				if (thirl[y] != EMPTY_VALUE && thirl[y] > 0.00001) {
					++z; ArrayResize(newthirl, z + 1); newthirl[z] = thirl[y];
				}
			}

			ArraySetAsSeries(newthirh,false); ArraySort(newthirh); ArraySort(newthirl);
			if (ArraySize(newthirh) >= 1) bobbit[5] = newthirh[0]; else bobbit[5] = 0;
			if (ArraySize(newthirl) >= 1) doddit[5] = newthirl[0]; else doddit[5] = 0;
			if (ArraySize(newthirh) >= 2) bobbit1[5] = newthirh[1]; else bobbit1[5] = 0;
			if (ArraySize(newthirl) >= 2) doddit1[5] = newthirl[1]; else doddit1[5] = 0;
			if (ArraySize(newthirh) >= 3) bobbit2[5] = newthirh[2]; else bobbit2[5] = 0;
			if (ArraySize(newthirl) >= 3) doddit2[5] = newthirl[2]; else doddit2[5] = 0;
		}
		new_thir_check = false;
	}

	// 15 Minutes
	bool new_fift_check = false;
	if (start_fift_time < iTime(NULL, PERIOD_M15, 0))
	{
		new_fift_check = true;
		start_fift_time = iTime(NULL, PERIOD_M15, 0);
	}
	if (new_fift_check)
	{
		double fifth[], fiftl[];
		double fifco[], fifol[], fifoc[], fifho[];
		ArrayResize(fifth, iBars(_Symbol, PERIOD_M15)); ArrayResize(fiftl, iBars(_Symbol, PERIOD_M15));
		ArrayResize(fifco, iBars(_Symbol, PERIOD_M15)); ArrayResize(fifol, iBars(_Symbol, PERIOD_M15)); ArrayResize(fifoc, iBars(_Symbol, PERIOD_M15)); ArrayResize(fifho, iBars(_Symbol, PERIOD_M15));
		for (int x = iBars(_Symbol, PERIOD_M15) - 2; x >= 1; x--) {
			if ((iClose(_Symbol, PERIOD_M15, x + 1) - iOpen(_Symbol, PERIOD_M15, x + 1)) > 0 && (iClose(_Symbol, PERIOD_M15, x) - iOpen(_Symbol, PERIOD_M15, x)) > 0)
			{
				fifco[x] = iClose(_Symbol, PERIOD_M15, x + 1) - iOpen(_Symbol, PERIOD_M15, x + 1);
				fifol[x] = iOpen(_Symbol, PERIOD_M15, x) - iLow(_Symbol, PERIOD_M15, x);
				if (fifol[x] < dist*fifco[x] && iOpen(_Symbol, PERIOD_M15, x + 1) + 0.75 * fifco[x] < iLow(_Symbol, PERIOD_M15, x) && iClose(_Symbol, PERIOD_M15, iLowest(_Symbol, PERIOD_M15, MODE_CLOSE, x - 1, 1)) > iLow(_Symbol, PERIOD_M15, x + 1)) { fifth[x] = iLow(_Symbol, PERIOD_M15, x + 1); }
				else { fifth[x] = EMPTY_VALUE; }
			}
		}
		for (int x = iBars(_Symbol, PERIOD_M15) - 2; x >= 1; x--) {
			if ((iOpen(_Symbol, PERIOD_M15, x + 1) - iClose(_Symbol, PERIOD_M15, x + 1)) > 0 && (iOpen(_Symbol, PERIOD_M15, x) - iClose(_Symbol, PERIOD_M15, x)) > 0)
			{
				fifoc[x] = iOpen(_Symbol, PERIOD_M15, x + 1) - iClose(_Symbol, PERIOD_M15, x + 1);
				fifho[x] = iHigh(_Symbol, PERIOD_M15, x) - iOpen(_Symbol, PERIOD_M15, x);
				if (fifho[x] < dist*fifoc[x] && iOpen(_Symbol, PERIOD_M15, x + 1) - 0.75 * fifoc[x] > iHigh(_Symbol, PERIOD_M15, x) && iClose(_Symbol, PERIOD_M15, iHighest(_Symbol, PERIOD_M15, MODE_CLOSE, x - 1, 1)) < iHigh(_Symbol, PERIOD_M15, x + 1)) { fiftl[x] = iHigh(_Symbol, PERIOD_M15, x + 1); }
				else { fiftl[x] = EMPTY_VALUE; }
			}
		}
		{//15 Minutes
			double newfifth[], newfiftl[];
			ArrayResize(newfifth, iBars(_Symbol, PERIOD_M15)); ArrayResize(newfiftl, iBars(_Symbol, PERIOD_M15));

			for (int y = ArraySize(fifth) - 1, z = EMPTY; y >= 0; y--) {
				if (fifth[y] != EMPTY_VALUE && fifth[y] > 0.00001) {
					++z; ArrayResize(newfifth, z + 1); newfifth[z] = fifth[y];
				}
			}
			for (int y = ArraySize(fiftl) - 1, z = EMPTY; y >= 0; y--) {
				if (fiftl[y] != EMPTY_VALUE && fiftl[y] > 0.00001) {
					++z; ArrayResize(newfiftl, z + 1); newfiftl[z] = fiftl[y];
				}
			}

			ArraySetAsSeries(newfifth,false); ArraySort(newfifth); ArraySort(newfiftl);
			if (ArraySize(newfifth) >= 1) bobbit[6] = newfifth[0]; else bobbit[6] = 0;
			if (ArraySize(newfiftl) >= 1) doddit[6] = newfiftl[0]; else doddit[6] = 0;
			if (ArraySize(newfifth) >= 2) bobbit[6] = newfifth[1]; else bobbit[6] = 0;
			if (ArraySize(newfiftl) >= 2) doddit[6] = newfiftl[1]; else doddit[6] = 0;
			if (ArraySize(newfifth) >= 3) bobbit[6] = newfifth[2]; else bobbit[6] = 0;
			if (ArraySize(newfiftl) >= 3) doddit[6] = newfiftl[2]; else doddit[6] = 0;
		}
		new_fift_check = false;
	}

	// 5 Minutes
	bool new_five_check = false;
	if (start_five_time < iTime(NULL, PERIOD_M5, 0))
	{
		new_five_check = true;
		start_five_time = iTime(NULL, PERIOD_M5, 0);
	}
	if (new_five_check)
	{
		double fiveh[], fivel[];
		double fivco[], fivol[], fivoc[], fivho[];
		ArrayResize(fiveh, iBars(_Symbol, PERIOD_M5)); ArrayResize(fivel, iBars(_Symbol, PERIOD_M5));
		ArrayResize(fivco, iBars(_Symbol, PERIOD_M5)); ArrayResize(fivol, iBars(_Symbol, PERIOD_M5)); ArrayResize(fivoc, iBars(_Symbol, PERIOD_M5)); ArrayResize(fivho, iBars(_Symbol, PERIOD_M5));
		for (int x = iBars(_Symbol, PERIOD_M5) - 2; x >= 1; x--) {
			if ((iClose(_Symbol, PERIOD_M5, x + 1) - iOpen(_Symbol, PERIOD_M5, x + 1)) > 0 && (iClose(_Symbol, PERIOD_M5, x) - iOpen(_Symbol, PERIOD_M5, x)) > 0)
			{
				fivco[x] = iClose(_Symbol, PERIOD_M5, x + 1) - iOpen(_Symbol, PERIOD_M5, x + 1);
				fivol[x] = iOpen(_Symbol, PERIOD_M5, x) - iLow(_Symbol, PERIOD_M5, x);
				if (fivol[x] < dist*fivco[x] && iOpen(_Symbol, PERIOD_M5, x + 1) + 0.75 * fivco[x] < iLow(_Symbol, PERIOD_M5, x) && iClose(_Symbol, PERIOD_M5, iLowest(_Symbol, PERIOD_M5, MODE_CLOSE, x - 1, 1)) > iLow(_Symbol, PERIOD_M5, x + 1)) { fiveh[x] = iLow(_Symbol, PERIOD_M5, x + 1); }
				else { fiveh[x] = EMPTY_VALUE; }
			}
		}
		for (int x = iBars(_Symbol, PERIOD_M5) - 2; x >= 1; x--) {
			if ((iOpen(_Symbol, PERIOD_M5, x + 1) - iClose(_Symbol, PERIOD_M5, x + 1)) > 0 && (iOpen(_Symbol, PERIOD_M5, x) - iClose(_Symbol, PERIOD_M5, x)) > 0)
			{
				fivoc[x] = iOpen(_Symbol, PERIOD_M5, x + 1) - iClose(_Symbol, PERIOD_M5, x + 1);
				fivho[x] = iHigh(_Symbol, PERIOD_M5, x) - iOpen(_Symbol, PERIOD_M5, x);
				if (fivho[x] < dist*fivoc[x] && iOpen(_Symbol, PERIOD_M5, x + 1) - 0.75 * fivoc[x] > iHigh(_Symbol, PERIOD_M5, x) && iClose(_Symbol, PERIOD_M5, iHighest(_Symbol, PERIOD_M5, MODE_CLOSE, x - 1, 1)) < iHigh(_Symbol, PERIOD_M5, x + 1)) { fivel[x] = iHigh(_Symbol, PERIOD_M5, x + 1); }
				else { fivel[x] = EMPTY_VALUE; }
			}
		}
		{//5 Minutes
			double newfiveh[], newfivel[];
			ArrayResize(newfiveh, iBars(_Symbol, PERIOD_M5)); ArrayResize(newfivel, iBars(_Symbol, PERIOD_M5));

			for (int y = ArraySize(fiveh) - 1, z = EMPTY; y >= 0; y--) {
				if (fiveh[y] != EMPTY_VALUE && fiveh[y] > 0.00001) {
					++z; ArrayResize(newfiveh, z + 1); newfiveh[z] = fiveh[y];
				}
			}
			for (int y = ArraySize(fivel) - 1, z = EMPTY; y >= 0; y--) {
				if (fivel[y] != EMPTY_VALUE && fivel[y] > 0.00001) {
					++z; ArrayResize(newfivel, z + 1); newfivel[z] = fivel[y];
				}
			}
			
			ArraySetAsSeries(newfiveh,false); ArraySort(newfiveh); ArraySort(newfivel);
			if (ArraySize(newfiveh) >= 1) bobbit[7] = newfiveh[0]; else bobbit[7] = 0;
			if (ArraySize(newfivel) >= 1) doddit[7] = newfivel[0]; else doddit[7] = 0;
			if (ArraySize(newfiveh) >= 2) bobbit1[7] = newfiveh[1]; else bobbit1[7] = 0;
			if (ArraySize(newfivel) >= 2) doddit1[7] = newfivel[1]; else doddit1[7] = 0;
			if (ArraySize(newfiveh) >= 3) bobbit2[7] = newfiveh[2]; else bobbit2[7] = 0;
			if (ArraySize(newfivel) >= 3) doddit2[7] = newfivel[2]; else doddit2[7] = 0;
		}
		new_five_check = false;
	}
}
//+------------------------------------------------------------------+

//+RECTANGLES & LABELS FOR HIGH TF PRICES----------------------------+
void reclabloc() {
	m = (int)GlobalVariableGet("um" + _Symbol); n = (int)GlobalVariableGet("un" + _Symbol);
	string obname;
	
	double cs = 0, cr = 0;
	
	int b = 0;
	for (int x = 7; x >=0; x--) {
		if (doddit[x] == 0) b++;
	}
	
	cs = bobbit[ArrayMaximum(bobbit, 0, 0)];
	cr = doddit[ArrayMinimum(doddit, 0, b)];
	if (cr == 0) cr = doddit[ArrayMinimum(doddit, 0, b + 1)];
	
	for (int x = 7; x >= 0; x--) {
		obname = Name + "Rec " + " s " + IntegerToString(x);
		if (bobbit[x] != 0)
		{ LabelMake(obname, 0, m + 40, n + 30 + (x * 20), DoubleToString(bobbit[x], _Digits) + " (" + DoubleToString((SymbolInfoDouble(_Symbol,SYMBOL_BID) - bobbit[x]) / Pip, 2) + ")", 8, clrBlue); ObjectSetString(0, obname, OBJPROP_TOOLTIP, DoubleToString(bobbit1[x], _Digits) + " // " + DoubleToString(bobbit2[x], _Digits)); }
		else if (bobbit[x] == 0)
		LabelMake(obname, 0, m + 40, n + 30 + (x * 20), DoubleToString(bobbit[x], _Digits) + " (0)", 8, clrBlue);
		if (MathAbs(SymbolInfoDouble(_Symbol,SYMBOL_BID) - bobbit[x]) <= 5 * Pip) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite);
		if (bobbit[x] == cs) { ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 8); ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
	}
	for (int x = 7; x >= 0; x--) {
		obname = Name + "Rec " + " r " + IntegerToString(x);
		if (doddit[x] != 0)
		{ LabelMake(obname, 0, m + 140, n + 30 + (x * 20), DoubleToString(doddit[x], _Digits) + " (" + DoubleToString((doddit[x] - SymbolInfoDouble(_Symbol,SYMBOL_BID)) / Pip, 2) + ")", 8, clrRed); ObjectSetString(0, obname, OBJPROP_TOOLTIP, DoubleToString(doddit1[x], _Digits) + " // " + DoubleToString(doddit2[x], _Digits)); }
		else if (doddit[x] == 0)
		LabelMake(obname, 0, m + 140, n + 30 + (x * 20), DoubleToString(doddit[x], _Digits) + " (0)", 8, clrRed); 
		if (MathAbs(SymbolInfoDouble(_Symbol,SYMBOL_BID) - doddit[x]) <= 5 * Pip) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlack);
		if (doddit[x] == cr) { ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 8); ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
	}
}
//+------------------------------------------------------------------+

//+STATIC DRAW-------------------------------------------------------+
void statd() {
	string obname;
	obname = Name + " Redraw";	LabelMake(obname, 1, 85, 20, "Redraw", 10, clrBlack);
	obname = Name + " ShowBox";
	if (GlobalVariableGet("ub" + _Symbol) == 1)
		LabelMake(obname, 1, 85, 35, "ShowMTFBox", 8, clrBlue);
	else
		LabelMake(obname, 1, 85, 35, "ShowMTFBox", 8, clrRed);	
	obname = Name + " Time"; LabelMake(obname, 1, 85, 50, "", 10, clrBlack);
	obname = Name + " Spread"; LabelMake(obname, 1, 85, 65, "", 10, clrBlack);
	
	if (showbox == 1) {
		m = (int)GlobalVariableGet("um" + _Symbol); n = (int)GlobalVariableGet("un" + _Symbol);
		obname = Name + "Rec " + " LabRec";	RecMake(obname, m, n, 240, 190, clrSilver, clrBlack);	
		obname = Name + "Rec " + " MovRec";	RecMake(obname, m + 5, n + 5, 10, 10, clrWhite, clrBlack);
		ObjectSetInteger(0, obname, OBJPROP_WIDTH, 3);
		ObjectSetInteger(0, obname, OBJPROP_SELECTABLE, true);
		ObjectSetInteger(0, obname, OBJPROP_SELECTED, true);
		
		string lab1[8] = { "MN:", "WK:", "DY:", "H4:", "H1:", "30:", "15:", "M5:" };
		for (int x = 7; x >= 0; x--) {
			obname = Name + "Rec " + " lab1 " + IntegerToString(x);
			LabelMake(obname, 0, m + 10, n + 30 + (x * 20), lab1[x], 8, clrBlack);
		}
		
		string lab2[2] = { "S:", "R:" };
		for (int x = 1; x >= 0; x--) {
			obname = Name + "Rec " + " lab2 " + IntegerToString(x);
			LabelMake(obname, 0, m + 40 + (x * 100), n + 10, lab2[x], 8, clrBlack);
		}
	}
}
//+------------------------------------------------------------------+
*/

//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett) {
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT);   // number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);
	
	ObjectSetInteger(0,name,OBJPROP_TIME,Time[t1]);
	ObjectSetInteger(0,name,OBJPROP_TIME,1,Time[t2] + t3);
	ObjectSetDouble(0,name,OBJPROP_PRICE,pr1);
	ObjectSetDouble(0,name,OBJPROP_PRICE,1,pr2);
	ObjectSetInteger(0,name,OBJPROP_STYLE,(int)st);
	ObjectSetInteger(0,name,OBJPROP_WIDTH,(int)wi);
	ObjectSetInteger(0,name,OBJPROP_RAY_RIGHT,(int)false);
	ObjectSetInteger(0,name,OBJPROP_BACK,(int)false);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, col);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToString(pr1, _Digits));// + " Date: " + TimeToString(Time[t1], TIME_DATE));
	
Print("Done");
}
//+------------------------------------------------------------------+

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const datetime y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0,name,OBJPROP_PRICE,x);
	ObjectSetInteger(0,name,OBJPROP_TIME,y + (Period() * 60));
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	//ObjectSetString(0, name, OBJPROP_TOOLTIP, "Price: " + DoubleToString(x, _Digits));
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToString(x, _Digits));
}
//+------------------------------------------------------------------+

//+ARROWPRICE--------------------------------------------------------+
void ArrowPrice(const string name, const double x, const double r, const datetime y, const color FCol)
{
   datetime Time[]; 
   ArraySetAsSeries(Time,true);
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW_RIGHT_PRICE, 0, 0, 0))
		{
			Print("error: can't create arrow_right_price! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0,name,OBJPROP_PRICE,x);
	ObjectSetInteger(0, name, OBJPROP_TIME, CopyTime(_Symbol,PERIOD_CURRENT,0,0,Time) + y);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);//
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 0);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "Range: " + DoubleToString(x, _Digits) + " - " + DoubleToString(r, _Digits));
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
	const int corner,
	const int x,
	const int y,
	const string label,
	const int FSize,
	const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetTextMQL4(name, label, FSize, "Arial", FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const int x, const int y, const int xs, const int ys, const color FCol, const color BCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_BGCOLOR, FCol);
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetInteger(0, name, OBJPROP_XSIZE, xs);
	ObjectSetInteger(0, name, OBJPROP_YSIZE, ys);
	ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, Name);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+
bool ObjectSetTextMQL4(string name,
                       string text,
                       int font_size,
                       string font="",
                       color text_color=CLR_NONE)
  {
   int tmpObjType=(int)ObjectGetInteger(0,name,OBJPROP_TYPE);
   if(tmpObjType!=OBJ_LABEL && tmpObjType!=OBJ_TEXT) return(false);
   if(StringLen(text)>0 && font_size>0)
     {
      if(ObjectSetString(0,name,OBJPROP_TEXT,text)==true
         && ObjectSetInteger(0,name,OBJPROP_FONTSIZE,font_size)==true)
        {
         if((StringLen(font)>0)
            && ObjectSetString(0,name,OBJPROP_FONT,font)==false)
            return(false);
         if(text_color>-1
            && ObjectSetInteger(0,name,OBJPROP_COLOR,text_color)==false)
            return(false);
         return(true);
        }
      return(false);
     }
   return(false);
  }