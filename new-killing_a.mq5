//+------------------------------------------------------------------+
//|                                                         xmas.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
//--- input parameters

#define Name MQLInfoString(MQL_PROGRAM_NAME)
#property indicator_buffers 17
#property indicator_plots 17

input int shift = 0;          //Shift all back X bars

//for sds
double mixa[];                //outer rim up
double mixb[];                //outer rim dn

double outsiderima[];         //outer rim lwma band up
double outsiderimb[];         //outer rim lwma band dn

double sath[];                //purple dots when adr bands > lr bands
double spath[];               //aqua dots when adr bands lwma > lr bands

//for cs
input string suffix = "";     //Broker pair suffix i.e. .r .raw etc

//dannys
int times = 288;              //Dylan's LR Periods
double multiplier = 2.0;      //Dylan's LR band multiplier
int timesb = 72;              //LR channel Period (long)
int timesc = 240;              //LR channel period (short)
int timesa = 120;             //LR channel Period (longer)

//LR variables
double DylanM[];              //LR 288
double DylanL[];              //LR 288 low
double DylanH[];              //LR 288 high
double trophya[];             //LR long high channel
double trophyb[];             //LR long low channel
double trigga[];              //LR short high channel
double triggb[];              //LR short low channel
double trophyc[];             //LR longer high channel
double trophyd[];             //LR longer low channel

double trifd72[];             //SD on LR long high
double trofd72[];             //SD on LR long low

//Indicator settings
//mixa
#property indicator_type1 DRAW_NONE
#property indicator_style1 STYLE_SOLID
#property indicator_color1 clrBlack
#property indicator_width1 1

//mixb
#property indicator_type2 DRAW_NONE
#property indicator_style2 STYLE_SOLID
#property indicator_color2 clrBlack
#property indicator_width2 1

//outsiderima
#property indicator_type3 DRAW_NONE
#property indicator_style3 STYLE_SOLID
#property indicator_color3 clrSalmon
#property indicator_width3 2

//outsiderimb
#property indicator_type4 DRAW_NONE
#property indicator_style4 STYLE_SOLID
#property indicator_color4 clrSalmon
#property indicator_width4 2

//DylanM
#property indicator_type5 DRAW_NONE

//DylanH
#property indicator_type6 DRAW_NONE

//DylanL
#property indicator_type7 DRAW_NONE

//sath
#property indicator_type8 DRAW_LINE
#property indicator_style8 STYLE_SOLID
#property indicator_color8 clrPurple
#property indicator_width8 5

//trophya
#property indicator_type9 DRAW_LINE
#property indicator_style9 STYLE_SOLID
#property indicator_color9 clrGoldenrod
#property indicator_width9 3

//trophyb
#property indicator_type10 DRAW_LINE
#property indicator_style10 STYLE_SOLID
#property indicator_color10 clrYellow
#property indicator_width10 3

//trigga
#property indicator_type11 DRAW_LINE
#property indicator_style11 STYLE_SOLID
#property indicator_color11 clrBlue
#property indicator_width11 1

//triggb
#property indicator_type12 DRAW_LINE
#property indicator_style12 STYLE_SOLID
#property indicator_color12 clrRed
#property indicator_width12 1

//spath
#property indicator_type13 DRAW_LINE
#property indicator_style13 STYLE_SOLID
#property indicator_color13 clrWhite
#property indicator_width13 1

//trophyc
#property indicator_type14 DRAW_LINE
#property indicator_style14 STYLE_SOLID
#property indicator_color14 clrGoldenrod
#property indicator_width14 2

//trophyd
#property indicator_type15 DRAW_LINE
#property indicator_style15 STYLE_SOLID
#property indicator_color15 clrYellow
#property indicator_width15 2

//trifd72
#property indicator_type16 DRAW_LINE
#property indicator_style16 STYLE_SOLID
#property indicator_color16 clrGoldenrod
#property indicator_width16 1

//trofd72
#property indicator_type17 DRAW_LINE
#property indicator_style17 STYLE_SOLID
#property indicator_color17 clrYellow
#property indicator_width17 1

int atr21;
double iatr21[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping

   DeleteObjects();

   SetIndexBuffer(0, mixa, INDICATOR_DATA); // outer std band
   SetIndexBuffer(1, mixb, INDICATOR_DATA); // outer std band
   SetIndexBuffer(2, outsiderima, INDICATOR_DATA); // outer band 21 lwma on mix high
   SetIndexBuffer(3, outsiderimb, INDICATOR_DATA); // outer band 21 lwma on mix low
   SetIndexBuffer(4, DylanM, INDICATOR_DATA);
   SetIndexBuffer(5, DylanH, INDICATOR_DATA);
   SetIndexBuffer(6, DylanL, INDICATOR_DATA);
   SetIndexBuffer(7, sath, INDICATOR_DATA);
   SetIndexBuffer(8, trophya, INDICATOR_DATA);
   SetIndexBuffer(9, trophyb, INDICATOR_DATA);
   SetIndexBuffer(10, trigga, INDICATOR_DATA);
   SetIndexBuffer(11, triggb, INDICATOR_DATA);
   SetIndexBuffer(12, spath, INDICATOR_DATA);
   SetIndexBuffer(13, trophyc, INDICATOR_DATA);
   SetIndexBuffer(14, trophyd, INDICATOR_DATA);
   SetIndexBuffer(15, trifd72, INDICATOR_DATA);
   SetIndexBuffer(16, trofd72, INDICATOR_DATA);
	
	atr21 = iATR(_Symbol, PERIOD_CURRENT, 21);
   
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---

      if (IsNewBar(PERIOD_CURRENT)) tsap();
		
		if (IsNewBar(PERIOD_M30)) ObjectsDeleteAll(0, Name + " y");
		
			//if (SymbolInfoDouble(_Symbol, SYMBOL_BID) > trophyc[0] && SymbolInfoDouble(_Symbol, SYMBOL_BID) > trophya[0]) { Alert(_Symbol + " too high " + EnumToString(ChartPeriod())); SendNotification(_Symbol + " too high " + EnumToString(ChartPeriod())); }
			//if (SymbolInfoDouble(_Symbol, SYMBOL_BID) < trophyd[0] && SymbolInfoDouble(_Symbol, SYMBOL_BID) < trophyb[0]) { Alert(_Symbol + " too low " + EnumToString(ChartPeriod())); SendNotification(_Symbol + " too low " + EnumToString(ChartPeriod())); }
			//if (SymbolInfoDouble(_Symbol, SYMBOL_BID) > trigga[0]) { Alert(_Symbol + " momentum up " + EnumToString(ChartPeriod())); SendNotification(_Symbol + " momentum up " + EnumToString(ChartPeriod())); }
			//if (SymbolInfoDouble(_Symbol, SYMBOL_BID) < triggb[0]) { Alert(_Symbol + " momentum down " + EnumToString(ChartPeriod())); SendNotification(_Symbol + " momentum down " + EnumToString(ChartPeriod())); }
			
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+

bool IsNewBar(ENUM_TIMEFRAMES period = PERIOD_CURRENT) {
    static datetime last_time = 0;
    datetime current_time = iTime(_Symbol, period, 0);
    if(last_time < current_time) {
        last_time = current_time;
        return true;
    }
    return false;
}

//MAIN PROGRAM
//+MAIN FUNCTION-----------------------------------------------------+
void tsap(){
   string obname;
         
   ArrayResize(iatr21, iBars(_Symbol, PERIOD_CURRENT));
   ArraySetAsSeries(iatr21, true);
   CopyBuffer(atr21, 0, 0, iBars(_Symbol, PERIOD_CURRENT), iatr21);
   
   double high[], low[], cc[], oo[];
   const int array_size = iBars(_Symbol, PERIOD_CURRENT);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(cc, true);
   ArraySetAsSeries(oo, true);
   if(!ArrayResize(high, array_size) || !ArrayResize(low, array_size) || !ArrayResize(cc, array_size) || !ArrayResize(oo, array_size)) {
       Print("Failed to allocate memory for arrays");
       return;
   }
   CopyHigh(_Symbol, PERIOD_CURRENT, 0, iBars(_Symbol, PERIOD_CURRENT), high);
   CopyLow(_Symbol, PERIOD_CURRENT, 0, iBars(_Symbol, PERIOD_CURRENT), low);
   CopyClose(_Symbol, PERIOD_CURRENT, 0, iBars(_Symbol, PERIOD_CURRENT), cc);
   CopyOpen(_Symbol, PERIOD_CURRENT, 0, iBars(_Symbol, PERIOD_CURRENT), oo);
   
   ArraySetAsSeries(mixa, true);
   ArraySetAsSeries(mixb, true);
   ArraySetAsSeries(outsiderima, true);
   ArraySetAsSeries(outsiderimb, true);
   ArraySetAsSeries(DylanM, true);
   ArraySetAsSeries(DylanH, true);
   ArraySetAsSeries(DylanL, true);   
   ArraySetAsSeries(sath, true);
   ArraySetAsSeries(trophya, true);
   ArraySetAsSeries(trophyb, true);
   ArraySetAsSeries(trigga, true);
   ArraySetAsSeries(triggb, true);
   ArraySetAsSeries(spath, true);
   ArraySetAsSeries(trophyc, true);
   ArraySetAsSeries(trophyd, true);
   ArraySetAsSeries(trifd72, true);
   ArraySetAsSeries(trofd72, true);

   ArrayInitialize(mixa, EMPTY_VALUE);
   ArrayInitialize(mixb, EMPTY_VALUE);
   ArrayInitialize(outsiderima, EMPTY_VALUE);
   ArrayInitialize(outsiderimb, EMPTY_VALUE);
   ArrayInitialize(DylanM, EMPTY_VALUE);
   ArrayInitialize(DylanH, EMPTY_VALUE);
   ArrayInitialize(DylanL, EMPTY_VALUE);   
   ArrayInitialize(sath, EMPTY_VALUE);
   ArrayInitialize(spath, EMPTY_VALUE);
   
   //LR variables   
   double summix = 0, summiy = 0, summixy = 0, summix2 = 0, summiy2 = 0;
   double lr2 = 0, yinttt = 0;//, lr3 = 0;
   
   for (int y = iBars(_Symbol, PERIOD_CURRENT) - 21; y >= 0 + shift; y--)
   {
      //Dylan's LR
      if (y < iBars(_Symbol, PERIOD_CURRENT) - (times + 1))
      {
         summix = 0;
         summiy = 0;
         summixy = 0;
         summix2 = 0;
         summiy2 = 0;
         
         for (int lr1 = 0; lr1 <= times - 1; lr1++)
         {
            summix = summix + lr1;
            summiy = summiy + cc[y + lr1];
            summixy = summixy + lr1 * cc[y + lr1];
            summix2 = summix2 + lr1 * lr1;
            summiy2 = summiy2 + cc[y + lr1] * cc[y + lr1];
         }
         
         lr2 = (times * summixy - summix * summiy) / (times * summix2 - summix * summix);
         yinttt = (summiy + lr2 * summix) / times;
         //lr3 = (times * summixy - summix * summiy) / MathSqrt((times * summix2 - summix * summix) * (times * summiy2 - summiy  * summiy));
         DylanM[y] = yinttt - lr2 * times;
         
         double rss = 0;
                  
         for (int x = (times - 1) + y; x >= y; x--)
         {
            rss += ((cc[x] - DylanM[x]) * (cc[x] - DylanM[x])) / (times - 1);
         }
         
         DylanH[y] = DylanM[y] + multiplier * MathSqrt(rss);
         DylanL[y] = DylanM[y] - multiplier * MathSqrt(rss); 
      }
      
      //Own system sd's and bands buffer fill
      //MathSqrt(startt * 0.5 * 0.5) * startadr;
      mixa[y] = oo[y] + MathSqrt(21 * 0.5 * 0.5) * iatr21[y];
      mixb[y] = oo[y] - MathSqrt(21 * 0.5 * 0.5) * iatr21[y];
      
      //LinearWeightedMAOnBuffer(rates_total, 0, 0, 10, mixa[y], outsiderima[y]);
      outsiderima[y] = iMAOnArray(mixa, iBars(_Symbol, PERIOD_CURRENT) - 21, 10, 0, y);
      //outsiderima[y] = iEMAOnArray(mixa, iBars(_Symbol, PERIOD_CURRENT) - 21, 10, 0, y);
      //Print(outsiderima[0] + " " + outsiderima[1] + " " + outsiderimb[0]);
      outsiderimb[y] = iMAOnArray(mixb, iBars(_Symbol, PERIOD_CURRENT) - 21, 10, 0, y);
      //outsiderimb[y] = iEMAOnArray(mixb, iBars(_Symbol, PERIOD_CURRENT) - 21, 10, 0, y);
      
      //Candle coloring for signals
      
      //Pink/Aqua bars for price exceeding outside LWMA band
      
      //Silver bars when both LWMA bands inside ATR bands
      
      if (mixa[y] > DylanH[y]) sath[y] = DylanH[y];
      if (mixb[y] < DylanL[y]) sath[y] = DylanL[y];
      if (outsiderima[y] > DylanH[y]) spath[y] = DylanH[y];
      if (outsiderimb[y] < DylanL[y]) spath[y] = DylanL[y];
   }
   
   static datetime nh = 0;
   bool nh_check = false;
   if (nh < iTime(_Symbol, PERIOD_CURRENT, 0))
   {
      nh = iTime(_Symbol, PERIOD_CURRENT, 0);
      nh_check = true;
   }
   if (nh_check)
   {
      int frank[2], madri[2];
      
      frank[0] = ArrayMaximum(mixa, 25 + shift, 224 + shift);
      frank[1] = ArrayMaximum(mixa, 25 + shift, 384 + shift);
      
      madri[0] = ArrayMinimum(mixb, 25 + shift, 224 + shift);
      madri[1] = ArrayMinimum(mixb, 25 + shift, 384 + shift);
      
   	datetime Time[];
   	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
   	ArraySetAsSeries(Time, true);
   	CopyTime(_Symbol, _Period, 0, counta, Time);
   	
      if (frank[0] != 0) 
      { 
         obname = Name + " recup1";
         if (iBars(_Symbol, PERIOD_CURRENT) > frank[0])
         RecMake(obname, high[frank[0]], mixa[frank[0]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, frank[0]), false), 0, 0, clrLemonChiffon, ""); 
         ObjectSetInteger(0, obname, OBJPROP_BACK, false); 
         ObjectSetInteger(0, obname, OBJPROP_FILL, false);
         ObjectSetInteger(0, obname, OBJPROP_WIDTH, 3); 
         ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[0] + 144000);
      }
      if (frank[1] != 0 && frank[1] != frank[0])
      { 
         obname = Name + " recup2"; 
         if (iBars(_Symbol, PERIOD_CURRENT) > frank[1])
         RecMake(obname, high[frank[1]], mixa[frank[1]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, frank[1]), false), 0, 0, clrWhite, ""); 
         ObjectSetInteger(0, obname, OBJPROP_BACK, false); 
         ObjectSetInteger(0, obname, OBJPROP_FILL, false); 
         ObjectSetInteger(0, obname, OBJPROP_WIDTH, 3); 
         ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[0] + 144000);
      }
      
      if (madri[0] != 0) 
      { 
         obname = Name + " recdn1";
         if (iBars(_Symbol, PERIOD_CURRENT) > madri[0])
         RecMake(obname, low[madri[0]], mixb[madri[0]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, madri[0]), false), 0, 0, clrLemonChiffon, ""); 
         ObjectSetInteger(0, obname, OBJPROP_BACK, false); 
         ObjectSetInteger(0, obname, OBJPROP_FILL, false); 
         ObjectSetInteger(0, obname, OBJPROP_WIDTH, 3); 
         ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[0] + 144000);
      }
      if (madri[1] != 0 && madri[1] != madri[0]) 
      { 
         obname = Name + " recdn2"; 
         if (iBars(_Symbol, PERIOD_CURRENT) > madri[1])
         RecMake(obname, low[madri[1]], mixb[madri[1]], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, madri[1]), false), 0, 0, clrWhite, ""); 
         ObjectSetInteger(0, obname, OBJPROP_BACK, false); 
         ObjectSetInteger(0, obname, OBJPROP_FILL, false); 
         ObjectSetInteger(0, obname, OBJPROP_WIDTH, 3); 
         ObjectSetInteger(0, obname, OBJPROP_TIME, 1, Time[0] + 144000);
      }
      {
         //Arrows when outside bands interact with Dylan's LR && band within 0-20% or 80-100% of 360 range
         double HH = mixa[ArrayMaximum(mixa, 25 + shift, 224 + shift)];
         int HHs = ArrayMaximum(mixa, 25 + shift, 224 + shift);
         double LL = mixb[ArrayMinimum(mixb, 25 + shift, 224 + shift)];
         int LLs = ArrayMinimum(mixb, 25 + shift, 224 + shift);
         double torop = 0.2 * (HH - LL);
         
         for (int y = 224 + shift; y >= 1 + shift; y--)
         {
            if (y < HHs && y != HHs && y != LLs && mixa[y] > mixa[y + 1] && mixa[y] > outsiderima[y] && mixa[y] > HH - torop) { obname = Name + "Arrh" + IntegerToString(y); burnarr(obname, mixa[y], 174, y, clrMagenta); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); }
            if (y < LLs && y != HHs && y != LLs && mixb[y] < mixb[y + 1] && mixb[y] < outsiderimb[y] && mixb[y] < LL + torop) { obname = Name + "Arrl" + IntegerToString(y); burnarr(obname, mixb[y], 174, y, clrLime); ; ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); }
            if (y < HHs && y != HHs && y != LLs && mixa[y + 2] < DylanM[y + 2] && mixa[y + 1] > DylanM[y + 1] && mixa[y] < DylanM[y]) { obname = Name + "Arrh" + IntegerToString(y); burnarr(obname, high[y], 108, y, clrYellow); ; ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); }
            if (y < LLs && y != HHs && y != LLs && mixb[y + 2] > DylanM[y + 2] && mixb[y + 1] < DylanM[y + 1] && mixb[y] > DylanM[y]) { obname = Name + "Arrl" + IntegerToString(y); burnarr(obname, low[y], 108, y, clrYellow); ; ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); }
         }
      }
      {
         //Arrows when outside bands interact with Dylan's LR && band within 0-20% or 80-100% of 360 range
         double HH = mixa[ArrayMaximum(mixa, 225 + shift, 384 + shift)];
         int HHs = ArrayMaximum(mixa, 225 + shift, 384 + shift);
         double LL = mixb[ArrayMinimum(mixb, 225 + shift, 384 + shift)];
         int LLs = ArrayMinimum(mixb, 225 + shift, 384 + shift);
         double torop = 0.2 * (HH - LL);
         
         for (int y = 384 + shift; y >= 224 + shift; y--)
         {
            if (y < HHs && y != HHs && y != LLs && mixa[y] > mixa[y + 1] && mixa[y] > outsiderima[y] && mixa[y] > HH - torop) { obname = Name + "Arrh" + IntegerToString(y); burnarr(obname, mixa[y], 174, y, clrMagenta); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); }
            if (y < LLs && y != HHs && y != LLs && mixb[y] < mixb[y + 1] && mixb[y] < outsiderimb[y] && mixb[y] < LL + torop) { obname = Name + "Arrl" + IntegerToString(y); burnarr(obname, mixb[y], 174, y, clrLime); ; ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); }
            if (y < HHs && y != HHs && y != LLs && mixa[y + 2] < DylanM[y + 2] && mixa[y + 1] > DylanM[y + 1] && mixa[y] < DylanM[y]) { obname = Name + "Arrh" + IntegerToString(y); burnarr(obname, high[y], 108, y, clrYellow); ; ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); }
            if (y < LLs && y != HHs && y != LLs && mixb[y + 2] > DylanM[y + 2] && mixb[y + 1] < DylanM[y + 1] && mixb[y] > DylanM[y]) { obname = Name + "Arrl" + IntegerToString(y); burnarr(obname, low[y], 108, y, clrYellow); ; ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); }
         }
      }
   
         ArrayInitialize(trophya, EMPTY_VALUE);
         ArrayInitialize(trophyb, EMPTY_VALUE);
         ArrayInitialize(trophyc, EMPTY_VALUE);
         ArrayInitialize(trophyd, EMPTY_VALUE);
         ArrayInitialize(trigga, EMPTY_VALUE);
         ArrayInitialize(triggb, EMPTY_VALUE);
         ArrayInitialize(trifd72, EMPTY_VALUE);
         ArrayInitialize(trofd72, EMPTY_VALUE);
         
         calculateLinearRegression(mixa, trophya, timesb);
         calculateLinearRegression(mixb, trophyb, timesb);
         calculateLinearRegression(mixa, trophyc, timesa);
         calculateLinearRegression(mixb, trophyd, timesa);
         calculateLinearRegression(mixa, trigga, timesc);
         calculateLinearRegression(mixb, triggb, timesc);
         
         //Print(trophya[0] + " " + trophya[100]);
         //LR regression lines on adr bands
         for (int j = shift + timesa - 1; j >= shift; j--)
         {
            // Inside SD calculation
            double ab = 0;
            double bc = 0;
            double cd = 0;
   
            for (int x = 24 - 1 + j; x >= j; x--)
            {
               ab += mixa[x] / (24);
            }
   
            for (int x = 24 - 1 + j; x >= j; x--)
            {
               bc += MathPow(mixa[x] - ab, 2);
            }
   
            cd = MathSqrt(bc / (24));
   
            // trifu72[j] = trophya[j] + 1 * cd;
            trifd72[j] = trophyc[j] + 1 * cd;
         }
         for (int j = shift + timesa - 1; j >= shift; j--)
         {
            // Inside SD calculation
            double ab = 0;
            double bc = 0;
            double cd = 0;
   
            for (int x = 24 - 1 + j; x >= j; x--)
            {
               ab += mixb[x] / (24);
            }
   
            for (int x = 24 - 1 + j; x >= j; x--)
            {
               bc += MathPow(mixb[x] - ab, 2);
            }
   
            cd = MathSqrt(bc / (24));
   
            // trofu72[j] = trophyd[j] + 1 * cd;
            trofd72[j] = trophyd[j] - 1 * cd;
         }
      }
}
//+------------------------------------------------------------------+

//+CREATE T-LINES----------------------------------------------------+
void objhoriz(string oname, double pr1, int width, color col, string stringa)
{
	if (ObjectFind(0, oname) < 0)
		if (!ObjectCreate(0, oname, OBJ_HLINE, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, oname, OBJPROP_STYLE, STYLE_SOLID);
	ObjectSetInteger(0, oname, OBJPROP_WIDTH, width);
	ObjectSetInteger(0, oname, OBJPROP_BACK, true);
	ObjectSetInteger(0, oname, OBJPROP_COLOR, col);
	ObjectSetDouble(0, oname, OBJPROP_PRICE, 0, pr1);
	ObjectSetInteger(0, oname, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, oname, OBJPROP_TEXT, stringa);
}
//+------------------------------------------------------------------+
//+ARROW CREATE------------------------------------------------------+
void burnarr(string name, double p, int arrow, int t, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
   	datetime Time[];
   	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
   	ArraySetAsSeries(Time, true);
   	CopyTime(_Symbol, _Period, 0, counta, Time);
	ObjectSetInteger(0, name, OBJPROP_TIME, 0, Time[t]);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 0, p);
	ObjectSetInteger(0, name, OBJPROP_ARROWCODE, arrow);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
}
//+------------------------------------------------------------------+
//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const double pr1, const double pr2, const int t1, const int t2, const int t3, const color BCol, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
   	datetime Time[];
   	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
   	ArraySetAsSeries(Time, true);
   	CopyTime(_Symbol, _Period, 0, counta, Time);
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_TIME, 0, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Time[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 0, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const datetime y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
   	datetime Time[];
   	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
   	ArraySetAsSeries(Time, true);
   	CopyTime(_Symbol, _Period, 0, counta, Time);
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 0, x);
	ObjectSetInteger(0, name, OBJPROP_TIME, 0, y + (Period() * 60));
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	//ObjectSetString(0, name, OBJPROP_TOOLTIP, "Price: " + DoubleToStr(x, _Digits));
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToString(x, _Digits));
}
//+------------------------------------------------------------------+
//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
   	datetime Time[];
   	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
   	ArraySetAsSeries(Time, true);
   	CopyTime(_Symbol, _Period, 0, counta, Time);
	ObjectSetInteger(0, name, OBJPROP_TIME, 0, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Time[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 0, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
	ObjectSetInteger(0, name, OBJPROP_STYLE, st);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
	ObjectSetInteger(0, name, OBJPROP_RAY, false);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToString(pr1, _Digits) + " Date: " + TimeToString(Time[t1], TIME_DATE));
}
//+------------------------------------------------------------------+
double adr(const int X, const int Y){
   
   double HD[], LD[];
   const int array_size = Y + 1;
   ArraySetAsSeries(HD, true);
   ArraySetAsSeries(LD, true);
   if(!ArrayResize(HD, array_size) || !ArrayResize(LD, array_size)) {
       Print("Failed to allocate memory for arrays");
       return 0;
   }
   CopyHigh(_Symbol, PERIOD_CURRENT, 1, Y + 1, HD);
   CopyLow(_Symbol, PERIOD_CURRENT, 1, Y + 1, LD);

   double adra = 0;
   
   for (int x = Y; x > Y - X; x--){
      adra += (HD[x] - LD[x]);
   }
   
   double adri = 0;
   adri = adra / X;
   
   ArrayFree(HD);
   ArrayFree(LD);
   return(adri);
}
//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	DeleteObjects();
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	for (int i = ObjectsTotal(0) - 1; i >= 0; i--)
	{
		string ObName = ObjectName(0, i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(0, ObName);
		}
	}
}
//+------------------------------------------------------------------+
//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend3(string name, double pr1, double pr2, int t1, int t2, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
   	datetime Time[];
   	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
   	ArraySetAsSeries(Time, true);
   	CopyTime(_Symbol, _Period, 0, counta, Time);
	ObjectSetInteger(0, name, OBJPROP_TIME, 0, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Time[t2]);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 0, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
	ObjectSetInteger(0, name, OBJPROP_STYLE, st);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
	ObjectSetInteger(0, name, OBJPROP_RAY, false);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett); // + " Price: " + DoubleToStr(pr1, _Digits));
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+

void calculateLinearRegression(double &mix[], double &tro[], int period) {
  double isumx = 0.0, isumy = 0.0;
  double isumxy = 0.0, isumx2 = 0.0;

  // First pass: calculate sums
  for (int j = shift; j < (shift + period); j++) {
      isumy += mix[j];
      isumxy += mix[j] * j;
      isumx += j;
      isumx2 += j * j;
  }

  // Calculate regression parameters
  double c = isumx2 * period - isumx * isumx;
  double a = (isumxy * period - isumx * isumy) / c;
  double b = (isumy - isumx * a) / period;

  // Second pass: calculate output values
  for (int j = shift; j < (shift + period); j++) {
      tro[j] = a * j + b;
  }
}
double CalculateSD(int timiz, int j, const double &array[])
{
    double ab = 0;
    double bc = 0;
    
    // Calculate mean
    for (int x = timiz - 1 + j; x >= j; x--) {
        ab += array[x] / timiz;
    }
    
    // Calculate sum of squared differences
    for (int x = timiz - 1 + j; x >= j; x--) {
        bc += MathPow(array[x] - ab, 2);
    }
    
    // Return standard deviation
    return MathSqrt(bc/24);
}

double iMAOnArray(double &array[],
                 int total,
                 int period,
                 int ma_shift,
                 int shifta)
{
    double buf[], arr[];
    
    // Validate inputs
    if(total==0) 
        total=ArraySize(array);
    if(total>0 && total<=period) 
        return(0);
    if(shifta>total-period-ma_shift) 
        return(0);
        
    // Resize buffer array
    if(ArrayResize(buf,total)<0) 
        return(0);
        
    double sum=0.0, lsum=0.0;
    double price;
    int i, weight=0, pos=total-1;
    
    // Calculate initial sums
    for(i=1; i<=period; i++, pos--)
    {
        price=array[pos];
        sum+=price*i;      // Weighted sum
        lsum+=price;       // Linear sum
        weight+=i;         // Total weight
    }
    
    // Calculate LWMA for each position
    pos++;
    i=pos+period;
    while(pos>=0)
    {
        buf[pos]=sum/weight;
        if(pos==0) 
            break;
            
        pos--;
        i--;
        price=array[pos];
        sum=sum-lsum+price*period;
        lsum-=array[i];
        lsum+=price;
    }
    
    return(buf[shifta+ma_shift]);
}

double iEMAOnArray(double &array[], int total, int period, int ma_shift, int shift)
{
    double alpha = 2.0/(period + 1.0);
    double buffer[];
    
    // Check inputs
    if(total == 0) total = ArraySize(array);
    if(total <= period || shift < 0) return 0;
    if(ArrayResize(buffer, total) < 0) return 0;
    
    // Initialize first EMA value with SMA
    double sum = 0;
    for(int i = 0; i < period; i++)
    {
        sum += array[total - 1 - i];
    }
    buffer[total - 1] = sum/period;
    
    // Calculate EMA
    for(int i = total - 2; i >= 0; i--)
    {
        buffer[i] = array[i] * alpha + buffer[i + 1] * (1 - alpha);
    }
    
    return buffer[shift + ma_shift];
}