//+------------------------------------------------------------------+
//|                                               wavelet-simple.mq5 |
//|                        Copyright 2019, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2019, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 8
#property indicator_plots 2
#property indicator_type1  DRAW_LINE
#property indicator_color1 clrPeru
#property indicator_style1 STYLE_SOLID
#property indicator_width1 2
#property indicator_label1 "d"
#property indicator_type2  DRAW_LINE
#property indicator_color2 clrPeru
#property indicator_style2 STYLE_SOLID
#property indicator_width2 2
#property indicator_label2 "k"
//--- input parameters
int waveperiods=3000;
int wpr = 10;
double ma3[], ma4[];
double dippe[], kippe[], ma36[], ma36h[], ma36l[], atrix[];
int ma36data, ma36hdata, ma36ldata, atrdays;
int Sampling_Period = 4 * wpr; // Sampling period for 3.0 (must be at least 4* smoothing)

#include <MovingAverages.mqh>

uint start, end;
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   
   start = GetTickCount();
	IndicatorSetInteger(INDICATOR_DIGITS,_Digits);
	
	SetIndexBuffer(0, ma3, INDICATOR_CALCULATIONS);
	SetIndexBuffer(1, ma4, INDICATOR_CALCULATIONS);
	SetIndexBuffer(2, dippe, INDICATOR_CALCULATIONS);
	SetIndexBuffer(3, kippe, INDICATOR_CALCULATIONS);
	SetIndexBuffer(4, ma36, INDICATOR_CALCULATIONS);
	SetIndexBuffer(5, ma36h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(6, ma36l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(7, atrix, INDICATOR_CALCULATIONS);
	
	ma36data = iMA(_Symbol, PERIOD_CURRENT, wpr, 0, MODE_EMA, PRICE_TYPICAL);
	ma36hdata = iMA(_Symbol, PERIOD_CURRENT, wpr, 0, MODE_EMA, PRICE_HIGH);
	ma36ldata = iMA(_Symbol, PERIOD_CURRENT, wpr, 0, MODE_EMA, PRICE_LOW);
	atrdays = iATR(_Symbol, PERIOD_CURRENT, wpr);

 	ArraySetAsSeries(ma36, true); ArraySetAsSeries(ma36h, true); ArraySetAsSeries(ma36l, true); ArraySetAsSeries(atrix, true);
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   
	bool new_1m_check = false;
	static datetime start_1m_time = 0;
	if (start_1m_time < iTime(NULL, ChartPeriod(), 0))
	{
		new_1m_check = true;
		start_1m_time = iTime(NULL, ChartPeriod(), 0);
	}
	if (new_1m_check)
	{
   int bars = Bars(_Symbol, PERIOD_CURRENT);
	if (rates_total < waveperiods + wpr || waveperiods == 0) waveperiods = rates_total - 1 - wpr;

	if (CopyBuffer(ma36data, 0, 0, waveperiods, ma36) <= 0) Print("Getting EMA10 failed! Error",GetLastError());
	if (CopyBuffer(ma36hdata, 0, 0, waveperiods, ma36h) <= 0) Print("Getting EMA10H failed! Error",GetLastError());
	if (CopyBuffer(ma36ldata, 0, 0, waveperiods, ma36l) <= 0) Print("Getting EMA10L failed! Error",GetLastError());
	if (CopyBuffer(atrdays, 0, 0, waveperiods, atrix) <= 0) Print("Getting ATR failed! Error",GetLastError());
	   
	{ // MAIN CALCS	   
		double volful[];
		double volp[], voln[];
		double cvolp[], cvoln[];
		ArrayResize(volful, waveperiods + 1 + wpr);
		ArrayInitialize(volful, 0);
		ArrayResize(volp, waveperiods + 1 + wpr);
		ArrayInitialize(volp, 0);
		ArrayResize(voln, waveperiods + 1 + wpr);
		ArrayInitialize(voln, 0);
		ArrayResize(cvolp, waveperiods + 1 + wpr);
		ArrayInitialize(cvolp, 0);
		ArrayResize(cvoln, waveperiods + 1 + wpr);
		ArrayInitialize(cvoln, 0);
				
		for (int x = waveperiods; x >= 0; x--) {
			for (int i = wpr + x; i > x; i--) {
				volful[x] += (double)iVolume(_Symbol, PERIOD_CURRENT, i);
				if (iClose(_Symbol, PERIOD_CURRENT, i) > iOpen(_Symbol, PERIOD_CURRENT, i)) volp[x] += (double)iVolume(_Symbol, PERIOD_CURRENT, i);
				if (iOpen(_Symbol, PERIOD_CURRENT, i) > iClose(_Symbol, PERIOD_CURRENT, i)) voln[x] += (double)iVolume(_Symbol, PERIOD_CURRENT, i);
			}
			cvolp[x] = volp[x] / volful[x];
			cvoln[x] = voln[x] / volful[x];
		}
		for (int x = waveperiods; x >= 0; x--) {
			dippe[x] = iHigh(_Symbol, PERIOD_CURRENT, x + 1) + atrix[x + 1] + cvolp[x] * atrix[x + 1];
			kippe[x] = iLow(_Symbol, PERIOD_CURRENT, x + 1) - atrix[x + 1] - cvoln[x] * atrix[x + 1];
		}
	}
	
	{ // YELLOW LINES
		double MA1[]; ArrayResize(MA1, rates_total + 1);
		ArrayInitialize(MA1, 0);
		double MA2[]; ArrayResize(MA2, rates_total + 1);
		ArrayInitialize(MA2, 0);
		double MA3[]; ArrayResize(MA3, rates_total + 1);
		ArrayInitialize(MA3, 0);
		double MA4[]; ArrayResize(MA4, rates_total + 1);
		ArrayInitialize(MA4, 0);

		//ArraySetAsSeries(MA1, true);
		//ArraySetAsSeries(MA3, true);
		//ArraySetAsSeries(MA2, true);
		//ArraySetAsSeries(MA4, true);

      ExponentialMAOnBuffer(rates_total, prev_calculated, 0, Sampling_Period, dippe, MA1);
      ExponentialMAOnBuffer(rates_total, prev_calculated, 0, Sampling_Period, kippe, MA3);
      
      ExponentialMAOnBuffer(rates_total, prev_calculated, 0, wpr, MA1, MA2);
      ExponentialMAOnBuffer(rates_total, prev_calculated, 0, wpr, MA3, MA4);
      
      
		//ArraySetAsSeries(MA1, true);
		//ArraySetAsSeries(MA3, true);
		//ArraySetAsSeries(MA2, true);
		//ArraySetAsSeries(MA4, true);
      
   /*
   	for (int i = waveperiods; i >= 0; i--) {
			MA1[i] = iMAOnArrayMQL4(dippe, 0, Sampling_Period, 0, MODE_EMA, i);
			MA3[i] = iMAOnArrayMQL4(kippe, 0, Sampling_Period, 0, MODE_EMA, i);
		}
   
		for (int i = waveperiods; i >= 0; i--) {
			MA2[i] = iMAOnArrayMQL4(MA1, 0, wpr, 0, MODE_EMA, i);
			MA4[i] = iMAOnArrayMQL4(MA3, 0, wpr, 0, MODE_EMA, i);
		}
   */
   
		double Lambda = 1.0 * Sampling_Period / (1.0 * wpr);
		double Alpha = Lambda * (Sampling_Period - 1) / (Sampling_Period - Lambda);
		
		ArraySetAsSeries(ma3, true); ArraySetAsSeries(ma4, true);
		for (int i = waveperiods; i >= 0; i--) {
			ma3[i] = (Alpha + 1) * MA1[i] - Alpha * MA2[i];
			ma4[i] = (Alpha + 1) * MA3[i] - Alpha * MA4[i];
		}
	}
	Print(ma3[0] + " " + ma4[0]);
		new_1m_check = false;
	}
	
	end = GetTickCount() - start;
	Print(end);
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+
//+iMAOnArray for Wavelet--------------------------------------------+
double iMAOnArrayMQL4(double &array[], int total, int period, int ma_shift, int ma_method, int shift){
   double buf[],arr[];
   if(total==0) total=ArraySize(array);
   if(total>0 && total<=period) return(0);
   if(shift>total-period-ma_shift) return(0);
   switch(ma_method)
     {
      case MODE_SMA :
        {
         total=ArrayCopy(arr,array,0,shift+ma_shift,period);
         if(ArrayResize(buf,total)<0) return(0);
         double sum=0;
         int    i,pos=total-1;
         for(i=1;i<period;i++,pos--)
            sum+=arr[pos];
         while(pos>=0)
           {
            sum+=arr[pos];
            buf[pos]=sum/period;
            sum-=arr[pos+period-1];
            pos--;
           }
         return(buf[0]);
        }
      case MODE_EMA :
        {
         if(ArrayResize(buf,total)<0) return(0);
         double per=2.0/(period+1);
         int    pos=total-2;
         while(pos>=0)
           {
            if(pos==total-2) buf[pos+1]=array[pos+1];
            buf[pos]=array[pos]*per+buf[pos+1]*(1-per);
            pos--;
           }
         return(buf[shift+ma_shift]);
        }
      case MODE_SMMA :
        {
         if(ArrayResize(buf,total)<0) return(0);
         double sum=0;
         int    i,k,pos;
         pos=total-period;
         while(pos>=0)
           {
            if(pos==total-period)
              {
               for(i=0,k=pos;i<period;i++,k++)
                 {
                  sum+=array[k];
                  buf[k]=0;
                 }
              }
            else sum=buf[pos+1]*(period-1)+array[pos];
            buf[pos]=sum/period;
            pos--;
           }
         return(buf[shift+ma_shift]);
        }
      case MODE_LWMA :
        {
         if(ArrayResize(buf,total)<0) return(0);
         double sum=0.0,lsum=0.0;
         double price;
         int    i,weight=0,pos=total-1;
         for(i=1;i<=period;i++,pos--)
           {
            price=array[pos];
            sum+=price*i;
            lsum+=price;
            weight+=i;
           }
         pos++;
         i=pos+period;
         while(pos>=0)
           {
            buf[pos]=sum/weight;
            if(pos==0) break;
            pos--;
            i--;
            price=array[pos];
            sum=sum-lsum+price*period;
            lsum-=array[i];
            lsum+=price;
           }
         return(buf[shift+ma_shift]);
        }
      default: return(0);
     }
   return(0);
}
//+------------------------------------------------------------------+