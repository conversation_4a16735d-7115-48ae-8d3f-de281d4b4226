//+------------------------------------------------------------------+
//|                                              RibbonWaveCombo.mq5 |
//|                                                        Sakis-Pit |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Sakis-Pit"
#property link      ""
#property version   "1.20"
#property indicator_chart_window

#property strict
#property indicator_buffers 41
#property indicator_plots 11

#define Name MQLInfoString(MQL_PROGRAM_NAME) + "U"

//PLOTS
//NewWaveMid
#property indicator_type1  DRAW_LINE
#property indicator_color1 clrLimeGreen
#property indicator_style1 STYLE_SOLID
#property indicator_width1 3
#property indicator_label1 "NWmid"
//NewWaveUpperHisto
#property indicator_type2  DRAW_HISTOGRAM2
#property indicator_color2 C'164,183,219'
#property indicator_style2 STYLE_SOLID
#property indicator_width2 1
#property indicator_label2 ""
//NewWaveLowerHisto
#property indicator_type3  DRAW_HISTOGRAM2
#property indicator_color3 C'255,111,131'
#property indicator_style3 STYLE_SOLID
#property indicator_width3 1
#property indicator_label3 ""
//NewWaveUpper
#property indicator_type4  DRAW_LINE
#property indicator_color4 clrSeaGreen
#property indicator_style4 STYLE_SOLID
#property indicator_width4 4
#property indicator_label4 "Upper"
//NewWaveLower
#property indicator_type5  DRAW_LINE
#property indicator_color5 clrSeaGreen
#property indicator_style5 STYLE_SOLID
#property indicator_width5 4
#property indicator_label5 "Lower"
//NewWaveRealMid
#property indicator_type6  DRAW_LINE
#property indicator_color6 clrGray
#property indicator_style6 STYLE_SOLID
#property indicator_width6 2
#property indicator_label6 "LWMA55"
//LWMA55
#property indicator_type7  DRAW_LINE
#property indicator_color7 clrMaroon
#property indicator_style7 STYLE_SOLID
#property indicator_width7 2
#property indicator_label7 "RealMid"
//NewWaveUpper-Consolidation
#property indicator_type8  DRAW_LINE
#property indicator_color8 clrBlack
#property indicator_style8 STYLE_SOLID
#property indicator_width8 4
#property indicator_label8 "UpperConsWave"
//NewWaveLower-Consolidation
#property indicator_type9  DRAW_LINE
#property indicator_color9 clrBlack
#property indicator_style9 STYLE_SOLID
#property indicator_width9 4
#property indicator_label9 "LowerConsWave"
//NewWaveLower-Consolidation
#property indicator_type10  DRAW_NONE
#property indicator_color10 clrPeru
#property indicator_style10 STYLE_SOLID
#property indicator_width10 1
#property indicator_label10 "d"
//NewWaveLower-Consolidation
#property indicator_type11  DRAW_NONE
#property indicator_color11 clrPeru
#property indicator_style11 STYLE_SOLID
#property indicator_width11 1
#property indicator_label11 "k"

//-----Arrays-----//
//NewWave
double ma8h[], ma8l[];
double ma13h[], ma13l[];
double ma21h[], ma21l[];
double ma34h[], ma34l[];
double ma45h[], ma45l[];
double ma54[], ma54a[], ma54b[];
double ma53[], ma52[];
double D1[], H4[];
//Wavelet
double dippe[], kippe[];
double ma3[], ma4[];
double ma36[], ma36l[], ma36h[], atrix[];
//RibbonWaveCombo
double ma35[], ma40[], ma45[], ma50[], ma55[], ma60[], ma65[], ma70[], ma75[];
double realmid[];
double upperw[], lowerw[];
double above[], below[];
double babove[], bbelow[];

//-----Handles-----//
//NewWave
double W1H, W1M, W1L, D1H, D1M, D1L, H1H, H1M, H1L;
int N8hh1, N8lh1, N13hh1, N13lh1, N21hh1, N21lh1, N34hh1, N34lh1;
int N8hh4, N8lh4, N13hh4, N13lh4, N21hh4, N21lh4, N34hh4, N34lh4;
int N8hd1, N8ld1, N13hd1, N13ld1, N21hd1, N21ld1, N34hd1, N34ld1;
int N8hw1, N8lw1, N13hw1, N13lw1, N21hw1, N21lw1, N34hw1, N34lw1;
//Wavelet
//RibbonWaveCombo
int m35, m40, m45, m50, m55, m60, m65, m70, m75;
int ma2, ma2l, ma02l, ma25;

//-----Other-----//
//NewWave
int m8h, m8l, m13h, m13l, m21h, m21l, m34h, m34l, m44h, m44l, m55h, m55l;
bool timerwait = false;
double point;
int PipAdjust, NrOfDigits;
//Wavelet
int waveperiods = 3000; //Bars to calculate for (0 = all)
int pr = 10; //Smoothing period
int Sampling_Period = 4 * pr; // Sampling period for 3.0 (must be at least 4* smoothing)
double Pip;
int ma36data, ma36hdata, ma36ldata, atrdays;
//RibbonWaveCombo

//-----Inputs-----//
//NewWave
input int period1 = 20; // 1st Period (NewWave)
input int period2 = 50; // 2nd Period (NewWave)
input int period3 = 100; // 3rd Period (NewWave)
input int period4 = 200; // 4th Period (NewWave)
input ENUM_MA_METHOD mamode = MODE_LWMA; // MA Mode (NewWave)
//Wavelet
//RibbonWaveCombo
input int count = 10; // Alert Arrows (RWC)

//+INIT FUNCTION-----------------------------------------------------+
int OnInit() {
	IndicatorSetString(INDICATOR_SHORTNAME, "RWC");
	IndicatorSetInteger(INDICATOR_DIGITS, _Digits);

	//NewWave
	SetIndexBuffer(0, ma54, INDICATOR_DATA);
	SetIndexBuffer(1, ma53, INDICATOR_DATA);
	SetIndexBuffer(2, ma54a, INDICATOR_DATA);
	SetIndexBuffer(3, ma54b, INDICATOR_DATA);
	SetIndexBuffer(4, ma52, INDICATOR_DATA);
	SetIndexBuffer(5, ma45h, INDICATOR_DATA);
	SetIndexBuffer(6, ma45l, INDICATOR_DATA);
	//RibbonWaveCombo
	SetIndexBuffer(7, ma55, INDICATOR_DATA);
	SetIndexBuffer(8, realmid, INDICATOR_DATA);
	SetIndexBuffer(9, upperw, INDICATOR_DATA);
	SetIndexBuffer(10, lowerw, INDICATOR_DATA);
	//Wavelet
	SetIndexBuffer(11, ma3, INDICATOR_DATA);
	SetIndexBuffer(12, ma4, INDICATOR_DATA);
	//Ribbon
	SetIndexBuffer(13, ma35, INDICATOR_CALCULATIONS);
	SetIndexBuffer(14, ma40, INDICATOR_CALCULATIONS);
	SetIndexBuffer(15, ma45, INDICATOR_CALCULATIONS);
	SetIndexBuffer(16, ma50, INDICATOR_CALCULATIONS);
	SetIndexBuffer(17, ma60, INDICATOR_CALCULATIONS);
	SetIndexBuffer(18, ma65, INDICATOR_CALCULATIONS);
	SetIndexBuffer(19, ma70, INDICATOR_CALCULATIONS);
	SetIndexBuffer(20, ma75, INDICATOR_CALCULATIONS);
	//Wavelet-Data
	SetIndexBuffer(21, dippe, INDICATOR_CALCULATIONS);
	SetIndexBuffer(22, kippe, INDICATOR_CALCULATIONS);
	SetIndexBuffer(23, ma36, INDICATOR_CALCULATIONS);
	SetIndexBuffer(24, ma36h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(25, ma36l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(26, atrix, INDICATOR_CALCULATIONS);
	//NewWave-Data
	SetIndexBuffer(27, ma8h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(28, ma8l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(29, ma13h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(30, ma13l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(31, ma21h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(32, ma21l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(33, ma34h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(34, ma34l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(35, D1, INDICATOR_CALCULATIONS);
	SetIndexBuffer(36, H4, INDICATOR_CALCULATIONS);
	//RibbonWaveCombo-data
	SetIndexBuffer(37, above, INDICATOR_DATA);
	SetIndexBuffer(38, below, INDICATOR_DATA);
	SetIndexBuffer(39, babove, INDICATOR_DATA);
	SetIndexBuffer(40, bbelow, INDICATOR_DATA);

	//-----NEWWAVE-----//
	//CURRENT
	m8h = iMA(_Symbol, PERIOD_CURRENT, period1, 0, mamode, PRICE_HIGH);
	m13h = iMA(_Symbol, PERIOD_CURRENT, period2, 0, mamode, PRICE_HIGH);
	m21h = iMA(_Symbol, PERIOD_CURRENT, period3, 0, mamode, PRICE_HIGH);
	m34h = iMA(_Symbol, PERIOD_CURRENT, period4, 0, mamode, PRICE_HIGH);
	m8l = iMA(_Symbol, PERIOD_CURRENT, period1, 0, mamode, PRICE_LOW);
	m13l = iMA(_Symbol, PERIOD_CURRENT, period2, 0, mamode, PRICE_LOW);
	m21l = iMA(_Symbol, PERIOD_CURRENT, period3, 0, mamode, PRICE_LOW);
	m34l = iMA(_Symbol, PERIOD_CURRENT, period4, 0, mamode, PRICE_LOW);
	//H1
	N8hh1 = iMA(_Symbol, PERIOD_H1, period1, 0, mamode, PRICE_HIGH);
	N8lh1 = iMA(_Symbol, PERIOD_H1, period1, 0, mamode, PRICE_LOW);
	N13hh1 = iMA(_Symbol, PERIOD_H1, period2, 0, mamode, PRICE_HIGH);
	N13lh1 = iMA(_Symbol, PERIOD_H1, period2, 0, mamode, PRICE_LOW);
	N21hh1 = iMA(_Symbol, PERIOD_H1, period3, 0, mamode, PRICE_HIGH);
	N21lh1 = iMA(_Symbol, PERIOD_H1, period3, 0, mamode, PRICE_LOW);
	N34hh1 = iMA(_Symbol, PERIOD_H1, period4, 0, mamode, PRICE_HIGH);
	N34lh1 = iMA(_Symbol, PERIOD_H1, period4, 0, mamode, PRICE_LOW);
	//H4
	N8hh4 = iMA(_Symbol, PERIOD_H4, period1, 0, mamode, PRICE_HIGH);
	N8lh4 = iMA(_Symbol, PERIOD_H4, period1, 0, mamode, PRICE_LOW);
	N13hh4 = iMA(_Symbol, PERIOD_H4, period2, 0, mamode, PRICE_HIGH);
	N13lh4 = iMA(_Symbol, PERIOD_H4, period2, 0, mamode, PRICE_LOW);
	N21hh4 = iMA(_Symbol, PERIOD_H4, period3, 0, mamode, PRICE_HIGH);
	N21lh4 = iMA(_Symbol, PERIOD_H4, period3, 0, mamode, PRICE_LOW);
	N34hh4 = iMA(_Symbol, PERIOD_H4, period4, 0, mamode, PRICE_HIGH);
	N34lh4 = iMA(_Symbol, PERIOD_H4, period4, 0, mamode, PRICE_LOW);
	//D1
	N8hd1 = iMA(_Symbol, PERIOD_D1, period1, 0, mamode, PRICE_HIGH);
	N8ld1 = iMA(_Symbol, PERIOD_D1, period1, 0, mamode, PRICE_LOW);
	N13hd1 = iMA(_Symbol, PERIOD_D1, period2, 0, mamode, PRICE_HIGH);
	N13ld1 = iMA(_Symbol, PERIOD_D1, period2, 0, mamode, PRICE_LOW);
	N21hd1 = iMA(_Symbol, PERIOD_D1, period3, 0, mamode, PRICE_HIGH);
	N21ld1 = iMA(_Symbol, PERIOD_D1, period3, 0, mamode, PRICE_LOW);
	N34hd1 = iMA(_Symbol, PERIOD_D1, period4, 0, mamode, PRICE_HIGH);
	N34ld1 = iMA(_Symbol, PERIOD_D1, period4, 0, mamode, PRICE_LOW);
	//W1
	N8hw1 = iMA(_Symbol, PERIOD_W1, period1, 0, mamode, PRICE_HIGH);
	N8lw1 = iMA(_Symbol, PERIOD_W1, period1, 0, mamode, PRICE_LOW);
	N13hw1 = iMA(_Symbol, PERIOD_W1, period2, 0, mamode, PRICE_HIGH);
	N13lw1 = iMA(_Symbol, PERIOD_W1, period2, 0, mamode, PRICE_LOW);
	N21hw1 = iMA(_Symbol, PERIOD_W1, period3, 0, mamode, PRICE_HIGH);
	N21lw1 = iMA(_Symbol, PERIOD_W1, period3, 0, mamode, PRICE_LOW);
	N34hw1 = iMA(_Symbol, PERIOD_W1, period4, 0, mamode, PRICE_HIGH);
	N34lw1 = iMA(_Symbol, PERIOD_W1, period4, 0, mamode, PRICE_LOW);

	//-----WAVELET-----//
	ma36data = iMA(_Symbol, PERIOD_CURRENT, pr, 0, MODE_EMA, PRICE_TYPICAL);
	ma36hdata = iMA(_Symbol, PERIOD_CURRENT, pr, 0, MODE_EMA, PRICE_HIGH);
	ma36ldata = iMA(_Symbol, PERIOD_CURRENT, pr, 0, MODE_EMA, PRICE_LOW);
	atrdays = iATR(_Symbol, PERIOD_CURRENT, pr);

	Pip = (_Point * MathPow(10, MathMod(_Digits, 2)));

	//-----RWC-----//
	m35 = iMA(_Symbol, PERIOD_CURRENT, 35, 0, MODE_LWMA, PRICE_CLOSE);
	m40 = iMA(_Symbol, PERIOD_CURRENT, 40, 0, MODE_LWMA, PRICE_CLOSE);
	m45 = iMA(_Symbol, PERIOD_CURRENT, 45, 0, MODE_LWMA, PRICE_CLOSE);
	m50 = iMA(_Symbol, PERIOD_CURRENT, 50, 0, MODE_LWMA, PRICE_CLOSE);
	m55 = iMA(_Symbol, PERIOD_CURRENT, 55, 0, MODE_LWMA, PRICE_CLOSE);
	m60 = iMA(_Symbol, PERIOD_CURRENT, 60, 0, MODE_LWMA, PRICE_CLOSE);
	m65 = iMA(_Symbol, PERIOD_CURRENT, 65, 0, MODE_LWMA, PRICE_CLOSE);
	m70 = iMA(_Symbol, PERIOD_CURRENT, 70, 0, MODE_LWMA, PRICE_CLOSE);
	m75 = iMA(_Symbol, PERIOD_CURRENT, 75, 0, MODE_LWMA, PRICE_CLOSE);
	ma2 = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_TYPICAL);
	ma2l = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_LWMA, PRICE_CLOSE);
	ma02l = iMA(_Symbol, PERIOD_CURRENT, 200, 0, MODE_LWMA, PRICE_CLOSE);
	ma25 = iMA(_Symbol, PERIOD_CURRENT, 250, 0, MODE_EMA, PRICE_CLOSE);

	EventSetMillisecondTimer(1);

	//-----NEWWAVE TABLE-----//
	string obname;
	obname = Name + " Dist1"; LabelMake(obname, 3, 105, 85, "W1", 9, clrNavy);
	obname = Name + " Dist2"; LabelMake(obname, 3, 80, 85, "D1", 9, clrDeepSkyBlue);
	obname = Name + " Dist3"; LabelMake(obname, 3, 55, 85, "H4", 9, clrGray);
	obname = Name + " Dist4"; LabelMake(obname, 3, 30, 85, "H1", 9, clrSeaGreen);

	int p = 15, ai = 4;
	string buls[4] = { "H", "UM", "LM", "L" };
	while (ai >= 1) {
		obname = Name + IntegerToString(ai) + " L"; LabelMake(obname, 3, 135, 85 + ai * p, buls[ai - 1], 9, clrBlack);
		obname = Name + IntegerToString(ai) + " W1"; LabelMake(obname, 3, 105, 85 + ai * p, " ", 9, clrBlack);
		obname = Name + IntegerToString(ai) + " D1"; LabelMake(obname, 3, 80, 85 + ai * p, " ", 9, clrBlack);
		obname = Name + IntegerToString(ai) + " H4"; LabelMake(obname, 3, 55, 85 + ai * p, " ", 9, clrBlack);
		obname = Name + IntegerToString(ai) + " H1"; LabelMake(obname, 3, 30, 85 + ai * p, " ", 9, clrBlack);
		ai--;
	}
	obname = Name + " BuyPos"; LabelMake(obname, 3, 135, 170, " ", 8, clrBlue);
	obname = Name + " SellPos"; LabelMake(obname, 3, 135, 180, " ", 8, clrRed);
	obname = Name + " AvgPos"; LabelMake(obname, 3, 135, 190, " ", 8, clrWhite);

	NrOfDigits = _Digits;
	if (NrOfDigits == 5 || NrOfDigits == 3) PipAdjust = 10;
	else
		if (NrOfDigits == 4 || NrOfDigits == 2) PipAdjust = 1;
	point = _Point * PipAdjust;

	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+ON TIMER----------------------------------------------------------+
void OnTimer() {
	EventKillTimer();
	timerwait = true;
}
//+------------------------------------------------------------------+

//+CALCULATE---------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	//Delay for MTF
	if (!timerwait) { return rates_total; }

	bool new_1s_check = false;
	static datetime start_1s_time = 0;
	datetime a;
	if (ChartPeriod() < 60) a = iTime(NULL, PERIOD_M1, 0); else a = iTime(NULL, PERIOD_M5, 0);
	if (start_1s_time < a)
	{
		new_1s_check = true;
		start_1s_time = a;
	}
	if (new_1s_check)
	{
		uint start = GetTickCount();
		int periods;
		if (Bars(_Symbol, _Period) <= 2001) periods = Bars(_Symbol, _Period) - 1; else periods = 2000;
		ArraySetAsSeries(ma35, true); ArraySetAsSeries(ma40, true);
		ArraySetAsSeries(ma45, true); ArraySetAsSeries(ma50, true);
		ArraySetAsSeries(ma55, true); ArraySetAsSeries(ma60, true);
		ArraySetAsSeries(ma65, true); ArraySetAsSeries(ma70, true);
		ArraySetAsSeries(ma75, true);
		if (CopyBuffer(m35, 0, 0, periods, ma35) <= 0)
			Print("Failed to copy 35MA.");
		if (CopyBuffer(m40, 0, 0, periods, ma40) <= 0)
			Print("Failed to copy 40MA.");
		if (CopyBuffer(m45, 0, 0, periods, ma45) <= 0)
			Print("Failed to copy 45MA.");
		if (CopyBuffer(m50, 0, 0, periods, ma50) <= 0)
			Print("Failed to copy 50MA.");
		if (CopyBuffer(m55, 0, 0, periods, ma55) <= 0)
			Print("Failed to copy 55MA.");
		if (CopyBuffer(m60, 0, 0, periods, ma60) <= 0)
			Print("Failed to copy 60MA.");
		if (CopyBuffer(m65, 0, 0, periods, ma65) <= 0)
			Print("Failed to copy 65MA.");
		if (CopyBuffer(m70, 0, 0, periods, ma70) <= 0)
			Print("Failed to copy 70MA.");
		if (CopyBuffer(m75, 0, 0, periods, ma75) <= 0)
			Print("Failed to copy 75MA.");
		uint end = GetTickCount() - start;
		Print(IntegerToString(end) + "ms" + " RibbonCopy");
		new_1s_check = false;
	}

	bool new_1m_check = false;
	static datetime start_1m_time = 0;
	if (start_1m_time < iTime(NULL, ChartPeriod(), 0))
	{
		new_1m_check = true;
		start_1m_time = iTime(NULL, ChartPeriod(), 0);
	}
	if (new_1m_check)
	{
		uint start1 = GetTickCount();
		Bingo(); //NewWave
		uint end1 = GetTickCount() - start1;
		Print(IntegerToString(end1) + "ms" + " NewWave");
		uint start2 = GetTickCount();
		bulak(); //Wavelet
		uint end2 = GetTickCount() - start2;
		Print(IntegerToString(end2) + "ms" + " Wavelet");
		uint start3 = GetTickCount();
		Ming(); //RWC
		uint end3 = GetTickCount() - start3;
		Print(IntegerToString(end3) + "ms" + " RWC");
		new_1m_check = false;
	}

	if (ChartPeriod() >= 60) {
		bool new_2m_check = false;
		static datetime start_2m_time = 0;
		if (start_2m_time < iTime(NULL, ChartPeriod(), 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_CURRENT, 0) + 25) && TimeCurrent() <= (iTime(NULL, PERIOD_CURRENT, 0) + 55)))
		{
			new_2m_check = true;
			start_2m_time = iTime(NULL, ChartPeriod(), 0);
		}
		if (new_2m_check)
		{
			uint start2 = GetTickCount();
			alerts();
			uint end2 = GetTickCount() - start2;
			Print(IntegerToString(end2) + "ms" + " Alerts");
			new_2m_check = false;
		}
	}

	bool new_4h_check = false;
	static datetime start_4h_time = 0;
	if (start_4h_time < iTime(NULL, PERIOD_H1, 0))
	{
		new_4h_check = true;
		start_4h_time = iTime(NULL, PERIOD_H1, 0);
	}
	if (new_4h_check)
	{
		uint start1 = GetTickCount();
		BingoH1();
		uint end1 = GetTickCount() - start1;
		Print(IntegerToString(end1) + "ms" + " NW H1");
		uint start2 = GetTickCount();
		BingoH();
		uint end2 = GetTickCount() - start2;
		Print(IntegerToString(end2) + "ms" + " NW H4");
		uint start3 = GetTickCount();
		BingoD();
		uint end3 = GetTickCount() - start3;
		Print(IntegerToString(end3) + "ms" + " NW D1");
		uint start4 = GetTickCount();
		BingoW();
		uint end4 = GetTickCount() - start4;
		Print(IntegerToString(end4) + "ms" + " NW W1");
		uint start5 = GetTickCount();
		marks();
		uint end5 = GetTickCount() - start5;
		Print(IntegerToString(end5) + "ms" + " NW Marks");
		new_4h_check = false;
	}
	return(rates_total);
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+NEWWAVE MAIN FUNCTION---------------------------------------------+
void Bingo() {
	int newperiods = Bars(_Symbol, PERIOD_CURRENT) - 1;

	ArraySetAsSeries(ma8h, true); ArraySetAsSeries(ma8l, true);
	ArraySetAsSeries(ma13h, true); ArraySetAsSeries(ma13l, true);
	ArraySetAsSeries(ma21h, true); ArraySetAsSeries(ma21l, true);
	ArraySetAsSeries(ma34h, true); ArraySetAsSeries(ma34l, true);
	if (CopyBuffer(m8h, 0, 0, newperiods, ma8h) <= 0 ||
		CopyBuffer(m8l, 0, 0, newperiods, ma8l) <= 0)
		Print("Failed to copy " + IntegerToString(period1) + "MA. ", GetLastError());
	if (CopyBuffer(m13h, 0, 0, newperiods, ma13h) <= 0 ||
		CopyBuffer(m13l, 0, 0, newperiods, ma13l) <= 0)
		Print("Failed to copy " + IntegerToString(period2) + "MA. ", GetLastError());
	if (CopyBuffer(m21h, 0, 0, newperiods, ma21h) <= 0 ||
		CopyBuffer(m21l, 0, 0, newperiods, ma21l) <= 0)
		Print("Failed to copy " + IntegerToString(period3) + "MA. ", GetLastError());
	if (CopyBuffer(m34h, 0, 0, newperiods, ma34h) <= 0 ||
		CopyBuffer(m34l, 0, 0, newperiods, ma34l) <= 0)
		Print("Failed to copy " + IntegerToString(period4) + "MA. ", GetLastError());

	double ma44h[], ma44l[];
	double ma55h[], ma55l[];
	ArrayResize(ma44h, newperiods + 1); ArrayResize(ma44l, newperiods + 1);
	ArrayResize(ma55h, newperiods + 1); ArrayResize(ma55l, newperiods + 1);
	ArrayInitialize(ma44h, 0); ArrayInitialize(ma44l, 0);
	ArrayInitialize(ma55h, 0); ArrayInitialize(ma55l, 0);
	ArraySetAsSeries(ma44h, true); ArraySetAsSeries(ma44l, true);
	ArraySetAsSeries(ma55h, true); ArraySetAsSeries(ma55l, true);

	int ax = newperiods;
	while (ax >= 0) {
		ma44h[ax] = MathMax(ma8h[ax], ma13h[ax]);
		ma55h[ax] = MathMax(ma21h[ax], ma34h[ax]);
		ma44l[ax] = MathMin(ma8l[ax], ma13l[ax]);
		ma55l[ax] = MathMin(ma21l[ax], ma34l[ax]);
		ax--;
	}

	ArraySetAsSeries(ma45h, true); ArraySetAsSeries(ma45l, true);

	int bx = newperiods;
	while (bx >= 0) {
		ma45h[bx] = MathMax(ma44h[bx], ma55h[bx]);
		ma45l[bx] = MathMin(ma44l[bx], ma55l[bx]);
		bx--;
	}

	ArraySetAsSeries(ma54, true); ArraySetAsSeries(ma54a, true); ArraySetAsSeries(ma54b, true); ArraySetAsSeries(ma53, true); ArraySetAsSeries(ma52, true);
	ArrayInitialize(ma54, 0); ArrayInitialize(ma54a, 0); ArrayInitialize(ma54b, 0); ArrayInitialize(ma53, 0); ArrayInitialize(ma52, 0);

	int cx = newperiods - 1;
	while (cx >= 0) {
		ma54[cx] = (ma45h[cx] + ma45l[cx]) / 2;
		ma54a[cx] = ma54[cx]; ma54b[cx] = ma54[cx];
		ma53[cx] = (ma54[cx] + iHigh(_Symbol, PERIOD_CURRENT, cx + 1)) / 2;
		ma52[cx] = (ma54[cx] + iLow(_Symbol, PERIOD_CURRENT, cx + 1)) / 2;
		if (ma53[cx] < ma54[cx]) ma53[cx] = ma54[cx];
		if (ma52[cx] > ma54[cx]) ma52[cx] = ma54[cx];
		cx--;
	}
}
//+------------------------------------------------------------------+

//+WAVELET MAIN FUNCTION---------------------------------------------+
void bulak() {
	int bars = Bars(_Symbol, PERIOD_CURRENT);
	if (bars < waveperiods + pr || waveperiods == 0) waveperiods = bars - 1 - pr;

	ArraySetAsSeries(ma36, true); ArraySetAsSeries(ma36h, true); ArraySetAsSeries(ma36l, true); ArraySetAsSeries(atrix, true);
	if (CopyBuffer(ma36data, 0, 0, waveperiods, ma36) <= 0) Print("Getting EMA10 failed! Error", GetLastError());
	if (CopyBuffer(ma36hdata, 0, 0, waveperiods, ma36h) <= 0) Print("Getting EMA10H failed! Error", GetLastError());
	if (CopyBuffer(ma36ldata, 0, 0, waveperiods, ma36l) <= 0) Print("Getting EMA10L failed! Error", GetLastError());
	if (CopyBuffer(atrdays, 0, 0, waveperiods, atrix) <= 0) Print("Getting ATR failed! Error", GetLastError());

	{ // MAIN CALCS
		double volful[];
		double volp[], voln[];
		double cvolp[], cvoln[];
		ArrayResize(volful, waveperiods + 1 + pr);
		ArrayInitialize(volful, 0);
		ArrayResize(volp, waveperiods + 1 + pr);
		ArrayInitialize(volp, 0);
		ArrayResize(voln, waveperiods + 1 + pr);
		ArrayInitialize(voln, 0);
		ArrayResize(cvolp, waveperiods + 1 + pr);
		ArrayInitialize(cvolp, 0);
		ArrayResize(cvoln, waveperiods + 1 + pr);
		ArrayInitialize(cvoln, 0);

		int dx = waveperiods;
		int bi;
		while (dx >= 0) {
			bi = pr + dx;
			while (bi > dx) {
				volful[dx] += (double)iVolume(_Symbol, PERIOD_CURRENT, bi);
				if (iClose(_Symbol, PERIOD_CURRENT, bi) > iOpen(_Symbol, PERIOD_CURRENT, bi)) volp[dx] += (double)iVolume(_Symbol, PERIOD_CURRENT, bi);
				if (iOpen(_Symbol, PERIOD_CURRENT, bi) > iClose(_Symbol, PERIOD_CURRENT, bi)) voln[dx] += (double)iVolume(_Symbol, PERIOD_CURRENT, bi);
				bi--;
			}
			cvolp[dx] = volp[dx] / volful[dx];
			cvoln[dx] = voln[dx] / volful[dx];
			dx--;
		}

		int ex = waveperiods;
		while (ex >= 0) {
			dippe[ex] = iHigh(_Symbol, PERIOD_CURRENT, ex + 1) + atrix[ex + 1] + cvolp[ex] * atrix[ex + 1];
			kippe[ex] = iLow(_Symbol, PERIOD_CURRENT, ex + 1) - atrix[ex + 1] - cvoln[ex] * atrix[ex + 1];
			ex--;
		}
	}

	{ // YELLOW LINES
		double MA1[]; ArrayResize(MA1, waveperiods + 1);
		ArrayInitialize(MA1, 0);
		double MA2[]; ArrayResize(MA2, waveperiods + 1);
		ArrayInitialize(MA2, 0);
		double MA3[]; ArrayResize(MA3, waveperiods + 1);
		ArrayInitialize(MA3, 0);
		double MA4[]; ArrayResize(MA4, waveperiods + 1);
		ArrayInitialize(MA4, 0);

		ArraySetAsSeries(MA1, true);
		ArraySetAsSeries(MA3, true);
		//ArraySetAsSeries(MA2, true);
		//ArraySetAsSeries(MA4, true);

		{ // MA1 & MA3
			double per = 2.0 / (1 + Sampling_Period);
			int pos = waveperiods - 2;
			while (pos >= 0) {
				if (pos == waveperiods - 2) { MA1[pos + 1] = dippe[pos + 1]; MA3[pos + 1] = kippe[pos + 1]; }
				MA1[pos] = dippe[pos] * per + MA1[pos + 1] * (1 - per);
				MA3[pos] = kippe[pos] * per + MA3[pos + 1] * (1 - per);
				pos--;
			}
		}
		{ //MA2 & MA4
			double per = 2.0 / (1 + pr);
			int pos = waveperiods - 2;
			while (pos >= 0) {
				if (pos == waveperiods - 2) { MA2[pos + 1] = MA1[pos + 1]; MA4[pos + 1] = MA3[pos + 1]; }
				MA2[pos] = MA1[pos] * per + MA2[pos + 1] * (1 - per);
				MA4[pos] = MA3[pos] * per + MA4[pos + 1] * (1 - per);
				pos--;
			}
		}

		double Lambda = 1.0 * Sampling_Period / (1.0 * pr);
		double Alpha = Lambda * (Sampling_Period - 1) / (Sampling_Period - Lambda);

		ArraySetAsSeries(ma3, true); ArraySetAsSeries(ma4, true);

		int ci = waveperiods;
		while (ci >= 0) {
			ma3[ci] = (Alpha + 1) * MA1[ci] - Alpha * MA2[ci];
			ma4[ci] = (Alpha + 1) * MA3[ci] - Alpha * MA4[ci];
			ci--;
		}
	}
	Print(DoubleToString(ma3[0], _Digits) + " " + DoubleToString(ma4[0], _Digits));

	{ // SHOW k - d PRICE
		string obname;
		obname = Name + "d"; ArrowPrice(obname, ma3[0], 0, clrPeru); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_BACK, false); ObjectSetString(0, obname, OBJPROP_TOOLTIP, "Upper Wavelet Price: " + DoubleToString(ma3[0], _Digits));
		obname = Name + "k"; ArrowPrice(obname, ma4[0], 0, clrPeru); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_BACK, false); ObjectSetString(0, obname, OBJPROP_TOOLTIP, "Lower Wavelet Price: " + DoubleToString(ma4[0], _Digits));
	}
}
//+------------------------------------------------------------------+

//+RIBBONWAVECOMBO MAIN FUNCTION-------------------------------------+
void Ming() {
	//int rwcperiods = Bars(_Symbol, PERIOD_CURRENT) - 1;
	int rwcperiods;
	if (Bars(_Symbol, _Period) <= 1001) rwcperiods = Bars(_Symbol, _Period) - 1; else rwcperiods = 1000;

	double ma20[];
	ArrayResize(ma20, rwcperiods + 1);
	ArrayInitialize(ma20, 0);
	ArraySetAsSeries(ma20, true);

	ArrayInitialize(realmid, 0);
	ArraySetAsSeries(realmid, true);

	double ma20l[], ma200l[];
	ArrayResize(ma20l, rwcperiods + 1);
	ArrayInitialize(ma20l, 0);
	ArrayResize(ma200l, rwcperiods + 1);
	ArrayInitialize(ma200l, 0);
	ArraySetAsSeries(ma20l, true); ArraySetAsSeries(ma200l, true);

	if (CopyBuffer(ma2, 0, 0, rwcperiods + 1, ma20) <= 0 ||
		CopyBuffer(ma2l, 0, 0, rwcperiods + 1, ma20l) <= 0 ||
		CopyBuffer(ma02l, 0, 0, rwcperiods + 1, ma200l) <= 0)
		Print("Failed to copy data for ema20 or realmid. ", GetLastError());

	int fx = rwcperiods;
	while (fx >= 0) {
		realmid[fx] = (ma20l[fx] + ma200l[fx]) / 2;
		fx--;
	}

	ArraySetAsSeries(above, true); ArraySetAsSeries(below, true);
	ArrayInitialize(above, 0); ArrayInitialize(below, 0);

	ArraySetAsSeries(babove, true); ArraySetAsSeries(bbelow, true);
	ArrayInitialize(babove, 0); ArrayInitialize(bbelow, 0);

	ObjectsDeleteAll(0, Name + "Arr", 0, OBJ_ARROW);
	ObjectsDeleteAll(0, Name + "XLINE", 0, OBJ_TREND);
	{//above/below/babove/bbelow
		int y = rwcperiods;
		while (y >= 0) {
			//for (int x = rwcperiods; x >= 0; x--) {
			if (ma75[y] > ma35[y] && ma3[y] < ma75[y]) above[y] = ma3[y]; else above[y] = EMPTY_VALUE;
			if (ma75[y] < ma35[y] && ma4[y] > ma75[y]) below[y] = ma4[y]; else below[y] = EMPTY_VALUE;

			if (realmid[y] > ma54[y]) bbelow[y] = ma4[y]; else bbelow[y] = EMPTY_VALUE;
			if (realmid[y] < ma54[y]) babove[y] = ma3[y]; else babove[y] = EMPTY_VALUE;
			y--;
		}
	}

	{//Dots & Lines
		string obname;
		int z = rwcperiods - 1;
		while (z >= 0) {
			//for (int x = rwcperiods; x >= 0; x--) {
			if (babove[z] != EMPTY_VALUE && above[z] == EMPTY_VALUE) { obname = Name + "ArrWD" + IntegerToString(z); burnarr(obname, babove[z], z, 108, clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
			if (bbelow[z] != EMPTY_VALUE && below[z] == EMPTY_VALUE) { obname = Name + "ArrWU" + IntegerToString(z); burnarr(obname, bbelow[z], z, 108, clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
			if (babove[z] != EMPTY_VALUE && above[z] != EMPTY_VALUE) { obname = Name + "ArrD" + IntegerToString(z); burnarr(obname, above[z], z, 108, clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
			if (bbelow[z] != EMPTY_VALUE && below[z] != EMPTY_VALUE) { obname = Name + "ArrU" + IntegerToString(z); burnarr(obname, below[z], z, 108, clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
			if (above[z] != EMPTY_VALUE) { obname = Name + "ArrDD" + IntegerToString(z); burnarr(obname, above[z], z, 108, clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
			if (below[z] != EMPTY_VALUE) { obname = Name + "ArrUU" + IntegerToString(z); burnarr(obname, below[z], z, 108, clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
			if (above[z] != EMPTY_VALUE && bbelow[z] != EMPTY_VALUE) { obname = Name + "ArrBU" + IntegerToString(z); burnarr(obname, bbelow[z], z, 108, clrBlack); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
			if (below[z] != EMPTY_VALUE && babove[z] != EMPTY_VALUE) { obname = Name + "ArrBD" + IntegerToString(z); burnarr(obname, babove[z], z, 108, clrBlack); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
			if (babove[z + 1] != EMPTY_VALUE && bbelow[z] != EMPTY_VALUE) { obname = Name + "XLINE" + IntegerToString(z); objtrend(obname, babove[z + 1], bbelow[z], z + 1, z, clrMagenta, "Xline " + IntegerToString(z)); }
			if (bbelow[z + 1] != EMPTY_VALUE && babove[z] != EMPTY_VALUE) { obname = Name + "XLINE" + IntegerToString(z); objtrend(obname, bbelow[z + 1], babove[z], z + 1, z, clrLime, "Xline " + IntegerToString(z)); }
			z--;
		}
		Print("Finished");
	}

	bool doogie[], boogie[];
	ArrayResize(doogie, rwcperiods + 1); ArrayResize(boogie, rwcperiods + 1);

	{//boogie/doogie reversals
		int y = rwcperiods - 3;
		while (y >= 0) {
			if (below[y] == EMPTY_VALUE && bbelow[y] != EMPTY_VALUE) {
				int z = 3;
				while (z >= 1) {
					if (below[y + z] != EMPTY_VALUE) boogie[y] = true;
					//Print("Here1?");
					z--;
				}
			}
			if (above[y] == EMPTY_VALUE && babove[y] != EMPTY_VALUE) {
				int x = 3;
				while (x >= 1) {
					if (above[y + x] != EMPTY_VALUE) doogie[y] = true;
					//Print("Here2?");
					x--;
				}
			}
			y--;
		}
	}

	double wavepp[]; ArrayResize(wavepp, rwcperiods + 1); ArrayInitialize(wavepp, 0);
	double meanwave[]; ArrayResize(meanwave, rwcperiods + 1); ArrayInitialize(meanwave, 0);
	ArraySetAsSeries(wavepp, true); ArraySetAsSeries(meanwave, true);

	int gx = rwcperiods - 60;
	int di;
	while (gx >= 0) {
		di = gx + 60;
		while (di > gx) {
			wavepp[gx] += ma45h[di] - ma45l[di];
			di--;
		}
		gx--;
	}

	int hx = rwcperiods - 60;
	while (hx >= 0) {
		meanwave[hx] = wavepp[hx] / 60;
		hx--;
	}

	ArrayInitialize(upperw, EMPTY_VALUE); ArrayInitialize(lowerw, EMPTY_VALUE);
	ArraySetAsSeries(upperw, true); ArraySetAsSeries(lowerw, true);

	int ox = rwcperiods - 60;
	int fi;
	while (ox >= 0) {
		if ((ma45h[ox] - ma45l[ox]) < 0.66 * meanwave[ox]) {
			fi = ox + 60;
			while (fi > ox) {
				upperw[ox] = ma45h[ox] + 30 * _Point;
				lowerw[ox] = ma45l[ox] - 30 * _Point;
				fi--;
			}
		}
		ox--;
	}

	double ma250[];
	ArrayResize(ma250, rwcperiods + 1);
	ArrayInitialize(ma250, 0);
	ArraySetAsSeries(ma250, true);
	if (CopyBuffer(ma25, 0, 0, rwcperiods, ma250) <= 0)
		Print("Copying EMA250 failed. ", GetLastError());

	string obname;
	int x = rwcperiods - 50;
	while (x >= 1) {
		//Main Arrows for changed direction - Red / Blue
		if (below[x] != EMPTY_VALUE && babove[x] != EMPTY_VALUE && ObjectFind(0, Name + "XLINE" + IntegerToString(x)) == 0) { obname = Name + "ArrDW" + IntegerToString(x); burnarr(obname, iHigh(_Symbol, PERIOD_CURRENT, x) + 20 * _Point, x, 238, clrRed); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (above[x] != EMPTY_VALUE && bbelow[x] != EMPTY_VALUE && ObjectFind(0, Name + "XLINE" + IntegerToString(x)) == 0) { obname = Name + "ArrUW" + IntegerToString(x); burnarr(obname, iLow(_Symbol, PERIOD_CURRENT, x) - 20 * _Point, x, 236, clrBlue); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }

		//Change Dots to Green for retracements and re-enter - Retrace to LWMA55 or switch from red / blue dots to white
		if (boogie[x] == true && iLow(_Symbol, PERIOD_CURRENT, x) > ma54[x]) { obname = Name + "ArrUNew" + IntegerToString(x); burnarr(obname, ma4[x], x, 108, clrLimeGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); ObjectDelete(0, Name + "ArrWU" + IntegerToString(x)); }
		if (below[x] != EMPTY_VALUE && iLow(_Symbol, PERIOD_CURRENT, x) < ma55[x] && iLow(_Symbol, PERIOD_CURRENT, x) > ma54[x]) { obname = Name + "ArrUByMA" + IntegerToString(x); burnarr(obname, ma4[x], x, 108, clrLimeGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (doogie[x] == true && iHigh(_Symbol, PERIOD_CURRENT, x) < ma54[x]) { obname = Name + "ArrDNew" + IntegerToString(x); burnarr(obname, ma3[x], x, 108, clrLimeGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); ObjectDelete(0, Name + "ArrWD" + IntegerToString(x)); }
		if (above[x] != EMPTY_VALUE && iHigh(_Symbol, PERIOD_CURRENT, x) > ma55[x] && iHigh(_Symbol, PERIOD_CURRENT, x) < ma54[x]) { obname = Name + "ArrDByMA" + IntegerToString(x); burnarr(obname, ma3[x], x, 108, clrLimeGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }

		//Arrows to point continuation of strength after hard retrace - Low/High breaching red/blue dot (opposing wavelet)
		if (above[x] != EMPTY_VALUE && iHigh(_Symbol, PERIOD_CURRENT, x) > above[x]) { obname = Name + "ArrClU" + IntegerToString(x); burnarr(obname, ma3[x] + 20 * _Point, x, 234, clrMaroon); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (below[x] != EMPTY_VALUE && iLow(_Symbol, PERIOD_CURRENT, x) < below[x]) { obname = Name + "ArrClD" + IntegerToString(x); burnarr(obname, ma4[x] - 20 * _Point, x, 233, clrNavy); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }

		//Long term dots (Gold) - when high new wave crosses below ema250 and low new wave crosses above ema250
		if (ma45l[x + 1] > ma250[x + 1] && ma45l[x] < ma250[x]) { obname = Name + "ArrDLong" + IntegerToString(x); burnarr(obname, iHigh(_Symbol, PERIOD_CURRENT, x) + 20 * _Point, x, 174, clrGold); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (ma45h[x + 1] < ma250[x + 1] && ma45h[x] > ma250[x]) { obname = Name + "ArrULong" + IntegerToString(x); burnarr(obname, iLow(_Symbol, PERIOD_CURRENT, x) - 20 * _Point, x, 174, clrGold); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		x--;
	}
}
//+------------------------------------------------------------------+

//+ALERTS------------------------------------------------------------+
void alerts() {
	int redarr[], reddot[], bluarr[], bludot[];
	ArrayResize(redarr, count + 1); ArrayResize(reddot, count + 1); ArrayResize(bluarr, count + 1); ArrayResize(bludot, count + 1);
	ArrayInitialize(redarr, 0); ArrayInitialize(reddot, 0); ArrayInitialize(bluarr, 0); ArrayInitialize(bludot, 0);

	string Dr1 = "", Hr4 = "";
	if (D1[0] == 1) Dr1 = "UP"; else Dr1 = "DN";
	if (H4[0] == 1) Hr4 = "UP"; else Hr4 = "DN";

	int px = 1;
	while (px <= count) {
		if (ObjectFind(0, Name + "ArrDW" + IntegerToString(px)) == 0) redarr[px] = 1;
		if (ObjectFind(0, Name + "ArrD" + IntegerToString(px)) == 0) reddot[px] = 1;
		if (ObjectFind(0, Name + "ArrUW" + IntegerToString(px)) == 0) bluarr[px] = 1;
		if (ObjectFind(0, Name + "ArrU" + IntegerToString(px)) == 0) bludot[px] = 1;
		px++;
	}

	int qx = 1;
	while (qx <= count) {
		if (redarr[qx] == 1 && ArrayMaximum(reddot) == 0) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " Red Arrow found " + IntegerToString(qx + 1) + " candles away - D1: " + Dr1 + " H4: " + Hr4);
		if (redarr[qx] == 1 && ArrayMaximum(reddot) == 1) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " Red Arrow found " + IntegerToString(qx + 1) + " candles away + RED DOT - D1: " + Dr1 + " H4: " + Hr4);
		if (bluarr[qx] == 1 && ArrayMaximum(bludot) == 0) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " Blue Arrow found " + IntegerToString(qx + 1) + " candles away - D1: " + Dr1 + " H4: " + Hr4);
		if (bluarr[qx] == 1 && ArrayMaximum(bludot) == 1) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " Blue Arrow found " + IntegerToString(qx + 1) + " candles away + BLUE DOT - D1: " + Dr1 + " H4: " + Hr4);
		qx++;
	}

	if (reddot[1] == 1) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " RED DOT previous candle - D1: " + Dr1 + " H4: " + Hr4);
	if (bludot[1] == 1) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " BLUE DOT previous candle - D1: " + Dr1 + " H4: " + Hr4);
	if (ObjectFind(0, Name + "ArrDNew" + IntegerToString(1)) == 0 || ObjectFind(0, Name + "ArrDByMA" + IntegerToString(1)) == 0) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " RETRACE DN DOT printed - D1: " + Dr1 + " H4: " + Hr4);
	if (ObjectFind(0, Name + "ArrUNew" + IntegerToString(1)) == 0 || ObjectFind(0, Name + "ArrUByMA" + IntegerToString(1)) == 0) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " RETRACE UP DOT printed - D1: " + Dr1 + " H4: " + Hr4);
}
//+------------------------------------------------------------------+

//+CREATE ARROWS-----------------------------------------------------+
void burnarr(string name, double p, int t, int arrow, color col) {
	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT);   // number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t]);
	ObjectSetDouble(0, name, OBJPROP_PRICE, p);
	ObjectSetInteger(0, name, OBJPROP_ARROWCODE, arrow);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 0);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION-----------------------------------------------------+
void BingoH1() {
	double maN8h[1], maN8l[1], maN13h[1], maN13l[1], maN21h[1], maN21l[1], maN34h[1], maN34l[1];

	ArrayInitialize(maN8h, 0); ArrayInitialize(maN8l, 0); ArrayInitialize(maN13h, 0); ArrayInitialize(maN13l, 0); ArrayInitialize(maN21h, 0); ArrayInitialize(maN21l, 0); ArrayInitialize(maN34h, 0); ArrayInitialize(maN34l, 0);

	if (CopyBuffer(N8hh1, 0, 0, 1, maN8h) <= 0 ||
		CopyBuffer(N8lh1, 0, 0, 1, maN8l) <= 0)
		Print("Failed to copy " + IntegerToString(period1) + "MA H1. ", GetLastError());
	if (CopyBuffer(N13hh1, 0, 0, 1, maN13h) <= 0 ||
		CopyBuffer(N13lh1, 0, 0, 1, maN13l) <= 0)
		Print("Failed to copy " + IntegerToString(period2) + "MA H1. ", GetLastError());
	if (CopyBuffer(N21hh1, 0, 0, 1, maN21h) <= 0 ||
		CopyBuffer(N21lh1, 0, 0, 1, maN21l) <= 0)
		Print("Failed to copy " + IntegerToString(period3) + "MA H1. ", GetLastError());
	if (CopyBuffer(N34hh1, 0, 0, 1, maN34h) <= 0 ||
		CopyBuffer(N34lh1, 0, 0, 1, maN34l) <= 0)
		Print("Failed to copy " + IntegerToString(period4) + "MA H1. ", GetLastError());

	double maN44h, maN55h, maN44l, maN55l;

	maN44h = MathMax(maN8h[0], maN13h[0]);
	maN55h = MathMax(maN21h[0], maN34h[0]);
	maN44l = MathMin(maN8l[0], maN13l[0]);
	maN55l = MathMin(maN21l[0], maN34l[0]);

	double maN45h, maN45l;

	maN45h = MathMax(maN44h, maN55h);
	maN45l = MathMin(maN44l, maN55l);

	double maN54, maN53, maN52;

	maN54 = (maN45h + maN45l) / 2;
	maN53 = (maN54 + iHigh(_Symbol, PERIOD_H1, 1)) / 2;
	maN52 = (maN54 + iLow(_Symbol, PERIOD_H1, 1)) / 2;
	if (maN53 < maN54) maN53 = maN54;
	if (maN52 > maN54) maN52 = maN54;

	if ((iClose(_Symbol, PERIOD_H1, 0) > maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(1) + " H1", CharToString(174), 12, "Wingdings", clrBlue); else ObjectSetTextMQL4(Name + IntegerToString(1) + " H1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H1, 0) > maN54) && (iClose(_Symbol, PERIOD_H1, 0) < maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(2) + " H1", CharToString(233), 12, "Wingdings", clrSteelBlue); else ObjectSetTextMQL4(Name + IntegerToString(2) + " H1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H1, 0) < maN54) && (iClose(_Symbol, PERIOD_H1, 0) > maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(3) + " H1", CharToString(234), 12, "Wingdings", clrIndianRed); else ObjectSetTextMQL4(Name + IntegerToString(3) + " H1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H1, 0) < maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(4) + " H1", CharToString(174), 12, "Wingdings", clrRed); else ObjectSetTextMQL4(Name + IntegerToString(4) + " H1", " ", 9, "Arial", clrBlack);

	int rx = 1;
	while (rx <= 4) {
		ObjectSetString(0, Name + IntegerToString(rx) + " H1", OBJPROP_TOOLTIP, "H: " + DoubleToString(maN45h, _Digits) + " / M: " + DoubleToString(maN54, _Digits) + " / L: " + DoubleToString(maN45l, _Digits));
		rx++;
	}
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION-----------------------------------------------------+
void BingoH() {
	double maN8h[1], maN8l[1], maN13h[1], maN13l[1], maN21h[1], maN21l[1], maN34h[1], maN34l[1];

	ArrayInitialize(maN8h, 0); ArrayInitialize(maN8l, 0); ArrayInitialize(maN13h, 0); ArrayInitialize(maN13l, 0); ArrayInitialize(maN21h, 0); ArrayInitialize(maN21l, 0); ArrayInitialize(maN34h, 0); ArrayInitialize(maN34l, 0);

	if (CopyBuffer(N8hh4, 0, 0, 1, maN8h) <= 0 ||
		CopyBuffer(N8lh4, 0, 0, 1, maN8l) <= 0)
		Print("Failed to copy " + IntegerToString(period1) + "MA H4. ", GetLastError());
	if (CopyBuffer(N13hh4, 0, 0, 1, maN13h) <= 0 ||
		CopyBuffer(N13lh4, 0, 0, 1, maN13l) <= 0)
		Print("Failed to copy " + IntegerToString(period2) + "MA H4. ", GetLastError());
	if (CopyBuffer(N21hh4, 0, 0, 1, maN21h) <= 0 ||
		CopyBuffer(N21lh4, 0, 0, 1, maN21l) <= 0)
		Print("Failed to copy " + IntegerToString(period3) + "MA H4. ", GetLastError());
	if (CopyBuffer(N34hh4, 0, 0, 1, maN34h) <= 0 ||
		CopyBuffer(N34lh4, 0, 0, 1, maN34l) <= 0)
		Print("Failed to copy " + IntegerToString(period4) + "MA H4. ", GetLastError());

	double maN44h, maN55h, maN44l, maN55l;

	maN44h = MathMax(maN8h[0], maN13h[0]);
	maN55h = MathMax(maN21h[0], maN34h[0]);
	maN44l = MathMin(maN8l[0], maN13l[0]);
	maN55l = MathMin(maN21l[0], maN34l[0]);

	double maN45h, maN45l;

	maN45h = MathMax(maN44h, maN55h);
	maN45l = MathMin(maN44l, maN55l);

	double maN54, maN53, maN52;

	maN54 = (maN45h + maN45l) / 2;
	maN53 = (maN54 + iHigh(_Symbol, PERIOD_H4, 1)) / 2;
	maN52 = (maN54 + iLow(_Symbol, PERIOD_H4, 1)) / 2;
	if (maN53 < maN54) maN53 = maN54;
	if (maN52 > maN54) maN52 = maN54;

	if ((iClose(_Symbol, PERIOD_H4, 0) > maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(1) + " H4", CharToString(174), 12, "Wingdings", clrBlue); else ObjectSetTextMQL4(Name + IntegerToString(1) + " H4", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H4, 0) > maN54) && (iClose(_Symbol, PERIOD_H4, 0) < maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(2) + " H4", CharToString(233), 12, "Wingdings", clrSteelBlue); else ObjectSetTextMQL4(Name + IntegerToString(2) + " H4", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H4, 0) < maN54) && (iClose(_Symbol, PERIOD_H4, 0) > maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(3) + " H4", CharToString(234), 12, "Wingdings", clrIndianRed); else ObjectSetTextMQL4(Name + IntegerToString(3) + " H4", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H4, 0) < maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(4) + " H4", CharToString(174), 12, "Wingdings", clrRed); else ObjectSetTextMQL4(Name + IntegerToString(4) + " H4", " ", 9, "Arial", clrBlack);

	if (iClose(_Symbol, PERIOD_H4, 0) > maN54) H4[0] = 1; else H4[0] = 0;

	int sx = 1;
	while (sx <= 4) {
		ObjectSetString(0, Name + IntegerToString(sx) + " H4", OBJPROP_TOOLTIP, "H: " + DoubleToString(maN45h, _Digits) + " / M: " + DoubleToString(maN54, _Digits) + " / L: " + DoubleToString(maN45l, _Digits));
		sx++;
	}

	H1H = maN45h; H1M = maN54; H1L = maN45l;
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION-----------------------------------------------------+
void BingoD() {
	double maN8h[1], maN8l[1], maN13h[1], maN13l[1], maN21h[1], maN21l[1], maN34h[1], maN34l[1];

	ArrayInitialize(maN8h, 0); ArrayInitialize(maN8l, 0); ArrayInitialize(maN13h, 0); ArrayInitialize(maN13l, 0); ArrayInitialize(maN21h, 0); ArrayInitialize(maN21l, 0); ArrayInitialize(maN34h, 0); ArrayInitialize(maN34l, 0);

	if (CopyBuffer(N8hd1, 0, 0, 1, maN8h) <= 0 ||
		CopyBuffer(N8ld1, 0, 0, 1, maN8l) <= 0)
		Print("Failed to copy " + IntegerToString(period1) + "MA D1. ", GetLastError());
	if (CopyBuffer(N13hd1, 0, 0, 1, maN13h) <= 0 ||
		CopyBuffer(N13ld1, 0, 0, 1, maN13l) <= 0)
		Print("Failed to copy " + IntegerToString(period2) + "MA D1. ", GetLastError());
	if (CopyBuffer(N21hd1, 0, 0, 1, maN21h) <= 0 ||
		CopyBuffer(N21ld1, 0, 0, 1, maN21l) <= 0)
		Print("Failed to copy " + IntegerToString(period3) + "MA D1. ", GetLastError());
	if (CopyBuffer(N34hd1, 0, 0, 1, maN34h) <= 0 ||
		CopyBuffer(N34ld1, 0, 0, 1, maN34l) <= 0)
		Print("Failed to copy " + IntegerToString(period4) + "MA D1. ", GetLastError());

	double maN44h, maN55h, maN44l, maN55l;

	maN44h = MathMax(maN8h[0], maN13h[0]);
	maN55h = MathMax(maN21h[0], maN34h[0]);
	maN44l = MathMin(maN8l[0], maN13l[0]);
	maN55l = MathMin(maN21l[0], maN34l[0]);

	double maN45h, maN45l;

	maN45h = MathMax(maN44h, maN55h);
	maN45l = MathMin(maN44l, maN55l);

	double maN54, maN53, maN52;

	maN54 = (maN45h + maN45l) / 2;
	maN53 = (maN54 + iHigh(_Symbol, PERIOD_D1, 1)) / 2;
	maN52 = (maN54 + iLow(_Symbol, PERIOD_D1, 1)) / 2;
	if (maN53 < maN54) maN53 = maN54;
	if (maN52 > maN54) maN52 = maN54;

	if ((iClose(_Symbol, PERIOD_D1, 0) > maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(1) + " D1", CharToString(174), 12, "Wingdings", clrBlue); else ObjectSetTextMQL4(Name + IntegerToString(1) + " D1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_D1, 0) > maN54) && (iClose(_Symbol, PERIOD_D1, 0) < maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(2) + " D1", CharToString(233), 12, "Wingdings", clrSteelBlue); else ObjectSetTextMQL4(Name + IntegerToString(2) + " D1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_D1, 0) < maN54) && (iClose(_Symbol, PERIOD_D1, 0) > maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(3) + " D1", CharToString(234), 12, "Wingdings", clrIndianRed); else ObjectSetTextMQL4(Name + IntegerToString(3) + " D1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_D1, 0) < maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(4) + " D1", CharToString(174), 12, "Wingdings", clrRed); else ObjectSetTextMQL4(Name + IntegerToString(4) + " D1", " ", 9, "Arial", clrBlack);

	if (iClose(_Symbol, PERIOD_D1, 0) > maN54) D1[0] = 1; else D1[0] = 0;

	int tx = 1;
	while (tx <= 4) {
		ObjectSetString(0, Name + IntegerToString(tx) + " D1", OBJPROP_TOOLTIP, "H: " + DoubleToString(maN45h, _Digits) + " / M: " + DoubleToString(maN54, _Digits) + " / L: " + DoubleToString(maN45l, _Digits));
		tx++;
	}

	D1H = maN45h; D1M = maN54; D1L = maN45l;
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION-----------------------------------------------------+
void BingoW() {
	double maN8h[1], maN8l[1], maN13h[1], maN13l[1], maN21h[1], maN21l[1], maN34h[1], maN34l[1];

	ArrayInitialize(maN8h, 0); ArrayInitialize(maN8l, 0); ArrayInitialize(maN13h, 0); ArrayInitialize(maN13l, 0); ArrayInitialize(maN21h, 0); ArrayInitialize(maN21l, 0); ArrayInitialize(maN34h, 0); ArrayInitialize(maN34l, 0);

	if (CopyBuffer(N8hw1, 0, 0, 1, maN8h) <= 0 ||
		CopyBuffer(N8lw1, 0, 0, 1, maN8l) <= 0)
		Print("Failed to copy " + IntegerToString(period1) + "MA W1. ", GetLastError());
	if (CopyBuffer(N13hw1, 0, 0, 1, maN13h) <= 0 ||
		CopyBuffer(N13lw1, 0, 0, 1, maN13l) <= 0)
		Print("Failed to copy " + IntegerToString(period2) + "MA W1. ", GetLastError());
	if (CopyBuffer(N21hw1, 0, 0, 1, maN21h) <= 0 ||
		CopyBuffer(N21lw1, 0, 0, 1, maN21l) <= 0)
		Print("Failed to copy " + IntegerToString(period3) + "MA W1. ", GetLastError());
	if (CopyBuffer(N34hw1, 0, 0, 1, maN34h) <= 0 ||
		CopyBuffer(N34lw1, 0, 0, 1, maN34l) <= 0)
		Print("Failed to copy " + IntegerToString(period4) + "MA W1. ", GetLastError());

	double maN44h, maN55h, maN44l, maN55l;

	maN44h = MathMax(maN8h[0], maN13h[0]);
	maN55h = MathMax(maN21h[0], maN34h[0]);
	maN44l = MathMin(maN8l[0], maN13l[0]);
	maN55l = MathMin(maN21l[0], maN34l[0]);

	double maN45h, maN45l;

	maN45h = MathMax(maN44h, maN55h);
	maN45l = MathMin(maN44l, maN55l);

	double maN54, maN53, maN52;

	maN54 = (maN45h + maN45l) / 2;
	maN53 = (maN54 + iHigh(_Symbol, PERIOD_W1, 1)) / 2;
	maN52 = (maN54 + iLow(_Symbol, PERIOD_W1, 1)) / 2;
	if (maN53 < maN54) maN53 = maN54;
	if (maN52 > maN54) maN52 = maN54;

	if ((iClose(_Symbol, PERIOD_W1, 0) > maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(1) + " W1", CharToString(174), 12, "Wingdings", clrBlue); else ObjectSetTextMQL4(Name + IntegerToString(1) + " W1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_W1, 0) > maN54) && (iClose(_Symbol, PERIOD_W1, 0) < maN45h))
		ObjectSetTextMQL4(Name + IntegerToString(2) + " W1", CharToString(233), 12, "Wingdings", clrSteelBlue); else ObjectSetTextMQL4(Name + IntegerToString(2) + " W1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_W1, 0) < maN54) && (iClose(_Symbol, PERIOD_W1, 0) > maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(3) + " W1", CharToString(234), 12, "Wingdings", clrIndianRed); else ObjectSetTextMQL4(Name + IntegerToString(3) + " W1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_W1, 0) < maN45l))
		ObjectSetTextMQL4(Name + IntegerToString(4) + " W1", CharToString(174), 12, "Wingdings", clrRed); else ObjectSetTextMQL4(Name + IntegerToString(4) + " W1", " ", 9, "Arial", clrBlack);

	int ux = 1;
	while (ux <= 4) {
		ObjectSetString(0, Name + IntegerToString(ux) + " W1", OBJPROP_TOOLTIP, "H: " + DoubleToString(maN45h, _Digits) + " / M: " + DoubleToString(maN54, _Digits) + " / L: " + DoubleToString(maN45l, _Digits));
		ux++;
	}

	W1H = maN45h; W1M = maN54; W1L = maN45l;
}
//+------------------------------------------------------------------+

//+MARKS ON CHART----------------------------------------------------+
void marks() {
	string obname;
	//Weekly
	obname = Name + "ArrW1Up";
	ArrowPrice(obname, W1H, 5 * Period(), clrNavy);
	//if(Bid < W1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "W1 High Wave " + DoubleToString(W1H, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 3);
	obname = Name + "ArrW1Mid";
	ArrowPrice(obname, W1M, 5 * Period(), clrNavy);
	//if(Bid < W1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "W1 Mid Wave " + DoubleToString(W1M, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 4);
	obname = Name + "ArrW1Dn";
	ArrowPrice(obname, W1L, 5 * Period(), clrNavy);
	//if(Bid < W1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "W1 Low Wave " + DoubleToString(W1L, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 3);

	//Daily
	obname = Name + "ArrD1Up";
	ArrowPrice(obname, D1H, 5 * Period(), clrDeepSkyBlue);
	//if(Bid < D1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "D1 High Wave " + DoubleToString(D1H, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
	obname = Name + "ArrD1Mid";
	ArrowPrice(obname, D1M, 5 * Period(), clrDeepSkyBlue);
	//if(Bid < D1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "D1 Mid Wave " + DoubleToString(D1M, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 3);
	obname = Name + "ArrD1Dn";
	ArrowPrice(obname, D1L, 5 * Period(), clrDeepSkyBlue);
	//if(Bid < D1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "D1 Low Wave " + DoubleToString(D1L, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);

	//H4
	obname = Name + "ArrH1Up";
	ArrowPrice(obname, H1H, 5 * Period(), clrGray);
	//if(Bid < H1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "H4 High Wave " + DoubleToString(H1H, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
	obname = Name + "ArrH1Mid";
	ArrowPrice(obname, H1M, 5 * Period(), clrGray);
	//if(Bid < H1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "H4 Mid Wave " + DoubleToString(H1M, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
	obname = Name + "ArrH1Dn";
	ArrowPrice(obname, H1L, 5 * Period(), clrGray);
	//if(Bid < H1H) ObjectSet(obname, OBJPROP_COLOR, clrRed);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "H4 Low Wave " + DoubleToString(H1L, _Digits));
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
}
//+------------------------------------------------------------------+

//+TF to STRING------------------------------------------------------+
string TFToStr(int tf)
// Converts a MT4-numeric timeframe to its descriptor string
// Usage:   string s=TFToStr(15) returns s="M15"
{
	switch (tf) {
	case     1:  return("M1");
	case     5:  return("M5");
	case    15:  return("M15");
	case    30:  return("M30");
	case    60:  return("H1");
	case   240:  return("H4");
	case  1440:  return("D1");
	case 10080:  return("W1");
	case 43200:  return("MN");
	}
	return("");
}
//+------------------------------------------------------------------+

//+TL CREATE---------------------------------------------------------+
void objtrend(string name, double pr1, double pr2, int t1, int t2, color col, string buls) {
	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT);   // number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Time[t2]);
	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
	ObjectSetInteger(0, name, OBJPROP_STYLE, 0);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetInteger(0, name, OBJPROP_RAY_RIGHT, false);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, buls);
}
//+------------------------------------------------------------------+

//+ARROWPRICE--------------------------------------------------------+
void ArrowPrice(const string name, const double x, const datetime y, const color FCol) {
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW_RIGHT_PRICE, 0, 0, 0))
		{
			Print("error: can't create arrow_right_price! code #", GetLastError());
		}
	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT);   // number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE, x);
	ObjectSetInteger(0, name, OBJPROP_TIME, Time[0] + y);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "SL Price: " + DoubleToString(x, _Digits));
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
	const int corner,
	const int x,
	const int y,
	const string label,
	const int FSize,
	const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetTextMQL4(name, label, FSize, "Arial", FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

/*
//+TRADE CHECK-------------------------------------------------------+
bool trades_on_symbol(string symbol)
{
	for (int i = OrdersTotal() - 1; OrderSelect(i, SELECT_BY_POS); i--)
		if (OrderSymbol() == symbol && OrderType() < 2)
			return true;
	return false;
}
//+------------------------------------------------------------------+
*/

//+OBJECTSETTEXT MQL4------------------------------------------------+
bool ObjectSetTextMQL4(string name,
	string text,
	int font_size,
	string font = "",
	color text_color = CLR_NONE)
{
	int tmpObjType = (int)ObjectGetInteger(0, name, OBJPROP_TYPE);
	if (tmpObjType != OBJ_LABEL && tmpObjType != OBJ_TEXT) return(false);
	if (StringLen(text) > 0 && font_size > 0)
	{
		if (ObjectSetString(0, name, OBJPROP_TEXT, text) == true
			&& ObjectSetInteger(0, name, OBJPROP_FONTSIZE, font_size) == true)
		{
			if ((StringLen(font) > 0)
				&& ObjectSetString(0, name, OBJPROP_FONT, font) == false)
				return(false);
			if (text_color > 0
				&& ObjectSetInteger(0, name, OBJPROP_COLOR, text_color) == false)
				return(false);
			return(true);
		}
		return(false);
	}
	return(false);
}
//+------------------------------------------------------------------+

/*
//+------------------------------------------------------------------+
void TradeInfo()
{
	int Total_Buy_Trades = 0;
	double Total_Buy_Size = 0, Total_Buy_Price = 0, Buy_Profit = 0;

	int Total_Sell_Trades = 0;
	double Total_Sell_Size = 0, Total_Sell_Price = 0, Sell_Profit = 0;

	int Net_Trades = 0;
	double Net_Lots = 0, Net_Result = 0;

	double Average_Price = 0, distance = 0;
	double Pip_Value = MarketInfo(Symbol(), MODE_TICKVALUE)*PipAdjust;
	double Pip_Size = MarketInfo(Symbol(), MODE_TICKSIZE)*PipAdjust;

	int total = OrdersTotal();

	for (int i = 0; i < total; i++)
	{
		int ord = OrderSelect(i, SELECT_BY_POS, MODE_TRADES);
		{
			if (OrderType() == OP_BUY && OrderSymbol() == Symbol())
			{
				Total_Buy_Trades++;
				Total_Buy_Price += OrderOpenPrice()*OrderLots();
				Total_Buy_Size += OrderLots();
				Buy_Profit += OrderProfit() + OrderSwap() + OrderCommission();
			}
			if (OrderType() == OP_SELL && OrderSymbol() == Symbol())
			{
				Total_Sell_Trades++;
				Total_Sell_Size += OrderLots();
				Total_Sell_Price += OrderOpenPrice()*OrderLots();
				Sell_Profit += OrderProfit() + OrderSwap() + OrderCommission();
			}
		}
	}
	if (Total_Buy_Price > 0)
	{
		Total_Buy_Price /= Total_Buy_Size;
	}
	if (Total_Sell_Price > 0)
	{
		Total_Sell_Price /= Total_Sell_Size;
	}

	Net_Trades = Total_Buy_Trades + Total_Sell_Trades;
	Net_Lots = Total_Buy_Size - Total_Sell_Size;
	Net_Result = Buy_Profit + Sell_Profit;

	if (Net_Trades > 0 && Net_Lots != 0)
	{
		distance = (Net_Result / (MathAbs(Net_Lots*MarketInfo(Symbol(), MODE_TICKVALUE)))*MarketInfo(Symbol(), MODE_TICKSIZE));
		if (Net_Lots > 0)
		{
			Average_Price = Bid - distance;
		}
		if (Net_Lots < 0)
		{
			Average_Price = Ask + distance;
		}
	}
	if (Net_Trades > 0 && Net_Lots == 0)
	{
		distance = (Net_Result / ((MarketInfo(Symbol(), MODE_TICKVALUE)))*MarketInfo(Symbol(), MODE_TICKSIZE));
		Average_Price = NormalizeDouble(Bid - distance, _Digits);
	}

	color cl = clrBlue;
	if (Net_Lots < 0) cl = clrRed;
	if (Net_Lots == 0) cl = clrWhite;

	if (Average_Price != 0 && ObjectFind(Name + "Average_Price_Line_" + _Symbol) < 0) {
		ObjectCreate(Name + "Average_Price_Line_" + _Symbol, OBJ_HLINE, 0, 0, Average_Price);
		ObjectSet(Name + "Average_Price_Line_" + _Symbol, OBJPROP_WIDTH, 2);
	}

	if (Average_Price != 0) {
		ObjectSet(Name + "Average_Price_Line_" + _Symbol, OBJPROP_PRICE1, Average_Price);
		ObjectSet(Name + "Average_Price_Line_" + _Symbol, OBJPROP_COLOR, cl);
		if (Total_Buy_Trades >= 1) ObjectSetTextMQL4(Name + " BuyPos", "Buy: " + DoubleToString(Bid - distance, _Digits) + " / " + DoubleToString(Total_Buy_Size, 2) + " / " + DoubleToString(Buy_Profit, 2), 7, "Arial", clrBlue);
		if (Total_Sell_Trades >= 1) ObjectSetTextMQL4(Name + " SellPos", "Sell: " + DoubleToString(Ask + distance, _Digits) + " / " + DoubleToString(Total_Sell_Size, 2) + " / " + DoubleToString(Sell_Profit, 2), 7, "Arial", clrRed);
		if (Total_Sell_Trades >= 1 && Total_Buy_Trades >= 1) ObjectSetTextMQL4(Name + " AvgPos", "Avg: " + DoubleToString(Average_Price, _Digits) + " / " + DoubleToString(Net_Lots, 2) + " / " + DoubleToString(Net_Result, 2), 7, "Arial", clrBlack);
	}
	else { ObjectSetTextMQL4(Name + " BuyPos", "", 7, "Arial", clrBlue); ObjectSetTextMQL4(Name + " SellPos", "", 7, "Arial", clrRed); ObjectSetTextMQL4(Name + " AvgPos", "", 7, "Arial", clrRed); ObjectDelete(Name + "Average_Price_Line_" + _Symbol); }
}
//+------------------------------------------------------------------+
*/

//TO BE TRASHED IF NOT REUSED - *TESTING*

// By Francesco - only print arrows depending on depth of price over newwave
	/*
	bool uptoumid[]; ArrayResize(uptoumid, periods); ArrayInitialize(uptoumid, false);
	bool umidtolmid[]; ArrayResize(umidtolmid, periods); ArrayInitialize(umidtolmid, false);
	bool lmidtodn[]; ArrayResize(lmidtodn, periods); ArrayInitialize(lmidtodn, false);

	for (int x = periods - 50; x >= 1; x--) {
		if (Close[x + 1] > ma45h[x + 1] && Close[x] < ma45h[x]) uptoumid[x] = true;
		if (Close[x + 1] < ma45h[x + 1] && Close[x + 1] > ma54[x + 1] && Close[x] < ma54[x]) umidtolmid[x] = true;
		if (Close[x + 1] < ma54[x + 1] && Close[x + 1] > ma45l[x + 1] && Close[x] < ma45l[x]) lmidtodn[x] = true;
	}

	bool dntolmid[]; ArrayResize(dntolmid, periods); ArrayInitialize(dntolmid, false);
	bool lmidtoumid[]; ArrayResize(lmidtoumid, periods); ArrayInitialize(lmidtoumid, false);
	bool umidtoup[]; ArrayResize(umidtoup, periods); ArrayInitialize(umidtoup, false);

	for (int x = periods - 50; x >= 1; x--) {
		if (Close[x + 1] < ma45l[x + 1] && Close[x] > ma45l[x]) dntolmid[x] = true;
		if (Close[x + 1] > ma45l[x + 1] && Close[x + 1] < ma54[x + 1] && Close[x] > ma54[x]) lmidtoumid[x] = true;
		if (Close[x + 1] > ma54[x + 1] && Close[x + 1] < ma45h[x + 1] && Close[x] > ma45h[x]) umidtoup[x] = true;
	}
	*/

	/*
	for (int x = periods - 50; x >= 1; x--) {
			if (toon[x] == true && d[x + 1] > ma45h[x + 1] && ma3[x] < ma45h[x]) { obname = Name + "ArrDW" + IntegerToString(x); burnarr(obname, High[x + 1] + 10 * _Point, x + 1, 238, clrRed); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
			if (toon[x] == true && k[x + 1] < ma45l[x + 1] && ma4[x] > ma45l[x]) { obname = Name + "ArrUW" + IntegerToString(x); burnarr(obname, Low[x + 1] - 10 * _Point, x + 1, 236, clrBlue); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
	}
	*/

	//By Francesco - Breach blue/red dot but close above/below it
		/*
		if (above[x] != EMPTY_VALUE && High[x] > above[x] && Close[x] < above[x]) { obname = Name + "ArrUpF" + IntegerToString(x); burnarr(obname, Low[x] - 10 * _Point, x, 176, clrGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (below[x] != EMPTY_VALUE && Low[x] < below[x] && Close[x] > below[x]) { obname = Name + "ArrDnF" + IntegerToString(x); burnarr(obname, High[x] + 10 * _Point, x, 176, clrMagenta); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		*/

		//By sakis - Brown arrows when white to red / blue
			/*
			if (numean[x] == true && below[x + 1] == EMPTY_VALUE && bbelow[x + 1] != EMPTY_VALUE && below[x] != EMPTY_VALUE) { obname = Name + "ArrUMAu" + IntegerToString(x); burnarr(obname, Low[x] - 10 * _Point, x, 236, clrMaroon); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
			if (ndmean[x] == true && above[x + 1] == EMPTY_VALUE && babove[x + 1] != EMPTY_VALUE && above[x] != EMPTY_VALUE) { obname = Name + "ArrDMAd" + IntegerToString(x); burnarr(obname, High[x] + 10 * _Point, x, 238, clrMaroon); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
			*/

			//By sakis - Count of minimum candles inside blue / red area of newwave for testing on arrows
				/*
				int umean[]; ArrayResize(umean, periods + 1); ArrayInitialize(umean, 0);
				int dmean[]; ArrayResize(dmean, periods + 1); ArrayInitialize(dmean, 0);

				for (int x = periods; x >= 1; x--) {
					if (iCustom(_Symbol, PERIOD_CURRENT, "NewWave", 20, 50, 100, 200, MODE_LWMA, 0, x) > ma54[x]) umean[x] = 1; else umean[x] = 0;
					if (iCustom(_Symbol, PERIOD_CURRENT, "NewWave", 20, 50, 100, 200, MODE_LWMA, 3, x) < ma54[x]) dmean[x] = 1; else dmean[x] = 0;
				}

				int numean[]; ArrayResize(numean, periods + 1); ArrayInitialize(numean, 0);
				int ndmean[]; ArrayResize(ndmean, periods + 1); ArrayInitialize(ndmean, 0);

				for (int x = periods - 10; x >= 1; x--) {
					if (umean[ArrayMinimum(umean, 10, x)] == 0) numean[x] = 0; else numean[x] = 1;
					if (dmean[ArrayMinimum(dmean, 10, x)] == 0) ndmean[x] = 0; else ndmean[x] = 1;
				}
				*/

				//By sakis - Count by daily range filter - ie if previous day broke range of 3 days before it
				/*
				input bool countdown = true; // Enable D1 check for h1/h4 signals

					if (ChartPeriod() <= 1440) {
						if(countdown) {
							for (int x = iBars(_Symbol, PERIOD_D1) - 5; x >= 1; x--) {
								if ((High[iHighest(_Symbol, PERIOD_D1, MODE_HIGH, 3, x)] > iHigh(_Symbol, PERIOD_D1, x + 3) + (iHigh(_Symbol, PERIOD_D1, x + 3) - iLow(_Symbol, PERIOD_D1, x + 3))) || (Low[iLowest(_Symbol, PERIOD_D1, MODE_LOW, 3, x)] < iLow(_Symbol, PERIOD_D1, x + 3) - (iHigh(_Symbol, PERIOD_D1, x + 3) - iLow(_Symbol, PERIOD_D1, x + 3)))) soon[x] = true;
							}
						}
					}

					bool toon[]; ArrayResize(toon, periods); ArrayInitialize(toon, true);
					if (countdown) ArrayCopy(toon, soon, 0, 0, 0);
				*/

				//By Dani - original arrows
				/*
				input color whitey = clrWhite; // Color for main ribbon based arrow
				input bool showarrows = false; // Enable ribbon based arrows

					for (int x = periods - 50; x >= 1; x--) {
							if (showarrows && Close[x + 1] < ma54[x + 1] && ma35[x + 2] > ma55[x + 2] && ma35[x + 1] < ma55[x + 1] && boogie[x] == true) { obname = Name + "ArrDnEnd" + IntegerToString(x); burnarr(obname, High[x + 1] + 10 * _Point, x + 1, 238, whitey); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
							if (showarrows && Close[x + 1] > ma54[x + 1] && ma35[x + 2] < ma55[x + 2] && ma35[x + 1] > ma55[x + 1] && doogie[x] == true) { obname = Name + "ArrUpEnd" + IntegerToString(x); burnarr(obname, Low[x + 1] - 10 * _Point, x + 1, 236, whitey); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }

							if (showarrows && ma20[x + 2] < ma55[x + 2] && ma20[x + 1] > ma55[x + 1] && doogie[x] == true) { obname = Name + "ArrDnn" + IntegerToString(x); burnarr(obname, Low[x + 1] - 10 * _Point, x + 1, 236, clrBlue); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
							if (showarrows && ma20[x + 2] > ma55[x + 2] && ma20[x + 1] < ma55[x + 1] && boogie[x] == true) { obname = Name + "ArrUpn" + IntegerToString(x); burnarr(obname, High[x + 1] + 10 * _Point, x + 1, 238, clrBlue); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }

							if (showarrows && boogie[x] == true && Close[x + 2] > ma54[x + 2] && Close[x + 1] < ma54[x + 1]) { obname = Name + "ArrDCl" + IntegerToString(x); burnarr(obname, Low[x + 1] - 10 * _Point, x + 1, 251, clrRed); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
							if (showarrows && doogie[x] == true && Close[x + 2] < ma54[x + 2] && Close[x + 1] > ma54[x + 1]) { obname = Name + "ArrUCl" + IntegerToString(x); burnarr(obname, High[x + 1] + 10 * _Point, x + 1, 251, clrRed); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
					}
				*/