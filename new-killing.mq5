//+------------------------------------------------------------------+
//|                                                         xmas.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
//--- input parameters

#define Name MQLInfoString(MQL_PROGRAM_NAME)
#property indicator_buffers 15
#property indicator_plots 8

//for sds
double mixa[], mixb[];
double mexa[], mexb[];
double moxa[], moxb[];
double maxa[], maxb[];

double him[], hil[], lim[], lil[];
double phim[], plil[];

//for LR
int LRLPeriod = 20;
double LRLBuffer[];

int n=0;
double sumx=0, sumy=0, sumxy=0, sumx2=0, sumy2=0;
double m=0, yint=0, r=0;

//for tradeinfo
double point;
int PipAdjust, NrOfDigits;

#property indicator_label3  "1st sd up"
#property indicator_type3   DRAW_COLOR_LINE
#property indicator_color3  clrGreen, clrRed
#property indicator_style3  STYLE_SOLID
#property indicator_width3  2
#property indicator_label4  "1st sd dn"
#property indicator_type4   DRAW_COLOR_LINE
#property indicator_color4  clrGreen, clrRed
#property indicator_style4  STYLE_SOLID
#property indicator_width4  2
#property indicator_label5  "Momentum up"
#property indicator_type5   DRAW_LINE
#property indicator_color5  clrYellow
#property indicator_style5  STYLE_SOLID
#property indicator_width5  3
#property indicator_label6  "Momentum dn"
#property indicator_type6   DRAW_LINE
#property indicator_color6  clrYellow
#property indicator_style6  STYLE_SOLID
#property indicator_width6  3
#property indicator_label7  "main sd < sma up"
#property indicator_type7   DRAW_LINE
#property indicator_color7  clrWhite
#property indicator_style7  STYLE_SOLID
#property indicator_width7  2
#property indicator_label8  "main sd > sma dn"
#property indicator_type8   DRAW_LINE
#property indicator_color8  clrWhite
#property indicator_style8  STYLE_SOLID
#property indicator_width8  2
#property indicator_label13  "2*sd high < sma"
#property indicator_type13   DRAW_LINE
#property indicator_color13  clrAqua
#property indicator_style13  STYLE_SOLID
#property indicator_width13  2
#property indicator_label14  "2*sd low > sma"
#property indicator_type14   DRAW_LINE
#property indicator_color14  clrMagenta
#property indicator_style14  STYLE_SOLID
#property indicator_width14  2

int ma20;
double ma20b[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping

   DeleteObjects();
   
   SetIndexBuffer(0, mexa, INDICATOR_CALCULATIONS);
   SetIndexBuffer(1, mexb, INDICATOR_CALCULATIONS);
   SetIndexBuffer(2, mixa, INDICATOR_CALCULATIONS);
   SetIndexBuffer(3, mixb, INDICATOR_CALCULATIONS);
   SetIndexBuffer(4, moxa, INDICATOR_CALCULATIONS);
   SetIndexBuffer(5, moxb, INDICATOR_CALCULATIONS);
   SetIndexBuffer(6, maxa, INDICATOR_CALCULATIONS);
   SetIndexBuffer(7, maxb, INDICATOR_CALCULATIONS);
   SetIndexBuffer(8, him, INDICATOR_CALCULATIONS);
   SetIndexBuffer(9, hil, INDICATOR_CALCULATIONS);
   SetIndexBuffer(10, lim, INDICATOR_CALCULATIONS);
   SetIndexBuffer(11, lil, INDICATOR_CALCULATIONS);
   SetIndexBuffer(12, phim, INDICATOR_CALCULATIONS);
   SetIndexBuffer(13, plil, INDICATOR_CALCULATIONS);
   SetIndexBuffer(14, LRLBuffer, INDICATOR_CALCULATIONS);
   
	PlotIndexSetInteger(2, DRAW_LINE);
	PlotIndexSetInteger(3, DRAW_LINE);
	PlotIndexSetInteger(4, DRAW_LINE);
	PlotIndexSetInteger(5, DRAW_LINE);
	PlotIndexSetInteger(6, DRAW_LINE);
	PlotIndexSetInteger(7, DRAW_LINE);
	PlotIndexSetInteger(12, DRAW_LINE);
	PlotIndexSetInteger(13, DRAW_LINE);
	
	ma20 = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE);
   
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, PERIOD_CURRENT, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, PERIOD_CURRENT, 0);
		}
		if (new_1m_check)
		{
         tsap();
			new_1m_check = false;
		}
		
		bool new_ctf_check = false;
		static datetime start_ctf_time = 0;
		if (start_ctf_time < iTime(_Symbol, PERIOD_M30, 0))
		{
			new_ctf_check = true;
			start_ctf_time = iTime(_Symbol, PERIOD_M30, 0);
		}
		if (new_ctf_check)
		{
			ObjectsDeleteAll(0, Name + " y");
			ydayweek();
			adrhoriz();
			//oldadr();
			//oldawr();
			new_ctf_check = false;
		}
		
		if (trades_on_symbol(_Symbol))
			TradeInfo();
		else if (!trades_on_symbol(_Symbol) && ObjectFind(Name + "Average_Price_Line_" + _Symbol) == 0)
		{
			ObjectDelete(Name + "Average_Price_Line_" + _Symbol);
			ObjectDelete(Name + " BuyPos");
			ObjectDelete(Name + " SellPos");
			ObjectDelete(Name + " AvgPos");
		}
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+

//+ARROW CREATE------------------------------------------------------+
void burnarr(string name, double p, int arrow, int t, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t]);
	ObjectSet(name, OBJPROP_PRICE1, p);
	ObjectSet(name, OBJPROP_ARROWCODE, arrow);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSet(name, OBJPROP_WIDTH, 1);
}
//+------------------------------------------------------------------+
//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const double pr1, const double pr2, const int t1, const int t2, const int t3, const color BCol, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
		IndicatorDigits(_Digits);
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME2, Time[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE2, pr2);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+
void tsap(){
   string obname;
   
   for (int y = iBars(_Symbol, PERIOD_CURRENT) - 22; y >= 0; y--)
   {
      //MathSqrt(startt * 0.5 * 0.5) * startadr;
      
     {
      sumx=0;
      sumy=0;
      sumxy=0;
      sumx2=0;
      sumy2=0;
      for(n=0; n<=LRLPeriod-1; n++)
        {
         sumx=sumx + n;
         sumy=sumy + Close[y + n];
         sumxy=sumxy + n * Close[y + n];
         sumx2=sumx2 + n * n;
         sumy2=sumy2 + Close[y + n] * Close[y + n];
        }
      m=(LRLPeriod*sumxy-sumx*sumy)/(LRLPeriod*sumx2-sumx*sumx);
      yint=(sumy+m*sumx)/LRLPeriod;
      r=(LRLPeriod*sumxy-sumx*sumy)/MathSqrt((LRLPeriod*sumx2-sumx*sumx)*(LRLPeriod*sumy2-sumy*sumy));
      LRLBuffer[y]=yint-m*LRLPeriod;
     }
     
      double ab = 0;
      double bc = 0;
      double cd = 0;
      
      for (int x = 21 + y; x >= y; x --){
         ab += iATR(_Symbol, PERIOD_CURRENT, 21, x) / 21;
      }
      
      for (int x = 21 + y; x >= y; x --){
         bc += MathPow(iATR(_Symbol, PERIOD_CURRENT, 21, x) - ab, 2);
      }
      
      cd = MathSqrt(bc/20);
      
      mexa[y] = iOpen(_Symbol, PERIOD_CURRENT, y) + 5 * cd; //0.5 * MathSqrt(21 * 0.5 * 0.5) * iATR(_Symbol, PERIOD_CURRENT, 21, y); //dico[iBarShift(_Symbol, timeframe, iTime(_Symbol, tinput, y), false)] + 0.5 * MathSqrt(startt * 0.5 * 0.5) * sico[iBarShift(_Symbol, timeframe, iTime(_Symbol, tinput, y), false)];
      mexb[y] = iOpen(_Symbol, PERIOD_CURRENT, y) - 5 * cd; //MathSqrt(21 * 0.5 * 0.5) * iATR(_Symbol, PERIOD_CURRENT, 21, y); //dico[iBarShift(_Symbol, timeframe, iTime(_Symbol, tinput, y), false)] - 0.5 * MathSqrt(startt * 0.5 * 0.5) * sico[iBarShift(_Symbol, timeframe, iTime(_Symbol, tinput, y), false)];
      mixa[y] = iOpen(_Symbol, PERIOD_CURRENT, y) + MathSqrt(21 * 0.5 * 0.5) * iATR(_Symbol, PERIOD_CURRENT, 21, y); //dico[iBarShift(_Symbol, timeframe, iTime(_Symbol, tinput, y), false)] + 0.5 * MathSqrt(startt * 0.5 * 0.5) * sico[iBarShift(_Symbol, timeframe, iTime(_Symbol, tinput, y), false)];
      mixb[y] = iOpen(_Symbol, PERIOD_CURRENT, y) - MathSqrt(21 * 0.5 * 0.5) * iATR(_Symbol, PERIOD_CURRENT, 21, y); //dico[iBarShift(_Symbol, timeframe, iTime(_Symbol, tinput, y), false)] - 0.5 * MathSqrt(startt * 0.5 * 0.5) * sico[iBarShift(_Symbol, timeframe, iTime(_Symbol, tinput, y), false)];
      //if (iLow(_Symbol, tinput, y) < mexb[y]) { obname = Name + "Arr" + IntegerToString(y); burnarr(obname, mexa[y], 234, y, clrRed); }
      //if (iLow(_Symbol, tinput, y) < mixb[y]) { obname = Name + "Arr" + IntegerToString(y); burnarr(obname, iLow(_Symbol, tinput, y), 233, y, clrGold); ObjectSet(obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
      //if (iHigh(_Symbol, tinput, y) > mexa[y]) { obname = Name + "Arr" + IntegerToString(y); burnarr(obname, mexb[y], 233, y, clrBlue); } 
      //if (iHigh(_Symbol, tinput, y) > mixa[y]) { obname = Name + "Arr" + IntegerToString(y); burnarr(obname, iHigh(_Symbol, tinput, y), 234, y, clrGold); ObjectSet(obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
      
      //if (iLow(_Symbol, PERIOD_CURRENT, y) < mixb[ArrayMaximum(mixb, 20, y)]) { moxa[y] = mexa[y]; maxb[y] = mixb[y]; }
      //if (iHigh(_Symbol, PERIOD_CURRENT, y) > mixa[ArrayMinimum(mixa, 20, y)]) { moxb[y] = mexb[y]; maxa[y] = mixa[y]; }
      
      //if ((moxa[y] != EMPTY_VALUE && moxb[y] != EMPTY_VALUE) && (moxa[y] != 0 && moxb[y] !=0)) { obname = Name + "Rec" + IntegerToString(y); RecMake(obname, iHigh(_Symbol, PERIOD_CURRENT, y), iLow(_Symbol, PERIOD_CURRENT, y), y, y, 3 * Period() * 60, clrGreen, ""); }
      
      //if ((mexa[y] - mexb[y]) < mexa[y + 21] - mexb[y + 21]) moxa[y] = iClose(_Symbol, PERIOD_CURRENT, y);
      //if (iLow(_Symbol, PERIOD_CURRENT, y + 2) > mexb[y + 2] && iLow(_Symbol, PERIOD_CURRENT, y + 1) > mexb[y + 1] && iLow(_Symbol, PERIOD_CURRENT, y) > mexb[y] && iHigh(_Symbol, PERIOD_CURRENT, y) < mexa[y]) { obname = Name + "Arrl" + IntegerToString(y); burnarr(obname, iLow(_Symbol, PERIOD_CURRENT, y), 233, y, clrGold); }
      //if (iHigh(_Symbol, PERIOD_CURRENT, y + 2) < mexa[y + 2] && iHigh(_Symbol, PERIOD_CURRENT, y + 1) < mexa[y + 1] && iHigh(_Symbol, PERIOD_CURRENT, y) < mexa[y] && iLow(_Symbol, PERIOD_CURRENT, y) > mexb[y]) { obname = Name + "Arrh" + IntegerToString(y); burnarr(obname, iHigh(_Symbol, PERIOD_CURRENT, y), 234, y, clrGold); }
      
      //if (mixa[y] < iMA(_Symbol, PERIOD_CURRENT, 75, 0, MODE_EMA, PRICE_CLOSE, y)) { moxa[y] = mexa[y]; maxb[y] = mixb[y]; }
      //if (mixb[y] > iMA(_Symbol, PERIOD_CURRENT, 75, 0, MODE_EMA, PRICE_CLOSE, y)) { moxb[y] = mexb[y]; maxa[y] = mixa[y]; }
      //if (mixa[y] < iHigh(_Symbol, tinput, y)) maxa[y] = iHigh(_Symbol, tinput, y);
      //if (mixb[y] > iLow(_Symbol, tinput, y)) maxb[y] = iLow(_Symbol, tinput, y);
      
      him[y] = iHigh(_Symbol, PERIOD_CURRENT, y + 1) + 2 * cd;
      hil[y] = iHigh(_Symbol, PERIOD_CURRENT, y + 1) - cd;
      lim[y] = iLow(_Symbol, PERIOD_CURRENT, y + 1) + cd;
      lil[y] = iLow(_Symbol, PERIOD_CURRENT, y + 1) - 2 * cd;
      
      if (him[y] < iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, y)) phim[y] = him[y];
      if (lil[y] > iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, y)) plil[y] = lil[y];
      
      if (mixa[y] < iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, y)) { maxa[y] = mixa[y]; }
      if (mixb[y] > iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, y)) { maxb[y] = mixb[y]; }
      
      if (y < 1000) { if (iClose(_Symbol, PERIOD_CURRENT, y + 1) < lim[y + 1] && iClose(_Symbol, PERIOD_CURRENT, y + 1) < lil[y + 1] && iClose(_Symbol, PERIOD_CURRENT, y + 1) < iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, y + 1) && LRLBuffer[y] < iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, y)) { moxa[y] = mixa[y]; obname = Name + "Arrh" + IntegerToString(y); burnarr(obname, moxa[y] + 5 * cd, 234, y, clrRed); } }
      if (y < 1000) { if (iClose(_Symbol, PERIOD_CURRENT, y + 1) > hil[y + 1] && iClose(_Symbol, PERIOD_CURRENT, y + 1) > him[y + 1] && iClose(_Symbol, PERIOD_CURRENT, y + 1) > iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, y + 1) && LRLBuffer[y] > iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, y)) { moxb[y] = mixb[y]; obname = Name + "Arrl" + IntegerToString(y); burnarr(obname, moxb[y] - 5 * cd, 233, y, clrGreen); } }
      
      /*
      for (int x = y + 5; x >= y; x--)
      {
         if (moxa[x] != EMPTY_VALUE || moxa[x] != EMPTY || moxa[x] != 0) { moxa[y] = mixa[x]; }
         if (moxb[x] != EMPTY_VALUE || moxb[x] != EMPTY || moxb[x] != 0) { moxb[y] = mixb[x]; } 
      }
      */
      
      ab = 0; bc = 0; cd = 0;
     
   }
}
void adrhoriz()
{
   double i = iClose(_Symbol, PERIOD_D1, 1);
   double adrtouse = iATR(_Symbol, PERIOD_D1, 5, 1);
   double adrmax = i + adrtouse;
   double adrmin = i - adrtouse;
   
   double x = iClose(_Symbol, PERIOD_W1, 1);
   double adrwtouse = iATR(_Symbol, PERIOD_W1, 26, 1);
   double adrwmax = x + adrwtouse;
   double adrwmin = x - adrwtouse;
   
   double z = iClose(_Symbol, PERIOD_MN1, 1);
   double adrmtouse = iATR(_Symbol, PERIOD_MN1, 12, 1);
   double adrmmax = z + adrmtouse;
   double adrmmin = z - adrmtouse;
   
   //if (_Symbol == "USDJPY") Print(x, " ", adrwtouse, " ", adrwmax, " ", adrwmin, " ", z, " ", adrmtouse, " ", adrmmax, " ", adrmmin);
   
   string obname;
   obname = (Name + " adrmax");
   objhoriz(obname, adrmax, 2, clrBlue, "AdrHigh5 " + DoubleToString(adrmax, _Digits));
   ObjectSetString(0, obname, OBJPROP_TEXT, "AdrHigh5 " + DoubleToString(adrmax, _Digits) + " (" + DoubleToString(adrtouse / _Point / 10, 0) + ")");
   /*
      //
      obname = Name + "adroteup";
      objtrend2(obname, i + adrtouse * 79 / 100, i + adrtouse * 62 / 100, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 2, 0, clrGreen, "ADR5maxote: ");
      obname = Name + "adroteup1";
      objtrend2(obname, i + adrtouse * 62 / 100, i + adrtouse * 62 / 100, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 2, 0, clrGreen, "ADR5maxote62: ");
      obname = Name + "adroteup2";
      objtrend2(obname, i + adrtouse * 79 / 100, i + adrtouse * 79 / 100, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 2, 0, clrGreen, "ADR5maxote79: ");
      obname = Name + "adroteup3";
      objtrend2(obname, i + adrtouse * 70.5 / 100, i + adrtouse * 70.5 / 100, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 2, 0, clrGreen, "ADR5maxote705: ");
      //
   */
   obname = (Name + " adrmin");
   objhoriz(obname, adrmin, 2, clrBlue, "AdrLow5 " + DoubleToString(adrmin, _Digits));
   ObjectSetString(0, obname, OBJPROP_TEXT, "AdrLow5 " + DoubleToString(adrmin, _Digits) + " (" + DoubleToString(adrtouse / _Point / 10, 0) + ")");
   /*
      //
      obname = Name + "adrotedn";
      objtrend2(obname, i - adrtouse * 79 / 100, i - adrtouse * 62 / 100, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 2, 0, clrGreen, "ADR5minote: ");
      obname = Name + "adrotedn1";
      objtrend2(obname, i - adrtouse * 62 / 100, i - adrtouse * 62 / 100, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 2, 0, clrGreen, "ADR5minote62: ");
      obname = Name + "adrotedn2";
      objtrend2(obname, i - adrtouse * 79 / 100, i - adrtouse * 79 / 100, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 2, 0, clrGreen, "ADR5minote79: ");
      obname = Name + "adrotedn3";
      objtrend2(obname, i - adrtouse * 70.5 / 100, i - adrtouse * 70.5 / 100, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 2, 0, clrGreen, "ADR5minote705: ");
      //
   */
   if ((iHigh(_Symbol, PERIOD_D1, 0) - iLow(_Symbol, PERIOD_D1, 0)) > adrtouse)
   {
      ObjectSetInteger(0, Name + " adrmax", OBJPROP_COLOR, clrPlum);
      ObjectSetInteger(0, Name + " adrmin", OBJPROP_COLOR, clrPlum);
   }
   
   obname = (Name + " adrwmax");
   objhoriz(obname, adrwmax, 2, clrOrange, "AdrWHigh52 " + DoubleToString(adrwmax, _Digits));
   ObjectSetString(0, obname, OBJPROP_TEXT, "AdrWHigh52 " + DoubleToString(adrwmax, _Digits) + " (" + DoubleToString(adrwtouse / _Point / 10, 0) + ")");
   obname = (Name + " adrwmin");
   objhoriz(obname, adrwmin, 2, clrOrange, "AdrWLow52 " + DoubleToString(adrwmin, _Digits));
   ObjectSetString(0, obname, OBJPROP_TEXT, "AdrWLow52 " + DoubleToString(adrwmin, _Digits) + " (" + DoubleToString(adrwtouse / _Point / 10, 0) + ")");
   
   obname = (Name + " adrmmax");
   objhoriz(obname, adrmmax, 2, clrGreen, "AdrMHigh12 " + DoubleToString(adrmmax, _Digits));
   ObjectSetString(0, obname, OBJPROP_TEXT, "AdrMHigh12 " + DoubleToString(adrmmax, _Digits) + " (" + DoubleToString(adrmtouse / _Point / 10, 0) + ")");
   obname = (Name + " adrmmin");
   objhoriz(obname, adrmmin, 2, clrGreen, "AdrMLow12 " + DoubleToString(adrmmin, _Digits));
   ObjectSetString(0, obname, OBJPROP_TEXT, "AdrMLow12 " + DoubleToString(adrmmin, _Digits) + " (" + DoubleToString(adrmtouse / _Point / 10, 0) + ")");
} 
//+YDAY HIGH/LOW/OPEN/CLOSE------------------------------------------+
void ydayweek()
{
	double o, h, l, c, oo, hh, ll, cc, ooo, hhh, lll, ccc;
	o = iOpen(_Symbol, PERIOD_D1, 1);
	oo = iOpen(_Symbol, PERIOD_W1, 1);
	ooo = iOpen(_Symbol, PERIOD_MN1, 1);
	h = iHigh(_Symbol, PERIOD_D1, 1);
	hh = iHigh(_Symbol, PERIOD_W1, 1);
	hhh = iHigh(_Symbol, PERIOD_MN1, 1);
	l = iLow(_Symbol, PERIOD_D1, 1);
	ll = iLow(_Symbol, PERIOD_W1, 1);
	lll = iLow(_Symbol, PERIOD_MN1, 1);
	c = iClose(_Symbol, PERIOD_D1, 1);
	cc = iClose(_Symbol, PERIOD_W1, 1);
	ccc = iClose(_Symbol, PERIOD_MN1, 1);

	int x = 24 + Hour();
	string obname;

	int od = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 1), false);
	int cd = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false);
	int ow = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 1), false);
	int cw = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 0), false);
	int om = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 1), false);
	int cm = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 0), false);

	if (ChartPeriod() >= 1 && ChartPeriod() <= 240)
	{
		if (ChartPeriod() < 70)
		{
			obname = Name + " ydayO";
			objtrend2(obname, o, o, od, 0, 20 * Period() * 30, 1, 0, clrBeige, "Yesterday Open");
		}
		{
			obname = Name + " ydayO F1";
			Texter(obname, o, Time[0] + 20 * Period() * 30, "D-1 O", clrBeige);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		if (DayOfWeek() != 1)
		{
			if (ChartPeriod() < 70)
			{
				obname = Name + " ydayC";
				objtrend2(obname, c, c, cd, 0, 20 * Period() * 30, 1, 0, clrBeige, "Yesterday Close");
			}
			{
				obname = Name + " ydayC F1";
				Texter(obname, c, Time[0] + 20 * Period() * 30, "D-1 C", clrBeige);
				ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
				ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
				ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
			}
		}
		if (ChartPeriod() < 70)
		{
			obname = Name + " ydayH";
			objtrend2(obname, h, h, od, 0, 20 * Period() * 30, 1, 0, clrWhite, "Yesterday High");
		}
		{
			obname = Name + " ydayH F1";
			Texter(obname, h, Time[0] + 20 * Period() * 30, "D-1 H", clrWhite);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		if (ChartPeriod() < 70)
		{
			obname = Name + " ydayL";
			objtrend2(obname, l, l, od, 0, 20 * Period() * 30, 1, 0, clrWhite, "Yesterday Low");
		}
		{
			obname = Name + " ydayL F1";
			Texter(obname, l, Time[0] + 20 * Period() * 30, "D-1 L", clrWhite);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}

		obname = Name + " ymonthC";
		objtrend2(obname, ccc, ccc, cm, 0, 27 * Period() * 30, 1, 0, clrRed, "Last Month Close");
		{
			obname = Name + " ymonthC F1";
			Texter(obname, ccc, Time[0] + 27 * Period() * 30, "M-1 C", clrRed);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		obname = Name + " ymonthH";
		objtrend2(obname, hhh, hhh, om, 0, 27 * Period() * 30, 1, 0, clrDarkOrange, "Last Month High");
		{
			obname = Name + " ymonthH F1";
			Texter(obname, hhh, Time[0] + 27 * Period() * 30, "M-1 H", clrDarkOrange);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		obname = Name + " ymonthL";
		objtrend2(obname, lll, lll, om, 0, 27 * Period() * 30, 1, 0, clrDarkOrange, "Last Month Low");
		{
			obname = Name + " ymonthL F1";
			Texter(obname, lll, Time[0] + 27 * Period() * 30, "M-1 L", clrDarkOrange);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}

		obname = Name + " yweekC";
		objtrend2(obname, cc, cc, cw, 0, 27 * Period() * 30, 1, 0, clrDarkGoldenrod, "Last Week Close");
		{
			obname = Name + " yweekC F1";
			Texter(obname, cc, Time[0] + 27 * Period() * 30, "W-1 C", clrDarkGoldenrod);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		obname = Name + " yweekH";
		objtrend2(obname, hh, hh, ow, 0, 27 * Period() * 30, 1, 0, clrYellow, "Last Week High");
		{
			obname = Name + " yweekH F1";
			Texter(obname, hh, Time[0] + 27 * Period() * 30, "W-1 H", clrYellow);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		obname = Name + " yweekL";
		objtrend2(obname, ll, ll, ow, 0, 27 * Period() * 30, 1, 0, clrYellow, "Last Week Low");
		{
			obname = Name + " yweekL F1";
			Texter(obname, ll, Time[0] + 27 * Period() * 30, "W-1 L", clrYellow);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
	}
}
//+------------------------------------------------------------------+


//+------------------------------------------------------------------+
void TradeInfo()
{
	int Total_Buy_Trades = 0;
	double Total_Buy_Size = 0, Total_Buy_Price = 0, Buy_Profit = 0;

	int Total_Sell_Trades = 0;
	double Total_Sell_Size = 0, Total_Sell_Price = 0, Sell_Profit = 0;

	int Net_Trades = 0;
	double Net_Lots = 0, Net_Result = 0;

	double Average_Price = 0, distance = 0;
	double Pip_Value = MarketInfo(_Symbol, MODE_TICKVALUE) * PipAdjust;
	double Pip_Size = MarketInfo(_Symbol, MODE_TICKSIZE) * PipAdjust;

	int total = OrdersTotal();

	for (int i = 0; i < total; i++)
	{
		int ord = OrderSelect(i, SELECT_BY_POS, MODE_TRADES);
		{
			if (OrderType() == OP_BUY && OrderSymbol() == _Symbol)
			{
				Total_Buy_Trades++;
				Total_Buy_Price += OrderOpenPrice() * OrderLots();
				Total_Buy_Size += OrderLots();
				Buy_Profit += OrderProfit() + OrderSwap() + OrderCommission();
			}
			if (OrderType() == OP_SELL && OrderSymbol() == _Symbol)
			{
				Total_Sell_Trades++;
				Total_Sell_Size += OrderLots();
				Total_Sell_Price += OrderOpenPrice() * OrderLots();
				Sell_Profit += OrderProfit() + OrderSwap() + OrderCommission();
			}
		}
	}
	if (Total_Buy_Price > 0)
	{
		Total_Buy_Price /= Total_Buy_Size;
	}
	if (Total_Sell_Price > 0)
	{
		Total_Sell_Price /= Total_Sell_Size;
	}

	Net_Trades = Total_Buy_Trades + Total_Sell_Trades;
	Net_Lots = Total_Buy_Size - Total_Sell_Size;
	Net_Result = Buy_Profit + Sell_Profit;

	if (Net_Trades > 0 && Net_Lots != 0)
	{
		distance = (Net_Result / (MathAbs(Net_Lots * MarketInfo(_Symbol, MODE_TICKVALUE))) * MarketInfo(_Symbol, MODE_TICKSIZE));
		if (Net_Lots > 0)
		{
			Average_Price = Bid - distance;
		}
		if (Net_Lots < 0)
		{
			Average_Price = Ask + distance;
		}
	}
	if (Net_Trades > 0 && Net_Lots == 0)
	{
		distance = (Net_Result / ((MarketInfo(_Symbol, MODE_TICKVALUE))) * MarketInfo(_Symbol, MODE_TICKSIZE));
		Average_Price = NormalizeDouble(Bid - distance, _Digits);
	}

	color cl = clrBlue;
	if (Net_Lots < 0)
		cl = clrRed;
	if (Net_Lots == 0)
		cl = clrWhite;

	if (Average_Price != 0 && ObjectFind(Name + "Average_Price_Line_" + _Symbol) < 0)
	{
		ObjectCreate(Name + "Average_Price_Line_" + _Symbol, OBJ_HLINE, 0, 0, Average_Price);
		ObjectSet(Name + "Average_Price_Line_" + _Symbol, OBJPROP_WIDTH, 2);
	}

	if (Average_Price != 0)
	{
		ObjectSet(Name + "Average_Price_Line_" + _Symbol, OBJPROP_PRICE1, Average_Price);
		ObjectSet(Name + "Average_Price_Line_" + _Symbol, OBJPROP_COLOR, cl);
		if (Total_Buy_Trades >= 1)
			ObjectSetText(Name + " BuyPos", "Buy: " + DoubleToStr(Bid - distance, _Digits) + " / " + DoubleToStr(Total_Buy_Size, 2) + " / " + DoubleToStr(Buy_Profit, 2), 7, "Arial", clrBlue);
		if (Total_Sell_Trades >= 1)
			ObjectSetText(Name + " SellPos", "Sell: " + DoubleToStr(Ask + distance, _Digits) + " / " + DoubleToStr(Total_Sell_Size, 2) + " / " + DoubleToStr(Sell_Profit, 2), 7, "Arial", clrRed);
		if (Total_Sell_Trades >= 1 && Total_Buy_Trades >= 1)
			ObjectSetText(Name + " AvgPos", "Avg: " + DoubleToStr(Average_Price, _Digits) + " / " + DoubleToStr(Net_Lots, 2) + " / " + DoubleToStr(Net_Result, 2), 7, "Arial", clrBeige);
	}
	else
	{
		ObjectSetText(Name + " BuyPos", "", 7, "Arial", clrBlue);
		ObjectSetText(Name + " SellPos", "", 7, "Arial", clrRed);
		ObjectSetText(Name + " AvgPos", "", 7, "Arial", clrRed);
		ObjectDelete(Name + "Average_Price_Line_" + _Symbol);
	}
}
//+------------------------------------------------------------------+

//+TRADE CHECK-------------------------------------------------------+
bool trades_on_symbol(string symbol)
{
	for (int i = OrdersTotal() - 1; OrderSelect(i, SELECT_BY_POS); i--)
		if (OrderSymbol() == symbol && OrderType() < 2)
			return true;
	return false;
}
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+

//+CREATE T-LINES----------------------------------------------------+
void objhoriz(string oname, double pr1, int width, color col, string stringa)
{
	if (ObjectFind(0, oname) < 0)
		if (!ObjectCreate(0, oname, OBJ_HLINE, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(oname, OBJPROP_STYLE, STYLE_SOLID);
	ObjectSet(oname, OBJPROP_WIDTH, width);
	ObjectSet(oname, OBJPROP_BACK, true);
	ObjectSet(oname, OBJPROP_COLOR, col);
	ObjectSet(oname, OBJPROP_PRICE1, pr1);
	ObjectSet(oname, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, oname, OBJPROP_TEXT, stringa);
}
//+------------------------------------------------------------------+

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const datetime y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, x);
	ObjectSetInteger(0, name, OBJPROP_TIME1, y + (Period() * 60));
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	//ObjectSetString(0, name, OBJPROP_TOOLTIP, "Price: " + DoubleToStr(x, _Digits));
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToStr(x, _Digits));
}
//+------------------------------------------------------------------+
//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t1]);
	ObjectSet(name, OBJPROP_TIME2, Time[t2] + t3);
	ObjectSet(name, OBJPROP_PRICE1, pr1);
	ObjectSet(name, OBJPROP_PRICE2, pr2);
	ObjectSet(name, OBJPROP_STYLE, st);
	ObjectSet(name, OBJPROP_WIDTH, wi);
	ObjectSet(name, OBJPROP_RAY, false);
	ObjectSet(name, OBJPROP_BACK, false);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToStr(pr1, _Digits) + " Date: " + TimeToStr(Time[t1], TIME_DATE));
}
//+------------------------------------------------------------------+
double adr(const int X, const int Y){
   
   double HD[], LD[];
   ArrayResize(HD, Y + 1);
   ArrayResize(LD, Y + 1);
   CopyHigh(_Symbol, PERIOD_CURRENT, 1, Y + 1, HD);
   CopyLow(_Symbol, PERIOD_CURRENT, 1, Y + 1, LD);
   double adra = 0;
   
   for (int x = Y; x > Y - X; x--){
      adra += (HD[x] - LD[x]);
   }
   
   double adri = 0;
   adri = adra / X;
   
   ArrayFree(HD);
   ArrayFree(LD);
   return(adri);
}

//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || IsTesting())
		if (!IsTesting())
		{
			DeleteObjects();
		}
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	for (int i = ObjectsTotal() - 1; i >= 0; i--)
	{
		string ObName = ObjectName(i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(ObName);
		}
	}
}
//+------------------------------------------------------------------+
//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend3(string name, double pr1, double pr2, int t1, int t2, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t1]);
	ObjectSet(name, OBJPROP_TIME2, Time[t2]);
	ObjectSet(name, OBJPROP_PRICE1, pr1);
	ObjectSet(name, OBJPROP_PRICE2, pr2);
	ObjectSet(name, OBJPROP_STYLE, st);
	ObjectSet(name, OBJPROP_WIDTH, wi);
	ObjectSet(name, OBJPROP_RAY, false);
	ObjectSet(name, OBJPROP_BACK, true);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett); // + " Price: " + DoubleToStr(pr1, _Digits));
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+