#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict
#property indicator_buffers 28
#property indicator_plots 6

#define Name MQLInfoString(MQL_PROGRAM_NAME)

input int period1 = 20;	 // 1st Period
input int period2 = 50;	 // 2nd Period
input int period3 = 100; // 3rd Period
input int period4 = 200; // 4th Period

input ENUM_MA_METHOD mamode = MODE_LWMA; // MA Mode

double ma8h[], ma8l[];
double ma13h[], ma13l[];
double ma21h[], ma21l[];
double ma34h[], ma34l[];
double ma44h[], ma44l[];
double ma55h[], ma55l[];
double ma45h[], ma45l[];
double ma54[], ma54a[], ma54b[];
double ma53[], ma52[];
double D1[], H4[];
double ma8c[], ma55c[];

double W1H, W1M, W1L, D1H, D1M, D1L, H1H, H1M, H1L;

double realmid[];
double upperw[], lowerw[];
double match[], matchdn[], matchup[];

int m8h, m8l, m13h, m13l, m21h, m21l, m34h, m34l, m44h, m44l, m55h, m55l;

//Handles for MTF
int N8hh1, N8lh1, N13hh1, N13lh1, N21hh1, N21lh1, N34hh1, N34lh1;
int N8hh4, N8lh4, N13hh4, N13lh4, N21hh4, N21lh4, N34hh4, N34lh4;
int N8hd1, N8ld1, N13hd1, N13ld1, N21hd1, N21ld1, N34hd1, N34ld1;
int N8hw1, N8lw1, N13hw1, N13lw1, N21hw1, N21lw1, N34hw1, N34lw1;

int m8c, m55c;

bool timerwait = false;

//+INIT FUNCTION-----------------------------------------------------+
int OnInit()
{
	IndicatorSetString(INDICATOR_SHORTNAME, "Rips");
	IndicatorSetInteger(INDICATOR_DIGITS,_Digits);

	SetIndexBuffer(0, ma45h, INDICATOR_DATA);
	PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_LINE);
	PlotIndexSetInteger(0, PLOT_LINE_WIDTH, 2);
	PlotIndexSetInteger(0, PLOT_LINE_COLOR, clrSeaGreen);
	PlotIndexSetString(0, PLOT_LABEL, "Upper");
	SetIndexBuffer(1, ma45l, INDICATOR_DATA);
	PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_LINE);
	PlotIndexSetInteger(1, PLOT_LINE_WIDTH, 2);
	PlotIndexSetInteger(1, PLOT_LINE_COLOR, clrSeaGreen);
	PlotIndexSetString(1, PLOT_LABEL, "Lower");
	SetIndexBuffer(2, matchup, INDICATOR_DATA);
	PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_LINE);
	PlotIndexSetInteger(2, PLOT_LINE_WIDTH, 2);
	PlotIndexSetInteger(2, PLOT_LINE_COLOR, clrBlue);
	PlotIndexSetString(2, PLOT_LABEL, "upmatch");
	SetIndexBuffer(3, matchdn, INDICATOR_DATA);
	PlotIndexSetInteger(3, PLOT_DRAW_TYPE, DRAW_LINE);
	PlotIndexSetInteger(3, PLOT_LINE_WIDTH, 2);
	PlotIndexSetInteger(3, PLOT_LINE_COLOR, clrRed);
	PlotIndexSetString(3, PLOT_LABEL, "dnmatch");
	SetIndexBuffer(4, upperw, INDICATOR_DATA);
	PlotIndexSetInteger(4, PLOT_DRAW_TYPE, DRAW_LINE);
	PlotIndexSetInteger(4, PLOT_LINE_WIDTH, 2);
	PlotIndexSetInteger(4, PLOT_LINE_COLOR, clrBlack);
	PlotIndexSetString(4, PLOT_LABEL, "upconsol");
	SetIndexBuffer(5, lowerw, INDICATOR_DATA);
	PlotIndexSetInteger(5, PLOT_DRAW_TYPE, DRAW_LINE);
	PlotIndexSetInteger(5, PLOT_LINE_WIDTH, 2);
	PlotIndexSetInteger(5, PLOT_LINE_COLOR, clrBlack);
	PlotIndexSetString(5, PLOT_LABEL, "dnconsol");
	SetIndexBuffer(6, ma53, INDICATOR_CALCULATIONS);
	SetIndexBuffer(7, ma54a, INDICATOR_CALCULATIONS);
	SetIndexBuffer(8, ma54b, INDICATOR_CALCULATIONS);
	SetIndexBuffer(9, ma52, INDICATOR_CALCULATIONS);
	SetIndexBuffer(10, ma54, INDICATOR_CALCULATIONS);
	SetIndexBuffer(11, realmid, INDICATOR_CALCULATIONS);
	SetIndexBuffer(12, ma8h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(13, ma8l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(14, ma13h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(15, ma13l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(16, ma21h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(17, ma21l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(18, ma34h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(19, ma34l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(20, ma44h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(21, ma44l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(22, ma55h, INDICATOR_CALCULATIONS);
	SetIndexBuffer(23, ma55l, INDICATOR_CALCULATIONS);
	SetIndexBuffer(24, D1, INDICATOR_CALCULATIONS);
	SetIndexBuffer(25, H4, INDICATOR_CALCULATIONS);
	SetIndexBuffer(26, ma8c, INDICATOR_CALCULATIONS);
	SetIndexBuffer(27, ma55c, INDICATOR_CALCULATIONS);

   /*
	string obname;

	obname = Name + " Dist1";
	LabelMake(obname, 3, 105, 85, "W1", 9, clrNavy);
	obname = Name + " Dist2";
	LabelMake(obname, 3, 80, 85, "D1", 9, clrDeepSkyBlue);
	obname = Name + " Dist3";
	LabelMake(obname, 3, 55, 85, "H4", 9, clrGray);
	obname = Name + " Dist4";
	LabelMake(obname, 3, 30, 85, "H1", 9, clrSeaGreen);

	int p = 15;

	string buls[5] = {"H", "UM", "LM", "L"};
	for (int i = 4; i >= 1; i--)
	{
		obname = Name + IntegerToString(i) + " L";
		LabelMake(obname, 3, 135, 85 + i * p, buls[i - 1], 9, clrBlack);
		obname = Name + IntegerToString(i) + " W1";
		LabelMake(obname, 3, 105, 85 + i * p, "", 9, clrBlack);
		obname = Name + IntegerToString(i) + " D1";
		LabelMake(obname, 3, 80, 85 + i * p, "", 9, clrBlack);
		obname = Name + IntegerToString(i) + " H4";
		LabelMake(obname, 3, 55, 85 + i * p, "", 9, clrBlack);
		obname = Name + IntegerToString(i) + " H1";
		LabelMake(obname, 3, 30, 85 + i * p, "", 9, clrBlack);
	}
	*/

	//CURRENT
	m8h = iMA(_Symbol, PERIOD_CURRENT, period1, 0, mamode, PRICE_HIGH);
	m13h = iMA(_Symbol, PERIOD_CURRENT, period2, 0, mamode, PRICE_HIGH);
	m21h = iMA(_Symbol, PERIOD_CURRENT, period3, 0, mamode, PRICE_HIGH);
	m34h = iMA(_Symbol, PERIOD_CURRENT, period4, 0, mamode, PRICE_HIGH);
	m8l = iMA(_Symbol, PERIOD_CURRENT, period1, 0, mamode, PRICE_LOW);
	m13l = iMA(_Symbol, PERIOD_CURRENT, period2, 0, mamode, PRICE_LOW);
	m21l = iMA(_Symbol, PERIOD_CURRENT, period3, 0, mamode, PRICE_LOW);
	m34l = iMA(_Symbol, PERIOD_CURRENT, period4, 0, mamode, PRICE_LOW);
	m8c = iMA(_Symbol, PERIOD_CURRENT, period1, 0, mamode, PRICE_CLOSE);
	m55c = iMA(_Symbol, PERIOD_CURRENT, period4, 0, mamode, PRICE_CLOSE);
	/*
	//H1
	N8hh1 = iMA(_Symbol, PERIOD_H1, period1, 0, mamode, PRICE_HIGH);
	N8lh1 = iMA(_Symbol, PERIOD_H1, period1, 0, mamode, PRICE_LOW);
	N13hh1 = iMA(_Symbol, PERIOD_H1, period2, 0, mamode, PRICE_HIGH);
	N13lh1 = iMA(_Symbol, PERIOD_H1, period2, 0, mamode, PRICE_LOW);
	N21hh1 = iMA(_Symbol, PERIOD_H1, period3, 0, mamode, PRICE_HIGH);
	N21lh1 = iMA(_Symbol, PERIOD_H1, period3, 0, mamode, PRICE_LOW);
	N34hh1 = iMA(_Symbol, PERIOD_H1, period4, 0, mamode, PRICE_HIGH);
	N34lh1 = iMA(_Symbol, PERIOD_H1, period4, 0, mamode, PRICE_LOW);
	//H4
	N8hh4 = iMA(_Symbol, PERIOD_H4, period1, 0, mamode, PRICE_HIGH);
	N8lh4 = iMA(_Symbol, PERIOD_H4, period1, 0, mamode, PRICE_LOW);
	N13hh4 = iMA(_Symbol, PERIOD_H4, period2, 0, mamode, PRICE_HIGH);
	N13lh4 = iMA(_Symbol, PERIOD_H4, period2, 0, mamode, PRICE_LOW);
	N21hh4 = iMA(_Symbol, PERIOD_H4, period3, 0, mamode, PRICE_HIGH);
	N21lh4 = iMA(_Symbol, PERIOD_H4, period3, 0, mamode, PRICE_LOW);
	N34hh4 = iMA(_Symbol, PERIOD_H4, period4, 0, mamode, PRICE_HIGH);
	N34lh4 = iMA(_Symbol, PERIOD_H4, period4, 0, mamode, PRICE_LOW);
	//D1
	N8hd1 = iMA(_Symbol, PERIOD_D1, period1, 0, mamode, PRICE_HIGH);
	N8ld1 = iMA(_Symbol, PERIOD_D1, period1, 0, mamode, PRICE_LOW);
	N13hd1 = iMA(_Symbol, PERIOD_D1, period2, 0, mamode, PRICE_HIGH);
	N13ld1 = iMA(_Symbol, PERIOD_D1, period2, 0, mamode, PRICE_LOW);
	N21hd1 = iMA(_Symbol, PERIOD_D1, period3, 0, mamode, PRICE_HIGH);
	N21ld1 = iMA(_Symbol, PERIOD_D1, period3, 0, mamode, PRICE_LOW);
	N34hd1 = iMA(_Symbol, PERIOD_D1, period4, 0, mamode, PRICE_HIGH);
	N34ld1 = iMA(_Symbol, PERIOD_D1, period4, 0, mamode, PRICE_LOW);
	//W1
	N8hw1 = iMA(_Symbol, PERIOD_W1, period1, 0, mamode, PRICE_HIGH);
	N8lw1 = iMA(_Symbol, PERIOD_W1, period1, 0, mamode, PRICE_LOW);
	N13hw1 = iMA(_Symbol, PERIOD_W1, period2, 0, mamode, PRICE_HIGH);
	N13lw1 = iMA(_Symbol, PERIOD_W1, period2, 0, mamode, PRICE_LOW);
	N21hw1 = iMA(_Symbol, PERIOD_W1, period3, 0, mamode, PRICE_HIGH);
	N21lw1 = iMA(_Symbol, PERIOD_W1, period3, 0, mamode, PRICE_LOW);
	N34hw1 = iMA(_Symbol, PERIOD_W1, period4, 0, mamode, PRICE_HIGH);
	N34lw1 = iMA(_Symbol, PERIOD_W1, period4, 0, mamode, PRICE_LOW);
	*/

   EventSetMillisecondTimer(1);
   
	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

void OnTimer(){
   EventKillTimer();
   timerwait = true;
   }

//+CALCULATE---------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	datetime expiry = D'2021.06.30 00:00'; // Set expiry date
	bool YesStop = false;

	/*
   if (BarsCalculated(N8hw1) < 10) return(0);
   if (BarsCalculated(N8lw1) < 10) return(0);
   if (BarsCalculated(N13hw1) < 10) return(0);
   if (BarsCalculated(N13lw1) < 10) return(0);
   if (BarsCalculated(N21hw1) < 10) return(0);
   if (BarsCalculated(N21lw1) < 10) return(0);
   if (BarsCalculated(N34hw1) < 10) return(0);
   if (BarsCalculated(N34lw1) < 10) return(0);
   */

   if(!timerwait) { return rates_total; }
   
	if (TimeCurrent() > expiry)
	{
		Print("NewWave expired on " + TimeToString(expiry, TIME_DATE) + ", contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true)
	{
		bool new_2m_check = false;
		static datetime start_2m_time = 0;
		if (start_2m_time < iTime(NULL, PERIOD_CURRENT, 0))
		{
			new_2m_check = true;
			start_2m_time = iTime(NULL, PERIOD_CURRENT, 0);
		}
		if (new_2m_check)
		{
			Bingo();
			Ming();
         ChartRedraw(0);
			new_2m_check = false;
		}
		bool new_4h_check = false;
		static datetime start_4h_time = 0;
		if (start_4h_time < iTime(NULL, PERIOD_H1, 0))
		{
			new_4h_check = true;
			start_4h_time = iTime(NULL, PERIOD_H1, 0);
		}
		if (new_4h_check)
		{
			//BingoH1();
			//BingoH();
			//BingoD();
			//BingoW();
         ChartRedraw(0);
			new_4h_check = false;
		}
	}
	return (rates_total);
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION-----------------------------------------------------+
void Bingo()
{
	int periods = Bars(_Symbol, PERIOD_CURRENT) - 1;

	ArraySetAsSeries(ma8h, true);
	ArraySetAsSeries(ma8l, true);
	ArraySetAsSeries(ma13h, true);
	ArraySetAsSeries(ma13l, true);
	ArraySetAsSeries(ma21h, true);
	ArraySetAsSeries(ma21l, true);
	ArraySetAsSeries(ma34h, true);
	ArraySetAsSeries(ma34l, true);
	if (CopyBuffer(m8h, 0, 0, periods, ma8h) <= 0 ||
		CopyBuffer(m8l, 0, 0, periods, ma8l) <= 0)
		Print("Failed to copy " + IntegerToString(period1) + "MA. ", GetLastError());
	if (CopyBuffer(m13h, 0, 0, periods, ma13h) <= 0 ||
		CopyBuffer(m13l, 0, 0, periods, ma13l) <= 0)
		Print("Failed to copy " + IntegerToString(period2) + "MA. ", GetLastError());
	if (CopyBuffer(m21h, 0, 0, periods, ma21h) <= 0 ||
		CopyBuffer(m21l, 0, 0, periods, ma21l) <= 0)
		Print("Failed to copy " + IntegerToString(period3) + "MA. ", GetLastError());
	if (CopyBuffer(m34h, 0, 0, periods, ma34h) <= 0 ||
		CopyBuffer(m34l, 0, 0, periods, ma34l) <= 0)
		Print("Failed to copy " + IntegerToString(period4) + "MA. ", GetLastError());

	ArraySetAsSeries(ma44h, true);
	ArraySetAsSeries(ma44l, true);
	ArraySetAsSeries(ma55h, true);
	ArraySetAsSeries(ma55l, true);
	for (int x = periods; x >= 0; x--)
	{
		ma44h[x] = MathMax(ma8h[x], ma13h[x]);
		ma55h[x] = MathMax(ma21h[x], ma34h[x]);
		ma44l[x] = MathMin(ma8l[x], ma13l[x]);
		ma55l[x] = MathMin(ma21l[x], ma34l[x]);
	}

	ArraySetAsSeries(ma45h, true);
	ArraySetAsSeries(ma45l, true);
	for (int x = periods; x >= 0; x--)
	{
		ma45h[x] = MathMax(ma44h[x], ma55h[x]);
		ma45l[x] = MathMin(ma44l[x], ma55l[x]);
	}

	ArraySetAsSeries(ma54, true);
	ArraySetAsSeries(ma54a, true);
	ArraySetAsSeries(ma54b, true);
	ArraySetAsSeries(ma53, true);
	ArraySetAsSeries(ma52, true);
	ArrayInitialize(ma54, 0);
	ArrayInitialize(ma54a, 0);
	ArrayInitialize(ma54b, 0);
	ArrayInitialize(ma53, 0);
	ArrayInitialize(ma52, 0);
	for (int x = periods - 1; x >= 0; x--)
	{
		ma54[x] = (ma45h[x] + ma45l[x]) / 2;
		ma54a[x] = ma54[x];
		ma54b[x] = ma54[x];
		ma53[x] = (ma54[x] + iHigh(_Symbol, PERIOD_CURRENT, x + 1)) / 2;
		ma52[x] = (ma54[x] + iLow(_Symbol, PERIOD_CURRENT, x + 1)) / 2;
		if (ma53[x] < ma54[x])
			ma53[x] = ma54[x];
		if (ma52[x] > ma54[x])
			ma52[x] = ma54[x];
	}
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION H1--------------------------------------------------+
void BingoH1()
{
	double maN8h[1], maN8l[1], maN13h[1], maN13l[1], maN21h[1], maN21l[1], maN34h[1], maN34l[1];

	ArrayInitialize(maN8h, 0);
	ArrayInitialize(maN8l, 0);
	ArrayInitialize(maN13h, 0);
	ArrayInitialize(maN13l, 0);
	ArrayInitialize(maN21h, 0);
	ArrayInitialize(maN21l, 0);
	ArrayInitialize(maN34h, 0);
	ArrayInitialize(maN34l, 0);

	if (CopyBuffer(N8hh1, 0, 0, 1, maN8h) <= 0 ||
		CopyBuffer(N8lh1, 0, 0, 1, maN8l) <= 0)
		Print("Failed to copy " + IntegerToString(period1) + "MA H1. ", GetLastError());
	if (CopyBuffer(N13hh1, 0, 0, 1, maN13h) <= 0 ||
		CopyBuffer(N13lh1, 0, 0, 1, maN13l) <= 0)
		Print("Failed to copy " + IntegerToString(period2) + "MA H1. ", GetLastError());
	if (CopyBuffer(N21hh1, 0, 0, 1, maN21h) <= 0 ||
		CopyBuffer(N21lh1, 0, 0, 1, maN21l) <= 0)
		Print("Failed to copy " + IntegerToString(period3) + "MA H1. ", GetLastError());
	if (CopyBuffer(N34hh1, 0, 0, 1, maN34h) <= 0 ||
		CopyBuffer(N34lh1, 0, 0, 1, maN34l) <= 0)
		Print("Failed to copy " + IntegerToString(period4) + "MA H1. ", GetLastError());

   IndicatorRelease(N8hh1);
   IndicatorRelease(N13hh1);
   IndicatorRelease(N21hh1);
   IndicatorRelease(N34hh1);
   IndicatorRelease(N8lh1);
   IndicatorRelease(N13lh1);
   IndicatorRelease(N21lh1);
   IndicatorRelease(N34lh1);
   
	double maN44h, maN55h, maN44l, maN55l;

	maN44h = MathMax(maN8h[0], maN13h[0]);
	maN55h = MathMax(maN21h[0], maN34h[0]);
	maN44l = MathMin(maN8l[0], maN13l[0]);
	maN55l = MathMin(maN21l[0], maN34l[0]);

	double maN45h, maN45l;

	maN45h = MathMax(maN44h, maN55h);
	maN45l = MathMin(maN44l, maN55l);

	double maN54, maN53, maN52;

	maN54 = (maN45h + maN45l) / 2;
	maN53 = (maN54 + iHigh(_Symbol, PERIOD_H1, 1)) / 2;
	maN52 = (maN54 + iLow(_Symbol, PERIOD_H1, 1)) / 2;
	if (maN53 < maN54)
		maN53 = maN54;
	if (maN52 > maN54)
		maN52 = maN54;

	if ((iClose(_Symbol, PERIOD_H1, 0) > maN45h))
		ObjectSetText(0, Name + IntegerToString(1) + " H1", CharToString(174), 12, "Wingdings", clrBlue);
	else
		ObjectSetText(0, Name + IntegerToString(1) + " H1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H1, 0) > maN54) && (iClose(_Symbol, PERIOD_H1, 0) < maN45h))
		ObjectSetText(0, Name + IntegerToString(2) + " H1", CharToString(233), 12, "Wingdings", clrSteelBlue);
	else
		ObjectSetText(0, Name + IntegerToString(2) + " H1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H1, 0) < maN54) && (iClose(_Symbol, PERIOD_H1, 0) > maN45l))
		ObjectSetText(0, Name + IntegerToString(3) + " H1", CharToString(234), 12, "Wingdings", clrIndianRed);
	else
		ObjectSetText(0, Name + IntegerToString(3) + " H1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H1, 0) < maN45l))
		ObjectSetText(0, Name + IntegerToString(4) + " H1", CharToString(174), 12, "Wingdings", clrRed);
	else
		ObjectSetText(0, Name + IntegerToString(4) + " H1", " ", 9, "Arial", clrBlack);

	for (int x = 1; x <= 4; x++)
	{
		ObjectSetString(0, Name + IntegerToString(x) + " H1", OBJPROP_TOOLTIP, "H: " + DoubleToString(maN45h, _Digits) + " / M: " + DoubleToString(maN54, _Digits) + " / L: " + DoubleToString(maN45l, _Digits));
	}
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION H4--------------------------------------------------+
void BingoH()
{
	double maN8h[1], maN8l[1], maN13h[1], maN13l[1], maN21h[1], maN21l[1], maN34h[1], maN34l[1];

	ArrayInitialize(maN8h, 0);
	ArrayInitialize(maN8l, 0);
	ArrayInitialize(maN13h, 0);
	ArrayInitialize(maN13l, 0);
	ArrayInitialize(maN21h, 0);
	ArrayInitialize(maN21l, 0);
	ArrayInitialize(maN34h, 0);
	ArrayInitialize(maN34l, 0);

	if (CopyBuffer(N8hh4, 0, 0, 1, maN8h) <= 0 ||
		CopyBuffer(N8lh4, 0, 0, 1, maN8l) <= 0)
		Print("Failed to copy " + IntegerToString(period1) + "MA H4. ", GetLastError());
	if (CopyBuffer(N13hh4, 0, 0, 1, maN13h) <= 0 ||
		CopyBuffer(N13lh4, 0, 0, 1, maN13l) <= 0)
		Print("Failed to copy " + IntegerToString(period2) + "MA H4. ", GetLastError());
	if (CopyBuffer(N21hh4, 0, 0, 1, maN21h) <= 0 ||
		CopyBuffer(N21lh4, 0, 0, 1, maN21l) <= 0)
		Print("Failed to copy " + IntegerToString(period3) + "MA H4. ", GetLastError());
	if (CopyBuffer(N34hh4, 0, 0, 1, maN34h) <= 0 ||
		CopyBuffer(N34lh4, 0, 0, 1, maN34l) <= 0)
		Print("Failed to copy " + IntegerToString(period4) + "MA H4. ", GetLastError());

   IndicatorRelease(N8hh4);
   IndicatorRelease(N13hh4);
   IndicatorRelease(N21hh4);
   IndicatorRelease(N34hh4);
   IndicatorRelease(N8lh4);
   IndicatorRelease(N13lh4);
   IndicatorRelease(N21lh4);
   IndicatorRelease(N34lh4);
   
	double maN44h, maN55h, maN44l, maN55l;

	maN44h = MathMax(maN8h[0], maN13h[0]);
	maN55h = MathMax(maN21h[0], maN34h[0]);
	maN44l = MathMin(maN8l[0], maN13l[0]);
	maN55l = MathMin(maN21l[0], maN34l[0]);

	double maN45h, maN45l;

	maN45h = MathMax(maN44h, maN55h);
	maN45l = MathMin(maN44l, maN55l);

	double maN54, maN53, maN52;

	maN54 = (maN45h + maN45l) / 2;
	maN53 = (maN54 + iHigh(_Symbol, PERIOD_H4, 1)) / 2;
	maN52 = (maN54 + iLow(_Symbol, PERIOD_H4, 1)) / 2;
	if (maN53 < maN54)
		maN53 = maN54;
	if (maN52 > maN54)
		maN52 = maN54;

	if ((iClose(_Symbol, PERIOD_H4, 0) > maN45h))
		ObjectSetText(0, Name + IntegerToString(1) + " H4", CharToString(174), 12, "Wingdings", clrBlue);
	else
		ObjectSetText(0, Name + IntegerToString(1) + " H4", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H4, 0) > maN54) && (iClose(_Symbol, PERIOD_H4, 0) < maN45h))
		ObjectSetText(0, Name + IntegerToString(2) + " H4", CharToString(233), 12, "Wingdings", clrSteelBlue);
	else
		ObjectSetText(0, Name + IntegerToString(2) + " H4", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H4, 0) < maN54) && (iClose(_Symbol, PERIOD_H4, 0) > maN45l))
		ObjectSetText(0, Name + IntegerToString(3) + " H4", CharToString(234), 12, "Wingdings", clrIndianRed);
	else
		ObjectSetText(0, Name + IntegerToString(3) + " H4", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_H4, 0) < maN45l))
		ObjectSetText(0, Name + IntegerToString(4) + " H4", CharToString(174), 12, "Wingdings", clrRed);
	else
		ObjectSetText(0, Name + IntegerToString(4) + " H4", " ", 9, "Arial", clrBlack);

	for (int x = 1; x <= 4; x++)
	{
		ObjectSetString(0, Name + IntegerToString(x) + " H4", OBJPROP_TOOLTIP, "H: " + DoubleToString(maN45h, _Digits) + " / M: " + DoubleToString(maN54, _Digits) + " / L: " + DoubleToString(maN45l, _Digits));
	}

	H1H = maN45h;
	H1M = maN54;
	H1L = maN45l;
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION D1--------------------------------------------------+
void BingoD()
{
	double maN8h[1], maN8l[1], maN13h[1], maN13l[1], maN21h[1], maN21l[1], maN34h[1], maN34l[1];

	ArrayInitialize(maN8h, 0);
	ArrayInitialize(maN8l, 0);
	ArrayInitialize(maN13h, 0);
	ArrayInitialize(maN13l, 0);
	ArrayInitialize(maN21h, 0);
	ArrayInitialize(maN21l, 0);
	ArrayInitialize(maN34h, 0);
	ArrayInitialize(maN34l, 0);

	if (CopyBuffer(N8hd1, 0, 0, 1, maN8h) <= 0 ||
		CopyBuffer(N8ld1, 0, 0, 1, maN8l) <= 0)
		Print("Failed to copy " + IntegerToString(period1) + "MA D1. ", GetLastError());
	if (CopyBuffer(N13hd1, 0, 0, 1, maN13h) <= 0 ||
		CopyBuffer(N13ld1, 0, 0, 1, maN13l) <= 0)
		Print("Failed to copy " + IntegerToString(period2) + "MA D1. ", GetLastError());
	if (CopyBuffer(N21hd1, 0, 0, 1, maN21h) <= 0 ||
		CopyBuffer(N21ld1, 0, 0, 1, maN21l) <= 0)
		Print("Failed to copy " + IntegerToString(period3) + "MA D1. ", GetLastError());
	if (CopyBuffer(N34hd1, 0, 0, 1, maN34h) <= 0 ||
		CopyBuffer(N34ld1, 0, 0, 1, maN34l) <= 0)
		Print("Failed to copy " + IntegerToString(period4) + "MA D1. ", GetLastError());

   IndicatorRelease(N8hd1);
   IndicatorRelease(N13hd1);
   IndicatorRelease(N21hd1);
   IndicatorRelease(N34hd1);
   IndicatorRelease(N8ld1);
   IndicatorRelease(N13ld1);
   IndicatorRelease(N21ld1);
   IndicatorRelease(N34ld1);
   
	double maN44h, maN55h, maN44l, maN55l;

	maN44h = MathMax(maN8h[0], maN13h[0]);
	maN55h = MathMax(maN21h[0], maN34h[0]);
	maN44l = MathMin(maN8l[0], maN13l[0]);
	maN55l = MathMin(maN21l[0], maN34l[0]);

	double maN45h, maN45l;

	maN45h = MathMax(maN44h, maN55h);
	maN45l = MathMin(maN44l, maN55l);

	double maN54, maN53, maN52;

	maN54 = (maN45h + maN45l) / 2;
	maN53 = (maN54 + iHigh(_Symbol, PERIOD_D1, 1)) / 2;
	maN52 = (maN54 + iLow(_Symbol, PERIOD_D1, 1)) / 2;
	if (maN53 < maN54)
		maN53 = maN54;
	if (maN52 > maN54)
		maN52 = maN54;

	if ((iClose(_Symbol, PERIOD_D1, 0) > maN45h))
		ObjectSetText(0, Name + IntegerToString(1) + " D1", CharToString(174), 12, "Wingdings", clrBlue);
	else
		ObjectSetText(0, Name + IntegerToString(1) + " D1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_D1, 0) > maN54) && (iClose(_Symbol, PERIOD_D1, 0) < maN45h))
		ObjectSetText(0, Name + IntegerToString(2) + " D1", CharToString(233), 12, "Wingdings", clrSteelBlue);
	else
		ObjectSetText(0, Name + IntegerToString(2) + " D1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_D1, 0) < maN54) && (iClose(_Symbol, PERIOD_D1, 0) > maN45l))
		ObjectSetText(0, Name + IntegerToString(3) + " D1", CharToString(234), 12, "Wingdings", clrIndianRed);
	else
		ObjectSetText(0, Name + IntegerToString(3) + " D1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_D1, 0) < maN45l))
		ObjectSetText(0, Name + IntegerToString(4) + " D1", CharToString(174), 12, "Wingdings", clrRed);
	else
		ObjectSetText(0, Name + IntegerToString(4) + " D1", " ", 9, "Arial", clrBlack);

	for (int x = 1; x <= 4; x++)
	{
		ObjectSetString(0, Name + IntegerToString(x) + " D1", OBJPROP_TOOLTIP, "H: " + DoubleToString(maN45h, _Digits) + " / M: " + DoubleToString(maN54, _Digits) + " / L: " + DoubleToString(maN45l, _Digits));
	}

	D1H = maN45h;
	D1M = maN54;
	D1L = maN45l;
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION W1--------------------------------------------------+
void BingoW()
{
	double maN8h[1], maN8l[1], maN13h[1], maN13l[1], maN21h[1], maN21l[1], maN34h[1], maN34l[1];

	ArrayInitialize(maN8h, 0);
	ArrayInitialize(maN8l, 0);
	ArrayInitialize(maN13h, 0);
	ArrayInitialize(maN13l, 0);
	ArrayInitialize(maN21h, 0);
	ArrayInitialize(maN21l, 0);
	ArrayInitialize(maN34h, 0);
	ArrayInitialize(maN34l, 0);
	
	//Print(BarsCalculated(N8hw1)+ " " + BarsCalculated(N13hw1) + " " + BarsCalculated(N21lw1) + " " + BarsCalculated(N34lw1));

	if (CopyBuffer(N8hw1, 0, 0, 1, maN8h) <= 0 ||
		CopyBuffer(N8lw1, 0, 0, 1, maN8l) <= 0)
		Print("Failed to copy " + IntegerToString(period1) + "MA W1. ", GetLastError());
	if (CopyBuffer(N13hw1, 0, 0, 1, maN13h) <= 0 ||
		CopyBuffer(N13lw1, 0, 0, 1, maN13l) <= 0)
		Print("Failed to copy " + IntegerToString(period2) + "MA W1. ", GetLastError());
	if (CopyBuffer(N21hw1, 0, 0, 1, maN21h) <= 0 ||
		CopyBuffer(N21lw1, 0, 0, 1, maN21l) <= 0)
		Print("Failed to copy " + IntegerToString(period3) + "MA W1. ", GetLastError());
	if (CopyBuffer(N34hw1, 0, 0, 1, maN34h) <= 0 ||
		CopyBuffer(N34lw1, 0, 0, 1, maN34l) <= 0)
		Print("Failed to copy " + IntegerToString(period4) + "MA W1. ", GetLastError());
   
   IndicatorRelease(N8hw1);
   IndicatorRelease(N13hw1);
   IndicatorRelease(N21hw1);
   IndicatorRelease(N34hw1);
   IndicatorRelease(N8lw1);
   IndicatorRelease(N13lw1);
   IndicatorRelease(N21lw1);
   IndicatorRelease(N34lw1);
   
	double maN44h, maN55h, maN44l, maN55l;

	maN44h = MathMax(maN8h[0], maN13h[0]);
	maN55h = MathMax(maN21h[0], maN34h[0]);
	maN44l = MathMin(maN8l[0], maN13l[0]);
	maN55l = MathMin(maN21l[0], maN34l[0]);

	double maN45h, maN45l;

	maN45h = MathMax(maN44h, maN55h);
	maN45l = MathMin(maN44l, maN55l);

	double maN54, maN53, maN52;

	maN54 = (maN45h + maN45l) / 2;
	maN53 = (maN54 + iHigh(_Symbol, PERIOD_W1, 1)) / 2;
	maN52 = (maN54 + iLow(_Symbol, PERIOD_W1, 1)) / 2;
	if (maN53 < maN54)
		maN53 = maN54;
	if (maN52 > maN54)
		maN52 = maN54;

	if ((iClose(_Symbol, PERIOD_W1, 0) > maN45h))
		ObjectSetText(0, Name + IntegerToString(1) + " W1", CharToString(174), 12, "Wingdings", clrBlue);
	else
		ObjectSetText(0, Name + IntegerToString(1) + " W1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_W1, 0) > maN54) && (iClose(_Symbol, PERIOD_W1, 0) < maN45h))
		ObjectSetText(0, Name + IntegerToString(2) + " W1", CharToString(233), 12, "Wingdings", clrSteelBlue);
	else
		ObjectSetText(0, Name + IntegerToString(2) + " W1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_W1, 0) < maN54) && (iClose(_Symbol, PERIOD_W1, 0) > maN45l))
		ObjectSetText(0, Name + IntegerToString(3) + " W1", CharToString(234), 12, "Wingdings", clrIndianRed);
	else
		ObjectSetText(0, Name + IntegerToString(3) + " W1", " ", 9, "Arial", clrBlack);
	if ((iClose(_Symbol, PERIOD_W1, 0) < maN45l))
		ObjectSetText(0, Name + IntegerToString(4) + " W1", CharToString(174), 12, "Wingdings", clrRed);
	else
		ObjectSetText(0, Name + IntegerToString(4) + " W1", " ", 9, "Arial", clrBlack);

	for (int x = 1; x <= 4; x++)
	{
		ObjectSetString(0, Name + IntegerToString(x) + " W1", OBJPROP_TOOLTIP, "H: " + DoubleToString(maN45h, _Digits) + " / M: " + DoubleToString(maN54, _Digits) + " / L: " + DoubleToString(maN45l, _Digits));
	}

	W1H = maN45h;
	W1M = maN54;
	W1L = maN45l;
}
//+------------------------------------------------------------------+

//+WAVE-INFO---------------------------------------------------------+
void Ming()
{
	int periods;
	if (Bars(_Symbol, PERIOD_CURRENT) <= 2001)
		periods = Bars(_Symbol, PERIOD_CURRENT) - 1;
	else
		periods = 2000;
	
	ArraySetAsSeries(ma8c, true);
	ArraySetAsSeries(ma55c, true);
	if (CopyBuffer(m8c, 0, 0, periods, ma8c) <= 0)
		Print("Failed to copy " + IntegerToString(period1) + "MA. ", GetLastError());
	if (CopyBuffer(m55c, 0, 0, periods, ma55c) <= 0)
	   Print("Failed to copy " + IntegerToString(period4) + "MA. ", GetLastError());
	
	ArraySetAsSeries(realmid, true);
	ArraySetAsSeries(matchup, true);
	ArraySetAsSeries(matchdn, true);
	for (int x = periods; x >= 0; x--)
	{
		realmid[x] = (ma8c[x] + ma55c[x]) / 2;
		if (ma45h[x] == ma8h[x])
			matchup[x] = ma8h[x];
		else
			matchup[x] = EMPTY_VALUE;
		if (ma45l[x] == ma8l[x])
			matchdn[x] = ma8l[x];
		else
			matchdn[x] = EMPTY_VALUE;
	}

	double wavepp[];
	ArrayResize(wavepp, periods + 1);
	ArrayInitialize(wavepp, 0);
	double meanwave[];
	ArrayResize(meanwave, periods + 1);
	ArrayInitialize(meanwave, 0);

	for (int x = periods - 60; x >= 0; x--)
	{
		for (int i = x + 60; i > x; i--)
			wavepp[x] += ma45h[i] - ma45l[i];
	}

	for (int x = periods - 60; x >= 0; x--)
	{
		meanwave[x] = wavepp[x] / 60;
	}
	
   ArraySetAsSeries(upperw, true);
   ArraySetAsSeries(lowerw, true);
   ArrayInitialize(upperw, EMPTY_VALUE);
   ArrayInitialize(lowerw, EMPTY_VALUE);
	for (int x = periods - 60; x >= 0; x--)
	{
		if ((ma45h[x] - ma45l[x]) < 0.66 * meanwave[x])
		{
			for (int i = x + 60; i > x; i--)
			{
				upperw[x] = ma45h[x];
				lowerw[x] = ma45l[x];
			}
		}
	}
}
//+------------------------------------------------------------------+

//+ARROWPRICE--------------------------------------------------------+
void ArrowPrice(const string name, const double x, const datetime y, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW_RIGHT_PRICE, 0, 0, 0))
		{
			Print("error: can't create arrow_right_price! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE, x);
	ObjectSetInteger(0, name, OBJPROP_TIME, y);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "SL Price: " + DoubleToString(x, _Digits));
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetText(0, name, label, FSize, "Arial", FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+OBJECTSETTEXT FUNCTION--------------------------------------------+
void ObjectSetText(const int window,
                   const string name,
                   const string text,
                   const int fontsize,
                   const string font,
                   const color col)
{
	if (ObjectFind(0, name) < 0)
		Print("Error: can't find label_object! code #", GetLastError());
   int object = (int)ObjectGetInteger(window, name, OBJPROP_TYPE);
   if (object != OBJ_LABEL && object != OBJ_TEXT)
      Print("Not a label or text object! code#", GetLastError());
   ObjectSetString(window, name, OBJPROP_TEXT, text);
   ObjectSetInteger(window, name, OBJPROP_FONTSIZE, fontsize);
   ObjectSetString(window, name, OBJPROP_FONT, font);
   ObjectSetInteger(window, name, OBJPROP_COLOR, col);
}
//+------------------------------------------------------------------+