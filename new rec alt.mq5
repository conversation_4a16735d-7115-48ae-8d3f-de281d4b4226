//+------------------------------------------------------------------+
//|                                                      ProjectName |
//+------------------------------------------------------------------+
#property copyright  "sakisf"
#property link       ""
#property version    "1.00"
#property strict

#property indicator_chart_window

#property indicator_plots   0
#property indicator_buffers 0

//--- Daily Pips
input int InpTimeShift=0;// Time Shift, hours
input int InpDayOffsetPips=0;//Daily Offset, pips
input int InpWeekOffsetPips=0;//Weekly Offset, pips
input string Dail="";    // Daily inputs pips
input int TradeZoneS= -1;// Daily TZ L
input int TradeZoneE = 1;// Daily TZ H
input int LowP = -2; // High % Area L
input int HighP = 2; // High % Area H
input int Lt30pctZL= -7; // Less than 30% Zone L
input int Lt30pctZH = 7; // Less than 30% Zone H
int LowExtremeS= LowP-1; // Low Extreme Start
input int LowExtremeE=-15; // Low Extreme End 
input int LowSL=-18; // Low Max SL
int HighExtremeS=HighP+1; // High Extreme Start
input int HighExtremeE=15; // High Extreme End
input int HighSL= 18; // High Max SL
input double SDd=1.0; //SD Day down
input double SDu=1.0; //SD Day up

//--- Weekly Pips
input string Weekl=""; // Weekly inputs pips
input int TradeZoneWS= -2; // Weekly TZ L
input int TradeZoneWE = 2; // Weekly TZ H
input int LowWP = -5; // W High % Area L
input int HighWP = 5; // W High % Area H
input int Lt30pctZWL= -7; // Less than 30% W Zone L
input int Lt30pctZWH = 7; // Less than 30% W Zone H
int LowExtremeWS=LowWP-1; // W Low Extreme Start
input int LowExtremeWE = -12; // W Low Extreme End 
int HighExtremeWS = HighWP+1; // W High Extreme Start
input int HighExtremeWE = 12; // W High Extreme End
input double SDWd = 1.0; // SD Week down
input double SDWu = 1.0; // SD Week up

//--- Additional Options
input string Options=""; //Additional Options
input int CheckSeconds=5;// Re-check Mid Every x Seconds

//--- Colors
input string DayCol=""; // Daily Colors
input color MidColor=clrGoldenrod; // Daily / Weekly Mid Color
input color DTZColor=clrAqua; // Daily Trade Zone Color
input color MZColor=clrRed; // Daily Main Zone Color
input color Z3Color=clrPurple; // Daily 30% Zones
input color ExtremesColor=clrYellow; // Daily Extreme Zones Color
input color SLZoneColor=clrRed; // Daily SL Zones Color
input color SDColor=clrGold; // Daily SD Color

input string WeekCol=""; // Weekly Colors
input color WTZColor=clrPink; // Weekly Trade Zone Color
input color WMZColor = clrOrange; // Weekly Main Zone Color
input color WZ3Color = clrPurple; // Weekly 30% Zones
input color WExtremesColor=clrGold; // Weekly Extreme Zones Color
color FibsColor = clrMagenta; // Fib Color
color FibsColorEx = clrBlack; // Fib Extension Color

//--- Alerts
input bool ALERT= true; // Popup Alert 
input bool pushm= true; // Push Notifications

//---
double DayMid;
double TradeZoneStart;
double TradeZoneEnd;
double HighPctAreaLow;
double HighPctAreaHigh;
double LessThan30PctZoneLow;
double LessThan30PctZoneHigh;
double LowExtremeStart;
double LowExtremeEnd;
double HighExtremeStart;
double HighExtremeEnd;
double MaxSLLow;
double MaxSLHigh;

//Week Pips variables
double WeekMid;
double WeekMida;
double WeekMidb;
double WeekMidaa;
double WeekMidbb;
double WeekMidaaa;
double WeekMidbbb;
double WeekMidaaaa;
double WeekMidbbbb;
double WeekMidaaaaa;
double WeekMidbbbbb;
double WeekMidc;
double WeekMidd;
double WeekTradeZoneStart;
double WeekTradeZoneEnd;
double WeekHighPctAreaLow;
double WeekHighPctAreaHigh;
double WeekLessThan30PctZoneLow;
double WeekLessThan30PctZoneHigh;
double WeekLowExtremeStart;
double WeekLowExtremeEnd;
double WeekHighExtremeStart;
double WeekHighExtremeEnd;

//Pivots
double Pivot;
double WeekPivot;

bool ALessThan30PctZoneLow      = true;
bool ALessThan30PctZoneHigh     = true;
bool ALowExtremeStart           = true;
bool AHighExtremeStart          = true;
bool AMaxSLLow                  = true;
bool AMaxSLHigh                 = true;
bool ASDdn                      = true;
bool ASDup                      = true;
bool AWeekLessThan30PctZoneLow  = true;
bool AWeekLessThan30PctZoneHigh = true;
bool AWeekLowExtremeStart       = true;
bool AWeekHighExtremeStart      = true;
bool AWeekHighPctAreaLow        = true;
bool AWeekHighPctAreaHigh       = true;
bool AWSDdn                     = true;
bool AWSDup                     = true;

//Screenshots
double CurrentMid,CurrentMida,CurrentMidb;
//+------------------------------------------------------------------+
string DateTimeReformat(string value)
  {
   StringReplace(value,":","");
   StringReplace(value,".","");
   return (value);
  }
//+------------------------------------------------------------------+
string PeriodDesc(const ENUM_TIMEFRAMES tf)
  {
   return StringSubstr(EnumToString(tf),7);
  }
//+------------------------------------------------------------------+
int TimeDayOfWeek(const datetime time)
  {
   MqlDateTime dt;
   TimeToStruct(time,dt);
   return dt.day_of_week;
  }
//+------------------------------------------------------------------+
double iLowD1(const string symbol,const uint index,const int shift=0)
  {
   datetime dt=iTime(symbol,PERIOD_D1,index);
   if(dt==0)
      return(-1);
//---      
   datetime dt_start=dt+PeriodSeconds(PERIOD_H1)*shift-(TimeDayOfWeek(dt)==0?PeriodSeconds(PERIOD_D1)*2:0);
   datetime dt_stop=dt_start+PeriodSeconds(PERIOD_D1)-1;
//Print(dt," ",dt_start,"-",dt_stop);
//---
   double Arr[];
   if(CopyLow(symbol,PERIOD_H1,dt_start,dt_stop,Arr)>0)
      return Arr[ArrayMinimum(Arr)];
//---
   return(-1);
  }
//+------------------------------------------------------------------+
double iLow(const string symbol,const ENUM_TIMEFRAMES timeframe,const uint index)
  {
   double Arr[];
   if(CopyLow(symbol,timeframe,index,1,Arr)>0)
      return(Arr[0]);
   return(-1);
  }
//+------------------------------------------------------------------+
double iHigh(const string symbol,const ENUM_TIMEFRAMES timeframe,const uint index)
  {
   double Arr[];
   if(CopyHigh(symbol,timeframe,index,1,Arr)>0)
      return(Arr[0]);
   return(-1);
  }
//+------------------------------------------------------------------+
double iHighD1(const string symbol,const uint index,const int shift=0)
  {
   datetime dt=iTime(symbol,PERIOD_D1,index);
   if(dt==0)
      return(-1);
//---      
//sunday->friday
   datetime dt_start=dt+PeriodSeconds(PERIOD_H1)*shift-TimeDayOfWeek(dt)-(TimeDayOfWeek(dt)==0?PeriodSeconds(PERIOD_D1)*2:0);
   datetime dt_stop=dt_start+PeriodSeconds(PERIOD_D1)-1;
//Print(dt," ",dt_start,"-",dt_stop);
//---
   double Arr[];
   if(CopyHigh(symbol,PERIOD_H1,dt_start,dt_stop,Arr)>0)
      return Arr[ArrayMaximum(Arr)];
//---
   return(-1);
  }
//+------------------------------------------------------------------+
double iClose(const string symbol,const ENUM_TIMEFRAMES timeframe,const uint index)
  {
   double Arr[];
   if(CopyClose(symbol,timeframe,index,1,Arr)>0)
      return(Arr[0]);
   return(-1);
  }
//+------------------------------------------------------------------+
double iCloseD1(const string symbol,const uint index,const int shift=0)
  {
   datetime dt=iTime(symbol,PERIOD_D1,index);
   if(dt==0)
      return(-1);
//---      
   datetime dt_start=dt+PeriodSeconds(PERIOD_H1)*shift-(TimeDayOfWeek(dt)==0?PeriodSeconds(PERIOD_D1)*2:0);
   datetime dt_stop=dt_start+PeriodSeconds(PERIOD_D1)-1;
//Print(dt," ",dt_start,"-",dt_stop);
//---
   double Arr[];
   if(CopyClose(symbol,PERIOD_H1,dt_stop,1,Arr)>0)
      return Arr[0];
//---
   return(-1);
  }
//+------------------------------------------------------------------+
datetime iTime(const string symbol,const ENUM_TIMEFRAMES timeframe,const uint index)
  {
   datetime Arr[];
   if(CopyTime(symbol,timeframe,index,1,Arr)>0)
      return(Arr[0]);
   return(0);
  }
//+------------------------------------------------------------------+
datetime iTimeD1(const string symbol,const uint index,const int shift=0)
  {
   datetime Arr[];
   if(CopyTime(symbol,PERIOD_D1,index,1,Arr)>0)
      return Arr[0]+PeriodSeconds(PERIOD_H1)*shift;
   return(0);
  }
//+------------------------------------------------------------------+
datetime iTimeW1(const string symbol,const uint index,const int shift=0)
  {
   datetime Arr[];
   if(CopyTime(symbol,PERIOD_W1,index,1,Arr)>0)
      return Arr[0]+PeriodSeconds(PERIOD_H1)*shift+(TimeDayOfWeek(Arr[0])==0?PeriodSeconds(PERIOD_D1):0);
   return(0);
  }
//+------------------------------------------------------------------+
double iHighW1(const string symbol,const uint index,const int shift=0)
  {
   datetime dt=iTime(symbol,PERIOD_W1,index);
   if(dt==0)
      return(-1);
//---      
   datetime dt_start=dt+PeriodSeconds(PERIOD_H1)*shift+(TimeDayOfWeek(dt)==0?PeriodSeconds(PERIOD_D1):0);
   datetime dt_stop=dt_start+PeriodSeconds(PERIOD_D1)*5-1;
//Print(dt," ",dt_start,"-",dt_stop);
//---
   double Arr[];
   if(CopyHigh(symbol,PERIOD_H1,dt_start,dt_stop,Arr)>0)
      return Arr[ArrayMaximum(Arr)];
//---
   return(-1);
  }
//+------------------------------------------------------------------+
double iLowW1(const string symbol,const uint index,const int shift=0)
  {
   datetime dt=iTime(symbol,PERIOD_W1,index);
   if(dt==0)
      return(-1);
//---      
   datetime dt_start=dt+PeriodSeconds(PERIOD_H1)*shift+(TimeDayOfWeek(dt)==0?PeriodSeconds(PERIOD_D1):0);
   datetime dt_stop=dt_start+PeriodSeconds(PERIOD_D1)*5-1;
//Print(dt," ",dt_start,"-",dt_stop);
//---
   double Arr[];
   if(CopyLow(symbol,PERIOD_H1,dt_start,dt_stop,Arr)>0)
      return Arr[ArrayMinimum(Arr)];
//---
   return(-1);
  }
//+------------------------------------------------------------------+
double iCloseW1(const string symbol,const uint index,const int shift=0)
  {
   datetime dt=iTime(symbol,PERIOD_W1,index);
   if(dt==0)
      return(-1);
//---      
   datetime dt_start=dt+PeriodSeconds(PERIOD_H1)*shift+(TimeDayOfWeek(dt)==0?PeriodSeconds(PERIOD_D1):0);
   datetime dt_stop=dt_start+PeriodSeconds(PERIOD_D1)*5-1;
//Print(dt," ",dt_start,"-",dt_stop);
//---
   double Arr[];
   if(CopyClose(symbol,PERIOD_H1,dt_stop,1,Arr)>0)
      return Arr[0];
//---
   return(-1);
  }

const string PROGRAM_NAME=MQLInfoString(MQL_PROGRAM_NAME);

#define FOLDER_SCREEN "charts screenshot\\sphot\\"
double day_offset_pips=0;
double week_offset_pips=0;
//+------------------------------------------------------------------+
//|   OnInit                                                         |
//+------------------------------------------------------------------+
int OnInit()
  {
   bool futures=(SymbolInfoInteger(_Symbol,SYMBOL_TRADE_CALC_MODE)==SYMBOL_CALC_MODE_EXCH_FUTURES);
   if((_Digits==4 && futures) || (_Digits==5 && futures))
   {
      day_offset_pips=NormalizeDouble((double)InpDayOffsetPips/10000,5);
      week_offset_pips=NormalizeDouble((double)InpWeekOffsetPips/10000,5);
   }

   FolderCreate(FOLDER_SCREEN);

   CalculateCurrentDayMid();
   CalculateDayPositions();
   CalculateWeekPositions();

   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//|   OnDeinit                                                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   DeleteObjects();
   ChartRedraw();
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   ArraySetAsSeries(close,true);
   ArraySetAsSeries(open,true);
   ArraySetAsSeries(time,true);

   static datetime start_day_time=0;
   datetime now_day_time=iTimeD1(NULL,0,InpTimeShift);
   if(now_day_time==0)
      return(0);
//---
   bool new_check=false;
   static int check_count=0;
   if(start_day_time!=now_day_time)
     {
      start_day_time=now_day_time;

      check_count=0;
      ALessThan30PctZoneLow      = true;
      ALessThan30PctZoneHigh     = true;
      ALowExtremeStart           = true;
      AHighExtremeStart          = true;
      AMaxSLLow                  = true;
      AMaxSLHigh                 = true;
      ASDdn                      = true;
      ASDup                      = true;
      AWeekLessThan30PctZoneLow  = true;
      AWeekLessThan30PctZoneHigh = true;
      AWeekLowExtremeStart       = true;
      AWeekHighExtremeStart      = true;
      AWeekHighPctAreaLow        = true;
      AWeekHighPctAreaHigh       = true;
      AWSDdn                     = true;
      AWSDup                     = true;
     }

   static datetime recheck_time=0;
   if(check_count<30)
     {
      new_check=true;
      check_count++;
      recheck_time=TimeCurrent();
     }

   if(TimeCurrent()>=recheck_time+CheckSeconds)
     {
      new_check=true;
      recheck_time=TimeCurrent();
     }

   if(new_check)
     {
      CalculateCurrentDayMid();
      BuildCurrentDayMid();
      ChartRedraw();
     }

   static datetime bar_time=0;
   if(bar_time!=time[0])
     {
      bar_time=time[0];

      CalculateDayPositions();
      BuildDayObjects();

      CalculateWeekPositions();
      BuildWeekObjects();

      CalculateCurrentDayMid();
      BuildCurrentDayMid();
      ChartRedraw();
     }

   string DD30 = "Pips " + _Symbol + " = DAILY DOWN < 30%" + " @ " + DoubleToString(LessThan30PctZoneLow,_Digits);
   string DU30 = "Pips " + _Symbol + " = DAILY UP < 30%" + " @ " + DoubleToString(LessThan30PctZoneHigh,_Digits);
   string WD30 = "Pips " + _Symbol + " = WEEKLY DOWN < 30%" + " @ " + DoubleToString(WeekLessThan30PctZoneLow,_Digits);
   string WU30 = "Pips " + _Symbol + " = WEEKLY UP < 30%" + " @ " + DoubleToString(WeekLessThan30PctZoneHigh,_Digits);
   string DDES = "Pips " + _Symbol + " = DAILY DOWN Extreme Start" + " @ " + DoubleToString(LowExtremeStart,_Digits);
   string DUES = "Pips " + _Symbol + " = DAILY UP Extreme Start" + " @ " + DoubleToString(HighExtremeStart,_Digits);
   string WDES = "Pips " + _Symbol + " = WEEKLY DOWN Extreme Start" + " @ " + DoubleToString(WeekLowExtremeStart,_Digits);
   string WUES = "Pips " + _Symbol + " = WEEKLY UP Extreme Start" + " @ " + DoubleToString(WeekHighExtremeStart,_Digits);
   string DDSL = "Pips " + _Symbol + " = DAILY DOWN SL" + " @ " + DoubleToString(MaxSLLow,_Digits);
   string DUSL = "Pips " + _Symbol + " = DAILY UP SL" + " @ " + DoubleToString(MaxSLHigh,_Digits);
   string WDHA = "Pips " + _Symbol + " = WEEKLY DOWN High % Area" + " @ " + DoubleToString(WeekHighPctAreaLow,_Digits);
   string WUHA = "Pips " + _Symbol + " = WEEKLY UP High % Area" + " @ " + DoubleToString(WeekHighPctAreaHigh,_Digits);
   string SDDN = "Pips " + _Symbol + " = DAILY SD DOWN " + " @ " + DoubleToString(SDd+day_offset_pips,_Digits);
   string SDUP = "Pips " + _Symbol + " = DAILY SD UP " + " @ " + DoubleToString(SDu+day_offset_pips,_Digits);
   string SDWD = "Pips " + _Symbol + " = WEEKLY SD DOWN " + " @ " + DoubleToString(SDWd+week_offset_pips,_Digits);
   string SDWU = "Pips " + _Symbol + " = WEEKLY SD UP " + " @ " + DoubleToString(SDWu+week_offset_pips,_Digits);

   bool make_screen=false;
   bool play_sound=false;

   if(ALERT==true)
     {
      //---
      if(ALessThan30PctZoneLow==true && close[0]<LessThan30PctZoneLow && open[0]>=LessThan30PctZoneLow)
        {
         Alert(DD30);
         if(pushm)
            SendNotification(DD30);

         make_screen=true;
         play_sound=true;
         ALessThan30PctZoneLow=false;
        }
      //---
      if(ALessThan30PctZoneHigh==true && close[0]>LessThan30PctZoneHigh && open[0]<=LessThan30PctZoneHigh)
        {
         Alert(DU30);
         if(pushm)
            SendNotification(DU30);

         make_screen=true;
         play_sound=true;
         ALessThan30PctZoneHigh=false;
        }

      //---
      if(ALowExtremeStart==true && close[0]<LowExtremeStart && open[0]>=LowExtremeStart)
        {
         Alert(DDES);
         if(pushm)
            SendNotification(DDES);

         make_screen=true;
         play_sound=true;
         ALowExtremeStart=false;
        }

      //---
      if(AHighExtremeStart==true && close[0]>HighExtremeStart && open[0]<=HighExtremeStart)
        {
         Alert(DUES);
         if(pushm)
           {
            SendNotification(DUES);
           }
         make_screen=true;
         play_sound=true;
         AHighExtremeStart=false;
        }

      //---        
      if(AMaxSLLow==true && close[0]>MaxSLLow && open[0]<=MaxSLLow)
        {
         Alert(DDSL);
         if(pushm)
            SendNotification(DDSL);

         make_screen=true;
         play_sound=true;
         AMaxSLLow=false;
        }

      //---
      if(AMaxSLHigh==true && close[0]>MaxSLHigh && open[0]<=MaxSLHigh)
        {
         Alert(DUSL);
         if(pushm)
            SendNotification(DUSL);

         make_screen=true;
         play_sound=true;
         AMaxSLHigh=false;
        }

      //---        
      if(ASDdn==true && close[0]>SDd+day_offset_pips && open[0]<=SDd+day_offset_pips)
        {
         Alert(SDDN);
         if(pushm)
            SendNotification(SDDN);

         make_screen=true;
         play_sound=true;
         ASDdn=false;
        }

      //---
      if(ASDup==true && close[0]>SDu+day_offset_pips && open[0]<=SDu+day_offset_pips)
        {
         Alert(SDUP);
         if(pushm)
            SendNotification(SDUP);

         make_screen=true;
         play_sound=true;
         ASDup=false;
        }

      //---
      if(AWeekLessThan30PctZoneLow==true && close[0]<WeekLessThan30PctZoneLow && open[0]>=WeekLessThan30PctZoneLow)
        {
         Alert(WD30);
         if(pushm)
            SendNotification(WD30);

         make_screen=true;
         play_sound=true;
         AWeekLessThan30PctZoneLow=false;
        }

      //---        
      if(AWeekLessThan30PctZoneHigh==true && close[0]>WeekLessThan30PctZoneHigh && open[0]<=WeekLessThan30PctZoneHigh)
        {
         Alert(WU30);
         if(pushm)
            SendNotification(WU30);

         make_screen=true;
         play_sound=true;
         AWeekLessThan30PctZoneHigh=false;
        }

      //---        
      if(AWeekLowExtremeStart==true && close[0]<WeekLowExtremeStart && open[0]>=WeekLowExtremeStart)
        {
         Alert(WDES);
         if(pushm)
            SendNotification(WDES);

         make_screen=true;
         play_sound=true;
         AWeekLowExtremeStart=false;
        }

      //---        
      if(AWeekHighExtremeStart==true && close[0]>WeekHighExtremeStart && open[0]<=WeekHighExtremeStart)
        {
         Alert(WUES);
         if(pushm)
            SendNotification(WUES);

         make_screen=true;
         play_sound=true;
         AWeekHighExtremeStart=false;
        }

      //---        
      if(AWeekHighPctAreaLow==true && close[0]>WeekHighPctAreaLow && open[0]<=WeekHighPctAreaLow)
        {
         Alert(WDHA);
         if(pushm)
            SendNotification(WDHA);

         make_screen=true;
         play_sound=true;
         AWeekHighPctAreaLow=false;
        }

      //---      
      if(AWeekHighPctAreaHigh==true && close[0]>WeekHighPctAreaHigh && open[0]<=WeekHighPctAreaHigh)
        {
         Alert(WUHA);
         if(pushm)
            SendNotification(WUHA);

         make_screen=true;
         play_sound=true;
         AWeekHighPctAreaHigh=false;
        }

      //---
      if(AWSDdn==true && close[0]>SDWd+week_offset_pips && open[0]<=SDWd+week_offset_pips)
        {
         Alert(SDWD);
         if(pushm)
            SendNotification(SDWD);

         make_screen=true;
         play_sound=true;
         AWSDdn=false;
        }

      //---
      if(AWSDup==true && close[0]>SDWu+week_offset_pips && open[0]<=SDWu+week_offset_pips)
        {
         Alert(SDWU);
         if(pushm)
            SendNotification(SDWU);

         make_screen=true;
         play_sound=true;
         AWSDup=false;
        }

      //--- maker screenshot
      if(make_screen)
        {
         string file_name=FOLDER_SCREEN+_Symbol+"_"+PeriodDesc(_Period)+"_"+DateTimeReformat(TimeToString(TimeCurrent(),TIME_DATE))+"-"+DateTimeReformat(TimeToString(TimeCurrent(),TIME_SECONDS))+".png";
         if(ChartScreenShot(0,file_name,1920,1080,ALIGN_RIGHT))
            Print("Screenshot was saved ",file_name);
        }

      //--- play sound
      if(play_sound)
         PlaySound("news.wav");
     }
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+
void DeleteObjects()
  {
   int total=ObjectsTotal(0,0);
   for(int i=total-1; i>=0; i--)
     {
      string obj_name=ObjectName(0,i);
      if(StringFind(obj_name,PROGRAM_NAME)!=-1)
         ObjectDelete(0,obj_name);
     }
  }
//+------------------------------------------------------------------+
void CalculateDayPositions()
  {

   double PreviousDayHigh=iHighD1(NULL,1,InpTimeShift);
   double PreviousDayLow=iLowD1(NULL,1,InpTimeShift);
   double PreviousDayClose=iCloseD1(NULL,1,InpTimeShift);

   double PreviousDayRange=PreviousDayHigh-PreviousDayLow;
   Pivot=(PreviousDayHigh+PreviousDayLow+PreviousDayClose)/3;
   DayMid=PreviousDayLow+(PreviousDayRange/2);

   string sym2=StringSubstr(_Symbol,0,2);
   double DayStep=0.001;
   switch(_Digits)
     {
      case 7: DayStep=0.015625; break;
      case 6: DayStep=0.03125; break;
      case 5:
      case 4: DayStep=0.001; break;
      case 3: DayStep=0.1; break;
      default:
        {
         if(sym2=="EP" || sym2=="EN")
            DayStep=2.5;
         if(sym2=="YM")
            DayStep=10;
        }
      break;
     }

   TradeZoneStart=DayMid+(DayStep*TradeZoneS);
   TradeZoneEnd=DayMid+(DayStep*TradeZoneE);

   HighPctAreaLow=DayMid+(DayStep*LowP);
   HighPctAreaHigh=DayMid+(DayStep*HighP);

   LessThan30PctZoneLow=DayMid+(DayStep*Lt30pctZL);
   LessThan30PctZoneHigh=DayMid+(DayStep*Lt30pctZH);

   LowExtremeStart=DayMid+(DayStep*LowExtremeS);
   LowExtremeEnd=DayMid+(DayStep*LowExtremeE);

   HighExtremeStart=DayMid+(DayStep*HighExtremeS);
   HighExtremeEnd=DayMid+(DayStep*HighExtremeE);

   MaxSLLow=DayMid+(DayStep*LowSL);
   MaxSLHigh=DayMid+(DayStep*HighSL);
  }
//+------------------------------------------------------------------+
void CalculateWeekPositions()
  {
   double PreviousWeekHigh=iHighW1(NULL,1,InpTimeShift);
   double PreviousWeekLow=iLowW1(NULL,1,InpTimeShift);
   double PreviousWeekClose=iCloseW1(NULL,1,InpTimeShift);
   double PreviousWeekRange=PreviousWeekHigh-PreviousWeekLow;
//---
   WeekMid=PreviousWeekLow+(PreviousWeekRange/2);
   WeekMida=PreviousWeekLow+(PreviousWeekRange)*0.72;
   WeekMidb=PreviousWeekHigh-(PreviousWeekRange)*0.72;
   WeekMidaa=PreviousWeekLow+(PreviousWeekRange)*0.82;
   WeekMidbb=PreviousWeekHigh-(PreviousWeekRange)*0.82;
   WeekMidaaa=PreviousWeekLow+(PreviousWeekRange)*1.146;
   WeekMidbbb=PreviousWeekHigh-(PreviousWeekRange)*1.146;
   WeekMidaaaa=PreviousWeekLow+(PreviousWeekRange)*1.382;
   WeekMidbbbb=PreviousWeekHigh-(PreviousWeekRange)*1.382;
   WeekMidaaaaa=PreviousWeekLow+(PreviousWeekRange)*1.618;
   WeekMidbbbbb=PreviousWeekHigh-(PreviousWeekRange)*1.618;
   WeekMidc=PreviousWeekLow+(PreviousWeekRange)*2;
   WeekMidd=PreviousWeekHigh-(PreviousWeekRange)*2;
   WeekPivot=(PreviousWeekHigh+PreviousWeekLow+PreviousWeekClose)/3;
//---
   double WeekStep=0.001;
   switch(_Digits)
     {
      case 7: WeekStep=0.03125; break;
      case 6: WeekStep=0.0625; break;
      case 5:
      case 4: WeekStep=0.001; break;
      case 3: WeekStep=0.1; break;
      case 2: WeekStep=2.5; break;
      default:
         WeekStep=10; break;
     }

   WeekTradeZoneStart=WeekMid+(WeekStep*TradeZoneWS);
   WeekTradeZoneEnd=WeekMid+(WeekStep*TradeZoneWE);

   WeekHighPctAreaLow=WeekMid+(WeekStep*LowWP);
   WeekHighPctAreaHigh=WeekMid+(WeekStep*HighWP);

   WeekLessThan30PctZoneLow=WeekMid+(WeekStep*Lt30pctZWL);
   WeekLessThan30PctZoneHigh=WeekMid+(WeekStep*Lt30pctZWH);

   WeekLowExtremeStart=WeekMid+(WeekStep*LowExtremeWS);
   WeekLowExtremeEnd=WeekMid+(WeekStep*LowExtremeWE);

   WeekHighExtremeStart=WeekMid+(WeekStep*HighExtremeWS);
   WeekHighExtremeEnd=WeekMid+(WeekStep*HighExtremeWE);
  }
//+------------------------------------------------------------------+
void CalculateCurrentDayMid()
  {
   double CurrentHigh=iHighD1(NULL,0,InpTimeShift);
   double CurrentLow=iLowD1(NULL,0,InpTimeShift);
   double CurrentRange=CurrentHigh-CurrentLow;
   CurrentMid=CurrentLow+(CurrentRange/2);
   CurrentMida=CurrentLow+(CurrentRange/2)*1.618;
   CurrentMidb=CurrentHigh-(CurrentRange/2)*1.618;
  }
//+------------------------------------------------------------------+
void BuildDayObjects()
  {
   datetime time_d0= iTimeD1(NULL,0,InpTimeShift);
   //datetime time_d0= iTimeD1(NULL,0,0);
   datetime time1d = time_d0+PeriodSeconds(PERIOD_M5);
   datetime time2d = time_d0+PeriodSeconds(PERIOD_D1)-PeriodSeconds(PERIOD_M1);

   if(TradeZoneS!=TradeZoneE)
      DrawRectangle(PROGRAM_NAME+" TZ",time1d,TradeZoneStart,time2d,TradeZoneEnd,1,STYLE_SOLID,DTZColor,true);
   else
      DrawTrendLine(PROGRAM_NAME+" TZ",time1d,TradeZoneStart,time2d,TradeZoneEnd,5,STYLE_SOLID,DTZColor);

   DrawTrendLine(PROGRAM_NAME+" Pivot",time1d,Pivot,time2d,Pivot,1,STYLE_DOT,clrGray);
   DrawTrendLine(PROGRAM_NAME+" 1xM",time1d,DayMid,time2d,DayMid,2,STYLE_SOLID,MidColor);

   DrawTrendLine(PROGRAM_NAME+" SDdown",time1d,SDd+day_offset_pips,time2d,SDd+day_offset_pips,3,STYLE_SOLID,SDColor);
   DrawTrendLine(PROGRAM_NAME+" SDup",time1d,SDu+day_offset_pips,time2d,SDu+day_offset_pips,3,STYLE_SOLID,SDColor);

   DrawRectangle(PROGRAM_NAME+" HPA",time1d,HighPctAreaLow,time2d,HighPctAreaHigh,1,STYLE_SOLID,MZColor);
   DrawRectangle(PROGRAM_NAME+" LEx",time1d,LowExtremeStart,time2d,LowExtremeEnd,1,STYLE_SOLID,ExtremesColor,true);
   DrawRectangle(PROGRAM_NAME+" HEx",time1d,HighExtremeStart,time2d,HighExtremeEnd,5,STYLE_SOLID,ExtremesColor,true);

   DrawTrendLine(PROGRAM_NAME+" L3p",time1d,LessThan30PctZoneLow,time2d,LessThan30PctZoneLow,3,STYLE_SOLID,Z3Color);
   DrawTrendLine(PROGRAM_NAME+" H3p",time1d,LessThan30PctZoneHigh,time2d,LessThan30PctZoneHigh,3,STYLE_SOLID,Z3Color);
   DrawTrendLine(PROGRAM_NAME+" SLL",time1d,MaxSLLow,time2d,MaxSLLow,3,STYLE_SOLID,SLZoneColor);
   DrawTrendLine(PROGRAM_NAME+" SLH",time1d,MaxSLHigh,time2d,MaxSLHigh,3,STYLE_SOLID,SLZoneColor);
  }
//+------------------------------------------------------------------+
void BuildWeekObjects()
  {
   datetime time_w0=iTimeW1(NULL,0,InpTimeShift);
   datetime time1w=time_w0;
   datetime time2w=time_w0+PeriodSeconds(PERIOD_D1)*5;

   DrawRectangle(PROGRAM_NAME+" WTZ",time1w,WeekTradeZoneStart,time2w,WeekTradeZoneEnd,5,STYLE_SOLID,WTZColor,true);

   DrawTrendLine(PROGRAM_NAME+" 1xWP",time1w,WeekPivot,time2w,WeekPivot,1,STYLE_DOT,clrGray);
   DrawTrendLine(PROGRAM_NAME+" 1xWM",time1w,WeekMid,time2w,WeekMid,2,STYLE_SOLID,MidColor);
   DrawTrendLine(PROGRAM_NAME+" W SDdown",time1w,SDWd+week_offset_pips,time2w,SDWd+week_offset_pips,5,STYLE_SOLID,SDColor);
   DrawTrendLine(PROGRAM_NAME+" W SDup",time1w,SDWu+week_offset_pips,time2w,SDWu+week_offset_pips,5,STYLE_SOLID,SDColor);
   DrawTrendLine(PROGRAM_NAME+" 1xWM+72",time1w,WeekMida,time2w,WeekMida,1,STYLE_DASH,FibsColor);
   DrawTrendLine(PROGRAM_NAME+" 1xWM-72",time1w,WeekMidb,time2w,WeekMidb,1,STYLE_DASH,FibsColor);
   DrawTrendLine(PROGRAM_NAME+" 1xWM+82",time1w,WeekMidaa,time2w,WeekMidaa,1,STYLE_DASH,FibsColor);
   DrawTrendLine(PROGRAM_NAME+" 1xWM-82",time1w,WeekMidbb,time2w,WeekMidbb,1,STYLE_DASH,FibsColor);
   DrawTrendLine(PROGRAM_NAME+" 1xWM+114.6",time1w,WeekMidaaa,time2w,WeekMidaaa,1,STYLE_DASH,FibsColorEx);
   DrawTrendLine(PROGRAM_NAME+" 1xWM-114.6",time1w,WeekMidbbb,time2w,WeekMidbbb,1,STYLE_DASH,FibsColorEx);
   DrawTrendLine(PROGRAM_NAME+" 1xWM+138.2",time1w,WeekMidaaaa,time2w,WeekMidaaaa,1,STYLE_DASH,FibsColorEx);
   DrawTrendLine(PROGRAM_NAME+" 1xWM-138.2",time1w,WeekMidbbbb,time2w,WeekMidbbbb,1,STYLE_DASH,FibsColorEx);
   DrawTrendLine(PROGRAM_NAME+" 1xWM+161.8",time1w,WeekMidaaaaa,time2w,WeekMidaaaaa,1,STYLE_DASH,FibsColorEx);
   DrawTrendLine(PROGRAM_NAME+" 1xWM-161.8",time1w,WeekMidbbbbb,time2w,WeekMidbbbbb,1,STYLE_DASH,FibsColorEx);
   DrawTrendLine(PROGRAM_NAME+" 1xWM+200",time1w,WeekMidc,time2w,WeekMidc,1,STYLE_DASH,FibsColorEx);
   DrawTrendLine(PROGRAM_NAME+" 1xWM-200",time1w,WeekMidd,time2w,WeekMidd,1,STYLE_DASH,FibsColorEx);

   DrawRectangle(PROGRAM_NAME+" WHPA",time1w,WeekHighPctAreaLow,time2w,WeekHighPctAreaHigh,4,STYLE_SOLID,WMZColor);
   DrawRectangle(PROGRAM_NAME+" WLEx",time1w,WeekLowExtremeStart,time2w,WeekLowExtremeEnd,4,STYLE_SOLID,WExtremesColor);
   DrawRectangle(PROGRAM_NAME+" WHEx",time1w,WeekHighExtremeStart,time2w,WeekHighExtremeEnd,4,STYLE_SOLID,WExtremesColor);

   DrawTrendLine(PROGRAM_NAME+" WL3p",time1w,WeekLessThan30PctZoneLow,time2w,WeekLessThan30PctZoneLow,5,STYLE_SOLID,WZ3Color);
   DrawTrendLine(PROGRAM_NAME+" WH3p",time1w,WeekLessThan30PctZoneHigh,time2w,WeekLessThan30PctZoneHigh,5,STYLE_SOLID,WZ3Color);

  }
//+------------------------------------------------------------------+
void BuildCurrentDayMid()
  {
   datetime time1c = iTimeD1(NULL,0,InpTimeShift)+PeriodSeconds(PERIOD_D1);
   datetime time2c = iTimeD1(NULL,0,InpTimeShift)+PeriodSeconds(PERIOD_D1)+PeriodSeconds(PERIOD_H12);

   DrawTrendLine(PROGRAM_NAME+" CM",time1c,CurrentMid,time2c,CurrentMid,3,STYLE_SOLID,clrSteelBlue);
   DrawTrendLine(PROGRAM_NAME+" CM+61.8",time1c,CurrentMida,time2c,CurrentMida,3,STYLE_SOLID,clrSteelBlue);
   DrawTrendLine(PROGRAM_NAME+" CM-61.8",time1c,CurrentMidb,time2c,CurrentMidb,3,STYLE_SOLID,clrSteelBlue);
  }
//+------------------------------------------------------------------+  
void DrawTrendLine(const string name,
                   const datetime time1,
                   const double price1,
                   const datetime time2,
                   const double price2,
                   const int width,
                   const ENUM_LINE_STYLE style,
                   const color colour)
  {
   if(ObjectFind(0,name)<0)
     {
      if(ObjectCreate(0,name,OBJ_TREND,0,time1,price1,time2,price2))
        {
         ObjectSetInteger(0,name,OBJPROP_COLOR,colour);
         ObjectSetInteger(0,name,OBJPROP_BACK,true);
         ObjectSetInteger(0,name,OBJPROP_RAY,false);
         ObjectSetInteger(0,name,OBJPROP_STYLE,style);
         ObjectSetInteger(0,name,OBJPROP_WIDTH,width);
        }
     }
   else
     {
      ObjectMove(0,name,0,time1,price1);
      ObjectMove(0,name,1,time2,price2);
     }
  }
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void DrawRectangle(const string name,
                   const datetime time1,
                   const double price1,
                   const datetime time2,
                   const double price2,
                   const int width,
                   const ENUM_LINE_STYLE style,
                   const color colour,
                   const bool fill=false)
  {
   if(ObjectFind(0,name)<0)
     {
      if(ObjectCreate(0,name,OBJ_RECTANGLE,0,time1,price1,time2,price2))
        {
         ObjectSetInteger(0,name,OBJPROP_COLOR,colour);
         ObjectSetInteger(0,name,OBJPROP_BACK,true);
         ObjectSetInteger(0,name,OBJPROP_RAY,false);
         ObjectSetInteger(0,name,OBJPROP_STYLE,style);
         ObjectSetInteger(0,name,OBJPROP_WIDTH,width);
         ObjectSetInteger(0,name,OBJPROP_FILL,fill);
        }
     }
   else
     {
      ObjectMove(0,name,0,time1,price1);
      ObjectMove(0,name,1,time2,price2);
     }
  }
//+------------------------------------------------------------------+
