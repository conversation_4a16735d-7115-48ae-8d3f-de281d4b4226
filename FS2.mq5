#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict
#property indicator_buffers 12
#property indicator_plots   0
#property indicator_color1 clrBlue
#property indicator_color2 clrRed
#property indicator_color3 clrBlue
#property indicator_color4 clrRed
#property indicator_color5 clrBlue
#property indicator_color6 clrRed
#property indicator_color7 clrBlue
#property indicator_color8 clrRed
#property indicator_color9 clrBlue
#property indicator_color10 clrRed
#property indicator_color11 clrBlue
#property indicator_color12 clrRed

#define Name "fs2_"

//+INPUTS------------------------------------------------------------+
input int periods_                     = 8000;				  // Candles back to check
input int drawlines_                   = 8000;					  // Candles back to mark with trendlines
ENUM_TIMEFRAMES DROP_TF                = PERIOD_CURRENT; // Check every X period
static input ENUM_TIMEFRAMES divisor_  = PERIOD_CURRENT; // Initial active drawing period
static input int check_                = 10; //Number of boxes to draw for FVG/OB
input bool show50                      = false; // Show 50% line in OBs
input string initialize                = ""; //


static bool alloff                     = false; // Close all drawings
static bool fvg                        = false;
static bool ob                         = true;
static bool fr                         = true;
static input bool alerts_               = false; // Alerts ON/OFF initial
static bool clean                      = false; // Show only tradeable OB (3 conditions) ON/OFF initial
double dist                            = 0.25;
input string a                         = "";


static ENUM_TIMEFRAMES divisor; 
static int check; 

 int periods;
 int drawlines;
 bool alerts;

double finp[];
double finn[];
double linp[];
double linn[];
double obfinp[];
double obfinn[];
double oblinp[];
double oblinn[];
double obsinp[];
double obsinn[];
double fickp[];
double fickn[];

double newarrn[], newarrn1[], newarrp[], newarrp1[];

//+------------------------------------------------------------------+

//+INIT--------------------------------------------------------------+
int OnInit()
  {
  
   divisor = divisor_;
   check = check_;   
   periods = periods_;
   drawlines = drawlines_;
   alerts = alerts_;

   int k = -1;
	ObjectsDeleteAll(0, Name);
	SetIndexBuffer(++k, finp);
	SetIndexBuffer(++k, finn);
	SetIndexBuffer(++k, linp);
	SetIndexBuffer(++k, linn);
	SetIndexBuffer(++k, obfinp);
	SetIndexBuffer(++k, obfinn);
	SetIndexBuffer(++k, oblinp);
	SetIndexBuffer(++k, oblinn);
	SetIndexBuffer(++k, obsinp);
	SetIndexBuffer(++k, obsinn);
	SetIndexBuffer(++k, fickp);
	SetIndexBuffer(++k, fickn);
	
	if (ChartPeriod() < PERIOD_M30 || clean) check = 10;
	
	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+


//+------------------------------------------------------------------+
//| ChartEvent function |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
				  const long &lparam,
				  const double &dparam,
				  const string &sparam)
{
	//---
	{ //Currency select tables 1-4 button events
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + "TFa" + IntegerToString(0))
			{
			   divisor = PERIOD_M1;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + "TFa" + IntegerToString(1))
			{
			   divisor = PERIOD_M5;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + "TFa" + IntegerToString(2))
			{
			   divisor = PERIOD_M15;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + "TFa" + IntegerToString(3))
			{
			   divisor = PERIOD_M30;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + "TFb" + IntegerToString(0))
			{
			   divisor = PERIOD_H1;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + "TFb" + IntegerToString(1))
			{
			   divisor = PERIOD_H4;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + "TFb" + IntegerToString(2))
			{
			   divisor = PERIOD_D1;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + "TFb" + IntegerToString(3))
			{
			   divisor = PERIOD_W1;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + "CH" + IntegerToString(0))
			{
			   check = 2;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + "CH" + IntegerToString(1))
			{
			   check = 5;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + "CH" + IntegerToString(2))
			{
			   check = 10;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + "CH" + IntegerToString(3))
			{
			   check = 15;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == Name + "CH" + IntegerToString(4))
			{
			   check = 20;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && alloff == false)
		{
			if (sparam == Name + "OFF")
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			alloff = true;
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && alloff == true)
		{
			if (sparam == Name + "ON")
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			buttons();
   			alloff = false;
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && fvg == true)
		{
			if (sparam == Name + "FVGb" && ObjectGetInteger(0, Name + "FVGb", OBJPROP_COLOR) == clrBlue)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			fvg = false;
   			ObjectSetInteger(0, Name + "FVGb", OBJPROP_COLOR, clrRed);
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		else if (id == CHARTEVENT_OBJECT_CLICK && fvg == false)
		{
			if (sparam == Name + "FVGb" && ObjectGetInteger(0, Name + "FVGb", OBJPROP_COLOR) == clrRed)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			fvg = true;
   			ObjectSetInteger(0, Name + "FVGb", OBJPROP_COLOR, clrBlue);
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && ob == true)
		{
			if (sparam == Name + "OBb" && ObjectGetInteger(0, Name + "OBb", OBJPROP_COLOR) == clrBlue)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			ob = false;
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		else if (id == CHARTEVENT_OBJECT_CLICK && ob == false)
		{
			if (sparam == Name + "OBb" && ObjectGetInteger(0, Name + "OBb", OBJPROP_COLOR) == clrRed)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			ob = true;
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && fr == true)
		{
			if (sparam == Name + "FRb" && ObjectGetInteger(0, Name + "FRb", OBJPROP_COLOR) == clrBlue)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			fr = false;
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		else if (id == CHARTEVENT_OBJECT_CLICK && fr == false)
		{
			if (sparam == Name + "FRb" && ObjectGetInteger(0, Name + "FRb", OBJPROP_COLOR) == clrRed)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			fr = true;
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && alerts == false)
		{
			if (sparam == Name + "AL" && ObjectGetInteger(0, Name + "AL", OBJPROP_COLOR) == clrRed)
			{
   			ChartRedraw();
   			alerts = true;
			   buttons();
			}
		}
		else if (id == CHARTEVENT_OBJECT_CLICK && alerts == true)
		{
			if (sparam == Name + "AL" && ObjectGetInteger(0, Name + "AL", OBJPROP_COLOR) == clrGreen)
			{
   			ChartRedraw();
			   alerts = false;
			   buttons();
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && clean == false)
		{
			if (sparam == Name + "CL" && ObjectGetInteger(0, Name + "CL", OBJPROP_COLOR) == clrMagenta)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			clean = true;
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		else if (id == CHARTEVENT_OBJECT_CLICK && clean == true)
		{
			if (sparam == Name + "CL" && ObjectGetInteger(0, Name + "CL", OBJPROP_COLOR) == clrGreen)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			//RefreshRates();
   			clean = false;
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
   }
}

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN PROGRAM------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	datetime expiry = D'2024.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("FVG/OB expired on " + TimeToString(expiry, TIME_DATE) + ", contact sakisf on FF for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true)
	{

		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, DROP_TF, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, DROP_TF, 0);
		}
		if (new_1m_check)
		{
			ObjectsDeleteAll(0, Name);
			ChartRedraw();
			// RefreshRates();
			buttons();
			if (!alloff)
			{
			   if (fvg) checkpre();
			   if (ob) checkpre1();
   			if (fr) checkpre2();
			}
			new_1m_check = false;
		}
	} //YesStop (expiry) end
	   
	return (rates_total);
}

//+------------------------------------------------------------------+

//+BUTTONS CREATE FOR TF & NUMBER------------------------------------+
void buttons()
{

	string obname;
   string tfs[8] = { "1", "5", "15", "30", "H1", "H4", "D1", "W1" };
   string checks[5] = { "2", "5", "10", "15", "20" };
   
   for (int x = 3; x >= 0; x--)
   {
      obname = Name + "TFa" + IntegerToString(x);
      LabelMake(obname, 2, 110 - x * 25, 60, tfs[x], 10, clrWhite);
      obname = Name + "TFb" + IntegerToString(x);
      LabelMake(obname, 2, 110 - x * 25, 40, tfs[x + 4], 10, clrWhite);
   }
   
   for (int x = 4; x >= 0; x--)
   {
      obname = Name + "CH" + IntegerToString(x);
      LabelMake(obname, 2, 135 - x * 25, 20, checks[x], 10, clrWhite);
   }
   
   string bull[4] = { "-", "-", "-", "-" };
   for (int x = 2; x >= 0; x--)
   {
      obname = Name + "--" + IntegerToString(x);
      LabelMake(obname, 2, 118 - x * 25, 20, bull[x], 10, clrWhite);
   }
   
   obname = Name + "ON";
   LabelMake(obname, 2, 95, 80, "ON /", 10, clrWhite);
   obname = Name + "OFF";
   LabelMake(obname, 2, 68, 80, " OFF", 10, clrWhite);
   
   obname = Name + "FRb";
   LabelMake(obname, 2, 132, 100, " F /", 10, clrWhite);
   if (fr) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if (!fr) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + "FVGb";
   LabelMake(obname, 2, 100, 100, " V /", 10, clrWhite);
   if (fvg) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if (!fvg) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + "OBb";
   LabelMake(obname, 2, 68, 100, " O", 10, clrWhite);
   if (ob) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if (!ob) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + "AL";
   LabelMake(obname, 2, 30, 80, "AL", 10, clrWhite);
   if (alerts) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
   if (!alerts) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + "CL";
   LabelMake(obname, 2, 30, 100, "CL", 10, clrWhite);
   if (clean) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
   if (!clean) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION FRACTALS--------------------------------------------+
void checkpre()
{	
	if (iBars(_Symbol, divisor) < periods)
		periods = iBars(_Symbol, divisor) - 4;
	if (iBars(_Symbol, PERIOD_CURRENT) < periods)
	   periods = iBars(_Symbol, PERIOD_CURRENT) - 4;
	   
   if (divisor == PERIOD_CURRENT) divisor = ChartPeriod();

	string obname;

	double CD1[], HD1[], LD1[];
	datetime TD1[];
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(TD1, true);
	ArrayResize(CD1, periods + 3);
	ArrayResize(HD1, periods + 3);
	ArrayResize(LD1, periods + 3);
	ArrayResize(TD1, periods + 3);
	CopyClose(_Symbol, divisor, 0, periods + 3, CD1);
	CopyHigh(_Symbol, divisor, 0, periods + 3, HD1);
	CopyLow(_Symbol, divisor, 0, periods + 3, LD1);
	CopyTime(_Symbol, divisor, 0, periods + 3, TD1);
		
	for (int x = 1; x <= periods - 4; x++)
	{
		if ((CD1[x + 3] < CD1[x + 2]) && LD1[x + 1] - HD1[x + 3] > 0)
		{
			finp[x] = HD1[x + 3];
			linp[x] = LD1[x + 1];
		}
		else { finp[x] = EMPTY_VALUE; linp[x] = EMPTY_VALUE; }
	}

	for (int x = 1; x <= periods - 4; x++)
	{
		if ((CD1[x + 3] > CD1[x + 2]) && (LD1[x + 3] - HD1[x + 1] > 0))
		{
			finn[x] = LD1[x + 3];
			linn[x] = HD1[x + 1];
		}
		else { finn[x] = EMPTY_VALUE; linn[x] = EMPTY_VALUE; }
	}

	int countpos = 0, countneg = 0;
	for (int x = 1; x <= periods - 4; x++)
	{
		string intrepl = "b" + TimeToString(TD1[x + 3], TIME_DATE | TIME_MINUTES);
		double LL = CD1[ArrayMinimum(CD1, x, 1)];
		double LL1 = LD1[ArrayMinimum(LD1, x, 1)];
      if (countpos == check) break;
		if (finp[x] != EMPTY_VALUE && x < drawlines && LL > finp[x])
		{
		   countpos++;

		   obname = Name + "FVGrec" + intrepl;
		   RecMake(obname, finp[x], linp[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period(), clrPowderBlue, "FVG: " + DoubleToString(finp[x], _Digits) + " - " + DoubleToString(linp[x], _Digits) + " @ " + intrepl);
	      ObjectSetInteger(0, obname, OBJPROP_FILL, true);
		   ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
			if (LL1 <= linp[x] && LL > finp[x])
			{
   		   ObjectSetInteger(0, Name + "FVGrec" + intrepl, OBJPROP_COLOR, clrLightSkyBlue);

			}
		}
		if (finp[x] == EMPTY_VALUE)
		{
			ObjectDelete(0,Name + "FVGrec" + intrepl);
		}
	}

	for (int x = 1; x <= periods - 4; x++)
	{
		string intrepl = "s" + TimeToString(TD1[x + 3], TIME_DATE | TIME_MINUTES);
		double HH = CD1[ArrayMaximum(CD1, 1, x)];
		double HH1 = HD1[ArrayMaximum(HD1, 1, x)];
      if (countneg == check) break;
		if (finn[x] != EMPTY_VALUE && x < drawlines && HH < finn[x])
		{
		   countneg++;

		   obname = Name + "FVGrec" + intrepl;
		   RecMake(obname, linn[x], finn[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period(), C'255,191,223', "FVG: " + DoubleToString(finn[x], _Digits) + " - " + DoubleToString(linn[x], _Digits) + " @ " + intrepl);
	      ObjectSetInteger(0, obname, OBJPROP_FILL, true);
	      ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
			if (HH1 >= linn[x] && HH < finn[x])
			{
   		   ObjectSetInteger(0, Name + "FVGrec" + intrepl, OBJPROP_COLOR, C'255,170,213');

			}
		}
		if (finn[x] == EMPTY_VALUE)
		{
			ObjectDelete(0,Name + "FVGrec" + intrepl);
		}
	}
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION FRACTALS--------------------------------------------+
void checkpre1()
{	
	if (iBars(_Symbol, divisor) < periods)
		periods = iBars(_Symbol, divisor) - 4;
	if (iBars(_Symbol, PERIOD_CURRENT) < periods)
	   periods = iBars(_Symbol, PERIOD_CURRENT) - 4;
   if (divisor == PERIOD_CURRENT) divisor = ChartPeriod();
	string obname;

	double CD1[], OD1[], HD1[], LD1[];
	datetime TD1[];
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(OD1, true);
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(TD1, true);
	ArrayResize(CD1, periods + 3);
	ArrayResize(OD1, periods + 3);
	ArrayResize(HD1, periods + 3);
	ArrayResize(LD1, periods + 3);
	ArrayResize(TD1, periods + 3);
	CopyClose(_Symbol, divisor, 0, periods + 3, CD1);
	CopyOpen(_Symbol, divisor, 0, periods + 3, OD1);
	CopyHigh(_Symbol, divisor, 0, periods + 3, HD1);
	CopyLow(_Symbol, divisor, 0, periods + 3, LD1);
	CopyTime(_Symbol, divisor, 0, periods + 3, TD1);
	
   double Ask = SymbolInfoDouble(Symbol(),SYMBOL_ASK);
   double Bid = SymbolInfoDouble(Symbol(),SYMBOL_BID);
	
	for (int x = 1; x <= periods - 4; x++)
	{
		//if (((OD1[x + 3] > CD1[x + 3]) && ((CD1[x + 2] - OD1[x + 2]) > (OD1[x + 3] - CD1[x + 3]))) || ((OD1[x + 3] > CD1[x + 3]) && ((CD1[x + 1] - OD1[x + 2]) > (OD1[x + 3] - CD1[x + 3]))))
		if ((OD1[x + 3] > CD1[x + 3]) && (((CD1[x + 2] > OD1[x + 2]) && (CD1[x + 1] > OD1[x + 1]) && ((CD1[x + 1] - OD1[x + 2]) > (OD1[x + 3] - CD1[x + 3]))) || ((CD1[x + 2] > OD1[x + 2]) && ((CD1[x + 2] - OD1[x + 2]) > (OD1[x + 3] - CD1[x + 3])))))
		{
		   //Print(periods + " " + iBars(_Symbol, divisor) + " " + iBars(_Symbol, PERIOD_CURRENT));
			obfinp[x] = LD1[x + 3];
			oblinp[x] = HD1[x + 3];
		}
		else { obfinp[x] = EMPTY_VALUE; oblinp[x] = EMPTY_VALUE; obsinp[x] = EMPTY_VALUE; }
	}

	for (int x = 1; x <= periods - 4; x++)
	{
		if ((CD1[x + 3] > OD1[x + 3]) && (((OD1[x + 2] > CD1[x + 2]) && (OD1[x + 1] > CD1[x + 1]) && ((OD1[x + 2] - CD1[x + 1]) > (CD1[x + 3] - OD1[x + 3]))) || ((OD1[x + 2] > CD1[x + 2]) && ((OD1[x + 2] - CD1[x + 2]) > (CD1[x + 3] - OD1[x + 3])))))
		{
			obfinn[x] = HD1[x + 3];
			oblinn[x] = LD1[x + 3];
		}
		else { obfinn[x] = EMPTY_VALUE; oblinn[x] = EMPTY_VALUE; obsinn[x] = EMPTY_VALUE; }
	}
   
   int countpos = 0, countneg = 0;
   int z = 0, w = 0;
   int countnewp = 0;
   double newarrayp[];
   double newarrayp1[];
   int countnewn = 0;
   double newarrayn[];
   double newarrayn1[];
   
   double LLa[];
   ArrayResize(LLa, periods);
   
	for (int x = 1; x <= periods - 4; x++)
	{
		string intrepl = "b" + TimeToString(TD1[x + 3], TIME_DATE | TIME_MINUTES);
		double LL = CD1[ArrayMinimum(CD1, 1, x)];
		double LL1 = LD1[ArrayMinimum(LD1, 1, x)];
		double HH1 = HD1[ArrayMaximum(HD1, 1, x)];
		//ArrayFree(LLa);
		ArrayCopy(LLa, CD1, 1, 1, x);
		ArraySort(LLa);
		
		//double LLa1 = LLa[0];
		//double LLa2 = LLa[1];
		double LLa3 = LLa[2];
		
		if (countpos == check) break;
      if (obfinp[x] != EMPTY_VALUE && x < drawlines && LLa3 > obfinp[x])
		{
	      z += 4;
		   countpos++;

   		double maxgo = HH1 - oblinp[x];
   		double size = oblinp[x] - obfinp[x];
   		double rr = MathFloor(maxgo / size);
   		
   		bool newton = false;
   		bool moonsoon = false;
   		bool lgrab = false;
         if (x > 2) { if (LD1[x + 2] < LD1[x + 3] || LD1[x + 1] < LD1[x + 3]) lgrab = true; }
   		if(x > 2 && obfinp[x] != EMPTY_VALUE)
   		{
      		for (int y = x + 3; y >= x; y--)
      		{
   		      double hh = MathMax(HD1[y - 1], HD1[y - 2]);
   		      double hh1 = HD1[ArrayMaximum(HD1, 50, y)];
         		if (CD1[y] < CD1[y - 2] && (hh - oblinp[x]) > 2 * (oblinp[x] - obfinp[x]))
         		{
         		   newton = true;
         		}
         		if (HH1 > hh1) moonsoon = true;
		//Print(obfinp[x] + " " + oblinp[x] + " " + hh1 + " "  + HH1 + " " + y + " " + x);
            }
         }
   		
		   obname = Name + "OBsl" + intrepl;
		   Texter(obname, LD1[x + 3], 3 * Period(), IntegerToString(z / 4) + " " + DoubleToString(rr, 0), clrMediumOrchid);
		   ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		   ObjectSetString(0, Name + "OBsl" + intrepl, OBJPROP_TOOLTIP, IntegerToString(z / 4) + " " + DoubleToString(oblinp[x], _Digits) + " rr: " + DoubleToString(rr, 0));
		   if (Bid < oblinp[x] || iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 10, 1)) < oblinp[x]) 
		   {
		      obname = Name + "OBTouch" + intrepl;
		      RecMake(obname, oblinp[x], obfinp[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period(), clrDodgerBlue, "OB: " + DoubleToString(obfinp[x], _Digits) + " - " + DoubleToString(oblinp[x], _Digits) + " @ " + intrepl);
		      ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		      ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
		   }
		   obname = Name + "OBrec" + intrepl;
		   objtrend2(obname, oblinp[x], oblinp[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period(), 1, 0, clrWhite, "OB: " + DoubleToString(oblinp[x], _Digits) + " - " + DoubleToString(obfinp[x], _Digits) + " @ " + intrepl);
		   obname = Name + "OBreca" + intrepl;
		   RecMake(obname, oblinp[x], obfinp[x], z/*iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false)*/, 0, 3 * Period(), clrWhite, "OB: " + DoubleToString(oblinp[x], _Digits) + " - " + DoubleToString(obfinp[x], _Digits) + " @ " + intrepl);
		   ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		   //if (rr >= 3)
			//ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_BACK, true);
		   ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
		   if (show50)
		   {
		      obname = Name + "50pc" + intrepl;
		      objtrend2(obname, (oblinp[x] + obfinp[x]) / 2, (oblinp[x] + obfinp[x]) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period(), 1, 2, clrWhite, "50%");
		   }
			if (LL < oblinp[x] || rr <= 2)
			{
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_WIDTH, 1);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 1);
			}
			if (LL > oblinp[x] && LL1 <= oblinp[x] && LL1 > obfinp[x])
			{
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_WIDTH, 2);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 2);
			}
			if (LL1 > oblinp[x] && rr >= 3)
			{
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_COLOR, clrYellow);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrYellow);
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_WIDTH, 2);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 2);
			}
			if (clean)
			{
			   //Print("A" + " " + newton + " " + moonsoon + " " + rr);
   			if ((!newton || !moonsoon) || rr < 3)
      			{
         			ObjectDelete(0, Name + "OBrec" + intrepl);
         			ObjectDelete(0, Name + "OBreca" + intrepl);
         			ObjectDelete(0, Name + "OBTouch" + intrepl);
         			ObjectDelete(0, Name + "OBsl" + intrepl);
      			}
			}	
			if ((newton && moonsoon) && rr >= 3)
			{
			   //Print("A");
			   countnewp++;
			   ArrayResize(newarrayp, countnewp + 1);
			   ArrayResize(newarrayp1, countnewp + 1);
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_COLOR, clrGreen);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrGreen);
			   ObjectSetInteger(0, Name + "OBTouch" + intrepl, OBJPROP_COLOR, clrGreen);
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_TIME,2, iTime(_Symbol,PERIOD_CURRENT,0) + 5 * Period());
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_TIME,2, iTime(_Symbol,PERIOD_CURRENT,0) + 5 * Period());
			   ObjectSetInteger(0, Name + "OBTouch" + intrepl, OBJPROP_TIME,2, iTime(_Symbol,PERIOD_CURRENT,0) + 5 * Period());
			   ObjectSetInteger(0, Name + "OBsl" + intrepl, OBJPROP_TIME, iTime(_Symbol,PERIOD_CURRENT,0) + 5 * Period());
			   newarrayp[countnewp] = oblinp[x];
			   newarrayp1[countnewp] = obfinp[x];
			   newton = false;
			   moonsoon = false;
			   if (lgrab)
			   {
			      ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrDimGray);
			      ObjectSetInteger(0, Name + "OBsl" + intrepl, OBJPROP_COLOR, clrDimGray);
			   }
			   if (LL1 < obfinp[x])
			   {
			      ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrBlack);
			      ObjectSetInteger(0, Name + "OBsl" + intrepl, OBJPROP_COLOR, clrBlack);
			      ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 2);
			   }
			   lgrab = false;
			}
		}
		if (obfinp[x] == EMPTY_VALUE)
		{
		   ObjectDelete(0,Name + "OBrec" + intrepl);
			ObjectDelete(0,Name + "OBTouch" + intrepl);
			ObjectDelete(0,Name + "50pc" + intrepl);
			ObjectDelete(0,Name + "OBsl" + intrepl);
			ObjectDelete(0,Name + "OBreca" + intrepl);
		}
	}
	//Print(ArraySize(newarrayp) +  " " + newarrayp[0] + " " + newarrayp[1] + " " + newarrayp[ArraySize(newarrayp) - 1]);
	if (ArraySize(newarrayp) > 0) { if (((LD1[1] - newarrayp[1]) < 10 * _Point) && alerts) lert(_Symbol, "near OB support", newarrayp[1], divisor); }
   
   double LLb[];
   ArrayResize(LLb, periods);
   
	for (int x = 1; x <= periods - 4; x++)
	{
		string intrepl = "s" + TimeToString(TD1[x + 3], TIME_DATE | TIME_MINUTES);
		double HH = CD1[ArrayMaximum(CD1, 1, x)];
		double HH1 = HD1[ArrayMaximum(HD1, 1, x)];
		double LL1 = LD1[ArrayMinimum(LD1, 1, x)];
      if (countneg == check) break;
      //ArrayFree(LLb);
		ArrayCopy(LLb, CD1, 1, 1, x);
		ArraySort(LLb);
		ArrayReverse(LLb);
		
		//double LLb1 = LLb[0];
		//double LLb2 = LLb[1];
		double LLb3 = LLb[2];
		if (obfinn[x] != EMPTY_VALUE && x < drawlines && LLb3 < obfinn[x])
		{
		   w += 4;
		   countneg++;

   		double maxgo = oblinn[x] - LL1;
   		double size = obfinn[x] - oblinn[x];
   		double rr = MathFloor(maxgo / size);
   		
   		bool newton = false;
   		bool moonsoon = false;
   		bool lgrab = false;
         if (x > 2) { if (HD1[x + 2] > HD1[x + 3] || HD1[x + 1] > HD1[x + 3]) lgrab = true; }
   		if(x > 2 && obfinn[x] != EMPTY_VALUE)
   		{
      		for (int y = x + 3; y >= x; y--)
      		{
   		      double ll = MathMin(LD1[y - 1], LD1[y - 2]);
   		      double ll1 = LD1[ArrayMinimum(LD1, y, 50)];
         		if (CD1[y] > CD1[y - 2] && (oblinn[x] - ll) > 2 * (obfinn[x] - oblinn[x]))
         		{
         		   newton = true;
         		}
         		if (LL1 < ll1) moonsoon = true;
            }
         }

		   obname = Name + "OBsl" + intrepl;
		   Texter(obname, HD1[x + 3], 3 * Period(), IntegerToString(w / 4) + " " + DoubleToString(rr, 0), clrMediumOrchid);
		   ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		   ObjectSetString(0, Name + "OBsl" + intrepl, OBJPROP_TOOLTIP, IntegerToString(w / 4) + " " + DoubleToString(oblinn[x], _Digits) + " rr: "  + DoubleToString(rr, 0));
		   if (Bid > oblinn[x] || iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 10, 1)) > oblinn[x])
		   {
		      obname = Name + "OBTouch" + intrepl;
		      RecMake(obname, obfinn[x], oblinn[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period(), clrOrangeRed, "OB: " + DoubleToString(obfinn[x], _Digits) + " - " + DoubleToString(oblinn[x], _Digits) + " @ " + intrepl);
		      ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		      ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
		   }
		   obname = Name + "OBrec" + intrepl;
		   objtrend2(obname, oblinn[x], oblinn[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period(), 1, 0, clrWhite, "OB: " + DoubleToString(oblinn[x], _Digits) + " - " + DoubleToString(obfinn[x], _Digits) + " @ " + intrepl);
		   obname = Name + "OBreca" + intrepl;
		   RecMake(obname, obfinn[x], oblinn[x], w/*iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false)*/, 0, 3 * Period(), clrWhite, "OB: " + DoubleToString(oblinn[x], _Digits) + " - " + DoubleToString(obfinn[x], _Digits) + " @ " + intrepl);
		   ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		   //if (rr >= 3)
			//ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_BACK, true);
		   ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
		   if (show50)
		   {
   		   obname = Name + "50pc" + intrepl;
   		   objtrend2(obname, (oblinn[x] + obfinn[x]) / 2, (oblinn[x] + obfinn[x]) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period(), 1, 2, clrWhite, "50%");
		   }
			if (HH > oblinn[x] || rr <= 2)
			{
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_WIDTH, 1);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 1);
			}
			if (HH < oblinn[x] && HH1 >= oblinn[x] && HH1 < obfinn[x])
			{
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_WIDTH, 2);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 2);
			}
			if (HH1 < oblinn[x] && rr >= 3)
			{
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_COLOR, clrYellow);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrYellow);
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_WIDTH, 2);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 2);
			}
			if (clean)
			{
   			if ((!newton || !moonsoon) || rr < 3)
      			{
         			ObjectDelete(0, Name + "OBrec" + intrepl);
         			ObjectDelete(0, Name + "OBreca" + intrepl);
         			ObjectDelete(0, Name + "OBTouch" + intrepl);
         			ObjectDelete(0, Name + "OBsl" + intrepl);
      			}
			}
			if ((newton && moonsoon) && rr >= 3)
			{
			   countnewn++;
			   ArrayResize(newarrayn, countnewn + 1);
			   ArrayResize(newarrayn1, countnewn + 1);
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_COLOR, clrMagenta);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrMagenta);
			   ObjectSetInteger(0, Name + "OBTouch" + intrepl, OBJPROP_COLOR, clrMagenta);
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_TIME,2, iTime(_Symbol,PERIOD_CURRENT,0) + 5 * Period());
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_TIME,2, iTime(_Symbol,PERIOD_CURRENT,0) + 5 * Period());
			   ObjectSetInteger(0, Name + "OBTouch" + intrepl, OBJPROP_TIME,2, iTime(_Symbol,PERIOD_CURRENT,0) + 5 * Period());
			   ObjectSetInteger(0, Name + "OBsl" + intrepl, OBJPROP_TIME, iTime(_Symbol,PERIOD_CURRENT,0) + 5 * Period());
			   newarrayn[countnewn] = oblinn[x];
			   newarrayn1[countnewn] = obfinn[x];
			   newton = false;
			   moonsoon = false;
			   if (lgrab)
			   {
			      ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrDimGray);
			      ObjectSetInteger(0, Name + "OBsl" + intrepl, OBJPROP_COLOR, clrDimGray);
			   }
			   if (HH1 > obfinn[x]) 
			   {
			      ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrBlack);
			      ObjectSetInteger(0, Name + "OBsl" + intrepl, OBJPROP_COLOR, clrBlack);
			      ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 2);
			   }
			   lgrab = false;
			}
		}
		if (obfinn[x] == EMPTY_VALUE)
		{
		   ObjectDelete(0,Name + "OBrec" + intrepl);
			ObjectDelete(0,Name + "OBTouch" + intrepl);
			ObjectDelete(0,Name + "50pc" + intrepl);
			ObjectDelete(0,Name + "OBsl" + intrepl);
		   ObjectDelete(0,Name + "OBreca" + intrepl);
		}
	}
	//Print(ArraySize(newarrayn) +  " " + newarrayn[0] + " " + newarrayn[1] + " " + newarrayn[ArraySize(newarrayn) - 1]);
	if (ArraySize(newarrayn) > 0) { if (((newarrayn[1] - HD1[1]) < 10 * _Point) && alerts) lert(_Symbol, "near OB resistance", newarrayn[1], divisor); }
	
	ArrayCopy(newarrn, newarrayn, 0, 0, 0);
	ArrayCopy(newarrn1, newarrayn1, 0, 0, 0);
	ArrayCopy(newarrp, newarrayp, 0, 0, 0);
	ArrayCopy(newarrp1, newarrayp1, 0, 0, 0);
	
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION FRACTALS--------------------------------------------+
void checkpre2()
{
	if (iBars(_Symbol, divisor) < periods)
		periods = iBars(_Symbol, divisor) - 4;
	if (iBars(_Symbol, PERIOD_CURRENT) < periods)
	   periods = iBars(_Symbol, PERIOD_CURRENT) - 4;
   if (divisor == PERIOD_CURRENT) divisor = ChartPeriod();
	string obname;
	
	double nexp[], prep[];
	double nexn[], pren[];
	ArrayResize(prep, periods + 2);
	ArrayResize(nexp, periods + 1);
	ArrayResize(pren, periods + 2);
	ArrayResize(nexn, periods + 1);
	ArrayInitialize(fickp, EMPTY_VALUE);
	ArrayInitialize(fickn, EMPTY_VALUE);

	double CD1[], OD1[], HD1[], LD1[];
	datetime TD1[];
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(OD1, true);
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(TD1, true);
	ArrayResize(CD1, periods + 3);
	ArrayResize(OD1, periods + 3);
	ArrayResize(HD1, periods + 3);
	ArrayResize(LD1, periods + 3);
	ArrayResize(TD1, periods + 3);
	CopyClose(_Symbol, divisor, 0, periods + 3, CD1);
	CopyOpen(_Symbol, divisor, 0, periods + 3, OD1);
	CopyHigh(_Symbol, divisor, 0, periods + 3, HD1);
	CopyLow(_Symbol, divisor, 0, periods + 3, LD1);
	CopyTime(_Symbol, divisor, 0, periods + 3, TD1);

	for (int x = 1; x <= periods; x++)
	{
		if ((CD1[x + 1] - OD1[x + 1]) > 0 && (CD1[x] - OD1[x]) > 0)
		{
			prep[x] = CD1[x + 1] - OD1[x + 1];
			nexp[x] = OD1[x] - LD1[x];
			if (nexp[x] < dist * prep[x] && OD1[x + 1] + 0.75 * prep[x] < LD1[x] && (CD1[ArrayMinimum(CD1, 1, x - 1)] >= LD1[x + 1]))
				fickp[x] = LD1[x] - 40 * _Point;
			else
				fickp[x] = EMPTY_VALUE;
		}
	}

	for (int x = 1; x <= periods; x++)
	{
		if ((OD1[x + 1] - CD1[x + 1]) > 0 && (OD1[x] - CD1[x]) > 0)
		{
			pren[x] = OD1[x + 1] - CD1[x + 1];
			nexn[x] = HD1[x] - OD1[x];
			if (nexn[x] < dist * pren[x] && OD1[x + 1] - 0.75 * pren[x] > HD1[x] && (CD1[ArrayMaximum(CD1, 1, x - 1)] <= HD1[x + 1]))
				fickn[x] = HD1[x] + 40 * _Point;
			else
				fickn[x] = EMPTY_VALUE;
		}
	}
	
	int countpos = 0, countneg = 0;

   double LLc[];
   ArrayResize(LLc, periods + 1);
   
	for (int x = 1; x <= periods; x++)
	{
		string intrepl = "s" + TimeToString(TD1[x + 1], TIME_DATE | TIME_MINUTES);
		double LL = LD1[ArrayMinimum(LD1, 1, x - 1)];
		if (countpos == check) break;
		//ArrayFree(LLc);
		ArrayCopy(LLc, CD1, 1, 1, x);
		ArraySort(LLc);
		
		double LLc3 = LLc[2];
		
		if (fickp[x] != EMPTY_VALUE && x < drawlines && (LLc3 >= LD1[x + 1]))// && OD1[x + 1] <= LL) (CD1[ArrayMinimum(CD1, x - 1, 1)] >= LD1[x + 1]))// && OD1[x + 1] <= LL)
		{
		   countpos++;
		   obname = Name + "BasLin" + intrepl;
			objtrend2(obname, OD1[x + 1], OD1[x + 1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 1), false), 0, 3 * Period(), 1, 0, clrWhite, "OSup1");
			obname = Name + "Lin" + intrepl;
			objtrend2(obname, LD1[x + 1], LD1[x + 1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 1), false), 0, 3 * Period(), 2, 0, clrWhite, "LSup2");
			obname = Name + "ConLin" + intrepl;
   		objtrend2(obname, LD1[x + 1], OD1[x + 1], 4, 0, 3 * Period(), 2, 0, clrWhite, "SCon");
   		if (LL > OD1[x + 1])
   		{
   		   ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_COLOR, clrGreen);
   		   ObjectDelete(0, Name + "Lin" + intrepl);
   		   ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_COLOR, clrGreen);
   		   ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_WIDTH, 1);
   		}
		}
		else if (fickp[x] == EMPTY_VALUE)
		{
			ObjectDelete(0,Name + "BasLin" + intrepl);
			ObjectDelete(0,Name + "Lin" + intrepl);
			ObjectDelete(0,Name + "ConLin" + intrepl);
		}
	}

   double LLd[];
   ArrayResize(LLd, periods + 1);
   
	for (int x = 1; x <= periods; x++)
	{
		string intrepl = "b" + TimeToString(TD1[x + 1], TIME_DATE | TIME_MINUTES);
		double HH = HD1[ArrayMaximum(HD1, 1, x - 1)];
		if (countneg == check) break;
		//ArrayFree(LLd);
		ArrayCopy(LLd, CD1, 1, 1, x);
		ArraySort(LLd);
		ArrayReverse(LLd);
		
		double LLd3 = LLd[2];
		
		if (fickn[x] != EMPTY_VALUE && x < drawlines && (LLd3 <= HD1[x + 1]))// && OD1[x + 1] >= HH) //(CD1[ArrayMaximum(CD1, x - 1, 1)] <= HD1[x + 1]))// && OD1[x + 1] >= HH)
		{
		   countneg++;
			obname = Name + "BasLin" + intrepl;
			objtrend2(obname, OD1[x + 1], OD1[x + 1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 1), false), 0, 3 * Period(), 1, 0, clrWhite, "ORes1");
			obname = Name + "Lin" + intrepl;
			objtrend2(obname, HD1[x + 1], HD1[x + 1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 1), false), 0, 3 * Period(), 2, 0, clrWhite, "HRes2");
			obname = Name + "ConLin" + intrepl;
			objtrend2(obname, HD1[x + 1], OD1[x + 1], 4, 0, 3 * Period(), 2, 0, clrWhite, "RCon");
   		if (HH < OD1[x + 1])
   		{
   		   ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_COLOR, clrGreen);
   		   ObjectDelete(0, Name + "Lin" + intrepl);
   		   ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_COLOR, clrGreen);
   		   ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_WIDTH, 1);
   		}
		}
		else if (fickn[x] == EMPTY_VALUE)
		{
			ObjectDelete(0,Name + "BasLin" + intrepl);
			ObjectDelete(0,Name + "Lin" + intrepl);
			ObjectDelete(0,Name + "ConLin" + intrepl);
		}
	}
	ArrayResize(newarrn, check + 1);
	ArrayResize(newarrn1, check + 1);
	ArrayResize(newarrp, check + 1);
	ArrayResize(newarrp1, check + 1);
	
	int xcount1 = 0, xcount2 = 0;
	
   double newarrayp[];
   int countnewp = 0;
	for (int x = 1; x < periods; x++)
	{
	   string intrepl = "s" + TimeToString(TD1[x + 1], TIME_DATE | TIME_MINUTES);
	   if (fickp[x] != EMPTY_VALUE)
	   {
	      for (int y = 1; y < check; y++)
	      {
            if ((OD1[x + 1] < newarrp[y] && OD1[x + 1] > newarrp1[y]) || (LD1[x + 1] < newarrp[y] && LD1[x + 1] > newarrp1[y]))
            {
   			   countnewp++;
   			   ArrayResize(newarrayp, countnewp + 1);
               ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_COLOR, clrSienna);
               ObjectSetInteger(0, Name + "Lin" + intrepl, OBJPROP_COLOR, clrSienna);
               ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_COLOR, clrSienna);
               ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_WIDTH, 2);
			      newarrayp[countnewp] = OD1[x + 1];
            }
	      }
	   }
	}
	if (ArraySize(newarrayp) > 0) { if (((LD1[1] - newarrayp[1]) < 10 * _Point) && alerts) lert(_Symbol, "near FR support", newarrayp[1], divisor); }
	
   double newarrayn[];
   int countnewn = 0;
	for (int x = 1; x < periods; x++)
	{
	   string intrepl = "b" + TimeToString(TD1[x + 1], TIME_DATE | TIME_MINUTES);
	   if (fickn[x] != EMPTY_VALUE)
	   {
	      for (int y = 1; y < check; y++)
	      {
            if ((OD1[x + 1] < newarrn1[y] && OD1[x + 1] > newarrn[y]) || (HD1[x + 1] < newarrn1[y] && HD1[x + 1] > newarrn[y]))
            {
   			   countnewn++;
   			   ArrayResize(newarrayn, countnewn + 1);
               ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_COLOR, clrSienna);
               ObjectSetInteger(0, Name + "Lin" + intrepl, OBJPROP_COLOR, clrSienna);
               ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_COLOR, clrSienna);
               ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_WIDTH, 2);
			      newarrayn[countnewn] = OD1[x + 1];
            }
	      }
	   }  
	}
	if (ArraySize(newarrayn) > 0) { if (((newarrayn[1]) - HD1[1] < 10 * _Point) && alerts) lert(_Symbol, "near FR resistance", newarrayn[1], divisor); }
}
//+------------------------------------------------------------------+

void lert(string symbol, string text, double price, int period)
{
   Alert(symbol, " ", text, " ", DoubleToString(price, _Digits), " TF: ", IntegerToString(period));
}


//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0,name, OBJPROP_TIME, iTime(_Symbol,PERIOD_CURRENT,t1));
	ObjectSetInteger(0,name, OBJPROP_TIME,1, iTime(_Symbol,PERIOD_CURRENT,t2) + t3);
	ObjectSetDouble(0,name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0,name, OBJPROP_PRICE,1, pr2);
	ObjectSetInteger(0,name, OBJPROP_STYLE, st);
	ObjectSetInteger(0,name, OBJPROP_WIDTH, wi);
	ObjectSetInteger(0,name, OBJPROP_RAY, false);
	ObjectSetInteger(0,name, OBJPROP_BACK, true);
	ObjectSetInteger(0,name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToString(pr1, _Digits) + " Date: " + TimeToString(iTime(_Symbol,PERIOD_CURRENT,t1), TIME_DATE));
}
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const double pr1, const double pr2, const int t1, const int t2, const int t3, const color BCol, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0,name, OBJPROP_TIME, iTime(_Symbol,PERIOD_CURRENT,t1));
	ObjectSetInteger(0,name, OBJPROP_TIME,1, iTime(_Symbol,PERIOD_CURRENT,t2) + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE,1, pr2);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

/*
//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett) {
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Time[0] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
	ObjectSetInteger(0, name, OBJPROP_STYLE, st);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
	ObjectSetInteger(0, name, OBJPROP_RAY, false);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToString(pr1, _Digits) + " Date: " + TimeToString(Time[t1], TIME_DATE));
}
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const double pr1, const double pr2, const int t1, const int t2, const int t3, const color BCol, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME,1, Time[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE,1, pr2);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+
*/

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const int y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE,1, x);
	ObjectSetInteger(0, name, OBJPROP_TIME,1, iTime(_Symbol,PERIOD_CURRENT,0) + y);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 8);
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToString(x, _Digits));
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	//ObjectSetText(name, label, FSize, "Arial", FCol);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetString(0, name, OBJPROP_TEXT, label);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+