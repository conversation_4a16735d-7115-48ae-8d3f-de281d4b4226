#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.metaquotes.net/"
#property version   "1.00"
#property strict

input string Symbols1 = "---;USD;EUR;GBP;AUD;NZD;CAD;CHF;JPY;USD;---;EURUSD;GBPUSD;AUDUSD;NZDUSD;USDCAD;USDCHF;USDJPY;EUR;EURUSD;---;EURGBP;EURAUD;EURNZD;EURCAD;EURCHF;EURJPY;GBP;GBPUSD;EURGBP;---;GBPAUD;GBPNZD;GBPCAD;GBPCHF;GBPJPY;AUD;AUDUSD;EURAUD;GBPAUD;---;AUDNZD";
input string Symbols2 = "AUDCAD;AUDCHF;AUDJPY;NZD;NZDUSD;EURNZD;GBPNZD;AUDNZD;---;NZDCAD;NZDCHF;NZDJPY;CAD;USDCAD;EURCAD;GBPCAD;AUDCAD;NZDCAD;---;CADCHF;CADJPY;CHF;USDCHF;EURCHF;GBPCHF;AUDCHF;NZDCHF;CADCHF;---;CHFJPY;JPY;USDJPY;EURJPY;GBPJPY";
input string Symbols3 = "AUDJPY;NZDJPY;CADJPY;CHFJPY;XAUUSD";
input string prefix="", suffix="", UniqueID="SymbolChanger1";
input int ButtonsInARow=9, XShift=220, YShift=20, XSize=45, YSize=15, FSize=7;
input color Bcolor=clrDarkGray, Dcolor=clrBlue, Tncolor=clrBlack, Sncolor=clrRed;
input bool Transparent=true;

// Global variables to maintain box position
int gBoxX = 200;
int gBoxY = 20;

string aSymbols[];
string boxName;
string dragButtonName;

int OnInit() {
    ParseSymbols();
    CreateButtons();
    CreateBox();
    CreateDragButton();
    return(INIT_SUCCEEDED);
}


void ParseSymbols() {
    string fullSymbols = Symbols1 + ";" + Symbols2 + ";" + Symbols3;
    string sym[];
    StringSplit(fullSymbols, ';', sym);
    ArrayResize(aSymbols, ArraySize(sym));
    for (int i = 0; i < ArraySize(sym); i++) {
        StringTrimLeft(sym[i]);  // Trim leading spaces
        StringTrimRight(sym[i]); // Trim trailing spaces
        aSymbols[i] = sym[i];    // Assign the trimmed symbol to aSymbols
    }
}

void CreateButtons() {
    int xpos=0, ypos=0;
    for(int i=0; i<ArraySize(aSymbols); i++) {
        if(i>0 && MathMod(i,ButtonsInARow)==0) {
            xpos=0;
            ypos+=YSize+1;
        }
        CreateButton(UniqueID+":symbol:"+IntegerToString(i), prefix+aSymbols[i]+suffix, XShift+xpos, YShift+ypos);
        xpos+=XSize+1;
    }
    SetSymbolButtonColor();
}

void CreateBox() {
    // Use global variables for initial positioning
    boxName = UniqueID + ":box";
    ObjectDelete(0, boxName);
    
    int minX = gBoxX, minY = gBoxY, maxX = gBoxX + (XSize+1)*ButtonsInARow, maxY = gBoxY + YSize*2;
    
    int padding = 5;
    ObjectCreate(0, boxName, OBJ_RECTANGLE, 0, 0, 0);
    ObjectSetInteger(0, boxName, OBJPROP_XDISTANCE, minX-padding-15);
    ObjectSetInteger(0, boxName, OBJPROP_YDISTANCE, minY-padding);
    ObjectSetInteger(0, boxName, OBJPROP_XSIZE, maxX-minX+padding*2);
    ObjectSetInteger(0, boxName, OBJPROP_YSIZE, maxY-minY+padding*2);
    ObjectSetInteger(0, boxName, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, boxName, OBJPROP_BGCOLOR, clrNONE);
    ObjectSetInteger(0, boxName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, boxName, OBJPROP_WIDTH, 1);
}

void CreateDragButton() {
    dragButtonName = UniqueID + ":drag";
    ObjectCreate(0, dragButtonName, OBJ_BUTTON, 0, 0, 0);
    
    ObjectSetInteger(0, dragButtonName, OBJPROP_XDISTANCE, gBoxX-15);
    ObjectSetInteger(0, dragButtonName, OBJPROP_YDISTANCE, gBoxY);
    ObjectSetInteger(0, dragButtonName, OBJPROP_XSIZE, 15);
    ObjectSetInteger(0, dragButtonName, OBJPROP_YSIZE, 15);
    ObjectSetString(0, dragButtonName, OBJPROP_TEXT, "+");
    ObjectSetInteger(0, dragButtonName, OBJPROP_FONTSIZE, 8);
    ObjectSetInteger(0, dragButtonName, OBJPROP_COLOR, Tncolor);
    ObjectSetInteger(0, dragButtonName, OBJPROP_BGCOLOR, Transparent ? clrLightGray : Bcolor);
    ObjectSetInteger(0, dragButtonName, OBJPROP_BORDER_COLOR, Dcolor);
    ObjectSetInteger(0, dragButtonName, OBJPROP_SELECTABLE, true);
    ObjectSetInteger(0, dragButtonName, OBJPROP_BACK, false);
    ObjectSetInteger(0, dragButtonName, OBJPROP_STATE, false);
}

void CreateButton(string name, string caption, int xpos, int ypos) {
    ObjectCreate(0, name, OBJ_BUTTON, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, xpos);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, ypos);
    ObjectSetInteger(0, name, OBJPROP_XSIZE, XSize);
    ObjectSetInteger(0, name, OBJPROP_YSIZE, YSize);
    ObjectSetString(0, name, OBJPROP_TEXT, caption);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
    ObjectSetInteger(0, name, OBJPROP_COLOR, Tncolor);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, Transparent ? clrLightGray : Bcolor);
    ObjectSetInteger(0, name, OBJPROP_BORDER_COLOR, Dcolor);
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, name, OBJPROP_BACK, false);
    ObjectSetInteger(0, name, OBJPROP_STATE, false);
}

void SetSymbolButtonColor() {
    for(int i=0; i<ArraySize(aSymbols); i++) {
        string objName = UniqueID+":symbol:"+IntegerToString(i);
        string symbol = ObjectGetString(0, objName, OBJPROP_TEXT);
        ObjectSetInteger(0, objName, OBJPROP_COLOR, symbol!=_Symbol ? Tncolor : Sncolor);
    }
}

void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam) {
    if(id==CHARTEVENT_OBJECT_CLICK) {
        if(StringFind(sparam, UniqueID+":symbol:", 0)==0)
            ChartSetSymbolPeriod(0, ObjectGetString(0, sparam, OBJPROP_TEXT), _Period);
    }
    else if(id==CHARTEVENT_OBJECT_DRAG && sparam==dragButtonName) {
        int newX = (int)ObjectGetInteger(0, dragButtonName, OBJPROP_XDISTANCE);
        int newY = (int)ObjectGetInteger(0, dragButtonName, OBJPROP_YDISTANCE);
        MoveInterface(newX+15, newY);
    }
}

void MoveInterface(int baseX, int baseY) {
    // Update global position
    gBoxX = baseX;
    gBoxY = baseY;
    
    int xpos=0, ypos=0;
    
    // Move all symbol buttons
    for(int i=0; i<ArraySize(aSymbols); i++) {
        if(i>0 && MathMod(i,ButtonsInARow)==0) {
            xpos=0;
            ypos+=YSize+1;
        }
        string buttonName = UniqueID+":symbol:"+IntegerToString(i);
        ObjectSetInteger(0, buttonName, OBJPROP_XDISTANCE, baseX+xpos);
        ObjectSetInteger(0, buttonName, OBJPROP_YDISTANCE, baseY+ypos);
        xpos+=XSize+1;
    }
    
    // Update box position
    int padding = 5;
    ObjectSetInteger(0, boxName, OBJPROP_XDISTANCE, baseX-padding-15);
    ObjectSetInteger(0, boxName, OBJPROP_YDISTANCE, baseY-padding);
    
    // Keep drag button at current position
    ObjectSetInteger(0, dragButtonName, OBJPROP_XDISTANCE, baseX-15);
    ObjectSetInteger(0, dragButtonName, OBJPROP_YDISTANCE, baseY);
}

void OnDeinit(const int reason) {
    if(reason!=REASON_CHARTCHANGE && reason!=REASON_RECOMPILE) {
        ObjectsDeleteAll(0, UniqueID);
    }
}

// Placeholder for transparency implementation
void SetTransparency(int alpha) {}
    // Implement transparency logic using Windows API