//+------------------------------------------------------------------+
//|                                                 TrendChecker.mq5 |
//|                                                        Sakis-Pit |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Sakis-Pit"
#property link "https://www.forexfactory.com/sakisf"
#property version "1.2"
#property strict
#property indicator_chart_window
#define Name MQLInfoString(MQL_PROGRAM_NAME)
#property indicator_chart_window
#property indicator_buffers 7
#property indicator_plots  0
//#include <initmql4.mqh>

// Version History
// ---------------
// v1.0 Made Calcs
// v1.1 Added alerts
// v1.2 Made it up to daily
// v1.3 Added weekly
// v1.4 Added count of last 4 candles if no 5 candle in specific TF (to see tendency)
// v1.5 Added proposed sl based on history * 2

//TODO
// Make it silent and export GVs for Dashboard

int p = 580;
int o = 10;
int FontSize = 8;
string Font = "Calibri";

enum draw {
	nodraw = 0, //False
	draw = 1 // True
};
draw drawgfx = 1; // Show table
input bool drawlines = true; // Show lines on MA (mTF)
input bool showstflines = true; // Show lines on smaller tf when <= H1

extern color smallLines = clrNONE; // Color for M5/M15/M30

int ch5u[7], ch5d[7], ch10u[7], ch10d[7], ch15u[7], ch15d[7], ch20u[7], ch20d[7], ch30u[7], ch30d[7];

input bool alerts = false; // Send alerts
input bool alerts5 = false; // 5m Alerts (on / off)

int periods = 20; // MA period
ENUM_MA_METHOD mamode = MODE_EMA; // MA Mode
ENUM_APPLIED_PRICE maprice = PRICE_TYPICAL; // MA Price Mode

input double slmulti = 2; // SL factor (x * avg)

double slup[7], sldn[7];

double Pip;

//Comments
int MaxCommentsToShow = 10;
string allcomments[];

int ma205h, ma2015h, ma2030h, ma2060h, ma20240h, ma201440h, ma2010080h;
double ma5[], ma15[], ma30[], ma60[], ma240[], ma1440[], ma10080[];
int macur;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
	if (showstflines == true && ChartPeriod() <= 60) smallLines = clrWhite;

	if (GlobalVariableCheck("3ma6" + _Symbol) == false) GlobalVariableSet("3ma6" + _Symbol, 1);
	drawgfx = (draw)GlobalVariableGet("3ma6" + _Symbol);
	if (GlobalVariableCheck("TCP" + _Symbol) == false) GlobalVariableSet("TCP" + _Symbol, p); else p = (int)GlobalVariableGet("TCP" + _Symbol);
	if (GlobalVariableCheck("TCO" + _Symbol) == false) GlobalVariableSet("TCO" + _Symbol, o); else o = (int)GlobalVariableGet("TCO" + _Symbol);
		
	if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == 0 && _Digits >= 3 && (StringFind(StringSubstr(_Symbol, 0, 1), "X", 0) == -1) && (StringFind(StringSubstr(_Symbol, 0, 1), "D", 0) == -1)) Pip = (_Point * MathPow(10, MathMod(_Digits, 2))); 
	else if (_Digits == 3 && StringFind(StringSubstr(_Symbol, 0, 1), "X", 0) == 0) Pip = 0.1;
	//else if ((MarketInfo(_Symbol, MODE_MARGINCALCMODE) == 0 && _Digits == 2)) Pip = 1;
	else Pip = 1;
	
	if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == 0 && _Digits >= 3 && (StringFind(StringSubstr(_Symbol, 0, 1), "X", 0) == -1) && (StringFind(StringSubstr(_Symbol, 0, 1), "D", 0) == -1)) Pip = (_Point * MathPow(10, MathMod(_Digits, 2))); 
	else if (_Digits == 3 && StringFind(StringSubstr(_Symbol, 0, 1), "X", 0) == 0) Pip = 0.1;
	//else if ((MarketInfo(_Symbol, MODE_MARGINCALCMODE) == 0 && _Digits == 2)) Pip = 1;
	else Pip = 1;
   
   ma205h = iMA(_Symbol, PERIOD_M5, periods, 0, MODE_EMA, PRICE_TYPICAL);
   ma2015h = iMA(_Symbol, PERIOD_M15, periods, 0, MODE_EMA, PRICE_TYPICAL);
   ma2030h = iMA(_Symbol, PERIOD_M30, periods, 0, MODE_EMA, PRICE_TYPICAL);
   ma2060h = iMA(_Symbol, PERIOD_H1, periods, 0, MODE_EMA, PRICE_TYPICAL);
   ma20240h = iMA(_Symbol, PERIOD_H4, periods, 0, MODE_EMA, PRICE_TYPICAL);
   ma201440h = iMA(_Symbol, PERIOD_D1, periods, 0, MODE_EMA, PRICE_TYPICAL);
   ma2010080h = iMA(_Symbol, PERIOD_W1, periods, 0, MODE_EMA, PRICE_TYPICAL);
   macur = iMA(_Symbol, PERIOD_CURRENT, periods, 0, MODE_EMA, PRICE_TYPICAL);
   
   ArraySetAsSeries(ma5, true); ArraySetAsSeries(ma15, true); ArraySetAsSeries(ma30, true); ArraySetAsSeries(ma60, true); ArraySetAsSeries(ma240, true); ArraySetAsSeries(ma1440, true); ArraySetAsSeries(ma10080, true);
	//sls();

	if (drawgfx) { reclabloc(); statbuild(); }

	//---
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || MQLInfoInteger(MQL_TESTER))
		if (!MQLInfoInteger(MQL_TESTER))
		{
			DeleteObjects();
		}
	Comment("");
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	//---

	{//MA COPY
      CopyBuffer(ma205h, 0, 0, rates_total, ma5);
      CopyBuffer(ma2015h, 0, 0, rates_total, ma15);
      CopyBuffer(ma2030h, 0, 0, rates_total, ma30);
      CopyBuffer(ma2060h, 0, 0, rates_total, ma60);
      CopyBuffer(ma20240h, 0, 0, rates_total, ma240);
      CopyBuffer(ma201440h, 0, 0, rates_total, ma1440);
      CopyBuffer(ma2010080h, 0, 0, rates_total, ma10080);
   }
   
	bool new_4h_check = false;
	static datetime start_4h_time = 0;
	if (start_4h_time < iTime(NULL, PERIOD_H4, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_H4, 0) + 60) && TimeCurrent() <= (iTime(NULL, PERIOD_H4, 0) + 360)))
	{
		new_4h_check = true;
		start_4h_time = iTime(NULL, PERIOD_H4, 0);
	}
	if (new_4h_check)
	{
		sls();
		new_4h_check = false;
	}
	
	bool new_gm_check = false;
	static datetime start_gm_time = 0;
	if (start_gm_time < iTime(NULL, PERIOD_M5, 0))
	{
		new_gm_check = true;
		start_gm_time = iTime(NULL, PERIOD_M5, 0);
	}
	if (new_gm_check)
	{
		TwoP();
		horizlines();
		if (drawgfx) buildtab();
		new_gm_check = false;
	}
      
	/*
	bool new_5m_check = false;
	static datetime start_5m_time = 0;
	if (start_5m_time < iTime(NULL, PERIOD_M15, 0))
	{
		new_5m_check = true;
		start_5m_time = iTime(NULL, PERIOD_M15, 0);
	}
	if (new_5m_check)
	{
		alerter15();
		new_5m_check = false;
	}
	*/

	if (alerts) {
		if (alerts5) {
			bool new_1m_check = false;
			static datetime start_1m_time = 0;
			if (start_1m_time < iTime(NULL, PERIOD_M1, 0))
			{
				new_1m_check = true;
				start_1m_time = iTime(NULL, PERIOD_M1, 0);
			}
			if (new_1m_check)
			{
				alerter5();
				new_1m_check = false;
			}
		}

		bool new_15m_check = false;
		static datetime start_15m_time = 0;
		if (start_15m_time < iTime(NULL, PERIOD_M15, 0))
		{
			new_15m_check = true;
			start_15m_time = iTime(NULL, PERIOD_M15, 0);
		}
		if (new_15m_check)
		{
			alerter15();
			new_15m_check = false;
		}

		bool new_60m_check = false;
		static datetime start_60m_time = 0;
		if (start_60m_time < iTime(NULL, PERIOD_M30, 0))
		{
			new_60m_check = true;
			start_60m_time = iTime(NULL, PERIOD_M30, 0);
		}
		if (new_60m_check)
		{
			alerter30();
			alerter60();
			new_60m_check = false;
		}

		bool new_240m_check = false;
		static datetime start_240m_time = 0;
		if (start_240m_time < iTime(NULL, PERIOD_H1, 0))
		{
			new_240m_check = true;
			start_240m_time = iTime(NULL, PERIOD_H1, 0);
		}
		if (new_240m_check)
		{
			alerter240();
			new_240m_check = false;
		}

		bool new_1440m_check = false;
		static datetime start_1440m_time = 0;
		if (start_1440m_time < iTime(NULL, PERIOD_H4, 0))
		{
			new_1440m_check = true;
			start_1440m_time = iTime(NULL, PERIOD_H4, 0);
		}
		if (new_1440m_check)
		{
			alerter1440();
			new_1440m_check = false;
		}

		bool new_10080m_check = false;
		static datetime start_10080m_time = 0;
		if (start_10080m_time < iTime(NULL, PERIOD_D1, 0))
		{
			new_10080m_check = true;
			start_10080m_time = iTime(NULL, PERIOD_D1, 0);
		}
		if (new_10080m_check)
		{
			alerter10080();
			new_10080m_check = false;
		}
	}
	//--- return value of prev_calculated for next call
	return(rates_total);
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	for (int i = ObjectsTotal(0, -1, -1) - 1; i >= 0; i--)
	{
		string ObName = ObjectName(0, i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(0, ObName);
		}
	}
}
//+------------------------------------------------------------------+

//+STATIC OBJECTS----------------------------------------------------+
void statbuild() {
	string horiz[7] = { "P", "5", "10", "15", "20", "30", "L4/B" };
	string vert[7] = { "M5", "M15", "M30", "H1", "H4", "D1", "W1" };

	string obname;

	for (int i = 0; i <= 6; i++) {
		obname = Name + "horlabel" + IntegerToString(i);
		LabelMake(obname, 0, p + 30 * i, o, horiz[i], FontSize, clrBlack);
	}
	for (int i = 0; i <= 6; i++) {
		obname = Name + "verlabel" + IntegerToString(i);
		LabelMake(obname, 0, p, o + 10 + (i * 10), vert[i], FontSize, clrBlack);
	}

	obname = Name + "SLS"; LabelMake(obname, 0, p + 300, o, "SL-" + "     " + "SL+", FontSize, clrBlack);
}
//+------------------------------------------------------------------+

//+ChartEvent function-----------------------------------------------+
void OnChartEvent(const int id,
	const long &lparam,
	const double &dparam,
	const string &sparam)
{
	//---
	{//Debug
		if (id == CHARTEVENT_KEYDOWN) {
			if (lparam == StringGetCharacter("Y", 0)) { GlobalVariableSet("3ma6" + _Symbol, 1); drawgfx = (draw)GlobalVariableGet("3ma6" + _Symbol); OnInit(); reclabloc(); statbuild(); buildtab(); }
			if (lparam == StringGetCharacter("U", 0)) { GlobalVariableSet("3ma6" + _Symbol, 0); drawgfx = (draw)GlobalVariableGet("3ma6" + _Symbol); OnInit(); ObjectsDeleteAll(0, Name); horizlines(); }
		}
	}
	{//Move rectangle
		if (id == CHARTEVENT_OBJECT_DRAG)
		{
			if (sparam == Name + " MovRec")
			{
				GlobalVariableSet("TCP" + _Symbol, ObjectGetInteger(0,Name + " MovRec",OBJPROP_XDISTANCE));
				GlobalVariableSet("TCO" + _Symbol, ObjectGetInteger(0,Name + " MovRec",OBJPROP_YDISTANCE));
				GlobalVariablesFlush();
				reclabloc(); statbuild(); TwoP(); horizlines(); if (drawgfx) buildtab();
			}
		}
	}
}
//+------------------------------------------------------------------+

//+240 Period Calcs--------------------------------------------------+
void TwoP() {
	double check5[], check15[], check30[], check60[], check240[], check1440[], check10080[];
	ArrayResize(check5, 31);
	ArrayResize(check15, 31);
	ArrayResize(check30, 31);
	ArrayResize(check60, 31);
	ArrayResize(check240, 31);
	ArrayResize(check1440, 31);
	ArrayResize(check10080, 31);
	
	for (int i = 30; i >= 1; i--) {
		check5[i] = iClose(_Symbol, PERIOD_M5, i) - ma5[i];
		check15[i] = iClose(_Symbol, PERIOD_M15, i) - ma15[i];
		check30[i] = iClose(_Symbol, PERIOD_M30, i) - ma30[i];
		check60[i] = iClose(_Symbol, PERIOD_H1, i) - ma60[i];
		check240[i] = iClose(_Symbol, PERIOD_H4, i) - ma240[i];
		check1440[i] = iClose(_Symbol, PERIOD_D1, i) - ma1440[i];
		check10080[i] = iClose(_Symbol, PERIOD_W1, i) - ma10080[i];
	}

	int ch5[7][2], ch10[7][2], ch15[7][2], ch20[7][2], ch30[7][2];
	ArrayInitialize(ch5, 0); ArrayInitialize(ch10, 0); ArrayInitialize(ch15, 0); ArrayInitialize(ch20, 0); ArrayInitialize(ch30, 0);

	for (int i = 5; i >= 1; i--) {
		if (check5[i] >= 0) ch5[0][0]++;
		if (check15[i] >= 0) ch5[1][0]++;
		if (check30[i] >= 0) ch5[2][0]++;
		if (check60[i] >= 0) ch5[3][0]++;
		if (check240[i] >= 0) ch5[4][0]++;
		if (check1440[i] >= 0) ch5[5][0]++;
		if (check10080[i] >= 0) ch5[6][0]++;

		if (check5[i] <= 0) ch5[0][1]++;
		if (check15[i] <= 0) ch5[1][1]++;
		if (check30[i] <= 0) ch5[2][1]++;
		if (check60[i] <= 0) ch5[3][1]++;
		if (check240[i] <= 0) ch5[4][1]++;
		if (check1440[i] <= 0) ch5[5][1]++;
		if (check10080[i] <= 0) ch5[6][1]++;
	}
	for (int i = 10; i >= 1; i--) {
		if (check5[i] >= 0) ch10[0][0]++;
		if (check15[i] >= 0) ch10[1][0]++;
		if (check30[i] >= 0) ch10[2][0]++;
		if (check60[i] >= 0) ch10[3][0]++;
		if (check240[i] >= 0) ch10[4][0]++;
		if (check1440[i] >= 0) ch10[5][0]++;
		if (check10080[i] >= 0) ch10[6][0]++;

		if (check5[i] <= 0) ch10[0][1]++;
		if (check15[i] <= 0) ch10[1][1]++;
		if (check30[i] <= 0) ch10[2][1]++;
		if (check60[i] <= 0) ch10[3][1]++;
		if (check240[i] <= 0) ch10[4][1]++;
		if (check1440[i] <= 0) ch10[5][1]++;
		if (check10080[i] <= 0) ch10[6][1]++;
	}
	for (int i = 15; i >= 1; i--) {
		if (check5[i] >= 0) ch15[0][0]++;
		if (check15[i] >= 0) ch15[1][0]++;
		if (check30[i] >= 0) ch15[2][0]++;
		if (check60[i] >= 0) ch15[3][0]++;
		if (check240[i] >= 0) ch15[4][0]++;
		if (check1440[i] >= 0) ch15[5][0]++;
		if (check10080[i] >= 0) ch15[6][0]++;

		if (check5[i] <= 0) ch15[0][1]++;
		if (check15[i] <= 0) ch15[1][1]++;
		if (check30[i] <= 0) ch15[2][1]++;
		if (check60[i] <= 0) ch15[3][1]++;
		if (check240[i] <= 0) ch15[4][1]++;
		if (check1440[i] <= 0) ch15[5][1]++;
		if (check10080[i] <= 0) ch15[6][1]++;
	}
	for (int i = 20; i >= 1; i--) {
		if (check5[i] >= 0) ch20[0][0]++;
		if (check15[i] >= 0) ch20[1][0]++;
		if (check30[i] >= 0) ch20[2][0]++;
		if (check60[i] >= 0) ch20[3][0]++;
		if (check240[i] >= 0) ch20[4][0]++;
		if (check1440[i] >= 0) ch20[5][0]++;
		if (check10080[i] >= 0) ch20[6][0]++;

		if (check5[i] <= 0) ch20[0][1]++;
		if (check15[i] <= 0) ch20[1][1]++;
		if (check30[i] <= 0) ch20[2][1]++;
		if (check60[i] <= 0) ch20[3][1]++;
		if (check240[i] <= 0) ch20[4][1]++;
		if (check1440[i] <= 0) ch20[5][1]++;
		if (check10080[i] <= 0) ch20[6][1]++;
	}
	for (int i = 30; i >= 1; i--) {
		if (check5[i] >= 0) ch30[0][0]++;
		if (check15[i] >= 0) ch30[1][0]++;
		if (check30[i] >= 0) ch30[2][0]++;
		if (check60[i] >= 0) ch30[3][0]++;
		if (check240[i] >= 0) ch30[4][0]++;
		if (check1440[i] >= 0) ch30[5][0]++;
		if (check10080[i] >= 0) ch30[6][0]++;

		if (check5[i] <= 0) ch30[0][1]++;
		if (check15[i] <= 0) ch30[1][1]++;
		if (check30[i] <= 0) ch30[2][1]++;
		if (check60[i] <= 0) ch30[3][1]++;
		if (check240[i] <= 0) ch30[4][1]++;
		if (check1440[i] <= 0) ch30[5][1]++;
		if (check10080[i] <= 0) ch30[6][1]++;
	}

	for (int i = 6; i >= 0; i--) {
		ch5u[i] = ch5[i][0];
		ch5d[i] = ch5[i][1];
		ch10u[i] = ch10[i][0];
		ch10d[i] = ch10[i][1];
		ch15u[i] = ch15[i][0];
		ch15d[i] = ch15[i][1];
		ch20u[i] = ch20[i][0];
		ch20d[i] = ch20[i][1];
		ch30u[i] = ch30[i][0];
		ch30d[i] = ch30[i][1];
	}
}
//+------------------------------------------------------------------+

//+BUILD VISUAL NOTICE-----------------------------------------------+
void buildtab() {
	for (int i = 0; i <= 6; i++)
	{
		string obname = Name + "5" + IntegerToString(i);
		LabelMake(obname, 0, p + 30, o + 10 + 10 * i, " 0 ", FontSize, clrBlack);
		if (ch5u[i] == 5) ObjectSetTextMQL4(obname, CharToString(233), 8, "WingDings", clrBlue);
		else if (ch5d[i] == 5) ObjectSetTextMQL4(obname, CharToString(234), 8, "WingDings", clrRed);
		else ObjectSetTextMQL4(obname, CharToString(232), 8, "WingDings", clrDarkGray);
	}

	for (int i = 0; i <= 6; i++)
	{
		string obname = Name + "10" + IntegerToString(i);
		LabelMake(obname, 0, p + 60, o + 10 + 10 * i, " 0 ", FontSize, clrBlack);
		if (ch10u[i] == 10) ObjectSetTextMQL4(obname, CharToString(233), 8, "WingDings", clrBlue);
		else if (ch10d[i] == 10) ObjectSetTextMQL4(obname, CharToString(234), 8, "WingDings", clrRed);
		else ObjectSetTextMQL4(obname, CharToString(232), 8, "WingDings", clrDarkGray);
	}

	for (int i = 0; i <= 6; i++)
	{
		string obname = Name + "15" + IntegerToString(i);
		LabelMake(obname, 0, p + 90, o + 10 + 10 * i, " 0 ", FontSize, clrBlack);
		if (ch15u[i] == 15) ObjectSetTextMQL4(obname, CharToString(233), 8, "WingDings", clrBlue);
		else if (ch15d[i] == 15) ObjectSetTextMQL4(obname, CharToString(234), 8, "WingDings", clrRed);
		else ObjectSetTextMQL4(obname, CharToString(232), 8, "WingDings", clrDarkGray);
	}

	for (int i = 0; i <= 6; i++)
	{
		string obname = Name + "20" + IntegerToString(i);
		LabelMake(obname, 0, p + 120, o + 10 + 10 * i, " 0 ", FontSize, clrBlack);
		if (ch20u[i] == 20) ObjectSetTextMQL4(obname, CharToString(233), 8, "WingDings", clrBlue);
		else if (ch20d[i] == 20) ObjectSetTextMQL4(obname, CharToString(234), 8, "WingDings", clrRed);
		else ObjectSetTextMQL4(obname, CharToString(232), 8, "WingDings", clrDarkGray);
	}

	for (int i = 0; i <= 6; i++)
	{
		string obname = Name + "30" + IntegerToString(i);
		LabelMake(obname, 0, p + 150, o + 10 + 10 * i, " 0 ", FontSize, clrBlack);
		if (ch30u[i] == 30) ObjectSetTextMQL4(obname, CharToString(233), 8, "WingDings", clrBlue);
		else if (ch30d[i] == 30) ObjectSetTextMQL4(obname, CharToString(234), 8, "WingDings", clrRed);
		else ObjectSetTextMQL4(obname, CharToString(232), 8, "WingDings", clrDarkGray);
	}

	{
		string obname;

		int lup5 = 0, ldn5 = 0, lup15 = 0, ldn15 = 0, lup30 = 0, ldn30 = 0, lup60 = 0, ldn60 = 0, lup240 = 0, ldn240 = 0, lup1440 = 0, ldn1440 = 0, lup10080 = 0, ldn10080 = 0;

		for (int x = 4; x >= 1; x--) {
			if ((iClose(_Symbol, PERIOD_M5, x) - ma5[x]) > 0) lup5++; if ((iClose(_Symbol, PERIOD_M5, x) - ma5[x]) < 0) ldn5++;
			if ((iClose(_Symbol, PERIOD_M15, x) - ma15[x]) > 0) lup15++; if ((iClose(_Symbol, PERIOD_M15, x) - ma15[x]) < 0) ldn15++;
			if ((iClose(_Symbol, PERIOD_M30, x) - ma30[x]) > 0) lup30++; if ((iClose(_Symbol, PERIOD_M30, x) - ma30[x]) < 0) ldn30++;
			if ((iClose(_Symbol, PERIOD_H1, x) - ma60[x]) > 0) lup60++; if ((iClose(_Symbol, PERIOD_H1, x) - ma60[x]) < 0) ldn60++;
			if ((iClose(_Symbol, PERIOD_H4, x) - ma240[x]) > 0) lup240++; if ((iClose(_Symbol, PERIOD_H4, x) - ma240[x]) < 0) ldn240++;
			if ((iClose(_Symbol, PERIOD_D1, x) - ma1440[x]) > 0) lup1440++; if ((iClose(_Symbol, PERIOD_D1, x) - ma1440[x]) < 0) ldn1440++;
			if ((iClose(_Symbol, PERIOD_W1, x) - ma10080[x]) > 0) lup10080++; if ((iClose(_Symbol, PERIOD_W1, x) - ma10080[x]) < 0) ldn10080++;
		}

		ObjectDelete(0, Name + "L4C5");
		if (ch5u[0] == 5 || ch5d[0] == 0) { obname = Name + "L4C5"; LabelMake(obname, 0, p + 180, o + 10, "", FontSize, clrDarkGray); }
		else if (ch5u[0] != 5 && ch5d[0] != 5 && lup5 > ldn5) { obname = Name + "L4C5"; LabelMake(obname, 0, p + 180, o + 10, IntegerToString(lup5), FontSize, clrBlue); if (lup5 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[0] != 5 && ch5d[0] != 5 && lup5 < ldn5) { obname = Name + "L4C5"; LabelMake(obname, 0, p + 180, o + 10, IntegerToString(ldn5), FontSize, clrRed); if (ldn5 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[0] != 5 && ch5d[0] != 5 && lup5 == ldn5) { obname = Name + "L4C5"; LabelMake(obname, 0, p + 180, o + 10, "=", FontSize, clrDarkGray); }

		ObjectDelete(0, Name + "L4C15");
		if (ch5u[1] == 5 || ch5d[1] == 0) { obname = Name + "L4C15"; LabelMake(obname, 0, p + 180, o + 20, "", FontSize, clrDarkGray); }
		else if (ch5u[1] != 5 && ch5d[1] != 5 && lup15 > ldn15) { obname = Name + "L4C15"; LabelMake(obname, 0, p + 180, o + 20, IntegerToString(lup15), FontSize, clrBlue); if (lup15 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[1] != 5 && ch5d[1] != 5 && lup15 < ldn15) { obname = Name + "L4C15"; LabelMake(obname, 0, p + 180, o + 20, IntegerToString(ldn15), FontSize, clrRed); if (ldn15 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[1] != 5 && ch5d[1] != 5 && lup15 == ldn15) { obname = Name + "L4C15"; LabelMake(obname, 0, p + 180, o + 20, "=", FontSize, clrDarkGray); }

		ObjectDelete(0, Name + "L4C30");
		if (ch5u[2] == 5 || ch5d[2] == 0) { obname = Name + "L4C30"; LabelMake(obname, 0, p + 180, o + 30, "", FontSize, clrDarkGray); }
		else if (ch5u[2] != 5 && ch5d[2] != 5 && lup30 > ldn30) { obname = Name + "L4C30"; LabelMake(obname, 0, p + 180, o + 30, IntegerToString(lup30), FontSize, clrBlue); if (lup30 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[2] != 5 && ch5d[2] != 5 && lup30 < ldn30) { obname = Name + "L4C30"; LabelMake(obname, 0, p + 180, o + 30, IntegerToString(ldn30), FontSize, clrRed); if (ldn30 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[2] != 5 && ch5d[2] != 5 && lup30 == ldn30) { obname = Name + "L4C30"; LabelMake(obname, 0, p + 180, o + 30, "=", FontSize, clrDarkGray); }

		ObjectDelete(0, Name + "L4C60");
		if (ch5u[3] == 5 || ch5d[3] == 0) { obname = Name + "L4C60"; LabelMake(obname, 0, p + 180, o + 40, "", FontSize, clrDarkGray); }
		else if (ch5u[3] != 5 && ch5d[3] != 5 && lup60 > ldn60) { obname = Name + "L4C60"; LabelMake(obname, 0, p + 180, o + 40, IntegerToString(lup60), FontSize, clrBlue); if (lup60 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[3] != 5 && ch5d[3] != 5 && lup60 < ldn60) { obname = Name + "L4C60"; LabelMake(obname, 0, p + 180, o + 40, IntegerToString(ldn60), FontSize, clrRed); if (ldn60 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[3] != 5 && ch5d[3] != 5 && lup60 == ldn60) { obname = Name + "L4C60"; LabelMake(obname, 0, p + 180, o + 40, "=", FontSize, clrDarkGray); }

		ObjectDelete(0, Name + "L4C240");
		if (ch5u[4] == 5 || ch5d[4] == 0) { obname = Name + "L4C250"; LabelMake(obname, 0, p + 180, o + 50, "", FontSize, clrDarkGray); }
		else if (ch5u[4] != 5 && ch5d[4] != 5 && lup240 > ldn240) { obname = Name + "L4C240"; LabelMake(obname, 0, p + 180, o + 50, IntegerToString(lup240), FontSize, clrBlue); if (lup240 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[4] != 5 && ch5d[4] != 5 && lup240 < ldn240) { obname = Name + "L4C240"; LabelMake(obname, 0, p + 180, o + 50, IntegerToString(ldn240), FontSize, clrRed); if (ldn240 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[4] != 5 && ch5d[4] != 5 && lup240 == ldn240) { obname = Name + "L4C240"; LabelMake(obname, 0, p + 180, o + 50, "=", FontSize, clrDarkGray); }

		ObjectDelete(0, Name + "L4C1440");
		if (ch5u[5] == 5 || ch5d[5] == 0) { obname = Name + "L4C1440"; LabelMake(obname, 0, p + 180, o + 60, "", FontSize, clrDarkGray); }
		else if (ch5u[5] != 5 && ch5d[5] != 5 && lup1440 > ldn1440) { obname = Name + "L4C1440"; LabelMake(obname, 0, p + 180, o + 60, IntegerToString(lup1440), FontSize, clrBlue); if (lup1440 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[5] != 5 && ch5d[5] != 5 && lup1440 < ldn1440) { obname = Name + "L4C1440"; LabelMake(obname, 0, p + 180, o + 60, IntegerToString(ldn1440), FontSize, clrRed); if (ldn1440 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[5] != 5 && ch5d[5] != 5 && lup1440 == ldn1440) { obname = Name + "L4C1440"; LabelMake(obname, 0, p + 180, o + 60, "=", FontSize, clrDarkGray); }

		ObjectDelete(0, Name + "L4C10080");
		if (ch5u[6] == 5 || ch5d[6] == 0) { obname = Name + "L4C10080"; LabelMake(obname, 0, p + 180, o + 70, "", FontSize, clrDarkGray); }
		else if (ch5u[6] != 5 && ch5d[6] != 5 && lup10080 > ldn10080) { obname = Name + "L4C10080"; LabelMake(obname, 0, p + 180, o + 70, IntegerToString(lup10080), FontSize, clrBlue); if (lup10080 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[6] != 5 && ch5d[6] != 5 && lup10080 < ldn10080) { obname = Name + "L4C10080"; LabelMake(obname, 0, p + 180, o + 70, IntegerToString(ldn10080), FontSize, clrRed); if (ldn10080 == 4) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); }
		else if (ch5u[6] != 5 && ch5d[6] != 5 && lup10080 == ldn10080) { obname = Name + "L4C10080"; LabelMake(obname, 0, p + 180, o + 70, "=", FontSize, clrDarkGray); }
	}

	{
		string obname;
		double sma5 = ma5[0];
		double sma15 = ma15[0];
		double sma30 = ma30[0];
		double sma60 = ma60[0];
		double sma240 = ma240[0];
		double sma1440 = ma1440[0];
		double sma10080 = ma10080[0];

		//M5 
		{ obname = Name + "5mMA"; LabelMake(obname, 0, p + 210, o + 10, DoubleToString(sma5, _Digits) + "  " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - sma5) / Pip, 2), FontSize, clrRed); if (iClose(_Symbol, PERIOD_M5, 0) > sma5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
		obname = Name + "5sl"; LabelMake(obname, 0, p + 300, o + 10, DoubleToString(slup[0], 1) + "     " + DoubleToString(sldn[0], 1), FontSize, clrBlack);
		if (drawlines && smallLines != clrNONE) { if (ch5u[0] == 5 || ch5d[0] == 5) { obname = Name + "5mMAL"; objhoriz(obname, sma5, smallLines); ObjectSetString(0,obname,OBJPROP_TEXT,"M5: " + DoubleToString(sma5, _Digits)); }
		else ObjectDelete(0, Name + "5mMAL"); }
		//M15
		{ obname = Name + "15mMA"; LabelMake(obname, 0, p + 210, o + 20, DoubleToString(sma15, _Digits) + "  " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - sma15) / Pip, 2), FontSize, clrRed); if (iClose(_Symbol, PERIOD_M15, 0) > sma15) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
		obname = Name + "15sl"; LabelMake(obname, 0, p + 300, o + 20, DoubleToString(slup[1], 1) + "     " + DoubleToString(sldn[1], 1), FontSize, clrBlack);
		if (drawlines && smallLines != clrNONE) { if (ch5u[1] == 5 || ch5d[1] == 5) { obname = Name + "15mMAL"; objhoriz(obname, sma15, smallLines);  ObjectSetString(0,obname,OBJPROP_TEXT,"M15: " + DoubleToString(sma15, _Digits)); } else ObjectDelete(0, Name + "15mMAL"); }
		else ObjectDelete(0, Name + "15mMAL"); }
		//M30
		{ obname = Name + "30mMA"; LabelMake(obname, 0, p + 210, o + 30, DoubleToString(sma30, _Digits) + "  " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - sma30) / Pip, 2), FontSize, clrRed); if (iClose(_Symbol, PERIOD_M30, 0) > sma30) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
		obname = Name + "30sl"; LabelMake(obname, 0, p + 300, o + 30, DoubleToString(slup[2], 1) + "     " + DoubleToString(sldn[2], 1), FontSize, clrBlack);
		if (drawlines && smallLines != clrNONE) { if (ch5u[2] == 5 || ch5d[2] == 5) { obname = Name + "30mMAL"; objhoriz(obname, sma30, smallLines); ObjectSetString(0,obname,OBJPROP_TEXT,"M30: " + DoubleToString(sma30, _Digits)); } else ObjectDelete(0, Name + "30mMAL"); }
		else ObjectDelete(0, Name + "30mMAL"); }
		//H1
		{ obname = Name + "60mMA"; LabelMake(obname, 0, p + 210, o + 40, DoubleToString(sma60, _Digits) + "  " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - sma60) / Pip, 2), FontSize, clrRed); if (iClose(_Symbol, PERIOD_H1, 0) > sma60) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
		obname = Name + "60sl"; LabelMake(obname, 0, p + 300, o + 40, DoubleToString(slup[3], 1) + "     " + DoubleToString(sldn[3], 1), FontSize, clrBlack);
		if (drawlines) { if (ch5u[3] == 5 || ch5d[3] == 5) { obname = Name + "60mMAL"; objhoriz(obname, sma60, clrBlack); ObjectSetInteger(0,obname,OBJPROP_STYLE,(int)STYLE_SOLID); ObjectSetInteger(0,obname,OBJPROP_WIDTH,(int)1); ObjectSetString(0,obname,OBJPROP_TEXT,"H1: " + DoubleToString(sma60, _Digits)); if (ch5u[3] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue); if (ch5d[3] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); } else ObjectDelete(0, Name + "60mMAL"); }
		else ObjectDelete(0, Name + "60mMAL"); }
		//H4
		{ obname = Name + "240mMA"; LabelMake(obname, 0, p + 210, o + 50, DoubleToString(sma240, _Digits) + "  " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - sma240) / Pip, 2), FontSize, clrRed); if (iClose(_Symbol, PERIOD_H4, 0) > sma240) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
		obname = Name + "240sl"; LabelMake(obname, 0, p + 300, o + 50, DoubleToString(slup[4], 1) + "     " + DoubleToString(sldn[4], 1), FontSize, clrBlack);
		if (drawlines) { if (ch5u[4] == 5 || ch5d[4] == 5) { obname = Name + "240mMAL"; objhoriz(obname, sma240, clrBlack); ObjectSetInteger(0,obname,OBJPROP_STYLE,(int)STYLE_SOLID); ObjectSetInteger(0,obname,OBJPROP_WIDTH,(int)2); ObjectSetString(0,obname,OBJPROP_TEXT,"H4: " + DoubleToString(sma240, _Digits)); if (ch5u[4] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue); if (ch5d[4] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); } else ObjectDelete(0, Name + "240mMAL"); }
		else ObjectDelete(0, Name + "240mMAL"); }
		//D1
		{ obname = Name + "1440mMA"; LabelMake(obname, 0, p + 210, o + 60, DoubleToString(sma1440, _Digits) + "  " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - sma1440) / Pip, 2), FontSize, clrRed); if (iClose(_Symbol, PERIOD_D1, 0) > sma1440) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
		obname = Name + "1440sl"; LabelMake(obname, 0, p + 300, o + 60, DoubleToString(slup[5], 1) + "     " + DoubleToString(sldn[5], 1), FontSize, clrBlack);
		if (drawlines) { if (ch5u[5] == 5 || ch5d[5] == 5) { obname = Name + "1440mMAL"; objhoriz(obname, sma1440, clrBlack); ObjectSetInteger(0,obname,OBJPROP_STYLE,(int)STYLE_SOLID); ObjectSetInteger(0,obname,OBJPROP_WIDTH,(int)2); ObjectSetString(0,obname,OBJPROP_TEXT,"D1: " + DoubleToString(sma1440, _Digits)); if (ch5u[5] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue); if (ch5d[5] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); } else ObjectDelete(0, Name + "1440mMAL"); }
		else ObjectDelete(0, Name + "1440mMAL"); }
		//W1
		{ obname = Name + "10080mMA"; LabelMake(obname, 0, p + 210, o + 70, DoubleToString(sma10080, _Digits) + "  " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - sma10080) / Pip, 2), FontSize, clrRed); if (iClose(_Symbol, PERIOD_W1, 0) > sma10080) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
		obname = Name + "10080sl"; LabelMake(obname, 0, p + 300, o + 70, DoubleToString(slup[6], 1) + "     " + DoubleToString(sldn[6], 1), FontSize, clrBlack);
		if (drawlines) { if (ch5u[6] == 5 || ch5d[6] == 5) { obname = Name + "10080mMAL"; objhoriz(obname, sma10080, clrBlack); ObjectSetInteger(0,obname,OBJPROP_STYLE,(int)STYLE_SOLID); ObjectSetInteger(0,obname,OBJPROP_WIDTH,(int)2); ObjectSetString(0,obname,OBJPROP_TEXT,"W1: " + DoubleToString(sma10080, _Digits)); if (ch5u[6] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue); if (ch5d[6] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); } else ObjectDelete(0, Name + "10080mMAL"); }
		else ObjectDelete(0, Name + "10080mMAL"); }
		
		double curma[];
		ArrayResize(curma, 1);
		CopyBuffer(macur, 0, 0, 1, curma);
		double sldistu, sldistd;
		double smart = curma[0];

		switch (Period())
		{
		case PERIOD_M5: sldistu = Pip * slup[0]; sldistd = Pip * sldn[0]; break;
		case PERIOD_M15: sldistu = Pip * slup[1]; sldistd = Pip * sldn[1]; break;
		case PERIOD_M30: sldistu = Pip * slup[2]; sldistd = Pip * sldn[2]; break;
		case PERIOD_H1: sldistu = Pip * slup[3]; sldistd = Pip * sldn[3]; break;
		case PERIOD_H4: sldistu = Pip * slup[4]; sldistd = Pip * sldn[4]; break;
		case PERIOD_D1: sldistu = Pip * slup[5]; sldistd = Pip * sldn[5]; break;
		case PERIOD_W1: sldistu = Pip * slup[6]; sldistd = Pip * sldn[6]; break;
		default: sldistu = 0; sldistd = 0; break;
		}

		obname = Name + "SLUP";
		ArrowPrice(obname, smart - sldistu, 5 * Period() * 60, clrRed);
		obname = Name + "SLDN";
		ArrowPrice(obname, smart + sldistd, 5 * Period() * 60, clrBlue);
	}
}
}
//+------------------------------------------------------------------+

//+HorizLines-----------------------------------------------------------+
void horizlines() {
	string obname;
	double sma5 = ma5[0];
	double sma15 = ma15[0];
	double sma30 = ma30[0];
	double sma60 = ma60[0];
	double sma240 = ma240[0];
	double sma1440 = ma1440[0];
	double sma10080 = ma10080[0];

		//M5
		{ if (drawlines && smallLines != clrNONE) { if (ch5u[0] == 5 || ch5d[0] == 5) { obname = Name + "5mMAL"; objhoriz(obname, sma5, smallLines); ObjectSetString(0,obname,OBJPROP_TEXT,"M5: " + DoubleToString(sma5, _Digits)); } else ObjectDelete(0, Name + "5mMAL"); }
		else ObjectDelete(0, Name + "5mMAL"); }
		//M15
		{ if (drawlines && smallLines != clrNONE) { if (ch5u[1] == 5 || ch5d[1] == 5) { obname = Name + "15mMAL"; objhoriz(obname, sma15, smallLines);  ObjectSetString(0,obname,OBJPROP_TEXT,"M15: " + DoubleToString(sma15, _Digits)); } else ObjectDelete(0, Name + "15mMAL"); }
		else ObjectDelete(0, Name + "15mMAL"); }
		//M30
		{ if (drawlines && smallLines != clrNONE) { if (ch5u[2] == 5 || ch5d[2] == 5) { obname = Name + "30mMAL"; objhoriz(obname, sma30, smallLines); ObjectSetString(0,obname,OBJPROP_TEXT,"M30: " + DoubleToString(sma30, _Digits)); } else ObjectDelete(0, Name + "30mMAL"); }
		else ObjectDelete(0, Name + "30mMAL"); }
		//H1
		{ if (drawlines) { if (ch5u[3] == 5 || ch5d[3] == 5) { obname = Name + "60mMAL"; objhoriz(obname, sma60, clrBlack); ObjectSetInteger(0,obname,OBJPROP_STYLE,(int)STYLE_SOLID); ObjectSetInteger(0,obname,OBJPROP_WIDTH,(int)1); ObjectSetString(0,obname,OBJPROP_TEXT,"H1: " + DoubleToString(sma60, _Digits)); if (ch5u[3] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue); if (ch5d[3] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); } else ObjectDelete(0, Name + "60mMAL"); }
		else ObjectDelete(0, Name + "60mMAL"); }
		//H4
		{ if (drawlines) { if (ch5u[4] == 5 || ch5d[4] == 5) { obname = Name + "240mMAL"; objhoriz(obname, sma240, clrBlack); ObjectSetInteger(0,obname,OBJPROP_STYLE,(int)STYLE_SOLID); ObjectSetInteger(0,obname,OBJPROP_WIDTH,(int)2); ObjectSetString(0,obname,OBJPROP_TEXT,"H4: " + DoubleToString(sma240, _Digits)); if (ch5u[4] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue); if (ch5d[4] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); } else ObjectDelete(0, Name + "240mMAL"); }
		else ObjectDelete(0, Name + "240mMAL"); }
		//D1
		{ if (drawlines) { if (ch5u[5] == 5 || ch5d[5] == 5) { obname = Name + "1440mMAL"; objhoriz(obname, sma1440, clrBlack); ObjectSetInteger(0,obname,OBJPROP_STYLE,(int)STYLE_SOLID); ObjectSetInteger(0,obname,OBJPROP_WIDTH,(int)2); ObjectSetString(0,obname,OBJPROP_TEXT,"D1: " + DoubleToString(sma1440, _Digits)); if (ch5u[5] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue); if (ch5d[5] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); } else ObjectDelete(0, Name + "1440mMAL"); }
		else ObjectDelete(0, Name + "1440mMAL"); }
		//W1
		{ if (drawlines) { if (ch5u[6] == 5 || ch5d[6] == 5) { obname = Name + "10080mMAL"; objhoriz(obname, sma10080, clrBlack); ObjectSetInteger(0,obname,OBJPROP_STYLE,(int)STYLE_SOLID); ObjectSetInteger(0,obname,OBJPROP_WIDTH,(int)2); ObjectSetString(0,obname,OBJPROP_TEXT,"W1: " + DoubleToString(sma10080, _Digits)); if (ch5u[6] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue); if (ch5d[6] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); } else ObjectDelete(0, Name + "10080mMAL"); }
		else ObjectDelete(0, Name + "10080mMAL"); }
}
//-------------------------------------------------------------------+

//+TF sl's-----------------------------------------------------------+
void sls() {
	int m5slu = 0, m5sld = 0;
	double sl5upnew = 0, sl5dnnew = 0;
	int m15slu = 0, m15sld = 0;
	double sl15upnew = 0, sl15dnnew = 0;
	int m30slu = 0, m30sld = 0;
	double sl30upnew = 0, sl30dnnew = 0;
	int m60slu = 0, m60sld = 0;
	double sl60upnew = 0, sl60dnnew = 0;
	int m240slu = 0, m240sld = 0;
	double sl240upnew = 0, sl240dnnew = 0;
	int m1440slu = 0, m1440sld = 0;
	double sl1440upnew = 0, sl1440dnnew = 0;
	int m10080slu = 0, m10080sld = 0;
	double sl10080upnew = 0, sl10080dnnew = 0;

	for (int x = iBars(_Symbol, PERIOD_M5) - 20; x >= 1; x--) {
		//M5
		if ((iClose(_Symbol, PERIOD_M5, x + 5) > ma5[x + 5] && iClose(_Symbol, PERIOD_M5, x + 4) > ma5[x + 4] && iClose(_Symbol, PERIOD_M5, x + 3) > ma5[x + 3] && iClose(_Symbol, PERIOD_M5, x + 2) > ma5[x + 2] && iClose(_Symbol, PERIOD_M5, x + 1) > ma5[x + 1]) && (iClose(_Symbol, PERIOD_M5, x) > ma5[x] && iLow(_Symbol, PERIOD_M5, x) < ma5[x])) {
			m5slu++;
			sl5upnew += ma5[x] - iLow(_Symbol, PERIOD_M5, x);
		}
		if ((iClose(_Symbol, PERIOD_M5, x + 5) < ma5[x + 5] && iClose(_Symbol, PERIOD_M5, x + 4) < ma5[x + 4] && iClose(_Symbol, PERIOD_M5, x + 3) < ma5[x + 3] && iClose(_Symbol, PERIOD_M5, x + 2) < ma5[x + 2] && iClose(_Symbol, PERIOD_M5, x + 1) < ma5[x + 1]) && (iClose(_Symbol, PERIOD_M5, x) < ma5[x] && iHigh(_Symbol, PERIOD_M5, x) > ma5[x])) {
			m5sld++;
			sl5dnnew += iHigh(_Symbol, PERIOD_M5, x) - ma5[x];
		}
	}

	for (int x = iBars(_Symbol, PERIOD_M15) - 20; x >= 1; x--) {
		//M15
		if ((iClose(_Symbol, PERIOD_M15, x + 5) > ma15[x + 5] && iClose(_Symbol, PERIOD_M15, x + 4) > ma15[x + 4] && iClose(_Symbol, PERIOD_M15, x + 3) > ma15[x + 3] && iClose(_Symbol, PERIOD_M15, x + 2) > ma15[x + 2] && iClose(_Symbol, PERIOD_M15, x + 1) > ma15[x + 1]) && (iClose(_Symbol, PERIOD_M15, x) > ma15[x] && iLow(_Symbol, PERIOD_M15, x) < ma15[x])) {
			m15slu++;
			sl15upnew += ma15[x] - iLow(_Symbol, PERIOD_M15, x);
		}
		if ((iClose(_Symbol, PERIOD_M15, x + 5) < ma15[x + 5] && iClose(_Symbol, PERIOD_M15, x + 4) < ma15[x + 4] && iClose(_Symbol, PERIOD_M15, x + 3) < ma15[x + 3] && iClose(_Symbol, PERIOD_M15, x + 2) < ma15[x + 2] && iClose(_Symbol, PERIOD_M15, x + 1) < ma15[x + 1]) && (iClose(_Symbol, PERIOD_M15, x) < ma15[x] && iHigh(_Symbol, PERIOD_M15, x) > ma15[x])) {
			m15sld++;
			sl15dnnew += iHigh(_Symbol, PERIOD_M15, x) - ma15[x];
		}
	}

	for (int x = iBars(_Symbol, PERIOD_M30) - 20; x >= 1; x--) {
		//M30
		if ((iClose(_Symbol, PERIOD_M30, x + 5) > ma30[x + 5] && iClose(_Symbol, PERIOD_M30, x + 4) > ma30[x + 4] && iClose(_Symbol, PERIOD_M30, x + 3) > ma30[x + 3] && iClose(_Symbol, PERIOD_M30, x + 2) > ma30[x + 2] && iClose(_Symbol, PERIOD_M30, x + 1) > ma30[x + 1]) && (iClose(_Symbol, PERIOD_M30, x) > ma30[x] && iLow(_Symbol, PERIOD_M30, x) < ma30[x])) {
			m30slu++;
			sl30upnew += ma30[x] - iLow(_Symbol, PERIOD_M30, x);
		}
		if ((iClose(_Symbol, PERIOD_M30, x + 5) < ma30[x + 5] && iClose(_Symbol, PERIOD_M30, x + 4) < ma30[x + 4] && iClose(_Symbol, PERIOD_M30, x + 3) < ma30[x + 3] && iClose(_Symbol, PERIOD_M30, x + 2) < ma30[x + 2] && iClose(_Symbol, PERIOD_M30, x + 1) < ma30[x + 1]) && (iClose(_Symbol, PERIOD_M30, x) < ma30[x] && iHigh(_Symbol, PERIOD_M30, x) > ma30[x])) {
			m30sld++;
			sl30dnnew += iHigh(_Symbol, PERIOD_M30, x) - ma30[x];
		}
	}

	for (int x = iBars(_Symbol, PERIOD_H1) - 20; x >= 1; x--) {
		//H1
		if ((iClose(_Symbol, PERIOD_H1, x + 5) > ma60[x + 5] && iClose(_Symbol, PERIOD_H1, x + 4) > ma60[x + 4] && iClose(_Symbol, PERIOD_H1, x + 3) > ma60[x + 3] && iClose(_Symbol, PERIOD_H1, x + 2) > ma60[x + 2] && iClose(_Symbol, PERIOD_H1, x + 1) > ma60[x + 1]) && (iClose(_Symbol, PERIOD_H1, x) > ma60[x] && iLow(_Symbol, PERIOD_H1, x) < ma60[x])) {
			m60slu++;
			sl60upnew += ma60[x] - iLow(_Symbol, PERIOD_H1, x);
		}
		if ((iClose(_Symbol, PERIOD_H1, x + 5) < ma60[x + 5] && iClose(_Symbol, PERIOD_H1, x + 4) < ma60[x + 4] && iClose(_Symbol, PERIOD_H1, x + 3) < ma60[x + 3] && iClose(_Symbol, PERIOD_H1, x + 2) < ma60[x + 2] && iClose(_Symbol, PERIOD_H1, x + 1) < ma60[x + 1]) && (iClose(_Symbol, PERIOD_H1, x) < ma60[x] && iHigh(_Symbol, PERIOD_H1, x) > ma60[x])) {
			m60sld++;
			sl60dnnew += iHigh(_Symbol, PERIOD_H1, x) - ma60[x];
		}
	}

	for (int x = iBars(_Symbol, PERIOD_H4) - 20; x >= 1; x--) {
		//H4
		if ((iClose(_Symbol, PERIOD_H4, x + 5) > ma240[x + 5] && iClose(_Symbol, PERIOD_H4, x + 4) > ma240[x + 4] && iClose(_Symbol, PERIOD_H4, x + 3) > ma240[x + 3] && iClose(_Symbol, PERIOD_H4, x + 2) > ma240[x + 2] && iClose(_Symbol, PERIOD_H4, x + 1) > ma240[x + 1]) && (iClose(_Symbol, PERIOD_H4, x) > ma240[x] && iLow(_Symbol, PERIOD_H4, x) < ma240[x])) {
			m240slu++;
			sl240upnew += ma240[x] - iLow(_Symbol, PERIOD_H4, x);
		}
		if ((iClose(_Symbol, PERIOD_H4, x + 5) < ma240[x + 5] && iClose(_Symbol, PERIOD_H4, x + 4) < ma240[x + 4] && iClose(_Symbol, PERIOD_H4, x + 3) < ma240[x + 3] && iClose(_Symbol, PERIOD_H4, x + 2) < ma240[x + 2] && iClose(_Symbol, PERIOD_H4, x + 1) < ma240[x + 1]) && (iClose(_Symbol, PERIOD_H4, x) < ma240[x] && iHigh(_Symbol, PERIOD_H4, x) > ma240[x])) {
			m240sld++;
			sl240dnnew += iHigh(_Symbol, PERIOD_H4, x) - ma240[x];
		}
	}

	for (int x = iBars(_Symbol, PERIOD_D1) - 20; x >= 1; x--) {
		//D1
		if ((iClose(_Symbol, PERIOD_D1, x + 5) > ma1440[x + 5] && iClose(_Symbol, PERIOD_D1, x + 4) > ma1440[x + 4] && iClose(_Symbol, PERIOD_D1, x + 3) > ma1440[x + 3] && iClose(_Symbol, PERIOD_D1, x + 2) > ma1440[x + 2] && iClose(_Symbol, PERIOD_D1, x + 1) > ma1440[x + 1]) && (iClose(_Symbol, PERIOD_D1, x) > ma1440[x] && iLow(_Symbol, PERIOD_D1, x) < ma1440[x])) {
			m1440slu++;
			sl1440upnew += ma1440[x] - iLow(_Symbol, PERIOD_D1, x);
		}
		if ((iClose(_Symbol, PERIOD_D1, x + 5) < ma1440[x + 5] && iClose(_Symbol, PERIOD_D1, x + 4) < ma1440[x + 4] && iClose(_Symbol, PERIOD_D1, x + 3) < ma1440[x + 3] && iClose(_Symbol, PERIOD_D1, x + 2) < ma1440[x + 2] && iClose(_Symbol, PERIOD_D1, x + 1) < ma1440[x + 1]) && (iClose(_Symbol, PERIOD_D1, x) < ma1440[x] && iHigh(_Symbol, PERIOD_D1, x) > ma1440[x])) {
			m1440sld++;
			sl1440dnnew += iHigh(_Symbol, PERIOD_D1, x) - ma1440[x];
		}
	}

	for (int x = iBars(_Symbol, PERIOD_W1) - 20; x >= 1; x--) {
		//W1
		if ((iClose(_Symbol, PERIOD_W1, x + 5) > ma10080[x + 5] && iClose(_Symbol, PERIOD_W1, x + 4) > ma10080[x + 4] && iClose(_Symbol, PERIOD_W1, x + 3) > ma10080[x + 3] && iClose(_Symbol, PERIOD_W1, x + 2) > ma10080[x + 2] && iClose(_Symbol, PERIOD_W1, x + 1) > ma10080[x + 1]) && (iClose(_Symbol, PERIOD_W1, x) > ma10080[x] && iLow(_Symbol, PERIOD_W1, x) < ma10080[x])) {
			m10080slu++;
			sl10080upnew += ma10080[x] - iLow(_Symbol, PERIOD_W1, x);
		}
		if ((iClose(_Symbol, PERIOD_W1, x + 5) < ma10080[x + 5] && iClose(_Symbol, PERIOD_W1, x + 4) < ma10080[x + 4] && iClose(_Symbol, PERIOD_W1, x + 3) < ma10080[x + 3] && iClose(_Symbol, PERIOD_W1, x + 2) < ma10080[x + 2] && iClose(_Symbol, PERIOD_W1, x + 1) < ma10080[x + 1]) && (iClose(_Symbol, PERIOD_W1, x) < ma10080[x] && iHigh(_Symbol, PERIOD_W1, x) > ma10080[x])) {
			m10080sld++;
			sl10080dnnew += iHigh(_Symbol, PERIOD_W1, x) - ma10080[x];
		}
	}
   
   Print(m5slu + " " + m15slu + " " + Pip);
	slup[0] = slmulti * (sl5upnew / m5slu) / Pip;
	slup[1] = slmulti * (sl15upnew / m15slu) / Pip;
	slup[2] = slmulti * (sl30upnew / m30slu) / Pip;
	slup[3] = slmulti * (sl60upnew / m60slu) / Pip;
	slup[4] = slmulti * (sl240upnew / m240slu) / Pip;
	slup[5] = slmulti * (sl1440upnew / m1440slu) / Pip;
	slup[6] = slmulti * (sl10080upnew / m10080slu) / Pip;

	sldn[0] = slmulti * (sl5dnnew / m5sld) / Pip;
	sldn[1] = slmulti * (sl15dnnew / m15sld) / Pip;
	sldn[2] = slmulti * (sl30dnnew / m30sld) / Pip;
	sldn[3] = slmulti * (sl60dnnew / m60sld) / Pip;
	sldn[4] = slmulti * (sl240dnnew / m240sld) / Pip;
	sldn[5] = slmulti * (sl1440dnnew / m1440sld) / Pip;
	sldn[6] = slmulti * (sl10080dnnew / m10080sld) / Pip;
}
//+------------------------------------------------------------------+

//+BUILD ALERT & COMMENT 15M-----------------------------------------+
void alerter5() {
	if (drawgfx && alerts) {
		if ((ch5u[0] == 5 && iClose(_Symbol, PERIOD_M5, 0) < ma5[0]) || (ch5d[0] == 5 && iClose(_Symbol, PERIOD_M5, 0) > ma5[0])) {
			string obname = Name + "W1";
			LabelMake(obname, 0, p + 180, o + 10, "TB", FontSize, clrBlue);
			if (ch5u[0] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
			ObjectSetInteger(0, Name + "5mMA", OBJPROP_COLOR, (color)ObjectGetInteger(0,obname,OBJPROP_COLOR));
			Alert(_Symbol + " Reversal? " + "M5");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "pos reversal M5 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		else ObjectDelete(0, Name + "W1");
		if (ch10u[0] == 10 || ch15u[0] == 15) {
			Alert(_Symbol + " strong trend up " + "M5");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong up M5 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch10d[0] == 10 || ch15d[0] == 15) {
			Alert(_Symbol + " strong trend down " + "M5");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong down M5 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30u[0] == 30 || ch20u[0] == 20) {
			Alert(_Symbol + " very strong trend up " + "M5. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong up M5 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30d[0] == 30 || ch20d[0] == 20) {
			Alert(_Symbol + " very strong trend down " + "M5. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong down M5 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
	}
}
//+------------------------------------------------------------------+

//+BUILD ALERT & COMMENT 15M-----------------------------------------+
void alerter15() {
	if (drawgfx) {
		if ((ch5u[5] >= 4) && (ch10u[0] == 10 || ch5u[1] == 5 || ch5u[2] == 5)) {
			Alert(_Symbol + " small TF align with bigger trend M5|M15|M30 - D1 **UP**");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "small TF align with bigger trend M5|M15 - D1 **UP** " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		else if ((ch10u[4] >= 8) && (ch10u[0] == 10 || ch5u[1] == 5 || ch5u[2] == 5)) {
			Alert(_Symbol + " small TF align with bigger trend M5|M15|M30 - H4 **UP**");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "small TF align with bigger trend M5|M15 - H4 **UP** " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if ((ch5d[5] >= 4) && (ch10d[0] == 10 || ch5d[1] == 5 || ch5d[2] == 5)) {
			Alert(_Symbol + " small TF align with bigger trend M5|M15|M30 - D1 **DN**");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "small TF align with bigger trend M5|M15 - D1 **DN** " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		else if ((ch10d[4] >= 8) && (ch10d[0] == 10 || ch5d[1] == 5 || ch5d[2] == 5)) {
			Alert(_Symbol + " small TF align with bigger trend M5|M15|M30 - H4 **DN**");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "small TF align with bigger trend M5|M15 - H4 **DN** " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
	}
}
//+------------------------------------------------------------------+
/*
//+BUILD ALERT & COMMENT 15M-----------------------------------------+
void alerter15() {
	if (drawgfx && alerts) {
		if ((ch5u[1] == 5 && iClose(_Symbol, PERIOD_M15, 0) < ma15[0]) || (ch5d[1] == 5 && iClose(_Symbol, PERIOD_M15, 0) > ma15[0])) {
			string obname = Name + "W2";
			LabelMake(obname, 0, p + 180, o + 20, "TB", FontSize, clrBlue);
			if (ch5u[1] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
			ObjectSetInteger(0, Name + "15mMA", OBJPROP_COLOR, (color)ObjectGet(obname, OBJPROP_COLOR));
			ObjectSetInteger(0, Name + "15mMA1", OBJPROP_COLOR, (color)ObjectGet(obname, OBJPROP_COLOR));
			Alert(_Symbol + " Reversal? " + "M15");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "pos reversal M15 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		else ObjectDelete(Name + "W2");
		if (ch10u[1] == 10 || ch15u[1] == 15) {
			Alert(_Symbol + " strong trend up " + "M15");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong up M15 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch10d[1] == 10 || ch15d[1] == 15) {
			Alert(_Symbol + " strong trend down " + "M15");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong down M15 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30u[1] == 30 || ch20u[1] == 20) {
			Alert(_Symbol + " very strong trend up " + "M15. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong up M15 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30d[1] == 30 || ch20d[1] == 20) {
			Alert(_Symbol + " very strong trend down " + "M15. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong down M15 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
	}
}
//+------------------------------------------------------------------+
*/
//+BUILD ALERT & COMMENT 30M-----------------------------------------+
void alerter30() {
	if (drawgfx && alerts) {
		if ((ch5u[2] == 5 && iClose(_Symbol, PERIOD_M30, 0) < ma30[0]) || (ch5d[2] == 5 && iClose(_Symbol, PERIOD_M30, 0) > ma30[0])) {
			string obname = Name + "W3";
			LabelMake(obname, 0, p + 180, o + 30, "TB", FontSize, clrBlue);
			if (ch5u[2] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
			ObjectSetInteger(0, Name + "30mMA", OBJPROP_COLOR, (color)ObjectGetInteger(0,obname,OBJPROP_COLOR));
			ObjectSetInteger(0, Name + "30mMA1", OBJPROP_COLOR, (color)ObjectGetInteger(0,obname,OBJPROP_COLOR));
			Alert(_Symbol + " Reversal? " + "M30");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "pos reversal M30 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		else ObjectDelete(0, Name + "W3");
		if (ch10u[2] == 10 || ch15u[2] == 15) {
			Alert(_Symbol + " strong trend up " + "M30");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong up M30 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch10d[2] == 10 || ch15d[2] == 15) {
			Alert(_Symbol + " strong trend down " + "M30");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong down M30 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30u[2] == 30 || ch20u[2] == 20) {
			Alert(_Symbol + " very strong trend up " + "M30. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong up M30 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30d[2] == 30 || ch20d[2] == 20) {
			Alert(_Symbol + " very strong trend down " + "M30. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong down M30 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
	}
}
//+------------------------------------------------------------------+

//+BUILD ALERT & COMMENT 60M-----------------------------------------+
void alerter60() {
	if (drawgfx && alerts) {
		if ((ch5u[3] == 5 && iClose(_Symbol, PERIOD_H1, 0) < ma60[0]) || (ch5d[3] == 5 && iClose(_Symbol, PERIOD_H1, 0) > ma60[0])) {
			string obname = Name + "W4";
			LabelMake(obname, 0, p + 180, o + 40, "TB", FontSize, clrBlue);
			if (ch5u[3] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
			ObjectSetInteger(0, Name + "60mMA", OBJPROP_COLOR, (color)ObjectGetInteger(0,obname,OBJPROP_COLOR));
			ObjectSetInteger(0, Name + "60mMA1", OBJPROP_COLOR, (color)ObjectGetInteger(0,obname,OBJPROP_COLOR));
			Alert(_Symbol + " Reversal? " + "H1");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "pos reversal H1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		else ObjectDelete(0, Name + "W4");
		if (ch10u[3] == 10 || ch15u[3] == 15) {
			Alert(_Symbol + " strong trend up " + "H1");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong up H1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch10d[3] == 10 || ch15d[3] == 15) {
			Alert(_Symbol + " strong trend down " + "H1");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong down H1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30u[3] == 30 || ch20u[3] == 20) {
			Alert(_Symbol + " very strong trend up " + "H1. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong up H1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30d[3] == 30 || ch20d[3] == 20) {
			Alert(_Symbol + " very strong trend down " + "H1. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong down H1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
	}
}
//+------------------------------------------------------------------+

//+BUILD ALERT & COMMENT 240M----------------------------------------+
void alerter240() {
	if (drawgfx && alerts) {
		if ((ch5u[4] == 5 && iClose(_Symbol, PERIOD_H4, 0) < ma240[0]) || (ch5d[4] == 5 && iClose(_Symbol, PERIOD_H4, 0) > ma240[0])) {
			string obname = Name + "W5";
			LabelMake(obname, 0, p + 180, o + 50, "TB", FontSize, clrBlue);
			if (ch5u[4] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
			ObjectSetInteger(0, Name + "240mMA", OBJPROP_COLOR, (color)ObjectGetInteger(0,obname,OBJPROP_COLOR));
			ObjectSetInteger(0, Name + "240mMA1", OBJPROP_COLOR, (color)ObjectGetInteger(0,obname,OBJPROP_COLOR));
			Alert(_Symbol + " Reversal? " + "H4");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "pos reversal H4 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		else ObjectDelete(0, Name + "W5");
		if (ch10u[4] == 10 || ch15u[4] == 15) {
			Alert(_Symbol + " strong trend up " + "H4");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong up H4 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch10d[4] == 10 || ch15d[4] == 15) {
			Alert(_Symbol + " strong trend down " + "H4");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong down H4 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30u[4] == 30 || ch20u[4] == 20) {
			Alert(_Symbol + " very strong trend up " + "H4. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong up H4 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30d[4] == 30 || ch20d[4] == 20) {
			Alert(_Symbol + " very strong trend down " + "H4. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong down H4 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
	}
}
//+------------------------------------------------------------------+

//+BUILD ALERT & COMMENT 1440M---------------------------------------+
void alerter1440() {
	if (drawgfx && alerts) {
		if ((ch5u[5] == 5 && iClose(_Symbol, PERIOD_D1, 0) < ma1440[0]) || (ch5d[5] == 5 && iClose(_Symbol, PERIOD_D1, 0) > ma1440[0])) {
			string obname = Name + "W6";
			LabelMake(obname, 0, p + 180, o + 60, "TB", FontSize, clrBlue);
			if (ch5u[5] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
			ObjectSetInteger(0, Name + "1440mMA", OBJPROP_COLOR, (color)ObjectGetInteger(0,obname,OBJPROP_COLOR));
			ObjectSetInteger(0, Name + "1440mMA1", OBJPROP_COLOR, (color)ObjectGetInteger(0,obname,OBJPROP_COLOR));
			Alert(_Symbol + " Reversal? " + "D1");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "pos reversal D1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		else ObjectDelete(0, Name + "W6");
		if (ch10u[5] == 10 || ch15u[5] == 15) {
			Alert(_Symbol + " strong trend up " + "D1");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong up D1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch10d[5] == 10 || ch15d[5] == 15) {
			Alert(_Symbol + " strong trend down " + "D1");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong down D1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30u[5] == 30 || ch20u[5] == 20) {
			Alert(_Symbol + " very strong trend up " + "D1. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong up D1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30d[5] == 30 || ch20d[5] == 20) {
			Alert(_Symbol + " very strong trend down " + "D1. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong down D1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
	}
}
//+------------------------------------------------------------------+

//+BUILD ALERT & COMMENT 10080M---------------------------------------+
void alerter10080() {
	if (drawgfx && alerts) {
		if ((ch5u[6] == 5 && iClose(_Symbol, PERIOD_W1, 0) < ma10080[0]) || (ch5d[6] == 5 && iClose(_Symbol, PERIOD_W1, 0) > ma10080[0])) {
			string obname = Name + "W7";
			LabelMake(obname, 0, p + 180, o + 70, "TB", FontSize, clrBlue);
			if (ch5u[6] == 5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
			ObjectSetInteger(0, Name + "10080mMA", OBJPROP_COLOR, (color)ObjectGetInteger(0, obname,OBJPROP_COLOR));
			ObjectSetInteger(0, Name + "10080mMA1", OBJPROP_COLOR, (color)ObjectGetInteger(0, obname,OBJPROP_COLOR));
			Alert(_Symbol + " Reversal? " + "W1");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "pos reversal W1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		else ObjectDelete(0, Name + "W7");
		if (ch10u[6] == 10 || ch15u[6] == 15) {
			Alert(_Symbol + " strong trend up " + "W1");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong up W1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch10d[6] == 10 || ch15d[6] == 15) {
			Alert(_Symbol + " strong trend down " + "W1");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "strong down W1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30u[6] == 30 || ch20u[6] == 20) {
			Alert(_Symbol + " very strong trend up " + "W1. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong up W1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
		if (ch30d[6] == 30 || ch20d[6] == 20) {
			Alert(_Symbol + " very strong trend down " + "W1. PB possible.");
			managecomments(TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES) + " " + "very strong down W1 - " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
		}
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
void reclabloc() {
	p = (int)GlobalVariableGet("TCP" + _Symbol); o = (int)GlobalVariableGet("TCO" + _Symbol);
	string obname;
	obname = Name + " LabRec";
	RecMake(obname, p - 5, o - 5, 380, 95, clrWhite, clrBlack);

	obname = Name + " MovRec";
	RecMake(obname, p - 4, o - 4, 0, 0, clrWhite, clrBlack);
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
	ObjectSetInteger(0, obname, OBJPROP_SELECTABLE, true);
	ObjectSetInteger(0, obname, OBJPROP_SELECTED, true);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function 
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed"; break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed"; break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed"; break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed"; break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled"; break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart"; break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart"; break;
	default:text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
	const int corner,
	const int x,
	const int y,
	const string label,
	const int FSize,
	const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetTextMQL4(name, label, FSize, Font, FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+COMMENT BOX-------------------------------------------------------+
void managecomments(string addcomment)
{
	string tempcomments[];
	int commentscroll;
	string output;
	int CommentCount = ArrayRange(allcomments, 0);
	if (CommentCount < MaxCommentsToShow)
	{
		ArrayResize(tempcomments, CommentCount + 1);
		ArrayCopy(tempcomments, allcomments, 1, 0, WHOLE_ARRAY);
	}
	else
	{
		ArrayResize(tempcomments, MaxCommentsToShow);
		ArrayCopy(tempcomments, allcomments, 1, 0, MaxCommentsToShow - 1);
	}
	tempcomments[0] = addcomment;
	CommentCount = ArrayRange(tempcomments, 0);
	ArrayResize(allcomments, CommentCount);
	ArrayCopy(allcomments, tempcomments, 0, 0, CommentCount);

	for (commentscroll = 0; commentscroll < CommentCount; commentscroll++)
	{
		output = output + allcomments[commentscroll] + "\n";
	}
	Comment(output);
}
//+------------------------------------------------------------------+

//+CREATE T-LINES----------------------------------------------------+
void objhoriz(string oname, double pr1, color col) {
	if (ObjectFind(0, oname) < 0)
		if (!ObjectCreate(0, oname, OBJ_HLINE, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0,oname,OBJPROP_STYLE,(int)STYLE_DOT);
	ObjectSetInteger(0,oname,OBJPROP_WIDTH,(int)0);
	ObjectSetInteger(0,oname,OBJPROP_BACK,(int)true);
	ObjectSetInteger(0,oname,OBJPROP_COLOR,(int)col);
	ObjectSetDouble(0,oname,OBJPROP_PRICE,pr1);
	ObjectSetInteger(0,oname,OBJPROP_SELECTABLE,false);
}
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const int x, const int y, const int xs, const int ys, const color FCol, const color BCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_BGCOLOR, FCol);
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetInteger(0, name, OBJPROP_XSIZE, xs);
	ObjectSetInteger(0, name, OBJPROP_YSIZE, ys);
	ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, Name);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+ARROWPRICE--------------------------------------------------------+
void ArrowPrice(const string name, const double x, const datetime y, const color FCol)
{  datetime Time[];
   ArraySetAsSeries(Time,true);
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW_RIGHT_PRICE, 0, 0, 0))
		{
			Print("error: can't create arrow_right_price! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE, x);
	ObjectSetInteger(0, name, OBJPROP_TIME, CopyTime(_Symbol,_Period,0,0, Time) + y);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "SL Price: " + DoubleToString(x, _Digits));
}
//+------------------------------------------------------------------+

bool ObjectSetTextMQL4(string name,
                       string text,
                       int font_size,
                       string font="",
                       color text_color=CLR_NONE)
  {
   int tmpObjType=(int)ObjectGetInteger(0,name,OBJPROP_TYPE);
   if(tmpObjType!=OBJ_LABEL && tmpObjType!=OBJ_TEXT) return(false);
   if(StringLen(text)>0 && font_size>0)
     {
      if(ObjectSetString(0,name,OBJPROP_TEXT,text)==true
         && ObjectSetInteger(0,name,OBJPROP_FONTSIZE,font_size)==true)
        {
         if((StringLen(font)>0)
            && ObjectSetString(0,name,OBJPROP_FONT,font)==false)
            return(false);
         if(text_color>-1
            && ObjectSetInteger(0,name,OBJPROP_COLOR,text_color)==false)
            return(false);
         return(true);
        }
      return(false);
     }
   return(false);
  }