//+------------------------------------------------------------------+
//|                                           Picture of Power.mq5   |
//|                                           Copyright 2019, Sakis  |
//|                                                                  |
//+------------------------------------------------------------------+

// Version History
// ---------------
// v1.0 First version (<PERSON>)

#property strict
#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"
#define Name MQLInfoString(MQL_PROGRAM_NAME) + "U"

#property indicator_buffers 0
#property indicator_plots 0

//--- bars minimum for calculation
#define DATA_LIMIT 200

int MA200s, MA20s;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
	IndicatorSetString(INDICATOR_SHORTNAME, "PoP");

	MA20s = iMA(Symbol(), PERIOD_M2, 20, 0, MODE_SMA, PRICE_CLOSE);
	MA200s = iMA(Symbol(), PERIOD_M2, 200, 0, MODE_SMA, PRICE_CLOSE);

	string obname;
	obname = Name + " PoP1"; LabelMake(obname, 3, 80, 250, "", 9, clrBlack);
	obname = Name + " PoP2"; LabelMake(obname, 3, 105, 250, "", 9, clrBlack);
	//---
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-init function								 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || MQLInfoInteger(MQL_TESTER))
		if (!MQLInfoInteger(MQL_TESTER))
		{
			DeleteObjects();
		}
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	//ObjectsDeleteAll(0, Name);
	for (int i = ObjectsTotal(0, 0) - 1; i >= 0; i--)
	{
		string ObName = ObjectName(0, i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(0, ObName);
		}
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	//--- check for rates total
	if (rates_total < DATA_LIMIT) return(0); // not enough bars for calculation

	if (BarsCalculated(MA20s) < 200 || BarsCalculated(MA200s) < 200)
		return(0);

	bool new_1m_check = false;
	static datetime start_1m_time = 0;
	if (start_1m_time < iTime(NULL, PERIOD_M2, 0))
	{
		new_1m_check = true;
		start_1m_time = iTime(NULL, PERIOD_M2, 0);
	}
	if (new_1m_check)
	{
		flip2();
		//Print("Did I run?");
		new_1m_check = false;
	}

	//--- return value of prev_calculated for next call
	return(rates_total);
}
//+------------------------------------------------------------------+

//+Fill past 20 edges & mid------------------------------------------+
void flip2() {
	double MA200[], MA20[];
	ArrayResize(MA200, 7); ArrayResize(MA20, 7);
	if ((CopyBuffer(MA20s, 0, 0, 7, MA20) < 0) || (CopyBuffer(MA200s, 0, 0, 7, MA200) < 0)) Print("Copy Failed");
	ArraySetAsSeries(MA20, true); ArraySetAsSeries(MA200, true);

	bool PoPup = false, PoPdn = false;

	int z = 0, y = 0;

	for (int x = 5; x >= 1; x--) {
		if (iLow(_Symbol, PERIOD_M2, x) > MA20[x] && iLow(_Symbol, PERIOD_M2, x) > MA200[x] && MA20[x] > MA200[x]) z++;
		if (iHigh(_Symbol, PERIOD_M2, x) < MA20[x] && iHigh(_Symbol, PERIOD_M2, x) < MA200[x] && MA20[x] < MA200[x]) y++;
	}

	if (z == 5) PoPup = true;
	if (y == 5) PoPdn = true;
	if (z < 5 && y < 5) { PoPup = false; PoPdn = false; }

	bool DoPup = false, DoPdn = false;

	int v = 0, w = 0;

	for (int x = 6; x >= 2; x--) {
		if (iLow(_Symbol, PERIOD_M2, x) > MA20[x] && iLow(_Symbol, PERIOD_M2, x) > MA200[x] && MA20[x] > MA200[x]) v++;
		if (iHigh(_Symbol, PERIOD_M2, x) < MA20[x] && iHigh(_Symbol, PERIOD_M2, x) < MA200[x] && MA20[x] < MA200[x]) w++;
	}

	if (v == 5 && iLow(_Symbol, PERIOD_M2, 1) < MA20[1] && iClose(_Symbol, PERIOD_M2, 1) > MA20[1]) DoPup = true;
	if (w == 5 && iHigh(_Symbol, PERIOD_M2, 1) > MA20[1] && iClose(_Symbol, PERIOD_M2, 1) < MA20[1]) DoPdn = true;
	if (v < 5 && w < 5) { DoPup = false; DoPdn = false; }

	//Print(z + " " + y);

	if (PoPup && iLow(_Symbol, PERIOD_M2, 1) > MA20[1] && iClose(_Symbol, PERIOD_M2, 1) > MA20[1]) { ObjectSetTextMQL4(Name + " PoP1", "PoP UP!", 12, "Arial Black", clrBlue); ObjectSetTextMQL4(Name + " PoP2", CharToString(233), 12, "Wingdings", clrBlue); Alert(_Symbol + " PoP UP!"); }
	if (PoPdn && iHigh(_Symbol, PERIOD_M2, 1) < MA20[1] && iClose(_Symbol, PERIOD_M2, 1) < MA20[1]) { ObjectSetTextMQL4(Name + " PoP1", "PoP DN!", 12, "Arial Black", clrRed); ObjectSetTextMQL4(Name + " PoP2", CharToString(234), 12, "Wingdings", clrRed); Alert(_Symbol + " PoP DN!"); }
	if (!PoPup && !PoPdn) { ObjectSetTextMQL4(Name + " PoP1", "No PoP :(", 12, "Arial Black", clrBlack); ObjectSetTextMQL4(Name + " PoP2", CharToString(232), 12, "Wingdings", clrBlack); }
	if (DoPup) Alert(_Symbol + " PoP UP CONTINUATION? *!*!*!*");
	if (DoPdn) Alert(_Symbol + " PoP DN CONTINUATION? *!*!*!*");
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
	const int corner,
	const int x,
	const int y,
	const string label,
	const int FSize,
	const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetString(0, name, OBJPROP_TEXT, label);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+OBJECTSETTEXT MQL4------------------------------------------------+
bool ObjectSetTextMQL4(string name,
	string text,
	int font_size,
	string font = "",
	color text_color = CLR_NONE)
{
	int tmpObjType = (int)ObjectGetInteger(0, name, OBJPROP_TYPE);
	if (tmpObjType != OBJ_LABEL && tmpObjType != OBJ_TEXT) return(false);
	if (StringLen(text) > 0 && font_size > 0)
	{
		if (ObjectSetString(0, name, OBJPROP_TEXT, text) == true
			&& ObjectSetInteger(0, name, OBJPROP_FONTSIZE, font_size) == true)
		{
			if ((StringLen(font) > 0)
				&& ObjectSetString(0, name, OBJPROP_FONT, font) == false)
				return(false);
			if (text_color > 0
				&& ObjectSetInteger(0, name, OBJPROP_COLOR, text_color) == false)
				return(false);
			return(true);
		}
		return(false);
	}
	return(false);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function 
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed"; break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed"; break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed"; break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed"; break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled"; break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart"; break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart"; break;
	default:text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+