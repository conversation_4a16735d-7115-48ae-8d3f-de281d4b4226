//+------------------------------------------------------------------+
//|                                                        vwapm.mq5 |
//|                        Copyright 2019, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2019, MetaQuotes Software Corp."
#property link "https://www.mql5.com"
#property version "1.00"
#property indicator_chart_window
#define Name MQLInfoString(MQL_PROGRAM_NAME)
#property indicator_buffers 28
#property indicator_plots 28

//VWAP
#property indicator_type1 DRAW_NONE
#property indicator_color1 clrLime
#property indicator_style1 STYLE_SOLID
#property indicator_width1 2
#property indicator_label1 "VWAP"
//VWAP SD POS1
#property indicator_type2 DRAW_NONE
#property indicator_color2 clrGray
#property indicator_style2 STYLE_DOT
#property indicator_label2 "VWAP SDPos1"
//VWAP SD NEG 1
#property indicator_type3 DRAW_NONE
#property indicator_color3 clrGray
#property indicator_style3 STYLE_DOT
#property indicator_label3 "VWAP SDNeg1"
//VWAP SD POS 2
#property indicator_type4 DRAW_NONE
#property indicator_color4 clrGray
#property indicator_style4 STYLE_DOT
#property indicator_label4 "VWAP SDPos2"
//VWAP SD NEG 2
#property indicator_type5 DRAW_NONE
#property indicator_color5 clrGray
#property indicator_style5 STYLE_DOT
#property indicator_label5 "VWAP SDNeg2"
//VWAP SD POS 3
#property indicator_type6 DRAW_NONE
#property indicator_color6 clrGray
#property indicator_style6 STYLE_DOT
#property indicator_label6 "VWAP SDPos3"
//VWAP SD NEG 3
#property indicator_type7 DRAW_NONE
#property indicator_color7 clrGray
#property indicator_style7 STYLE_DOT
#property indicator_label7 "VWAP SDNeg3"
//VWAP
#property indicator_type8 DRAW_NONE
#property indicator_color8 clrBlue
#property indicator_style8 STYLE_SOLID
#property indicator_width8 2
#property indicator_label8 "iVWAP"
//VWAP SD POS1
#property indicator_type9 DRAW_NONE
#property indicator_color9 clrDodgerBlue
#property indicator_style9 STYLE_DOT
#property indicator_label9 "iVWAP SDPos1"
//VWAP SD NEG 1
#property indicator_type10 DRAW_NONE
#property indicator_color10 clrDodgerBlue
#property indicator_style10 STYLE_DOT
#property indicator_label10 "iVWAP SDNeg1"
//VWAP SD POS 2
#property indicator_type11 DRAW_NONE
#property indicator_color11 clrDodgerBlue
#property indicator_style11 STYLE_DOT
#property indicator_label11 "iVWAP SDPos2"
//VWAP SD NEG 2
#property indicator_type12 DRAW_NONE
#property indicator_color12 clrDodgerBlue
#property indicator_style12 STYLE_DOT
#property indicator_label12 "iVWAP SDNeg2"
//VWAP SD POS 3
#property indicator_type13 DRAW_NONE
#property indicator_color13 clrDodgerBlue
#property indicator_style13 STYLE_DOT
#property indicator_label13 "iVWAP SDPos3"
//VWAP SD NEG 3
#property indicator_type14 DRAW_NONE
#property indicator_color14 clrDodgerBlue
#property indicator_style14 STYLE_DOT
#property indicator_label14 "iVWAP SDNeg3"
//VWAP
#property indicator_type15 DRAW_NONE
#property indicator_color15 clrWhite
#property indicator_style15 STYLE_SOLID
#property indicator_width15 2
#property indicator_label15 "wkVWAP"
//VWAP SD POS1
#property indicator_type16 DRAW_NONE
#property indicator_color16 clrWhiteSmoke
#property indicator_style16 STYLE_SOLID
#property indicator_label16 "wkVWAP SDPos1"
//VWAP SD NEG 1
#property indicator_type17 DRAW_NONE
#property indicator_color17 clrWhiteSmoke
#property indicator_style17 STYLE_SOLID
#property indicator_label17 "wkVWAP SDNeg1"
//VWAP SD POS 2
#property indicator_type18 DRAW_NONE
#property indicator_color18 clrWhiteSmoke
#property indicator_style18 STYLE_SOLID
#property indicator_label18 "wkVWAP SDPos2"
//VWAP SD NEG 2
#property indicator_type19 DRAW_NONE
#property indicator_color19 clrWhiteSmoke
#property indicator_style19 STYLE_SOLID
#property indicator_label19 "wkVWAP SDNeg2"
//VWAP SD POS 3
#property indicator_type20 DRAW_NONE
#property indicator_color20 clrWhiteSmoke
#property indicator_style20 STYLE_SOLID
#property indicator_label20 "wkVWAP SDPos3"
//VWAP SD NEG 3
#property indicator_type21 DRAW_NONE
#property indicator_color21 clrWhiteSmoke
#property indicator_style21 STYLE_SOLID
#property indicator_label21 "wkVWAP SDNeg3"
//VWAP
#property indicator_type22 DRAW_NONE
#property indicator_color22 clrBlack
#property indicator_style22 STYLE_SOLID
#property indicator_width22 2
#property indicator_label22 "mnVWAP"
//VWAP SD POS1
#property indicator_type23 DRAW_NONE
#property indicator_color23 clrNavy
#property indicator_style23 STYLE_SOLID
#property indicator_label23 "mnVWAP SDPos1"
//VWAP SD NEG 1
#property indicator_type24 DRAW_NONE
#property indicator_color24 clrNavy
#property indicator_style24 STYLE_SOLID
#property indicator_label24 "mnVWAP SDNeg1"
//VWAP SD POS 2
#property indicator_type25 DRAW_NONE
#property indicator_color25 clrNavy
#property indicator_style25 STYLE_SOLID
#property indicator_label25 "mnVWAP SDPos2"
//VWAP SD NEG 2
#property indicator_type26 DRAW_NONE
#property indicator_color26 clrNavy
#property indicator_style26 STYLE_SOLID
#property indicator_label26 "mnVWAP SDNeg2"
//VWAP SD POS 3
#property indicator_type27 DRAW_NONE
#property indicator_color27 clrNavy
#property indicator_style27 STYLE_SOLID
#property indicator_label27 "mnVWAP SDPos3"
//VWAP SD NEG 3
#property indicator_type28 DRAW_NONE
#property indicator_color28 clrNavy
#property indicator_style28 STYLE_SOLID
#property indicator_label28 "mnVWAP SDNeg3"

double vwap[], SDP[], SDN[], SDP2[], SDN2[], SDP3[], SDN3[];
double ivwap[], iSDP[], iSDN[], iSDP2[], iSDN2[], iSDP3[], iSDN3[];
double v3wap[], S3DP[], S3DN[], S3DP2[], S3DN2[], S3DP3[], S3DN3[];
double v13wap[], S13DP[], S13DN[], S13DP2[], S13DN2[], S13DP3[], S13DN3[];

input string startim1 = "14:30:00"; //Start time for vwap2 (RTH)
input string endtim1 = "21:59:59";	//End time for vwap2 (RTH)

string startim = startim1;
string endtim = endtim1;

input string test = " ";
bool debug = false;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
	//--- indicator buffers mapping
	SetIndexBuffer(0, vwap, INDICATOR_DATA);
	SetIndexBuffer(1, SDP, INDICATOR_DATA);
	SetIndexBuffer(2, SDN, INDICATOR_DATA);
	SetIndexBuffer(3, SDP2, INDICATOR_DATA);
	SetIndexBuffer(4, SDN2, INDICATOR_DATA);
	SetIndexBuffer(5, SDP3, INDICATOR_DATA);
	SetIndexBuffer(6, SDN3, INDICATOR_DATA);
	PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(4, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(5, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(6, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	SetIndexBuffer(7, ivwap, INDICATOR_DATA);
	SetIndexBuffer(8, iSDP, INDICATOR_DATA);
	SetIndexBuffer(9, iSDN, INDICATOR_DATA);
	SetIndexBuffer(10, iSDP2, INDICATOR_DATA);
	SetIndexBuffer(11, iSDN2, INDICATOR_DATA);
	SetIndexBuffer(12, iSDP3, INDICATOR_DATA);
	SetIndexBuffer(13, iSDN3, INDICATOR_DATA);
	PlotIndexSetDouble(7, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(8, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(9, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(10, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(11, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(12, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(13, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	SetIndexBuffer(14, v3wap, INDICATOR_DATA);
	SetIndexBuffer(15, S3DP, INDICATOR_DATA);
	SetIndexBuffer(16, S3DN, INDICATOR_DATA);
	SetIndexBuffer(17, S3DP2, INDICATOR_DATA);
	SetIndexBuffer(18, S3DN2, INDICATOR_DATA);
	SetIndexBuffer(19, S3DP3, INDICATOR_DATA);
	SetIndexBuffer(20, S3DN3, INDICATOR_DATA);
	PlotIndexSetDouble(14, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(15, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(16, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(17, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(18, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(19, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(20, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	SetIndexBuffer(21, v13wap, INDICATOR_DATA);
	SetIndexBuffer(22, S13DP, INDICATOR_DATA);
	SetIndexBuffer(23, S13DN, INDICATOR_DATA);
	SetIndexBuffer(24, S13DP2, INDICATOR_DATA);
	SetIndexBuffer(25, S13DN2, INDICATOR_DATA);
	SetIndexBuffer(26, S13DP3, INDICATOR_DATA);
	SetIndexBuffer(27, S13DN3, INDICATOR_DATA);
	PlotIndexSetDouble(21, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(22, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(23, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(24, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(25, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(26, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(27, PLOT_EMPTY_VALUE, EMPTY_VALUE);

   if (startim == "14:30:00")
   {
      if (StringSubstr(_Symbol, 2, 1) == "6" || StringSubstr(_Symbol, 0, 2) == "DD" || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE)
   	{
   		startim = "07:00:00";
   	}
   	else if (StringSubstr(_Symbol, 0, 2) == "CL" || StringSubstr(_Symbol, 0, 2) == "NG")
   	{
   		startim = "08:00:00";
   	}
	}
	else
		startim = startim1;

	ObjectsDeleteAll(0, Name + "vwap");

	if (test == "debug")
		debug = true;
	//---
	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || MQLInfoInteger(MQL_TESTER))
		if (!MQLInfoInteger(MQL_TESTER))
		{
			DeleteObjects();
		}
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const int begin,
				const double &price[])
{
	//---
	datetime expiry = D'2022.06.30 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("vwapm expired on " + TimeToString(expiry, TIME_DATE) + ", contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true)
	{
		bool new_1ma_check = false;
		static datetime start_1ma_time = 0;
		if (start_1ma_time < iTime(NULL, PERIOD_M1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_M1, 0) + 2) && TimeCurrent() <= (iTime(NULL, PERIOD_M1, 0) + 58)))
		{
			new_1ma_check = true;
			start_1ma_time = iTime(NULL, PERIOD_M1, 0);
		}
		if (new_1ma_check)
		{
			if (ChartPeriod(0) <= 16385)
			{
				if (debug)
				{
					uint start1 = GetTickCount();
					PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(3, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(4, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(5, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(6, PLOT_DRAW_TYPE, DRAW_LINE);
					vwap();
					uint end1 = GetTickCount();
					Print(_Symbol + " day ", end1 - start1);
				}
				else
				{
					PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(3, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(4, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(5, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(6, PLOT_DRAW_TYPE, DRAW_LINE);
					vwap();
				}
			}
			else
			{
				ArrayInitialize(vwap, EMPTY_VALUE);
				ArrayInitialize(SDP, EMPTY_VALUE);
				ArrayInitialize(SDN, EMPTY_VALUE);
				ArrayInitialize(SDP2, EMPTY_VALUE);
				ArrayInitialize(SDN2, EMPTY_VALUE);
				ArrayInitialize(SDP3, EMPTY_VALUE);
				ArrayInitialize(SDN3, EMPTY_VALUE);
				PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(3, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(4, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(5, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(6, PLOT_DRAW_TYPE, DRAW_NONE);
			}
			if (ChartPeriod(0) <= 16388)
			{
				if (debug)
				{
					uint start1 = GetTickCount();
					PlotIndexSetInteger(14, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(15, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(16, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(17, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(18, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(19, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(20, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(21, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(22, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(23, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(24, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(25, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(26, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(27, PLOT_DRAW_TYPE, DRAW_LINE);
					v3wap();
					v13wap();
					uint end1 = GetTickCount();
					Print(_Symbol + " week & month ", end1 - start1);
				}
				else
				{
					PlotIndexSetInteger(14, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(15, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(16, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(17, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(18, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(19, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(20, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(21, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(22, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(23, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(24, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(25, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(26, PLOT_DRAW_TYPE, DRAW_LINE);
					PlotIndexSetInteger(27, PLOT_DRAW_TYPE, DRAW_LINE);
					v3wap();
					v13wap();
				}
			}
			else
			{
				ArrayInitialize(v3wap, EMPTY_VALUE);
				ArrayInitialize(S3DP, EMPTY_VALUE);
				ArrayInitialize(S3DN, EMPTY_VALUE);
				ArrayInitialize(S3DP2, EMPTY_VALUE);
				ArrayInitialize(S3DN2, EMPTY_VALUE);
				ArrayInitialize(S3DP3, EMPTY_VALUE);
				ArrayInitialize(S3DN3, EMPTY_VALUE);
				ArrayInitialize(v13wap, EMPTY_VALUE);
				ArrayInitialize(S13DP, EMPTY_VALUE);
				ArrayInitialize(S13DN, EMPTY_VALUE);
				ArrayInitialize(S13DP2, EMPTY_VALUE);
				ArrayInitialize(S13DN2, EMPTY_VALUE);
				ArrayInitialize(S13DP3, EMPTY_VALUE);
				ArrayInitialize(S13DN3, EMPTY_VALUE);
				PlotIndexSetInteger(14, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(15, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(16, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(17, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(18, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(19, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(20, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(21, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(22, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(23, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(24, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(25, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(26, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(27, PLOT_DRAW_TYPE, DRAW_NONE);
			}
			new_1ma_check = false;
		}

		bool new_2m_check = false;
		static datetime start_2m_time = 0;
		if (start_2m_time < iTime(NULL, PERIOD_M1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_M1, 0) + 2) && TimeCurrent() <= (iTime(NULL, PERIOD_M1, 0) + 58)))
		{
			new_2m_check = true;
			start_2m_time = iTime(NULL, PERIOD_M1, 0);
		}
		if (new_2m_check)
		{
			if (ChartPeriod(0) <= 16385)
			{
				if (debug)
				{
					uint start1 = GetTickCount();
					if (!checktime(startim, endtim))
					{
						ArrayInitialize(ivwap, EMPTY_VALUE);
						ArrayInitialize(iSDP, EMPTY_VALUE);
						ArrayInitialize(iSDN, EMPTY_VALUE);
						ArrayInitialize(iSDP2, EMPTY_VALUE);
						ArrayInitialize(iSDN2, EMPTY_VALUE);
						ArrayInitialize(iSDP3, EMPTY_VALUE);
						ArrayInitialize(iSDN3, EMPTY_VALUE);
						PlotIndexSetInteger(7, PLOT_DRAW_TYPE, DRAW_NONE);
						PlotIndexSetInteger(8, PLOT_DRAW_TYPE, DRAW_NONE);
						PlotIndexSetInteger(9, PLOT_DRAW_TYPE, DRAW_NONE);
						PlotIndexSetInteger(10, PLOT_DRAW_TYPE, DRAW_NONE);
						PlotIndexSetInteger(11, PLOT_DRAW_TYPE, DRAW_NONE);
						PlotIndexSetInteger(12, PLOT_DRAW_TYPE, DRAW_NONE);
						PlotIndexSetInteger(13, PLOT_DRAW_TYPE, DRAW_NONE);
					}
					else if (checktime(startim, endtim))
					{
						PlotIndexSetInteger(7, PLOT_DRAW_TYPE, DRAW_LINE);
						PlotIndexSetInteger(8, PLOT_DRAW_TYPE, DRAW_LINE);
						PlotIndexSetInteger(9, PLOT_DRAW_TYPE, DRAW_LINE);
						PlotIndexSetInteger(10, PLOT_DRAW_TYPE, DRAW_LINE);
						PlotIndexSetInteger(11, PLOT_DRAW_TYPE, DRAW_LINE);
						PlotIndexSetInteger(12, PLOT_DRAW_TYPE, DRAW_LINE);
						PlotIndexSetInteger(13, PLOT_DRAW_TYPE, DRAW_LINE);
						vwaprth();
					}
					uint end1 = GetTickCount();
					Print(_Symbol + " RTH ", end1 - start1);
				}
				else
				{
					if (!checktime(startim, endtim))
					{
						ArrayInitialize(ivwap, EMPTY_VALUE);
						ArrayInitialize(iSDP, EMPTY_VALUE);
						ArrayInitialize(iSDN, EMPTY_VALUE);
						ArrayInitialize(iSDP2, EMPTY_VALUE);
						ArrayInitialize(iSDN2, EMPTY_VALUE);
						ArrayInitialize(iSDP3, EMPTY_VALUE);
						ArrayInitialize(iSDN3, EMPTY_VALUE);
						PlotIndexSetInteger(7, PLOT_DRAW_TYPE, DRAW_NONE);
						PlotIndexSetInteger(8, PLOT_DRAW_TYPE, DRAW_NONE);
						PlotIndexSetInteger(9, PLOT_DRAW_TYPE, DRAW_NONE);
						PlotIndexSetInteger(10, PLOT_DRAW_TYPE, DRAW_NONE);
						PlotIndexSetInteger(11, PLOT_DRAW_TYPE, DRAW_NONE);
						PlotIndexSetInteger(12, PLOT_DRAW_TYPE, DRAW_NONE);
						PlotIndexSetInteger(13, PLOT_DRAW_TYPE, DRAW_NONE);
					}
					else if (checktime(startim, endtim))
					{
						PlotIndexSetInteger(7, PLOT_DRAW_TYPE, DRAW_LINE);
						PlotIndexSetInteger(8, PLOT_DRAW_TYPE, DRAW_LINE);
						PlotIndexSetInteger(9, PLOT_DRAW_TYPE, DRAW_LINE);
						PlotIndexSetInteger(10, PLOT_DRAW_TYPE, DRAW_LINE);
						PlotIndexSetInteger(11, PLOT_DRAW_TYPE, DRAW_LINE);
						PlotIndexSetInteger(12, PLOT_DRAW_TYPE, DRAW_LINE);
						PlotIndexSetInteger(13, PLOT_DRAW_TYPE, DRAW_LINE);
						vwaprth();
					}
				}
			}
			else
			{
				ArrayInitialize(ivwap, EMPTY_VALUE);
				ArrayInitialize(iSDP, EMPTY_VALUE);
				ArrayInitialize(iSDN, EMPTY_VALUE);
				ArrayInitialize(iSDP2, EMPTY_VALUE);
				ArrayInitialize(iSDN2, EMPTY_VALUE);
				ArrayInitialize(iSDP3, EMPTY_VALUE);
				ArrayInitialize(iSDN3, EMPTY_VALUE);
				PlotIndexSetInteger(7, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(8, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(9, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(10, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(11, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(12, PLOT_DRAW_TYPE, DRAW_NONE);
				PlotIndexSetInteger(13, PLOT_DRAW_TYPE, DRAW_NONE);
			}
			new_2m_check = false;
		}

		if (debug && ((DayOfWeek() == SATURDAY || DayOfWeek() == SUNDAY) || (checktime("20:00:00", "20:14:59") || checktime("22:00:00", "22:59:59"))))
		{
			uint start1 = GetTickCount();
			if (ChartPeriod(0) <= 16385)
			{
				vwap();
				PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(3, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(4, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(5, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(6, PLOT_DRAW_TYPE, DRAW_LINE);
			}
			if (ChartPeriod(0) <= 16388)
			{
				v3wap();
				PlotIndexSetInteger(14, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(15, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(16, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(17, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(18, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(19, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(20, PLOT_DRAW_TYPE, DRAW_LINE);
			}
			if (ChartPeriod(0) <= 16388)
			{
				v13wap();
				PlotIndexSetInteger(21, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(22, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(23, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(24, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(25, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(26, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(27, PLOT_DRAW_TYPE, DRAW_LINE);
			}
			uint end1 = GetTickCount();
			Print(_Symbol + " closed market run 1 ", end1 - start1);
		}
		else if ((DayOfWeek() == SATURDAY || DayOfWeek() == SUNDAY) || (checktime("20:00:00", "20:14:59") || checktime("22:00:00", "22:59:59")))
		{
			if (ChartPeriod(0) <= 16385)
			{
				vwap();
				PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(3, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(4, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(5, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(6, PLOT_DRAW_TYPE, DRAW_LINE);
			}
			if (ChartPeriod(0) <= 16388)
			{
				v3wap();
				PlotIndexSetInteger(14, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(15, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(16, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(17, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(18, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(19, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(20, PLOT_DRAW_TYPE, DRAW_LINE);
			}
			if (ChartPeriod(0) <= 16388)
			{
				v13wap();
				PlotIndexSetInteger(21, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(22, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(23, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(24, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(25, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(26, PLOT_DRAW_TYPE, DRAW_LINE);
				PlotIndexSetInteger(27, PLOT_DRAW_TYPE, DRAW_LINE);
			}
		}
	}
	//--- return value of prev_calculated for next call
	return (rates_total);
}
//+------------------------------------------------------------------+

//+VWAP--------------------------------------------------------------+
void vwap()
{
	long tickvol[];
	ArraySetAsSeries(tickvol, true);
	if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFD || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFDINDEX)
	{
		if (CopyTickVolume(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 7), false) + 1, tickvol) <= 0)
		{
			Print("Failed to get tick volumes ", GetLastError());
			return;
		}
	}
	else if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_EXCH_FUTURES || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_EXCH_FUTURES_FORTS)
	{
		if (CopyRealVolume(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 7), false) + 1, tickvol) <= 0)
		{
			Print("Failed to get real volumes ", GetLastError());
			return;
		}
	}
	double close[];
	ArraySetAsSeries(close, true);
	if (CopyClose(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 7), false) + 1, close) <= 0)
	{
		Print("Failed to get closes ", GetLastError());
		return;
	}
	double high[];
	ArraySetAsSeries(high, true);
	if (CopyHigh(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 7), false) + 1, high) <= 0)
	{
		Print("Failed to get highs ", GetLastError());
		return;
	}
	double low[];
	ArraySetAsSeries(low, true);
	if (CopyLow(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 7), false) + 1, low) <= 0)
	{
		Print("Failed to get lows ", GetLastError());
		return;
	}
	datetime time[];
	ArraySetAsSeries(time, true);
	if (CopyTime(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 7), false) + 1, time) <= 0)
	{
		Print("Failed to get times ", GetLastError());
		return;
	}

	ArraySetAsSeries(vwap, true);
	ArrayInitialize(vwap, EMPTY_VALUE);
	ArraySetAsSeries(SDP, true);
	ArrayInitialize(SDP, EMPTY_VALUE);
	ArraySetAsSeries(SDN, true);
	ArrayInitialize(SDN, EMPTY_VALUE);
	ArraySetAsSeries(SDP2, true);
	ArrayInitialize(SDP2, EMPTY_VALUE);
	ArraySetAsSeries(SDN2, true);
	ArrayInitialize(SDN2, EMPTY_VALUE);
	ArraySetAsSeries(SDP3, true);
	ArrayInitialize(SDP3, EMPTY_VALUE);
	ArraySetAsSeries(SDN3, true);
	ArrayInitialize(SDN3, EMPTY_VALUE);

	datetime newsession = 0;
	double totvol = 0;
	double pervol = 0;
	double SD = 0;

	int daymin = 0;
	if (DayOfWeek() == MONDAY || DayOfWeek() == TUESDAY)
		daymin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 3), false);
	else
		daymin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false);
	vwap[daymin] = (high[daymin] + low[daymin] + close[daymin]) / 3;
	for (int x = daymin; x >= 0; x--)
	{
		totvol += (double)tickvol[x];
		pervol += (double)tickvol[x] * (high[x] + close[x] + low[x]) / 3;
		if (TimeDay(time[x]) != TimeDay(newsession))
		{
			newsession = time[x];
			totvol = 0;
			pervol = 0;
			//vwap[x] = (high[x] + low[x] + close[x]) / 3;
		}
		if (totvol != 0)
		{
			vwap[x] = pervol / totvol;
		}

		if (ChartPeriod() <= 30)
		{
			SD = 0;
			int baymin = 0;
			if (ChartPeriod() == 1)
				baymin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false);
			else
				baymin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false);
			for (int k = baymin; k >= x; k--)
			{
				double avg = (close[k] + high[k] + low[k]) / 3;
				double diff = avg - vwap[x];
				if (TimeDay(time[k]) != TimeDay(newsession))
				{
					newsession = time[k];
					SD = 0;
				}
				if (totvol != 0)
					SD += (tickvol[k] / totvol) * (diff * diff);
			}

			SD = MathSqrt(SD);
			SDP[x] = vwap[x] + SD;
			SDN[x] = vwap[x] - SD;
			SDP2[x] = SDP[x] + SD;
			SDN2[x] = SDN[x] - SD;
			SDP3[x] = SDP2[x] + SD;
			SDN3[x] = SDN2[x] - SD;
		}
	}

	string obname;
	int a = 4, b = 3, c = 2, d = 5;
	if (DayOfWeek() == SUNDAY)
	{
		a = 3;
		b = 2;
		c = 1;
		d = 4;
	}
	if (DayOfWeek() == MONDAY)
	{
		a = 4;
		b = 3;
		c = 2;
	}
	if (DayOfWeek() == TUESDAY)
	{
		a = 4;
		b = 3;
		c = 2;
	}
	if (DayOfWeek() == WEDNESDAY)
	{
		a = 4;
		b = 3;
		c = 1;
	}
	if (DayOfWeek() == THURSDAY)
	{
		a = 4;
		b = 2;
		c = 1;
	}
	if (DayOfWeek() == FRIDAY)
	{
		a = 3;
		b = 2;
		c = 1;
	}

	double v3wap1[];
	ArrayResize(v3wap1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, d + 1), false) + 1);
	ArraySetAsSeries(v3wap1, true);
	int weekmin1 = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, d + 1), false);
	v3wap1[weekmin1] = (high[weekmin1] + low[weekmin1] + close[weekmin1]) / 3;
	double totvol1 = 0, pervol1 = 0;
	for (int x = weekmin1; x >= iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, d), false); x--)
	{
		totvol1 += (double)tickvol[x];
		pervol1 += (double)tickvol[x] * (high[x] + close[x] + low[x]) / 3;
		if (totvol1 != 0)
		{
			v3wap1[x] = pervol1 / totvol1;
		}
	}
	obname = Name + "vwap5d";
	objhoriz(obname, v3wap1[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, d), false)], 2, STYLE_SOLID, clrMagenta, "VWAP-5d " + TimeToString(iTime(_Symbol, PERIOD_D1, d + 1), TIME_DATE));
	ObjectSetString(0, obname, OBJPROP_TEXT, "VW5 : " + TimeToString(iTime(_Symbol, PERIOD_D1, d + 1), TIME_DATE) + " " + DoubleToString(v3wap1[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, d), false)], _Digits));
	if (debug)
	{
		if (_Symbol == "@EP")
			Print("a: ", v3wap1[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, d), false)]);
	}

	double v3wap2[];
	ArrayResize(v3wap2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, a + 1), false) + 1);
	ArraySetAsSeries(v3wap2, true);
	int weekmin2 = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, a + 1), false);
	v3wap2[weekmin2] = (high[weekmin2] + low[weekmin2] + close[weekmin2]) / 3;
	double totvol2 = 0, pervol2 = 0;
	for (int x = weekmin2; x >= iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, a), false); x--)
	{
		totvol2 += (double)tickvol[x];
		pervol2 += (double)tickvol[x] * (high[x] + close[x] + low[x]) / 3;
		if (totvol2 != 0)
		{
			v3wap2[x] = pervol2 / totvol2;
		}
	}
	obname = Name + "vwap4d";
	objhoriz(obname, v3wap2[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, a), false)], 2, STYLE_SOLID, clrMagenta, "VWAP-4d " + TimeToString(iTime(_Symbol, PERIOD_D1, a + 1), TIME_DATE));
	ObjectSetString(0, obname, OBJPROP_TEXT, "VW4 : " + TimeToString(iTime(_Symbol, PERIOD_D1, a + 1), TIME_DATE) + " " + DoubleToString(v3wap2[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, a), false)], _Digits));
	if (debug)
	{
		if (_Symbol == "@EP")
			Print("b: ", v3wap2[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, a), false)]);
	}

	double v3wap3[];
	ArrayResize(v3wap3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, b + 1), false) + 1);
	ArraySetAsSeries(v3wap3, true);
	int weekmin3 = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, b + 1), false);
	v3wap3[weekmin3] = (high[weekmin3] + low[weekmin3] + close[weekmin3]) / 3;
	double totvol3 = 0, pervol3 = 0;
	for (int x = weekmin3; x >= iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, b), false); x--)
	{
		totvol3 += (double)tickvol[x];
		pervol3 += (double)tickvol[x] * (high[x] + close[x] + low[x]) / 3;
		if (totvol3 != 0)
		{
			v3wap3[x] = pervol3 / totvol3;
		}
	}
	obname = Name + "vwap3d";
	objhoriz(obname, v3wap3[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, b), false)], 2, STYLE_SOLID, clrMagenta, "VWAP-3d " + TimeToString(iTime(_Symbol, PERIOD_D1, b + 1), TIME_DATE));
	ObjectSetString(0, obname, OBJPROP_TEXT, "VW3 : " + TimeToString(iTime(_Symbol, PERIOD_D1, b + 1), TIME_DATE) + " " + DoubleToString(v3wap3[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, b), false)], _Digits));
	if (debug)
	{
		if (_Symbol == "@EP")
			Print("c: ", v3wap3[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, b), false)]);
	}

	double v3wap4[];
	ArrayResize(v3wap4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, c + 1), false) + 1);
	ArraySetAsSeries(v3wap4, true);
	int weekmin4 = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, c + 1), false);
	v3wap4[weekmin4] = (high[weekmin4] + low[weekmin4] + close[weekmin4]) / 3;
	double totvol4 = 0, pervol4 = 0;
	for (int x = weekmin4; x >= iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, c), false); x--)
	{
		totvol4 += (double)tickvol[x];
		pervol4 += (double)tickvol[x] * (high[x] + close[x] + low[x]) / 3;
		if (totvol4 != 0)
		{
			v3wap4[x] = pervol4 / totvol4;
		}
	}
	obname = Name + "vwap2d";
	objhoriz(obname, v3wap4[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, c), false)], 2, STYLE_SOLID, clrMagenta, "VWAP-2d " + TimeToString(iTime(_Symbol, PERIOD_D1, c + 1), TIME_DATE));
	ObjectSetString(0, obname, OBJPROP_TEXT, "VW2 : " + TimeToString(iTime(_Symbol, PERIOD_D1, c + 1), TIME_DATE) + " " + DoubleToString(v3wap4[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, c), false)], _Digits));
	if (debug)
	{
		if (_Symbol == "@EP")
			Print("d: ", v3wap4[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, c), false)]);
	}

	if (debug)
	{
		//obname = Name + "vwap5d";
		//objhoriz(obname, vwap[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, d), false) + 1], 2, STYLE_SOLID, clrMagenta, "VWAP-5d");
		//ObjectSetString(0, obname, OBJPROP_TEXT, "VW5 : " + DoubleToString(vwap[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, d), false) + 1], _Digits));
		if (_Symbol == "@EP")
			Print("a: ", vwap[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, d), false) + 1]);
		//obname = Name + "vwap4d";
		//objhoriz(obname, vwap[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, a), false) + 1], 2, STYLE_SOLID, clrMagenta, "VWAP-4d");
		//ObjectSetString(0, obname, OBJPROP_TEXT, "VW4 : " + DoubleToString(vwap[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, a), false) + 1], _Digits));
		if (_Symbol == "@EP")
			Print("b: ", vwap[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, a), false) + 1]);
		//obname = Name + "vwap3d";
		//objhoriz(obname, vwap[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, b), false) + 1], 2, STYLE_SOLID, clrMagenta, "VWAP-3d");
		//ObjectSetString(0, obname, OBJPROP_TEXT, "VW3 : " + DoubleToString(vwap[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, b), false) + 1], _Digits));
		if (_Symbol == "@EP")
			Print("c: ", vwap[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, b), false) + 1]);
		//obname = Name + "vwap2d";
		//objhoriz(obname, vwap[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, c), false) + 1], 2, STYLE_SOLID, clrMagenta, "VWAP-2d");
		//ObjectSetString(0, obname, OBJPROP_TEXT, "VW2 : " + DoubleToString(vwap[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, c), false) + 1], _Digits));
		if (_Symbol == "@EP")
			Print("d: ", vwap[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, c), false) + 1]);
	}
	ArrayResize(v3wap1, 0);
	ArrayResize(v3wap2, 0);
	ArrayResize(v3wap3, 0);
	ArrayResize(v3wap4, 0);

	ArrayResize(high, 0);
	ArrayResize(low, 0);
	ArrayResize(close, 0);
	ArrayResize(time, 0);
	ArrayResize(tickvol, 0);
}
//+------------------------------------------------------------------+

//+V3WAP--------------------------------------------------------------+
void v3wap()
{
	long tickvol[];
	ArraySetAsSeries(tickvol, true);
	if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFD || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFDINDEX)
	{
		if (CopyTickVolume(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 1), false) + 1, tickvol) <= 0)
		{
			Print("Failed to get weekly tick volumes ", GetLastError());
			return;
		}
	}
	else if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_EXCH_FUTURES || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_EXCH_FUTURES_FORTS)
	{
		if (CopyRealVolume(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 1), false) + 1, tickvol) <= 0)
		{
			Print("Failed to get weekly real volumes ", GetLastError());
			return;
		}
	}
	double close[];
	ArraySetAsSeries(close, true);
	if (CopyClose(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 1), false) + 1, close) <= 0)
	{
		Print("Failed to get weekly closes ", GetLastError());
		return;
	}
	double high[];
	ArraySetAsSeries(high, true);
	if (CopyHigh(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 1), false) + 1, high) <= 0)
	{
		Print("Failed to get weekly highs ", GetLastError());
		return;
	}
	double low[];
	ArraySetAsSeries(low, true);
	if (CopyLow(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 1), false) + 1, low) <= 0)
	{
		Print("Failed to get weekly lows ", GetLastError());
		return;
	}
	datetime time[];
	ArraySetAsSeries(time, true);
	if (CopyTime(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 1), false) + 1, time) <= 0)
	{
		Print("Failed to get weekly times ", GetLastError());
		return;
	}

	ArraySetAsSeries(v3wap, true);
	ArrayInitialize(v3wap, EMPTY_VALUE);
	ArraySetAsSeries(S3DP, true);
	ArrayInitialize(S3DP, EMPTY_VALUE);
	ArraySetAsSeries(S3DN, true);
	ArrayInitialize(S3DN, EMPTY_VALUE);
	ArraySetAsSeries(S3DP2, true);
	ArrayInitialize(S3DP2, EMPTY_VALUE);
	ArraySetAsSeries(S3DN2, true);
	ArrayInitialize(S3DN2, EMPTY_VALUE);
	ArraySetAsSeries(S3DP3, true);
	ArrayInitialize(S3DP3, EMPTY_VALUE);
	ArraySetAsSeries(S3DN3, true);
	ArrayInitialize(S3DN3, EMPTY_VALUE);

	datetime newsession = 0;
	double totvol = 0;
	double pervol = 0;
	double SD = 0;

	int daymin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 0), false);
	v3wap[daymin] = (high[daymin] + low[daymin] + close[daymin]) / 3;
	for (int x = daymin; x >= 0; x--)
	{
		totvol += (double)tickvol[x];
		pervol += (double)tickvol[x] * (high[x] + close[x] + low[x]) / 3;
		if (totvol != 0)
		{
			v3wap[x] = pervol / totvol;
		}

		if (ChartPeriod() <= 16388)
		{
			SD = 0;
			int baymin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 0), false);
			for (int k = baymin; k >= x; k--)
			{
				double avg = (close[k] + high[k] + low[k]) / 3;
				double diff = avg - v3wap[x];
				if (totvol != 0)
					SD += (tickvol[k] / totvol) * (diff * diff);
			}

			SD = MathSqrt(SD);
			S3DP[x] = v3wap[x] + SD;
			S3DN[x] = v3wap[x] - SD;
			S3DP2[x] = S3DP[x] + SD;
			S3DN2[x] = S3DN[x] - SD;
			S3DP3[x] = S3DP2[x] + SD;
			S3DN3[x] = S3DN2[x] - SD;
		}
	}

	double v3wap2[];
	ArrayResize(v3wap2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 1), false) + 1);
	ArraySetAsSeries(v3wap2, true);
	int weekmin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 1), false);
	v3wap2[weekmin] = (high[weekmin] + low[weekmin] + close[weekmin]) / 3;
	double totvol1 = 0, pervol1 = 0;
	for (int x = weekmin; x >= iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 0), false); x--)
	{
		totvol1 += (double)tickvol[x];
		pervol1 += (double)tickvol[x] * (high[x] + close[x] + low[x]) / 3;
		if (totvol1 != 0)
		{
			v3wap2[x] = pervol1 / totvol1;
		}
	}
	string obname;
	obname = Name + "vwap1w";
	objhoriz(obname, v3wap2[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 0), false)], 2, STYLE_SOLID, clrDarkViolet, "VWAP-1w " + TimeToString(iTime(_Symbol, PERIOD_W1, 1), TIME_DATE));
	ObjectSetString(0, obname, OBJPROP_TEXT, "VPW1 : " + TimeToString(iTime(_Symbol, PERIOD_W1, 1), TIME_DATE) + " " + DoubleToString(v3wap2[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 0), false)], _Digits));

	ArrayResize(v3wap2, 0);

	ArrayResize(high, 0);
	ArrayResize(low, 0);
	ArrayResize(close, 0);
	ArrayResize(time, 0);
	ArrayResize(tickvol, 0);
}
//+------------------------------------------------------------------+

//+V3WAP--------------------------------------------------------------+
void v13wap()
{
	long tickvol[];
	ArraySetAsSeries(tickvol, true);
	if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFD || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFDINDEX)
	{
		if (CopyTickVolume(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 1), false) + 1, tickvol) <= 0)
		{
			Print("Failed to get monthly tick volumes ", GetLastError());
			return;
		}
	}
	else if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_EXCH_FUTURES || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_EXCH_FUTURES_FORTS)
	{
		if (CopyRealVolume(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 1), false) + 1, tickvol) <= 0)
		{
			Print("Failed to get monthly real volumes ", GetLastError());
			return;
		}
	}
	double close[];
	ArraySetAsSeries(close, true);
	if (CopyClose(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 1), false) + 1, close) <= 0)
	{
		Print("Failed to get monthly closes ", GetLastError());
		return;
	}
	double high[];
	ArraySetAsSeries(high, true);
	if (CopyHigh(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 1), false) + 1, high) <= 0)
	{
		Print("Failed to get monthly highs ", GetLastError());
		return;
	}
	double low[];
	ArraySetAsSeries(low, true);
	if (CopyLow(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 1), false) + 1, low) <= 0)
	{
		Print("Failed to get monthly lows ", GetLastError());
		return;
	}
	datetime time[];
	ArraySetAsSeries(time, true);
	if (CopyTime(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 1), false) + 1, time) <= 0)
	{
		Print("Failed to get monthly times ", GetLastError());
		return;
	}

	ArraySetAsSeries(v13wap, true);
	ArrayInitialize(v13wap, EMPTY_VALUE);
	ArraySetAsSeries(S13DP, true);
	ArrayInitialize(S13DP, EMPTY_VALUE);
	ArraySetAsSeries(S13DN, true);
	ArrayInitialize(S13DN, EMPTY_VALUE);
	ArraySetAsSeries(S13DP2, true);
	ArrayInitialize(S13DP2, EMPTY_VALUE);
	ArraySetAsSeries(S13DN2, true);
	ArrayInitialize(S13DN2, EMPTY_VALUE);
	ArraySetAsSeries(S13DP3, true);
	ArrayInitialize(S13DP3, EMPTY_VALUE);
	ArraySetAsSeries(S13DN3, true);
	ArrayInitialize(S13DN3, EMPTY_VALUE);

	datetime newsession = 0;
	double totvol = 0;
	double pervol = 0;
	double SD = 0;

	int daymin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 0), false);
	v13wap[daymin] = (high[daymin] + low[daymin] + close[daymin]) / 3;
	for (int x = daymin; x >= 0; x--)
	{
		totvol += (double)tickvol[x];
		pervol += (double)tickvol[x] * (high[x] + close[x] + low[x]) / 3;
		if (totvol != 0)
		{
			v13wap[x] = pervol / totvol;
		}

		if (ChartPeriod() <= 16388)
		{
			SD = 0;
			int baymin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 0), false);
			for (int k = baymin; k >= x; k--)
			{
				double avg = (close[k] + high[k] + low[k]) / 3;
				double diff = avg - v13wap[x];
				if (totvol != 0)
					SD += (tickvol[k] / totvol) * (diff * diff);
			}

			SD = MathSqrt(SD);
			S13DP[x] = v13wap[x] + SD;
			S13DN[x] = v13wap[x] - SD;
			S13DP2[x] = S13DP[x] + SD;
			S13DN2[x] = S13DN[x] - SD;
			S13DP3[x] = S13DP2[x] + SD;
			S13DN3[x] = S13DN2[x] - SD;
		}
	}

	double v3wap2[];
	ArrayResize(v3wap2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 1), false) + 1);
	ArraySetAsSeries(v3wap2, true);
	int weekmin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 1), false);
	v3wap2[weekmin] = (high[weekmin] + low[weekmin] + close[weekmin]) / 3;
	double totvol1 = 0, pervol1 = 0;
	for (int x = weekmin; x >= iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 0), false); x--)
	{
		totvol1 += (double)tickvol[x];
		pervol1 += (double)tickvol[x] * (high[x] + close[x] + low[x]) / 3;
		if (totvol1 != 0)
		{
			v3wap2[x] = pervol1 / totvol1;
		}
	}
	string obname;
	obname = Name + "vwap1m";
	objhoriz(obname, v3wap2[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 0), false)], 2, STYLE_SOLID, clrDarkViolet, "VWAP-1m " + TimeToString(iTime(_Symbol, PERIOD_MN1, 1), TIME_DATE));
	ObjectSetString(0, obname, OBJPROP_TEXT, "VPMN1 : " + TimeToString(iTime(_Symbol, PERIOD_MN1, 1), TIME_DATE) + " " + DoubleToString(v3wap2[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 0), false)], _Digits));

	ArrayResize(v3wap2, 0);

	ArrayResize(high, 0);
	ArrayResize(low, 0);
	ArrayResize(close, 0);
	ArrayResize(time, 0);
	ArrayResize(tickvol, 0);
}
//+------------------------------------------------------------------+

//+VWAP--------------------------------------------------------------+
void vwaprth()
{
	//Print("Done");
	string startbar = startim;
	string DTstr = TimeToString(TimeCurrent(), TIME_DATE);
	string bar = DTstr + " " + startbar;
	int start = iBarShift(_Symbol, PERIOD_CURRENT, (datetime)bar, true);
	if (debug)
	{
		if (_Symbol == "EU6H21")
			Print(bar, " ", start, " ", D'01:00:00');
	}

	long tickvol[];
	ArraySetAsSeries(tickvol, true);
	if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFD || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFDINDEX)
	{
		if (CopyTickVolume(_Symbol, PERIOD_CURRENT, 0, start + 1, tickvol) <= 0)
		{
			Print("ivwap error getting volume ", GetLastError());
			return;
		}
	}
	else if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_EXCH_FUTURES || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_EXCH_FUTURES_FORTS)
	{
		if (CopyRealVolume(_Symbol, PERIOD_CURRENT, 0, start + 1, tickvol) <= 0)
		{
			Print("ivwap error getting volume ", GetLastError());
			return;
		}
	}
	double close[];
	ArraySetAsSeries(close, true);
	CopyClose(_Symbol, PERIOD_CURRENT, 0, start + 1, close);
	double high[];
	ArraySetAsSeries(high, true);
	CopyHigh(_Symbol, PERIOD_CURRENT, 0, start + 1, high);
	double low[];
	ArraySetAsSeries(low, true);
	CopyLow(_Symbol, PERIOD_CURRENT, 0, start + 1, low);
	datetime time[];
	ArraySetAsSeries(time, true);
	CopyTime(_Symbol, PERIOD_CURRENT, 0, start + 1, time);

	ArraySetAsSeries(ivwap, true);
	ArrayInitialize(ivwap, EMPTY_VALUE);
	ArraySetAsSeries(iSDP, true);
	ArrayInitialize(iSDP, EMPTY_VALUE);
	ArraySetAsSeries(iSDN, true);
	ArrayInitialize(iSDN, EMPTY_VALUE);
	ArraySetAsSeries(iSDP2, true);
	ArrayInitialize(iSDP2, EMPTY_VALUE);
	ArraySetAsSeries(iSDN2, true);
	ArrayInitialize(iSDN2, EMPTY_VALUE);
	ArraySetAsSeries(iSDP3, true);
	ArrayInitialize(iSDP3, EMPTY_VALUE);
	ArraySetAsSeries(iSDN3, true);
	ArrayInitialize(iSDN3, EMPTY_VALUE);

	datetime newsession = 0;
	double totvol = 0;
	double pervol = 0;
	double SD = 0;

	ivwap[start] = (high[start] + low[start] + close[start]) / 3;
	for (int x = start - 1; x >= 0; x--)
	{
		totvol += (double)tickvol[x];
		pervol += (double)tickvol[x] * (high[x] + close[x] + low[x]) / 3;
		if (totvol != 0)
		{
			ivwap[x] = pervol / totvol;
		}

		if (ChartPeriod() <= 30)
		{
			SD = 0;
			for (int k = start - 1; k >= x; k--)
			{
				double avg = (close[k] + high[k] + low[k]) / 3;
				double diff = avg - ivwap[x];
				if (totvol != 0)
					SD += (tickvol[k] / totvol) * (diff * diff);
			}

			SD = MathSqrt(SD);
			iSDP[x] = ivwap[x] + SD;
			iSDN[x] = ivwap[x] - SD;
			iSDP2[x] = iSDP[x] + SD;
			iSDN2[x] = iSDN[x] - SD;
			iSDP3[x] = iSDP2[x] + SD;
			iSDN3[x] = iSDN2[x] - SD;
		}
	}

	ArrayResize(high, 0);
	ArrayResize(low, 0);
	ArrayResize(close, 0);
	ArrayResize(time, 0);
	ArrayResize(tickvol, 0);
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	//ObjectsDeleteAll(0, Name);
	for (int i = ObjectsTotal(0, 0) - 1; i >= 0; i--)
	{
		string ObName = ObjectName(0, i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(0, ObName);
		}
	}
}
//+------------------------------------------------------------------+

//+HORIZLINE FUNCTION------------------------------------------------+
void objhoriz(string name, double pr1, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_HLINE, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetInteger(0, name, OBJPROP_STYLE, st);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToString(pr1, _Digits));
}
//+------------------------------------------------------------------+

//+TIMEDAY / HOUR / DAYOFWEEK MQL4-----------------------------------+
int TimeDay(datetime date)
{
	MqlDateTime tm;
	TimeToStruct(date, tm);
	return (tm.day);
}
int Hour()
{
	MqlDateTime tm;
	TimeCurrent(tm);
	return (tm.hour);
}
int Minute()
{
	MqlDateTime tm;
	TimeCurrent(tm);
	return (tm.min);
}
int DayOfWeek()
{
	MqlDateTime tm;
	TimeCurrent(tm);
	return (tm.day_of_week);
}
int TimeDayOfWeek(datetime date)
{
	MqlDateTime tm;
	TimeToStruct(date, tm);
	return (tm.day_of_week);
}
//+------------------------------------------------------------------+

//+CHECKTIME FUNCTION------------------------------------------------+
bool checktime(string starttime, string endtime)
{
	string dt = TimeToString(TimeCurrent());
	string DTstr = TimeToString(TimeCurrent(), TIME_DATE);
	string start = DTstr + " " + starttime;
	string end = DTstr + " " + endtime;
	StringToTime(start);
	StringToTime(end);
	StringToTime(dt);

	if (start < end)
		if (dt >= start && dt <= end)
			return (true);
	if (start >= end)
		if (dt >= start || dt < end)
			return (true);
	return (false);
}
//+------------------------------------------------------------------+