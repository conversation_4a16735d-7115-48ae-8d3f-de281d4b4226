//+------------------------------------------------------------------+
//|                                                          kkk.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict

#property indicator_buffers    1
#property indicator_plots      1
#property indicator_separate_window
#property indicator_minimum    0
#property indicator_maximum    100

#define Name MQLInfoString(MQL_PROGRAM_NAME)

//A/D
#property indicator_type1  DRAW_LINE
#property indicator_color1 clrBlue
#property indicator_style1 STYLE_SOLID
#property indicator_width1 1
#property indicator_label1 "A/D"

#property indicator_level1     10.0
#property indicator_level2     90.0
#property indicator_levelcolor clrWhite
#property indicator_levelstyle STYLE_DOT

//Inputs
extern int check = 2000; // Periods

//Buffers
double ExtRSIBuffer[];

//Handles
int iad_handle;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
	IndicatorSetString(INDICATOR_SHORTNAME,"RADIS");
	IndicatorSetInteger(INDICATOR_DIGITS, 2);
	
   SetIndexBuffer(0, ExtRSIBuffer, INDICATOR_DATA);
   
   //Handles  
   iad_handle = iAD(_Symbol, PERIOD_CURRENT, VOLUME_TICK);
   /*
   if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_EXCH_FUTURES)
   {
   iad_handle = iAD(_Symbol, PERIOD_CURRENT, VOLUME_REAL);
   }
   else if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) != SYMBOL_CALC_MODE_EXCH_FUTURES)
   {
   iad_handle = iAD(_Symbol, PERIOD_CURRENT, VOLUME_TICK);
   }
   */
   
//---
   return(INIT_SUCCEEDED);
  }
//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
//---
   int periods = check;
   if (periods > BarsCalculated(iad_handle)) periods = BarsCalculated(iad_handle) - 2;
   else periods = check;
   
   int calculated=BarsCalculated(iad_handle);
   if(calculated<rates_total)
     {
      Print("Not all data of AD are calculated (",calculated," bars). Error ",GetLastError());
      return(0);
     }
//--- we can copy not all data
//---
   double iad[];
   ArrayResize(iad, periods + 2);
   double riad[];
   ArrayResize(riad, periods + 2);
      
//--- the main loop of calculations
      if(CopyBuffer(iad_handle, 0, 0, periods, iad) < 0)
      {
      //--- if the copying fails, tell the error code
      PrintFormat("Failed to copy data from the iAD indicator, error code %d",GetLastError());
      //--- quit with zero result - it means that the indicator is considered as not calculated
      return(false);
      }
      		
      ArraySetAsSeries(iad, true);
      
      double bop = iad[ArrayMinimum(iad, 0, 0)];
      
      //Real
      if (bop < 0)
      {
   		for (int i = 0; i < periods; i++)
   		{
            riad[i] = iad[i] + MathAbs(bop);
         }
      }
      else if (bop >= 0)      
      {
   		for (int i = 0; i < periods; i++)
   		{
            riad[i] = iad[i] - bop;
         }
      }
		
		ArraySetAsSeries(ExtRSIBuffer, true);
            
		double mid = riad[ArrayMaximum(riad, 0, 0)];
		
		for (int i = 0; i < periods; i++)
		{
		   ExtRSIBuffer[i] = riad[i] / mid * 100;
		}
//---
//--- return value of prev_calculated for next call
   return(rates_total);
}
//+------------------------------------------------------------------+

//+ARROW CREATE------------------------------------------------------+
void burnarr(string name, double p, int arrow, int t, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
   datetime Time[];
   int count = 2 * check;   // number of elements to copy
   ArraySetAsSeries(Time,true);
   CopyTime(_Symbol,_Period,0,count,Time);
   
	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t]);
	ObjectSetDouble(0, name, OBJPROP_PRICE, p);
	ObjectSetInteger(0, name, OBJPROP_ARROWCODE, arrow);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
}
//+------------------------------------------------------------------+