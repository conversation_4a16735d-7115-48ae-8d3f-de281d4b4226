#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict
#property indicator_buffers 14
#property indicator_color1 clrBlue
#property indicator_color2 clrRed
#property indicator_color3 clrBlue
#property indicator_color4 clrRed

#define Name WindowExpertName()

//+INPUTS------------------------------------------------------------+
extern int periods = 8000;				  // Candles back to check
int drawlines = 8000;					  // Candles back to mark with trendlines
ENUM_TIMEFRAMES DROP_TF = PERIOD_CURRENT; // Check every X period

input int addTP = 400;
input int addSL = 20;

double finp[];
double finn[];
double linp[];
double linn[];
double ffinp[];
double ffinn[];
double flinp[];
double flinn[];

double signalcomboup[];
double signalcombodn[];
double signalbuysl[];
double signalselsl[];
double signalbuytp[];
double signalseltp[];
static double fsignalbuy;
static double fsignalsel;
static double fsignalbuystop;
static double fsignalselstop;
static double signalbuy;
static double signalsel;
static double signalbuystop;
static double signalselstop;

int ma200_handle;
//+------------------------------------------------------------------+

//+INIT--------------------------------------------------------------+
int OnInit()
{
	IndicatorBuffers(14);
	IndicatorShortName("FVG /w limit");
	ObjectsDeleteAll(0, Name);
	SetIndexBuffer(0, finp);
	SetIndexBuffer(1, finn);
	SetIndexBuffer(2, linp);
	SetIndexBuffer(3, linn);
	SetIndexBuffer(4, ffinp);
	SetIndexBuffer(5, ffinn);
	SetIndexBuffer(6, flinp);
	SetIndexBuffer(7, flinn);
	SetIndexBuffer(8, signalcomboup);
	SetIndexBuffer(9, signalcombodn);
	SetIndexBuffer(10, signalbuysl);
	SetIndexBuffer(11, signalselsl);
	SetIndexBuffer(12, signalbuytp);
	SetIndexBuffer(13, signalseltp);
   ma200_handle = iMA(_Symbol, PERIOD_CURRENT, 200, 0, MODE_EMA, PRICE_CLOSE);
   if(ma200_handle < 0)
     {
      Print("The creation of MA200_handle has failed: MA200_handle=", INVALID_HANDLE);
      Print("Runtime error = ", GetLastError());
      return(INIT_FAILED);
     }
   

	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN PROGRAM------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	datetime expiry = D'2023.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("FVG expired on " + TimeToStr(expiry, TIME_DATE) + ", contact sakisf on FF for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true)
	{

		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, DROP_TF, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, DROP_TF, 0);
		}
		if (new_1m_check)
		{
			ObjectsDeleteAll(0, Name);
			RefreshRates();
			checkpre();
			new_1m_check = false;
		}
	} //YesStop (expiry) end
	   
	return (rates_total);
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION FRACTALS--------------------------------------------+
void checkpre()
{
	//periods = 240;
	//if (periods < Bars) periods = Bars - 20;
	double ma200[];
   if(CopyBuffer(ma200_handle, 0, 0, 5, ma200) <= 0) return;
   ArraySetAsSeries(ma200_handle, true);
   
	periods = Bars - 20;
	ArrayInitialize(ffinp, EMPTY_VALUE);
	ArrayInitialize(ffinn, EMPTY_VALUE);
	ArrayInitialize(flinp, EMPTY_VALUE);
	ArrayInitialize(flinn, EMPTY_VALUE);
	ArrayInitialize(finp, EMPTY_VALUE);
	ArrayInitialize(finn, EMPTY_VALUE);
	ArrayInitialize(linp, EMPTY_VALUE);
	ArrayInitialize(linn, EMPTY_VALUE);
	
	double CD1[], OD1[], HD1[], LD1[];
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(OD1, true);
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArrayResize(CD1, periods + 3);
	ArrayResize(OD1, periods + 3);
	ArrayResize(HD1, periods + 3);
	ArrayResize(LD1, periods + 3);
	CopyClose(_Symbol, PERIOD_CURRENT, 0, periods + 3, CD1);
	CopyOpen(_Symbol, PERIOD_CURRENT, 0, periods + 3, OD1);
	CopyHigh(_Symbol, PERIOD_CURRENT, 0, periods + 3, HD1);
	CopyLow(_Symbol, PERIOD_CURRENT, 0, periods + 3, LD1);
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(OD1, true);
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);

	for (int x = periods - 3; x >= 1; x--)
	{
		if ((CD1[x + 3] < CD1[x + 2]) && LD1[x + 1] - HD1[x + 3] > 0
		&& CD1[x + 1] > iMA(_Symbol, PERIOD_CURRENT, 200, 0, MODE_EMA, PRICE_CLOSE, x))
		{
			ffinp[x] = HD1[x + 3];
			flinp[x] = LD1[x + 1];			
		}
		else { ffinp[x] = EMPTY_VALUE; flinp[x] = EMPTY_VALUE; }
		//
		if ((CD1[x + 3] > CD1[x + 2]) && (LD1[x + 3] - HD1[x + 1] > 0)
		&& CD1[x + 1] < iMA(_Symbol, PERIOD_CURRENT, 200, 0, MODE_EMA, PRICE_CLOSE, x))
		{
			ffinn[x] = LD1[x + 3];
			flinn[x] = HD1[x + 1];
		}
		else { ffinn[x] = EMPTY_VALUE; flinn[x] = EMPTY_VALUE; }
		//
		if ((OD1[x + 3] > CD1[x + 3]) && (((CD1[x + 2] > OD1[x + 2]) && (CD1[x + 1] > OD1[x + 1]) && ((CD1[x + 1] - OD1[x + 2]) > (CD1[x + 3] - OD1[x + 3]))) || ((CD1[x + 2] > OD1[x + 2]) && ((CD1[x + 2] - OD1[x + 2]) > (CD1[x + 3] - OD1[x + 3]))))
		&& CD1[x + 1] > iMA(_Symbol, PERIOD_CURRENT, 200, 0, MODE_EMA, PRICE_CLOSE, x))
		{
			finp[x] = OD1[x + 3];
			linp[x] = HD1[x + 3];
		}
		else { finp[x] = EMPTY_VALUE; linp[x] = EMPTY_VALUE; }
		//
		if ((CD1[x + 3] > OD1[x + 3]) && (((OD1[x + 2] > CD1[x + 2]) && (OD1[x + 1] > CD1[x + 1]) && ((OD1[x + 2] - CD1[x + 1]) > (OD1[x + 3] - CD1[x + 3]))) || ((OD1[x + 2] > CD1[x + 2]) && ((OD1[x + 2] - CD1[x + 2]) > (CD1[x + 3] - OD1[x + 3]))))
		&& CD1[x + 1] < iMA(_Symbol, PERIOD_CURRENT, 200, 0, MODE_EMA, PRICE_CLOSE, x))
		{
			finn[x] = OD1[x + 3];
			linn[x] = LD1[x + 3];
		}
		else { finn[x] = EMPTY_VALUE; linn[x] = EMPTY_VALUE; }
	}

	double fcopybuy[], fcopybuystop[];
	double fcopysel[], fcopyselstop[];
	double copybuy[], copybuystop[];
	double copysel[], copyselstop[];

	int fcount11 = 0, fcount22 = 0;
	int fadda = -1, faddb = -1;
	int count11 = 0, count22 = 0;
	int adda = -1, addb = -1;
   
   for (int x = periods; x >= 1; x--)
   {
      double LL = CD1[ArrayMinimum(CD1, x, 1)];
      double HH = CD1[ArrayMaximum(CD1, x, 1)];
      if (flinp[x] != EMPTY_VALUE && LL > ffinp[x])
      { 
         fadda++;
         ArrayResize(fcopybuy, fadda + 1);
         ArrayResize(fcopybuystop, fadda + 1);
         fcopybuy[fadda] = flinp[x];
         fcopybuystop[fadda] = ffinp[x];
      }
      //
      if (flinn[x] != EMPTY_VALUE && HH < ffinn[x])
      {
         faddb++;
         ArrayResize(fcopysel, faddb + 1);
         ArrayResize(fcopyselstop, faddb + 1);
         fcopysel[faddb] = flinn[x];
         fcopyselstop[faddb] = ffinn[x];
      }
      //
      if (linp[x] != EMPTY_VALUE && LL > finp[x])
      { 
         adda++;
         ArrayResize(copybuy, adda + 1);
         ArrayResize(copybuystop, adda + 1);
         copybuy[adda] = linp[x];
         copybuystop[adda] = finp[x];
      }
      //
      if (linn[x] != EMPTY_VALUE && HH < finn[x])
      {
         addb++;
         ArrayResize(copysel, addb + 1);
         ArrayResize(copyselstop, addb + 1);
         copysel[addb] = linn[x];
         copyselstop[addb] = finn[x];
      }
   }
   
   if (ArraySize(fcopysel) > 0)
   {
      ArraySort(fcopysel, 0, 0, MODE_ASCEND);
      ArraySort(fcopyselstop, 0, 0, MODE_ASCEND);
   }
   if (ArraySize(fcopybuy) > 0)
   {
      ArraySort(fcopybuy, 0, 0, MODE_DESCEND);
      ArraySort(fcopybuystop, 0, 0, MODE_DESCEND);
   }
   
   if (ArraySize(fcopybuy) > 0)
   {   
      fsignalbuy = fcopybuy[0];
      fsignalbuystop = fcopybuystop[0];
   }
   if (ArraySize(fcopysel) > 0)
   {
      fsignalsel = fcopysel[0];
      fsignalselstop = fcopyselstop[0];
   }
	
	if (ArraySize(copysel) > 0)
   {
      ArraySort(copysel, 0, 0, MODE_ASCEND);
      ArraySort(copyselstop, 0, 0, MODE_ASCEND);
   }
   if (ArraySize(copybuy) > 0)
   {
      ArraySort(copybuy, 0, 0, MODE_DESCEND);
      ArraySort(copybuystop, 0, 0, MODE_DESCEND);
   }
   
   if (ArraySize(copybuy) > 0)
   {   
      signalbuy = copybuy[0];
      signalbuystop = copybuystop[0];
   }
   if (ArraySize(copysel) > 0)
   {
      signalsel = copysel[0];
      signalselstop = copyselstop[0];
   }
   
   double buystop = 0, selstop = 0;
   double buy = 0, sel = 0;
      
   if ((fsignalbuy != EMPTY_VALUE && signalbuy != EMPTY_VALUE) && (fsignalsel != EMPTY_VALUE && signalsel != EMPTY_VALUE)) buy = MathMax(fsignalbuy, signalbuy);
   if ((fsignalsel != EMPTY_VALUE && signalsel != EMPTY_VALUE) && (fsignalsel != EMPTY_VALUE && signalsel != EMPTY_VALUE)) sel = MathMin(fsignalsel, signalsel);
   if ((fsignalbuy != EMPTY_VALUE && signalbuy != EMPTY_VALUE) && (fsignalsel != EMPTY_VALUE && signalsel != EMPTY_VALUE)) buystop = MathMin(fsignalbuystop, signalbuystop);
   if ((fsignalsel != EMPTY_VALUE && signalsel != EMPTY_VALUE) && (fsignalbuy != EMPTY_VALUE && signalbuy != EMPTY_VALUE)) selstop = MathMax(fsignalselstop, signalselstop);
   
	if (buy != EMPTY_VALUE && LD1[1] < buy && buy != 0 && (fsignalsel != EMPTY_VALUE || signalsel != EMPTY_VALUE) && (ArraySize(fcopybuy) > 1 || ArraySize(copybuy) > 1) && (iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, 1) < 45 && LD1[1] < iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, 1))) signalcomboup[1] = 1;
	if (buy != EMPTY_VALUE && LD1[1] < buy && buy != 0 && (fsignalsel != EMPTY_VALUE || signalsel != EMPTY_VALUE)) signalbuysl[1] = buystop - addSL * _Point;
	if (buy != EMPTY_VALUE && LD1[1] < buy && buy != 0 && (fsignalsel != EMPTY_VALUE || signalsel != EMPTY_VALUE)) signalbuytp[1] = sel + addTP * _Point;
	
	if (sel != EMPTY_VALUE && HD1[1] > sel && sel != 0 && (fsignalbuy != EMPTY_VALUE || signalbuy != EMPTY_VALUE) && (ArraySize(fcopysel) > 1 || ArraySize(copysel) > 1) && (iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, 1) > 55 && HD1[1] > iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, 1))) signalcombodn[1] = 1;
	if (sel != EMPTY_VALUE && HD1[1] > sel && sel != 0 && (fsignalbuy != EMPTY_VALUE || signalbuy != EMPTY_VALUE)) signalselsl[1] = selstop + addSL * _Point;
	if (sel != EMPTY_VALUE && HD1[1] > sel && sel != 0 && (fsignalbuy != EMPTY_VALUE || signalbuy != EMPTY_VALUE)) signalseltp[1] = buy - addTP * _Point; 
	
	//if ((buy != EMPTY_VALUE || sel != EMPTY_VALUE) && sel != 0) Print(buy + " " + sel + " " + buystop + " " + selstop);
}
//+------------------------------------------------------------------+