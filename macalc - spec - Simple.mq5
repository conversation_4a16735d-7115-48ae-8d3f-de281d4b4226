//+------------------------------------------------------------------+
//|                                           MACalculator.mq4       |
//|                                           Copyright 2017, Sakis  |
//|                                                                  |
//+------------------------------------------------------------------+

// Version History
// ---------------
// v1.0 Made EMA calcs + build on top right corner - DISABLED

// TODO
// v1.x Make alerts when 1h & 30m emas are close to each other - within 10 pips - DISABLED
// v1.x Make visual presentation for 1-2-3 like WS indi - DISABLED
// v1.4 Changed to very simplified version of macalc
// v1.5 Added 5m counts - DISABLED
// v1.6 Added 200 & 666 distances & signals
// v1.7 Added vwap and 3 sd levels + previous day lines
// v1.8 Added RTH vwap
// v1.8 Added weekly & monthly vwap + previous month line

#property strict
#property indicator_chart_window
#define Name MQLInfoString(MQL_PROGRAM_NAME)
#define DATA_LIMIT 666

#property indicator_buffers 10
#property indicator_plots 8

//PLOTS
//MA250
#property indicator_type1 DRAW_LINE
#property indicator_color1 clrAqua
#property indicator_style1 STYLE_SOLID
#property indicator_width1 1
#property indicator_label1 "EMA250"
//MA200
#property indicator_type2 DRAW_LINE
#property indicator_color2 clrRed
#property indicator_style2 STYLE_SOLID
#property indicator_width2 2
#property indicator_label2 "EMA200"
//MA300
#property indicator_type3 DRAW_LINE
#property indicator_color3 clrBlack
#property indicator_style3 STYLE_SOLID
#property indicator_width3 2
#property indicator_label3 "EMA300"
//MA50-100
#property indicator_type4 DRAW_LINE
#property indicator_color4 clrDarkBlue
#property indicator_style4 STYLE_SOLID
#property indicator_width4 2
#property indicator_label4 "EMA50 + EMA100 / 2"
//SMA666
#property indicator_type5 DRAW_LINE
#property indicator_color5 clrYellow
#property indicator_style5 STYLE_SOLID
#property indicator_width5 3
#property indicator_label5 "SMA666"
//EMA666
#property indicator_type6 DRAW_LINE
#property indicator_color6 clrGold
#property indicator_style6 STYLE_SOLID
#property indicator_width6 3
#property indicator_label6 "EMA666"
//MA20
#property indicator_type7 DRAW_LINE
#property indicator_color7 clrViolet
#property indicator_style7 STYLE_SOLID
#property indicator_width7 1
#property indicator_label7 "EMA20"
//MA20
#property indicator_type8 DRAW_LINE
#property indicator_color8 clrIndigo
#property indicator_style8 STYLE_SOLID
#property indicator_width8 1
#property indicator_label8 "EMA34"

double MA200[], MA300[], MA250L[], MA50L[], MA100[], MA50[], MA34E[], MA20[];
double MA666s[], MA666e[];

int m200, m300, m250, m50, m100, m20, m666, m666e, m34;
//int a11, a21, a31, a41, a51, a61, a71, b11, b21, b31, b41, b51, b61, b71;

input bool EnableDWM = true; //Enable / disable lines on previous D/W/M ohlc

input string test = " ";
bool debug = false;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
	IndicatorSetInteger(INDICATOR_DIGITS, _Digits);

	SetIndexBuffer(0, MA250L, INDICATOR_DATA);
	SetIndexBuffer(1, MA200, INDICATOR_DATA);
	SetIndexBuffer(2, MA300, INDICATOR_DATA);
	SetIndexBuffer(3, MA50, INDICATOR_DATA);
	SetIndexBuffer(4, MA666s, INDICATOR_DATA);
	SetIndexBuffer(5, MA666e, INDICATOR_DATA);
	SetIndexBuffer(6, MA20, INDICATOR_DATA);
	SetIndexBuffer(7, MA34E, INDICATOR_DATA);
	SetIndexBuffer(8, MA50L, INDICATOR_DATA);
	SetIndexBuffer(9, MA100, INDICATOR_DATA);

	string obname;
	obname = Name + " LivePCTD";
	LabelMake(obname, 3, 160, 0, "", 10, clrWhite);
	obname = Name + " LivePCTW";
	LabelMake(obname, 3, 120, 12, "", 10, clrWhite);
	obname = Name + " LivePCTM";
	LabelMake(obname, 3, 80, 24, "", 10, clrWhite);
	obname = Name + " LivePrice";
	LabelMake(obname, 3, 160, 36, "", 10, clrWhite);

	m250 = iMA(_Symbol, PERIOD_CURRENT, 250, 0, MODE_EMA, PRICE_CLOSE);
	m200 = iMA(_Symbol, PERIOD_CURRENT, 200, 0, MODE_EMA, PRICE_CLOSE);
	m300 = iMA(_Symbol, PERIOD_CURRENT, 300, 0, MODE_EMA, PRICE_CLOSE);
	m50 = iMA(_Symbol, PERIOD_CURRENT, 50, 0, MODE_EMA, PRICE_CLOSE);
	m100 = iMA(_Symbol, PERIOD_CURRENT, 100, 0, MODE_EMA, PRICE_CLOSE);
	m20 = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_TYPICAL);
	m34 = iMA(_Symbol, PERIOD_CURRENT, 34, 0, MODE_EMA, PRICE_TYPICAL);
	m666 = iMA(_Symbol, PERIOD_CURRENT, 666, 0, MODE_SMA, PRICE_CLOSE);
	m666e = iMA(_Symbol, PERIOD_CURRENT, 666, 0, MODE_EMA, PRICE_CLOSE);

	/*
	a11 = iMA(_Symbol, PERIOD_M1, 666, 0, MODE_SMA, PRICE_CLOSE);
	a21 = iMA(_Symbol, PERIOD_M5, 666, 0, MODE_SMA, PRICE_CLOSE);
	a31 = iMA(_Symbol, PERIOD_M15, 666, 0, MODE_SMA, PRICE_CLOSE);
	a41 = iMA(_Symbol, PERIOD_M30, 666, 0, MODE_SMA, PRICE_CLOSE);
	a51 = iMA(_Symbol, PERIOD_H1, 666, 0, MODE_SMA, PRICE_CLOSE);
	a61 = iMA(_Symbol, PERIOD_H4, 666, 0, MODE_SMA, PRICE_CLOSE);
	a71 = iMA(_Symbol, PERIOD_D1, 666, 0, MODE_SMA, PRICE_CLOSE);

	b11 = iMA(_Symbol, PERIOD_M1, 250, 0, MODE_EMA, PRICE_CLOSE);
	b21 = iMA(_Symbol, PERIOD_M5, 250, 0, MODE_EMA, PRICE_CLOSE);
	b31 = iMA(_Symbol, PERIOD_M15, 250, 0, MODE_EMA, PRICE_CLOSE);
	b41 = iMA(_Symbol, PERIOD_M30, 250, 0, MODE_EMA, PRICE_CLOSE);
	b51 = iMA(_Symbol, PERIOD_H1, 250, 0, MODE_EMA, PRICE_CLOSE);
	b61 = iMA(_Symbol, PERIOD_H4, 250, 0, MODE_EMA, PRICE_CLOSE);
	b71 = iMA(_Symbol, PERIOD_D1, 250, 0, MODE_EMA, PRICE_CLOSE);
	*/

	/*	
	int p = 15;
	string tfs[7] = {"M1", "M5", "M15", "M30", "H1", "H4", "D1"};
	for (int i = 7; i >= 1; i--)
	{
		obname = Name + IntegerToString(i) + " TF";
		LabelMake(obname, 0, 100 + i * 2 * p, 80, tfs[i - 1], 7, clrBlack);
		ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
		obname = Name + IntegerToString(i) + " 250";
		LabelMake(obname, 0, 100 + i * 2 * p, 95, "", 7, clrBlack);
		obname = Name + IntegerToString(i) + " 666";
		LabelMake(obname, 0, 100 + i * 2 * p, 110, " ", 7, clrBlack);
		obname = Name + IntegerToString(i) + " Signal";
		LabelMake(obname, 0, 100 + i * 2 * p, 125, " ", 7, clrBlack);
	}

	obname = Name + " 250L";
	LabelMake(obname, 0, 90, 95, "250", 7, clrAqua);
	obname = Name + " 666L";
	LabelMake(obname, 0, 90, 110, "666", 7, clrYellow);
	obname = Name + " SignalL";
	LabelMake(obname, 0, 90, 125, "Sig", 7, clrBlack);
	*/

	if (test == "debug")
		debug = true;
	//---
	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+ON TIMER----------------------------------------------------------+
void OnTimer()
{
	EventKillTimer();
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || MQLInfoInteger(MQL_TESTER))
		if (!MQLInfoInteger(MQL_TESTER))
		{
			DeleteObjects();
		}
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	//---
	datetime expiry = D'2022.06.30 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("macalc-spec expired on " + TimeToString(expiry, TIME_DATE) + ", contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true)
	{
		//--- check for rates total
		if (rates_total < DATA_LIMIT)
			return (0); // not enough bars for calculation
				//--- not all data may be calculated
		int calculated = BarsCalculated(m666e);
		if (calculated < rates_total)
		{
			Print("Not all data of EMA 666 is calculated (", calculated, "bars ). Error", GetLastError());
			return (0);
		}
		//--- we can copy not all data
		int to_copy;
		if (prev_calculated > rates_total || prev_calculated < 0)
			to_copy = rates_total;
		else
		{
			to_copy = rates_total - prev_calculated;
			if (prev_calculated > 0)
				to_copy++;
		}

		//--- get EMA20 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(m20, 0, 0, to_copy, MA20) <= 0)
		{
			Print("Getting EMA20 has failed! Error", GetLastError());
			return (0);
		}
		//--- get EMA34 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(m34, 0, 0, to_copy, MA34E) <= 0)
		{
			Print("Getting EMA34 has failed! Error", GetLastError());
			return (0);
		}
		//--- get EMA200 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(m200, 0, 0, to_copy, MA200) <= 0)
		{
			Print("Getting EMA200 has failed! Error", GetLastError());
			return (0);
		}
		//--- get EMA250 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(m250, 0, 0, to_copy, MA250L) <= 0)
		{
			Print("Getting EMA250 has failed! Error", GetLastError());
			return (0);
		}
		//--- get EMA300 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(m300, 0, 0, to_copy, MA300) <= 0)
		{
			Print("Getting EMA300 has failed! Error", GetLastError());
			return (0);
		}
		//--- get EMA50 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(m50, 0, 0, to_copy, MA50L) <= 0)
		{
			Print("Getting EMA50 has failed! Error", GetLastError());
			return (0);
		}
		//--- get EMA100 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(m100, 0, 0, to_copy, MA100) <= 0)
		{
			Print("Getting EMA100 has failed! Error", GetLastError());
			return (0);
		}
		//--- get SMA666 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(m666, 0, 0, to_copy, MA666s) <= 0)
		{
			Print("Getting SMA666 has failed! Error", GetLastError());
			return (0);
		}
		//--- get EMA666 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(m666e, 0, 0, to_copy, MA666e) <= 0)
		{
			Print("Getting EMA666 has failed! Error", GetLastError());
			return (0);
		}

		bool new1h_check = false;
		static datetime new1h_time = 0;
		if (new1h_time < iTime(_Symbol, PERIOD_CURRENT, 0))
		{
			new1h_check = true;
			new1h_time = iTime(_Symbol, PERIOD_CURRENT, 0);
		}
		if (new1h_check)
		{
			for (int i = rates_total - 1; i >= 1; i--)
			{
				MA50[i] = (MA50L[i] + MA100[i]) / 2;
			}
			new1h_check = false;
		}

		{
			MA50[0] = (MA50L[0] + MA100[0]) / 2;
		}

		//Bingo666();
		LivePrices();

		if (EnableDWM)
		{
			bool new_ctf_check = false;
			static datetime start_ctf_time = 0;
			if (start_ctf_time < iTime(_Symbol, PERIOD_M30, 0))
			{
				new_ctf_check = true;
				start_ctf_time = iTime(_Symbol, PERIOD_M30, 0);
			}
			if (new_ctf_check)
			{
				ObjectsDeleteAll(0, Name + " y");
				ydayweek();
				new_ctf_check = false;
			}
		}
	}
	return (rates_total);
}
//+------------------------------------------------------------------+

//+LIVE PERCENTAGES & PRICE------------------------------------------+
void LivePrices()
{
	double CD1[];
	ArrayResize(CD1, 2);
	ArraySetAsSeries(CD1, true);
	if (!CopyClose(_Symbol, PERIOD_D1, 0, 2, CD1))
		Print("Failed to copy daily closes, ", GetLastError());
	double CW1[];
	ArrayResize(CW1, 2);
	ArraySetAsSeries(CW1, true);
	if (!CopyClose(_Symbol, PERIOD_W1, 0, 2, CW1))
		Print("Failed to copy weekly closes, ", GetLastError());
	double CMN1[];
	ArrayResize(CMN1, 2);
	ArraySetAsSeries(CMN1, true);
	if (!CopyClose(_Symbol, PERIOD_MN1, 0, 2, CMN1))
		Print("Failed to copy montly closes, ", GetLastError());

	MqlTick ticktack;
	SymbolInfoTick(_Symbol, ticktack);

	double pric = 0;
	if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFD || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFDINDEX)
		pric = ticktack.bid;
	else
		pric = ticktack.last;

	if (CD1[1] != 0)
	{
		if ((pric - CD1[1]) >= 0)
			ObjectSetText(Name + " LivePCTD", "D: +" + DoubleToString((pric - CD1[1]) / CD1[1] * 100, 2) + "%", 10, "Arial Black", clrBlack);
		else
			ObjectSetText(Name + " LivePCTD", "D: " + DoubleToString((pric - CD1[1]) / CD1[1] * 100, 2) + "%", 10, "Arial Black", clrBlack);
		if ((pric - CD1[1]) / CD1[1] * 100 > 0.25)
			ObjectSetInteger(0, Name + " LivePCTD", OBJPROP_COLOR, clrBlue);
		else if ((pric - CD1[1]) / CD1[1] * 100 < -0.25)
			ObjectSetInteger(0, Name + " LivePCTD", OBJPROP_COLOR, clrRed);
	}

	if (CW1[1] != 0)
	{
		if ((pric - CW1[1]) >= 0)
			ObjectSetText(Name + " LivePCTW", "W: +" + DoubleToString((pric - CW1[1]) / CW1[1] * 100, 2) + "%", 10, "Arial Black", clrBlack);
		else
			ObjectSetText(Name + " LivePCTW", "W: " + DoubleToString((pric - CW1[1]) / CW1[1] * 100, 2) + "%", 10, "Arial Black", clrBlack);
		if ((pric - CW1[1]) / CW1[1] * 100 > 0.25)
			ObjectSetInteger(0, Name + " LivePCTW", OBJPROP_COLOR, clrBlue);
		else if ((pric - CW1[1]) / CW1[1] * 100 < -0.25)
			ObjectSetInteger(0, Name + " LivePCTW", OBJPROP_COLOR, clrRed);
	}

	if (CMN1[1] != 0)
	{
		if ((pric - CMN1[1]) >= 0)
			ObjectSetText(Name + " LivePCTM", "M: +" + DoubleToString((pric - CMN1[1]) / CMN1[1] * 100, 2) + "%", 10, "Arial Black", clrBlack);
		else
			ObjectSetText(Name + " LivePCTM", "M: " + DoubleToString((pric - CMN1[1]) / CMN1[1] * 100, 2) + "%", 10, "Arial Black", clrBlack);
		if ((pric - CMN1[1]) / CMN1[1] * 100 > 0.25)
			ObjectSetInteger(0, Name + " LivePCTM", OBJPROP_COLOR, clrBlue);
		else if ((pric - CMN1[1]) / CMN1[1] * 100 < -0.25)
			ObjectSetInteger(0, Name + " LivePCTM", OBJPROP_COLOR, clrRed);
	}

	ObjectSetText(Name + " LivePrice", DoubleToString(pric, _Digits), 24, "Arial Black", clrBlack);
}
//+------------------------------------------------------------------+

//+YDAY HIGH/LOW/OPEN/CLOSE------------------------------------------+
void ydayweek()
{
	double o, h, l, c, oo, hh, ll, cc, ooo, hhh, lll, ccc;
	o = iOpen(_Symbol, PERIOD_D1, 1);
	oo = iOpen(_Symbol, PERIOD_W1, 1);
	ooo = iOpen(_Symbol, PERIOD_MN1, 1);
	h = iHigh(_Symbol, PERIOD_D1, 1);
	hh = iHigh(_Symbol, PERIOD_W1, 1);
	hhh = iHigh(_Symbol, PERIOD_MN1, 1);
	l = iLow(_Symbol, PERIOD_D1, 1);
	ll = iLow(_Symbol, PERIOD_W1, 1);
	lll = iLow(_Symbol, PERIOD_MN1, 1);
	c = iClose(_Symbol, PERIOD_D1, 1);
	cc = iClose(_Symbol, PERIOD_W1, 1);
	ccc = iClose(_Symbol, PERIOD_MN1, 1);

	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT); // number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	int x = 24 + Hour();
	string obname;

	int od = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 1), false);
	int cd = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false);
	int ow = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 1), false);
	int cw = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 0), false);
	int om = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 1), false);
	int cm = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 0), false);

	if (ChartPeriod() >= 1 && ChartPeriod() <= 16385)
	{
		if (ChartPeriod() <= 16385)
		{
			obname = Name + " ydayO";
			objtrend2(obname, o, o, od, 0, 4 * Period(), 1, 0, clrBlack, "Yesterday Open");
		}
		{
			obname = Name + " ydayO F1";
			Texter(obname, o, Time[0] + 4 * Period(), "D-1 O", clrBlack);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		if (DayOfWeek() != 1)
		{
			if (ChartPeriod() <= 16385)
			{
				obname = Name + " ydayC";
				objtrend2(obname, c, c, cd, 0, 4 * Period(), 1, 0, clrBlack, "Yesterday Close");
			}
			{
				obname = Name + " ydayC F1";
				Texter(obname, c, Time[0] + 4 * Period(), "D-1 C", clrBlack);
				ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
				ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
				ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
			}
		}
		if (ChartPeriod() <= 16385)
		{
			obname = Name + " ydayH";
			objtrend2(obname, h, h, od, 0, 4 * Period(), 1, 0, clrWhite, "Yesterday High");
		}
		{
			obname = Name + " ydayH F1";
			Texter(obname, h, Time[0] + 4 * Period(), "D-1 H", clrWhite);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		if (ChartPeriod() <= 16385)
		{
			obname = Name + " ydayL";
			objtrend2(obname, l, l, od, 0, 4 * Period(), 1, 0, clrWhite, "Yesterday Low");
		}
		{
			obname = Name + " ydayL F1";
			Texter(obname, l, Time[0] + 4 * Period(), "D-1 L", clrWhite);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}

		obname = Name + " ymonthC";
		objtrend2(obname, ccc, ccc, od, 0, 6 * Period(), 2, 0, clrRed, "Last Month Close");
		{
			obname = Name + " ymonthC F1";
			Texter(obname, ccc, Time[0] + 6 * Period(), "M-1 C", clrRed);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		obname = Name + " ymonthH";
		objtrend2(obname, hhh, hhh, od, 0, 6 * Period(), 2, 0, clrDarkOrange, "Last Month High");
		{
			obname = Name + " ymonthH F1";
			Texter(obname, hhh, Time[0] + 6 * Period(), "M-1 H", clrDarkOrange);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		obname = Name + " ymonthL";
		objtrend2(obname, lll, lll, od, 0, 6 * Period(), 2, 0, clrDarkOrange, "Last Month Low");
		{
			obname = Name + " ymonthL F1";
			Texter(obname, lll, Time[0] + 6 * Period(), "M-1 L", clrDarkOrange);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}

		obname = Name + " yweekC";
		objtrend2(obname, cc, cc, od, 0, 6 * Period(), 2, 0, clrDarkGoldenrod, "Last Week Close");
		{
			obname = Name + " yweekC F1";
			Texter(obname, cc, Time[0] + 6 * Period(), "W-1 C", clrDarkGoldenrod);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		obname = Name + " yweekH";
		objtrend2(obname, hh, hh, od, 0, 6 * Period(), 2, 0, clrYellow, "Last Week High");
		{
			obname = Name + " yweekH F1";
			Texter(obname, hh, Time[0] + 6 * Period(), "W-1 H", clrYellow);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		obname = Name + " yweekL";
		objtrend2(obname, ll, ll, od, 0, 6 * Period(), 2, 0, clrYellow, "Last Week Low");
		{
			obname = Name + " yweekL F1";
			Texter(obname, ll, Time[0] + 6 * Period(), "W-1 L", clrYellow);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
	}
}
//+------------------------------------------------------------------+

/*
//+250 & 666 Distances-----------------------------------------------+
void Bingo666()
{
	double a1[1], a2[1], a3[1], a4[1], a5[1], a6[1], a7[1];
	double b1[1], b2[1], b3[1], b4[1], b5[1], b6[1], b7[1];

	//--- get EMA666 M1 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(a11, 0, 0, 1, a1) <= 0)
	{
		//Print("Getting EMA666 M1 has failed! Error", GetLastError());
		//return;
	}
	//--- get EMA250 M1 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(b11, 0, 0, 1, b1) <= 0)
	{
		//Print("Getting EMA250 M1 has failed! Error", GetLastError());
		//return;
	}
	//--- get EMA666 M5 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(a21, 0, 0, 1, a2) <= 0)
	{
		//Print("Getting EMA666 M5 has failed! Error", GetLastError());
		//return;
	}
	//--- get EMA250 M5 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(b21, 0, 0, 1, b2) <= 0)
	{
		//Print("Getting EMA250 M5 has failed! Error", GetLastError());
		//return;
	}
	//--- get EMA666 M15 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(a31, 0, 0, 1, a3) <= 0)
	{
		//Print("Getting EMA666 M15 has failed! Error", GetLastError());
		//return;
	}
	//--- get EMA250 M15 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(b31, 0, 0, 1, b3) <= 0)
	{
		//Print("Getting EMA250 M15 has failed! Error", GetLastError());
		//return;
	}
	//--- get EMA666 M30 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(a41, 0, 0, 1, a4) <= 0)
	{
		//Print("Getting EMA666 M30 has failed! Error", GetLastError());
		//return;
	}
	//--- get EMA250 M30 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(b41, 0, 0, 1, b4) <= 0)
	{
		//Print("Getting EMA250 M30 has failed! Error", GetLastError());
		//return;
	}
	//--- get EMA666 H1 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(a51, 0, 0, 1, a5) <= 0)
	{
		//Print("Getting EMA666 H1 has failed! Error", GetLastError());
		//return;
	}
	//--- get EMA250 H1 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(b51, 0, 0, 1, b5) <= 0)
	{
		//Print("Getting EMA250 H1 has failed! Error", GetLastError());
		//return;
	}
	//--- get EMA666 H4 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(a61, 0, 0, 1, a6) <= 0)
	{
		//Print("Getting EMA666 H4 has failed! Error", GetLastError());
		//return;
	}
	//--- get EMA250 H4 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(b61, 0, 0, 1, b6) <= 0)
	{
		//Print("Getting EMA250 H4 has failed! Error", GetLastError());
		//return;
	}
	//--- get EMA666 D1 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(a71, 0, 0, 1, a7) <= 0)
	{
		//Print("Getting EMA666 D1 has failed! Error", GetLastError());
		//return;
	}
	//--- get EMA250 D1 buffer
	if (IsStopped())
		return; //Checking for stop flag
	if (CopyBuffer(b71, 0, 0, 1, b7) <= 0)
	{
		//Print("Getting EMA250 D1 has failed! Error", GetLastError());
		//return;
	}

	double p1 = 0, p2 = 0, p3 = 0, p4 = 0, p5 = 0, p6 = 0, p7 = 0;
	double s1 = 0, s2 = 0, s3 = 0, s4 = 0, s5 = 0, s6 = 0, s7 = 0;

   MqlTick ticktack;
   SymbolInfoTick(_Symbol, ticktack);

	if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFD || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_CFDINDEX)
	{
		if (a1[0] != 0)
			p1 = ticktack.bid - a1[0];
		if (a2[0] != 0)
			p2 = ticktack.bid - a2[0];
		if (a3[0] != 0)
			p3 = ticktack.bid - a3[0];
		if (a4[0] != 0)
			p4 = ticktack.bid - a4[0];
		if (a5[0] != 0)
			p5 = ticktack.bid - a5[0];
		if (a6[0] != 0)
			p6 = ticktack.bid - a6[0];
		if (a7[0] != 0)
			p7 = ticktack.bid - a7[0];

		if (b1[0] != 0)
			s1 = ticktack.bid - b1[0];
		if (b2[0] != 0)
			s2 = ticktack.bid - b2[0];
		if (b3[0] != 0)
			s3 = ticktack.bid - b3[0];
		if (b4[0] != 0)
			s4 = ticktack.bid - b4[0];
		if (b5[0] != 0)
			s5 = ticktack.bid - b5[0];
		if (b6[0] != 0)
			s6 = ticktack.bid - b6[0];
		if (b7[0] != 0)
			s7 = ticktack.bid - b7[0];
	}
	else
	{
		if (a1[0] != 0)
			p1 = ticktack.last - a1[0];
		if (a2[0] != 0)
			p2 = ticktack.last - a2[0];
		if (a3[0] != 0)
			p3 = ticktack.last - a3[0];
		if (a4[0] != 0)
			p4 = ticktack.last - a4[0];
		if (a5[0] != 0)
			p5 = ticktack.last - a5[0];
		if (a6[0] != 0)
			p6 = ticktack.last - a6[0];
		if (a7[0] != 0)
			p7 = ticktack.last - a7[0];

		if (b1[0] != 0)
			s1 = ticktack.last - b1[0];
		if (b2[0] != 0)
			s2 = ticktack.last - b2[0];
		if (b3[0] != 0)
			s3 = ticktack.last - b3[0];
		if (b4[0] != 0)
			s4 = ticktack.last - b4[0];
		if (b5[0] != 0)
			s5 = ticktack.last - b5[0];
		if (b6[0] != 0)
			s6 = ticktack.last - b6[0];
		if (b7[0] != 0)
			s7 = ticktack.last - b7[0];
	}
	ObjectSetInteger(0, Name + IntegerToString(5) + " L", OBJPROP_COLOR, clrYellow);
	ObjectSetString(0, Name + IntegerToString(5) + " L", OBJPROP_FONT, "Arial Black");

	double Pip_Value = 0;
	if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE || SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE) <= 0.0001)
		Pip_Value = (_Point * MathPow(10, MathMod(_Digits, 2)));
	else
		Pip_Value = 1;

	//666 SMA
	if (a1[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_M1, 0) > a1[0]))
			ObjectSetText(Name + IntegerToString(1) + " 666", DoubleToString(p1 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(1) + " 666", DoubleToString(p1 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(1) + " 666", OBJPROP_TOOLTIP, DoubleToString(a1[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(1) + " 666", " ", 7, "Arial", clrBlue);
	if (a2[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_M5, 0) > a2[0]))
			ObjectSetText(Name + IntegerToString(2) + " 666", DoubleToString(p2 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(2) + " 666", DoubleToString(p2 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(2) + " 666", OBJPROP_TOOLTIP, DoubleToString(a2[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(2) + " 666", " ", 7, "Arial", clrBlue);
	if (a3[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_M15, 0) > a3[0]))
			ObjectSetText(Name + IntegerToString(3) + " 666", DoubleToString(p3 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(3) + " 666", DoubleToString(p3 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(3) + " 666", OBJPROP_TOOLTIP, DoubleToString(a3[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(3) + " 666", " ", 7, "Arial", clrBlue);
	if (a4[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_M30, 0) > a4[0]))
			ObjectSetText(Name + IntegerToString(4) + " 666", DoubleToString(p4 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(4) + " 666", DoubleToString(p4 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(4) + " 666", OBJPROP_TOOLTIP, DoubleToString(a4[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(4) + " 666", " ", 7, "Arial", clrBlue);
	if (a5[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_H1, 0) > a5[0]))
			ObjectSetText(Name + IntegerToString(5) + " 666", DoubleToString(p5 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(5) + " 666", DoubleToString(p5 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(5) + " 666", OBJPROP_TOOLTIP, DoubleToString(a5[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(5) + " 666", " ", 7, "Arial", clrBlue);
	if (a6[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_H4, 0) > a6[0]))
			ObjectSetText(Name + IntegerToString(6) + " 666", DoubleToString(p6 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(6) + " 666", DoubleToString(p6 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(6) + " 666", OBJPROP_TOOLTIP, DoubleToString(a6[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(6) + " 666", " ", 7, "Arial", clrBlue);
	if (a7[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_D1, 0) > a7[0]))
			ObjectSetText(Name + IntegerToString(7) + " 666", DoubleToString(p7 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(7) + " 666", DoubleToString(p7 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(7) + " 666", OBJPROP_TOOLTIP, DoubleToString(a7[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(7) + " 666", " ", 7, "Arial", clrBlue);

	//250 EMA
	if (b1[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_M1, 0) > b1[0]))
			ObjectSetText(Name + IntegerToString(1) + " 250", DoubleToString(s1 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(1) + " 250", DoubleToString(s1 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(1) + " 250", OBJPROP_TOOLTIP, DoubleToString(b1[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(1) + " 250", " ", 7, "Arial", clrBlue);
	if (b2[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_M5, 0) > b2[0]))
			ObjectSetText(Name + IntegerToString(2) + " 250", DoubleToString(s2 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(2) + " 250", DoubleToString(s2 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(2) + " 250", OBJPROP_TOOLTIP, DoubleToString(b2[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(2) + " 250", " ", 7, "Arial", clrBlue);
	if (b3[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_M15, 0) > b3[0]))
			ObjectSetText(Name + IntegerToString(3) + " 250", DoubleToString(s3 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(3) + " 250", DoubleToString(s3 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(3) + " 250", OBJPROP_TOOLTIP, DoubleToString(b3[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(3) + " 250", " ", 7, "Arial", clrBlue);
	if (b4[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_M30, 0) > b4[0]))
			ObjectSetText(Name + IntegerToString(4) + " 250", DoubleToString(s4 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(4) + " 250", DoubleToString(s4 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(4) + " 250", OBJPROP_TOOLTIP, DoubleToString(b4[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(4) + " 250", " ", 7, "Arial", clrBlue);
	if (b5[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_H1, 0) > b5[0]))
			ObjectSetText(Name + IntegerToString(5) + " 250", DoubleToString(s5 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(5) + " 250", DoubleToString(s5 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(5) + " 250", OBJPROP_TOOLTIP, DoubleToString(b5[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(5) + " 250", " ", 7, "Arial", clrBlue);
	if (b6[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_H4, 0) > b6[0]))
			ObjectSetText(Name + IntegerToString(6) + " 250", DoubleToString(s6 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(6) + " 250", DoubleToString(s6 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(6) + " 250", OBJPROP_TOOLTIP, DoubleToString(b6[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(6) + " 250", " ", 7, "Arial", clrBlue);
	if (b7[0] != 0)
	{
		if ((iClose(_Symbol, PERIOD_D1, 0) > b7[0]))
			ObjectSetText(Name + IntegerToString(7) + " 250", DoubleToString(s7 / Pip_Value, 1), 7, "Arial", clrBlue);
		else
			ObjectSetText(Name + IntegerToString(7) + " 250", DoubleToString(s7 / Pip_Value, 1), 7, "Arial", clrRed);
		ObjectSetString(0, Name + IntegerToString(7) + " 250", OBJPROP_TOOLTIP, DoubleToString(b7[0], _Digits));
	}
	else
		ObjectSetText(Name + IntegerToString(7) + " 250", " ", 7, "Arial", clrBlue);

	//M1
	if (a1[0] != 0 && b1[0] != 0)
	{
		if (iClose(_Symbol, PERIOD_M1, 0) > a1[0] && iClose(_Symbol, PERIOD_M1, 0) > b1[0])
			ObjectSetText(Name + IntegerToString(1) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_M1, 0) < a1[0] && iClose(_Symbol, PERIOD_M1, 0) < b1[0])
			ObjectSetText(Name + IntegerToString(1) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
		else if (iClose(_Symbol, PERIOD_M1, 0) > a1[0] && iClose(_Symbol, PERIOD_M1, 0) < b1[0])
			ObjectSetText(Name + IntegerToString(1) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_M1, 0) < a1[0] && iClose(_Symbol, PERIOD_M1, 0) > b1[0])
			ObjectSetText(Name + IntegerToString(1) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
	}
	//M5
	if (a2[0] != 0 && b2[0] != 0)
	{
		if (iClose(_Symbol, PERIOD_M5, 0) > a2[0] && iClose(_Symbol, PERIOD_M5, 0) > b2[0])
			ObjectSetText(Name + IntegerToString(2) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_M5, 0) < a2[0] && iClose(_Symbol, PERIOD_M5, 0) < b2[0])
			ObjectSetText(Name + IntegerToString(2) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
		else if (iClose(_Symbol, PERIOD_M5, 0) > a2[0] && iClose(_Symbol, PERIOD_M5, 0) < b2[0])
			ObjectSetText(Name + IntegerToString(2) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_M5, 0) < a2[0] && iClose(_Symbol, PERIOD_M5, 0) > b2[0])
			ObjectSetText(Name + IntegerToString(2) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
	}
	//M15
	if (a3[0] != 0 && b3[0] != 0)
	{
		if (iClose(_Symbol, PERIOD_M15, 0) > a3[0] && iClose(_Symbol, PERIOD_M15, 0) > b3[0])
			ObjectSetText(Name + IntegerToString(3) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_M15, 0) < a3[0] && iClose(_Symbol, PERIOD_M15, 0) < b3[0])
			ObjectSetText(Name + IntegerToString(3) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
		else if (iClose(_Symbol, PERIOD_M15, 0) > a3[0] && iClose(_Symbol, PERIOD_M15, 0) < b3[0])
			ObjectSetText(Name + IntegerToString(3) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_M15, 0) < a3[0] && iClose(_Symbol, PERIOD_M15, 0) > b3[0])
			ObjectSetText(Name + IntegerToString(3) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
	}
	//M30
	if (a4[0] != 0 && b4[0] != 0)
	{
		if (iClose(_Symbol, PERIOD_M30, 0) > a4[0] && iClose(_Symbol, PERIOD_M30, 0) > b4[0])
			ObjectSetText(Name + IntegerToString(4) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_M30, 0) < a4[0] && iClose(_Symbol, PERIOD_M30, 0) < b4[0])
			ObjectSetText(Name + IntegerToString(4) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
		else if (iClose(_Symbol, PERIOD_M30, 0) > a4[0] && iClose(_Symbol, PERIOD_M30, 0) < b4[0])
			ObjectSetText(Name + IntegerToString(4) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_M30, 0) < a4[0] && iClose(_Symbol, PERIOD_M30, 0) > b4[0])
			ObjectSetText(Name + IntegerToString(4) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
	}
	//H1
	if (a5[0] != 0 && b5[0] != 0)
	{
		if (iClose(_Symbol, PERIOD_H1, 0) > a5[0] && iClose(_Symbol, PERIOD_H1, 0) > b5[0])
			ObjectSetText(Name + IntegerToString(5) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_H1, 0) < a5[0] && iClose(_Symbol, PERIOD_H1, 0) < b5[0])
			ObjectSetText(Name + IntegerToString(5) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
		else if (iClose(_Symbol, PERIOD_H1, 0) > a5[0] && iClose(_Symbol, PERIOD_H1, 0) < b5[0])
			ObjectSetText(Name + IntegerToString(5) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_H1, 0) < a5[0] && iClose(_Symbol, PERIOD_H1, 0) > b5[0])
			ObjectSetText(Name + IntegerToString(5) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
	}
	//H4
	if (a6[0] != 0 && b6[0] != 0)
	{
		if (iClose(_Symbol, PERIOD_H4, 0) > a6[0] && iClose(_Symbol, PERIOD_H4, 0) > b6[0])
			ObjectSetText(Name + IntegerToString(6) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_H4, 0) < a6[0] && iClose(_Symbol, PERIOD_H4, 0) < b6[0])
			ObjectSetText(Name + IntegerToString(6) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
		else if (iClose(_Symbol, PERIOD_H4, 0) > a6[0] && iClose(_Symbol, PERIOD_H4, 0) < b6[0])
			ObjectSetText(Name + IntegerToString(6) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_H4, 0) < a6[0] && iClose(_Symbol, PERIOD_H4, 0) > b6[0])
			ObjectSetText(Name + IntegerToString(6) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
	}
	//D1
	if (a7[0] != 0 && b7[0] != 0)
	{
		if (iClose(_Symbol, PERIOD_D1, 0) > a7[0] && iClose(_Symbol, PERIOD_D1, 0) > b7[0])
			ObjectSetText(Name + IntegerToString(7) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_D1, 0) < a7[0] && iClose(_Symbol, PERIOD_D1, 0) < b7[0])
			ObjectSetText(Name + IntegerToString(7) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
		else if (iClose(_Symbol, PERIOD_D1, 0) > a7[0] && iClose(_Symbol, PERIOD_D1, 0) < b7[0])
			ObjectSetText(Name + IntegerToString(7) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
		else if (iClose(_Symbol, PERIOD_D1, 0) < a7[0] && iClose(_Symbol, PERIOD_D1, 0) > b7[0])
			ObjectSetText(Name + IntegerToString(7) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
	}
	else
		ObjectSetText(Name + IntegerToString(7) + " Signal", " ", 7, "Arial", clrBlack);
}
//+------------------------------------------------------------------+
*/

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	//ObjectsDeleteAll(0, Name);
	for (int i = ObjectsTotal(0, 0) - 1; i >= 0; i--)
	{
		string ObName = ObjectName(0, i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(0, ObName);
		}
	}
}
//+------------------------------------------------------------------+

//+ChartEvent function-----------------------------------------------+
void OnChartEvent(const int id,
				  const long &lparam,
				  const double &dparam,
				  const string &sparam)
{
	//---
	{ //Switch autoscroll
		if (id == CHARTEVENT_KEYDOWN)
		{
			if (lparam == StringGetCharacter("E", 0))
			{
				ChartSetInteger(0, CHART_AUTOSCROLL, false);
			}
			if (lparam == StringGetCharacter("Q", 0))
			{
				ChartSetInteger(0, CHART_AUTOSCROLL, true);
			}
			if (lparam == StringGetCharacter("U", 0))
			{
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_KEYDOWN)
		{
			if (lparam == StringGetCharacter("O", 0))
			{
				ObjectsDeleteAll(0, 0, -1);
				Comment("");
				ChartApplyTemplate(0, "simple28.tpl");
			}
			if (lparam == StringGetCharacter("I", 0))
			{
				ObjectsDeleteAll(0, 0, -1);
				Comment("");
				ChartApplyTemplate(0, "simple28indi.tpl");
			}
			if (lparam == StringGetCharacter("P", 0))
			{
				ObjectsDeleteAll(0, 0, -1);
				Comment("");
				ChartApplyTemplate(0, "simple28inon.tpl");
			}
		}
	}
	{ //Switch TF by hand
		if (id == CHARTEVENT_KEYDOWN)
		{
			switch (int(lparam))
			{
			case 97:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M1);
				break;
			case 98:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M5);
				break;
			case 99:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M15);
				break;
			case 100:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M30);
				break;
			case 101:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_H1);
				break;
			case 102:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_H4);
				break;
			case 103:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_D1);
				break;
			case 104:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_W1);
				break;
			case 105:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_MN1);
				break;
			}
		}
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+

//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT); // number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Time[0] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
	ObjectSetInteger(0, name, OBJPROP_STYLE, st);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
	ObjectSetInteger(0, name, OBJPROP_RAY, false);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToString(pr1, _Digits) + " Date: " + TimeToString(Time[t1], TIME_DATE));
}
//+------------------------------------------------------------------+

//+HORIZLINE FUNCTION------------------------------------------------+
void objhoriz(string name, double pr1, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_HLINE, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetInteger(0, name, OBJPROP_STYLE, st);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToString(pr1, _Digits));
}
//+------------------------------------------------------------------+

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const datetime y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE, x);
	ObjectSetInteger(0, name, OBJPROP_TIME, y + Period());
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToString(x, _Digits));
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetText(name, label, FSize, "Arial", FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+TIMEDAY / HOUR / DAYOFWEEK MQL4-----------------------------------+
int TimeDay(datetime date)
{
	MqlDateTime tm;
	TimeToStruct(date, tm);
	return (tm.day);
}
int Hour()
{
	MqlDateTime tm;
	TimeCurrent(tm);
	return (tm.hour);
}
int Minute()
{
	MqlDateTime tm;
	TimeCurrent(tm);
	return (tm.min);
}
int DayOfWeek()
{
	MqlDateTime tm;
	TimeCurrent(tm);
	return (tm.day_of_week);
}
int TimeDayOfWeek(datetime date)
{
	MqlDateTime tm;
	TimeToStruct(date, tm);
	return (tm.day_of_week);
}
//+------------------------------------------------------------------+

//+CHECKTIME FUNCTION------------------------------------------------+
bool checktime(string starttime, string endtime)
{
	string dt = TimeToString(TimeCurrent());
	string DTstr = TimeToString(TimeCurrent(), TIME_DATE);
	string start = DTstr + " " + starttime;
	string end = DTstr + " " + endtime;
	StringToTime(start);
	StringToTime(end);
	StringToTime(dt);

	if (start < end)
		if (dt >= start && dt <= end)
			return (true);
	if (start >= end)
		if (dt >= start || dt < end)
			return (true);
	return (false);
}
//+------------------------------------------------------------------+

//+OBJECTSETTEXT FUNCTION--------------------------------------------+
void ObjectSetText(const string name,
				   const string text,
				   const int fontsize,
				   const string font,
				   const color col)
{
	if (ObjectFind(0, name) < 0)
		Print("Error: can't find label_object! code #", GetLastError());
	int object = (int)ObjectGetInteger(0, name, OBJPROP_TYPE);
	if (object != OBJ_LABEL && object != OBJ_TEXT)
		Print("Not a label or text object! code#", GetLastError());
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, fontsize);
	ObjectSetString(0, name, OBJPROP_FONT, font);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
}
//+------------------------------------------------------------------+