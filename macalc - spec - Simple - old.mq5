//+------------------------------------------------------------------+
//|                                           MA Calculator.mq5      |
//|                                           Copyright 2019, Sakis  |
//|                                                                  |
//+------------------------------------------------------------------+

// Version History
// ---------------
// v1.0 Made EMA calcs + build on top right corner
// v1.1 Translated to MQL5
// v1.2 Make visual presentation for 1-2-3 like WS indi
// v1.3 Changed to very simplified version of macalc
// v1.4 Added EMA50 for new system on D1
// v1.5 Changed 200-300 & 20H-20L to histograms
// v1.6 Added mouse capture for checking candles OHLC & added static crosshair

#property strict
#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"
#define Name MQLInfoString(MQL_PROGRAM_NAME)

#property indicator_buffers 18
#property indicator_plots 16

//PLOTS
//EMA300
#property indicator_type1 DRAW_LINE
#property indicator_color1 clrBlack
#property indicator_style1 STYLE_SOLID
#property indicator_width1 2
#property indicator_label1 "EMA300"
//EMA200
#property indicator_type2 DRAW_LINE
#property indicator_color2 clrRed
#property indicator_style2 STYLE_SOLID
#property indicator_width2 2
#property indicator_label2 "EMA200"
//LWMA20L
#property indicator_type3 DRAW_LINE
#property indicator_color3 clrWhiteSmoke
#property indicator_style3 STYLE_SOLID
#property indicator_width3 1
#property indicator_label3 "LWMA20L"
//LWMA20H
#property indicator_type4 DRAW_LINE
#property indicator_color4 clrWhiteSmoke
#property indicator_style4 STYLE_SOLID
#property indicator_width4 1
#property indicator_label4 "LWMA20H"
//LWMA20
#property indicator_type5 DRAW_LINE
#property indicator_color5 clrSienna
#property indicator_style5 STYLE_SOLID
#property indicator_width5 2
#property indicator_label5 "LWMA20"
//EMA250
#property indicator_type6 DRAW_LINE
#property indicator_color6 clrAqua
#property indicator_style6 STYLE_SOLID
#property indicator_width6 1
#property indicator_label6 "EMA250"
//EMA50-100
#property indicator_type7 DRAW_LINE
#property indicator_color7 clrBlue
#property indicator_style7 STYLE_SOLID
#property indicator_width7 2
#property indicator_label7 "EMA50 + EMA100 / 2"
//SMA666
#property indicator_type8 DRAW_LINE
#property indicator_color8 clrYellow
#property indicator_style8 STYLE_SOLID
#property indicator_width8 3
#property indicator_label8 "SMA666"
//EMA666
#property indicator_type9 DRAW_LINE
#property indicator_color9 clrGold
#property indicator_style9 STYLE_SOLID
#property indicator_width9 3
#property indicator_label9 "EMA666"
//VWAP
#property indicator_type10 DRAW_LINE
#property indicator_color10 clrLime
#property indicator_style10 STYLE_SOLID
#property indicator_width10 2
#property indicator_label10 "VWAP"
//VWAP SD POS1
#property indicator_type11 DRAW_LINE
#property indicator_color11 clrGray
#property indicator_style11 STYLE_DOT
#property indicator_label11 "VWAP SDPos1"
//VWAP SD NEG 1
#property indicator_type12 DRAW_LINE
#property indicator_color12 clrGray
#property indicator_style12 STYLE_DOT
#property indicator_label12 "VWAP SDNeg1"
//VWAP SD POS 2
#property indicator_type13 DRAW_LINE
#property indicator_color13 clrGray
#property indicator_style13 STYLE_DOT
#property indicator_label13 "VWAP SDPos2"
//VWAP SD NEG 2
#property indicator_type14 DRAW_LINE
#property indicator_color14 clrGray
#property indicator_style14 STYLE_DOT
#property indicator_label14 "VWAP SDNeg2"
//VWAP SD POS 3
#property indicator_type15 DRAW_LINE
#property indicator_color15 clrGray
#property indicator_style15 STYLE_DOT
#property indicator_label15 "VWAP SDPos3"
//VWAP SD NEG 3
#property indicator_type16 DRAW_LINE
#property indicator_color16 clrGray
#property indicator_style16 STYLE_DOT
#property indicator_label16 "VWAP SDNeg3"

//--- bars minimum for calculation
#define DATA_LIMIT 666

double MA200[], MA300[], MA250[], MA20LL[], MA20HH[], MA20WW[], MA50E[], MA100E[], MA75[], MA666S[], MA666E[];
double vwap[], SDP[], SDN[], SDP2[], SDN2[], SDP3[], SDN3[];
int M20WL, M20WH, M20W, M300, M200, M250, M50E, M100E, M75, M666S, M666E;

//TradeInfo
double point;
int PipAdjust, NrOfDigits;

bool timerwait = false;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
	IndicatorSetInteger(INDICATOR_DIGITS, _Digits);

	SetIndexBuffer(0, MA300, INDICATOR_DATA);
	SetIndexBuffer(1, MA200, INDICATOR_DATA);
	SetIndexBuffer(2, MA20LL, INDICATOR_DATA);
	SetIndexBuffer(3, MA20HH, INDICATOR_DATA);
	SetIndexBuffer(4, MA20WW, INDICATOR_DATA);
	SetIndexBuffer(5, MA250, INDICATOR_DATA);
	SetIndexBuffer(6, MA75, INDICATOR_DATA);
	SetIndexBuffer(7, MA666S, INDICATOR_DATA);
	SetIndexBuffer(8, MA666E, INDICATOR_DATA);
	SetIndexBuffer(9, vwap, INDICATOR_DATA);
	SetIndexBuffer(10, SDP, INDICATOR_DATA);
	SetIndexBuffer(11, SDN, INDICATOR_DATA);
	SetIndexBuffer(12, SDP2, INDICATOR_DATA);
	SetIndexBuffer(13, SDN2, INDICATOR_DATA);
	SetIndexBuffer(14, SDP3, INDICATOR_DATA);
	SetIndexBuffer(15, SDN3, INDICATOR_DATA);
	PlotIndexSetDouble(9, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(10, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(11, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(12, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(13, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(14, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	PlotIndexSetDouble(15, PLOT_EMPTY_VALUE, EMPTY_VALUE);
	SetIndexBuffer(16, MA50E, INDICATOR_CALCULATIONS);
	SetIndexBuffer(17, MA100E, INDICATOR_CALCULATIONS);

	M20WL = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_LWMA, PRICE_LOW);
	M20WH = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_LWMA, PRICE_HIGH);
	M20W = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_LWMA, PRICE_TYPICAL);
	M300 = iMA(Symbol(), PERIOD_CURRENT, 300, 0, MODE_EMA, PRICE_CLOSE);
	M200 = iMA(Symbol(), PERIOD_CURRENT, 200, 0, MODE_EMA, PRICE_CLOSE);
	M250 = iMA(Symbol(), PERIOD_CURRENT, 250, 0, MODE_EMA, PRICE_CLOSE);
	M50E = iMA(Symbol(), PERIOD_CURRENT, 50, 0, MODE_EMA, PRICE_CLOSE);
	M100E = iMA(Symbol(), PERIOD_CURRENT, 100, 0, MODE_EMA, PRICE_CLOSE);
	M666S = iMA(Symbol(), PERIOD_CURRENT, 666, 0, MODE_SMA, PRICE_CLOSE);
	M666E = iMA(Symbol(), PERIOD_CURRENT, 666, 0, MODE_EMA, PRICE_CLOSE);

	string obname;
	int p = 15;
	string tfs[7] = {"M1", "M5", "M15", "M30", "H1", "H4", "D1"};
	for (int i = 7; i >= 1; i--)
	{
		obname = Name + IntegerToString(i) + " TF";
		LabelMake(obname, 0, 100 + i * 2 * p, 80, tfs[i - 1], 7, clrBlack);
		ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
		obname = Name + IntegerToString(i) + " 250";
		LabelMake(obname, 0, 100 + i * 2 * p, 95, "", 7, clrBlack);
		obname = Name + IntegerToString(i) + " 666";
		LabelMake(obname, 0, 100 + i * 2 * p, 110, "", 7, clrBlack);
		obname = Name + IntegerToString(i) + " Signal";
		LabelMake(obname, 0, 100 + i * 2 * p, 125, "", 7, clrBlack);
	}

	obname = Name + " 250L";
	LabelMake(obname, 0, 90, 95, "250", 6, clrAqua);
	obname = Name + " 666L";
	LabelMake(obname, 0, 90, 110, "666", 6, clrYellow);
	obname = Name + " SignalL";
	LabelMake(obname, 0, 90, 125, "Sig", 6, clrBlack);

	obname = Name + " BuyPos";
	LabelMake(obname, 0, 90, 150, "", 8, clrBlue);
	obname = Name + " SellPos";
	LabelMake(obname, 0, 90, 160, "", 8, clrRed);
	obname = Name + " AvgPos";
	LabelMake(obname, 0, 90, 170, "", 8, clrWhite);

	obname = Name + " RealValue";
	LabelMake(obname, 0, 90, 190, "", 8, clrWhite);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "Relative currency basket value vs actual value");
	obname = Name + " 5MValue";
	LabelMake(obname, 0, 90, 205, "", 8, clrWhite);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "Cum 5m close from 03:00 to 22:00");
	obname = Name + " 5MValueN";
	LabelMake(obname, 0, 160, 205, "", 8, clrWhite);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "Cum 5m open from 03:00 to 22:00");
	obname = Name + " 5MEU";
	LabelMake(obname, 0, 90, 220, "", 8, clrWhite);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "Cum 5m close during EU hours 09:00 to 18:00");
	obname = Name + " 5MEUN";
	LabelMake(obname, 0, 170, 220, "", 8, clrWhite);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "Cum 5m open during EU hours 09:00 to 18:00");

	obname = Name + " Range-5m";
	LabelMake(obname, 0, 90, 235, "", 8, clrWhite);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "Daily range - 5m closing range");

	obname = Name + " LivePCTD";
	LabelMake(obname, 3, 160, 0, "", 10, clrWhite);
	obname = Name + " LivePCTW";
	LabelMake(obname, 3, 120, 12, "", 10, clrWhite);
	obname = Name + " LivePCTM";
	LabelMake(obname, 3, 80, 24, "", 10, clrWhite);
	obname = Name + " LivePrice";
	LabelMake(obname, 3, 160, 30, "", 10, clrWhite);

	NrOfDigits = _Digits;
	if (NrOfDigits == 5 || NrOfDigits == 3)
		PipAdjust = 10;
	else if (NrOfDigits == 4 || NrOfDigits == 2)
		PipAdjust = 1;
	point = _Point * PipAdjust;
	/*
	string obname;
	obname = Name + " Price"; LabelMake(obname, 3, 105, 280, " ", 12, clrBlack);
	obname = Name + " Percent"; LabelMake(obname, 3, 105, 300, " ", 9, clrBlack);
	obname = Name + " Spread"; LabelMake(obname, 3, 105, 320, " ", 9, clrBlack);
   */

	//---
	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-init function								 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || MQLInfoInteger(MQL_TESTER))
		if (!MQLInfoInteger(MQL_TESTER))
		{
			DeleteObjects();
		}
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	//ObjectsDeleteAll(0, Name);
	for (int i = ObjectsTotal(0, 0) - 1; i >= 0; i--)
	{
		string ObName = ObjectName(0, i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(0, ObName);
		}
	}
}
//+------------------------------------------------------------------+

//+ChartEvent function-----------------------------------------------+
void OnChartEvent(const int id,
				  const long &lparam,
				  const double &dparam,
				  const string &sparam)
{
	//---
	{ //Switch autoscroll
		if (id == CHARTEVENT_KEYDOWN)
		{
			if (lparam == StringGetCharacter("E", 0))
			{
				ChartSetInteger(0, CHART_AUTOSCROLL, false);
			}
			if (lparam == StringGetCharacter("Q", 0))
			{
				ChartSetInteger(0, CHART_AUTOSCROLL, true);
			}
		}
	}
	{
		if (id == CHARTEVENT_KEYDOWN)
		{
			if (lparam == StringGetCharacter("O", 0))
			{
				ObjectsDeleteAll(0, 0, -1);
				Comment("");
				ChartApplyTemplate(0, "simple28.tpl");
			}
			//if (lparam == StringGetChar("P", 0)) { ChartClose(0); }
		}
	}
	{ //Switch TF by hand
		if (id == CHARTEVENT_KEYDOWN)
		{
			switch (int(lparam))
			{
			case 97:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M1);
				break;
			case 98:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M5);
				break;
			case 99:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M15);
				break;
			case 100:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M30);
				break;
			case 101:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_H1);
				break;
			case 102:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_H4);
				break;
			case 103:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_D1);
				break;
			case 104:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_W1);
				break;
			case 105:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_MN1);
				break;
			}
		}
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	datetime expiry = D'2021.06.30 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("macalc-spec expired on " + TimeToString(expiry, TIME_DATE) + ", contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true)
	{
		//--- check for rates total
		if (rates_total < DATA_LIMIT)
			return (0); // not enough bars for calculation
				//--- not all data may be calculated
		int calculated = BarsCalculated(M666E);
		if (calculated < rates_total)
		{
			Print("Not all data of EXP 666 is calculated (", calculated, "bars ). Error", GetLastError());
			return (0);
		}
		//--- we can copy not all data
		int to_copy;
		if (prev_calculated > rates_total || prev_calculated < 0)
			to_copy = rates_total;
		else
		{
			to_copy = rates_total - prev_calculated;
			if (prev_calculated > 0)
				to_copy++;
		}
		//--- get LW20L buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(M20WL, 0, 0, to_copy, MA20LL) <= 0)
		{
			Print("Getting LWMA20L has failed! Error", GetLastError());
			return (0);
		}
		//--- get LW20H buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(M20WH, 0, 0, to_copy, MA20HH) <= 0)
		{
			Print("Getting LWMA20H has failed! Error", GetLastError());
			return (0);
		}
		//--- get LW20 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(M20W, 0, 0, to_copy, MA20WW) <= 0)
		{
			Print("Getting LWMA20 has failed! Error", GetLastError());
			return (0);
		}
		//--- get EXP300 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(M300, 0, 0, to_copy, MA300) <= 0)
		{
			Print("Getting EMA300 has failed! Error", GetLastError());
			return (0);
		}
		//--- get EXP200 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(M200, 0, 0, to_copy, MA200) <= 0)
		{
			Print("Getting EMA200 has failed! Error", GetLastError());
			return (0);
		}
		//--- get EXP250 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(M250, 0, 0, to_copy, MA250) <= 0)
		{
			Print("Getting EMA250 has failed! Error", GetLastError());
			return (0);
		}
		//--- get EXP50 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(M50E, 0, 0, to_copy, MA50E) <= 0)
		{
			Print("Getting EMA50 has failed! Error", GetLastError());
			return (0);
		}
		//--- get EXP100 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(M100E, 0, 0, to_copy, MA100E) <= 0)
		{
			Print("Getting EMA100 has failed! Error", GetLastError());
			return (0);
		}
		//--- get S666 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(M666S, 0, 0, to_copy, MA666S) <= 0)
		{
			Print("Getting SMA666 has failed! Error", GetLastError());
			return (0);
		}
		//--- get EXP666 buffer
		if (IsStopped())
			return (0); //Checking for stop flag
		if (CopyBuffer(M666E, 0, 0, to_copy, MA666E) <= 0)
		{
			Print("Getting EMA666 has failed! Error", GetLastError());
			return (0);
		}

		for (int i = to_copy - 1; i >= 0; i--)
		{
			MA75[i] = (MA50E[i] + MA100E[i]) / 2;
		}

		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, PERIOD_M1, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, PERIOD_M1, 0);
		}
		if (new_1m_check)
		{
			if (ChartPeriod(0) <= 16385)
			{
				uint start1 = GetTickCount();
				vwap();
				uint end1 = GetTickCount();
				Print(end1 - start1);
			}
			new_1m_check = false;
		}
      
      
		Bingo666();
		LivePrice();

		bool new_2m_check = false;
		static datetime start_2m_time = 0;
		if (start_2m_time < iTime(NULL, PERIOD_M1, 0))
		{
			new_2m_check = true;
			start_2m_time = iTime(NULL, PERIOD_M1, 0);
		}
		if (new_2m_check)
		{
			double realvalue = 0;
			double Pip_Value = 0;
			if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE == SYMBOL_CALC_MODE_FOREX)
				Pip_Value = (_Point * MathPow(10, MathMod(_Digits, 2)));
			else
				Pip_Value = 1;
			double chfvalue = (1 / SymbolInfoDouble("USDCHF", SYMBOL_BID) + 1 / SymbolInfoDouble("EURCHF", SYMBOL_BID) + 1 / SymbolInfoDouble("GBPCHF", SYMBOL_BID) + SymbolInfoDouble("CHFJPY", SYMBOL_BID) / 100 + 1 / SymbolInfoDouble("AUDCHF", SYMBOL_BID) + 1 / SymbolInfoDouble("NZDCHF", SYMBOL_BID) + 1 / SymbolInfoDouble("CADCHF", SYMBOL_BID));
			double eurvalue = (SymbolInfoDouble("EURUSD", SYMBOL_BID) + SymbolInfoDouble("EURCHF", SYMBOL_BID) + SymbolInfoDouble("EURGBP", SYMBOL_BID) + SymbolInfoDouble("EURJPY", SYMBOL_BID) / 100 + SymbolInfoDouble("EURAUD", SYMBOL_BID) + SymbolInfoDouble("EURNZD", SYMBOL_BID) + SymbolInfoDouble("EURCAD", SYMBOL_BID));
			double gbpvalue = (1 / SymbolInfoDouble("EURGBP", SYMBOL_BID) + SymbolInfoDouble("GBPCHF", SYMBOL_BID) + SymbolInfoDouble("GBPUSD", SYMBOL_BID) + SymbolInfoDouble("GBPJPY", SYMBOL_BID) / 100 + SymbolInfoDouble("GBPAUD", SYMBOL_BID) + SymbolInfoDouble("GBPNZD", SYMBOL_BID) + SymbolInfoDouble("GBPCAD", SYMBOL_BID));
			double jpyvalue = (1 / SymbolInfoDouble("EURJPY", SYMBOL_BID) + 1 / SymbolInfoDouble("CHFJPY", SYMBOL_BID) + 1 / SymbolInfoDouble("GBPJPY", SYMBOL_BID) + 1 / SymbolInfoDouble("USDJPY", SYMBOL_BID) + 1 / SymbolInfoDouble("AUDJPY", SYMBOL_BID) + 1 / SymbolInfoDouble("NZDJPY", SYMBOL_BID) + 1 / SymbolInfoDouble("CADJPY", SYMBOL_BID));
			double audvalue = (1 / SymbolInfoDouble("EURAUD", SYMBOL_BID) + SymbolInfoDouble("AUDCHF", SYMBOL_BID) + 1 / SymbolInfoDouble("GBPAUD", SYMBOL_BID) + SymbolInfoDouble("AUDJPY", SYMBOL_BID) / 100 + SymbolInfoDouble("AUDUSD", SYMBOL_BID) + SymbolInfoDouble("AUDNZD", SYMBOL_BID) + SymbolInfoDouble("AUDCAD", SYMBOL_BID));
			double nzdvalue = (1 / SymbolInfoDouble("EURNZD", SYMBOL_BID) + SymbolInfoDouble("NZDCHF", SYMBOL_BID) + 1 / SymbolInfoDouble("GBPNZD", SYMBOL_BID) + SymbolInfoDouble("NZDJPY", SYMBOL_BID) / 100 + SymbolInfoDouble("NZDUSD", SYMBOL_BID) + 1 / SymbolInfoDouble("AUDNZD", SYMBOL_BID) + SymbolInfoDouble("NZDCAD", SYMBOL_BID));
			double cadvalue = (1 / SymbolInfoDouble("EURCAD", SYMBOL_BID) + SymbolInfoDouble("CADCHF", SYMBOL_BID) + 1 / SymbolInfoDouble("GBPCAD", SYMBOL_BID) + SymbolInfoDouble("CADJPY", SYMBOL_BID) / 100 + 1 / SymbolInfoDouble("USDCAD", SYMBOL_BID) + 1 / SymbolInfoDouble("AUDCAD", SYMBOL_BID) + 1 / SymbolInfoDouble("NZDCAD", SYMBOL_BID));
			double usdvalue = (1 / SymbolInfoDouble("EURUSD", SYMBOL_BID) + 1 / SymbolInfoDouble("GBPUSD", SYMBOL_BID) + SymbolInfoDouble("USDCHF", SYMBOL_BID) + SymbolInfoDouble("USDJPY", SYMBOL_BID) / 100 + 1 / SymbolInfoDouble("AUDUSD", SYMBOL_BID) + 1 / SymbolInfoDouble("NZDUSD", SYMBOL_BID) + SymbolInfoDouble("USDCAD", SYMBOL_BID));
			if (_Symbol == "USDCHF")
				realvalue = usdvalue / chfvalue;
			else if (_Symbol == "EURUSD")
				realvalue = eurvalue / usdvalue;
			else if (_Symbol == "GBPUSD")
				realvalue = gbpvalue / usdvalue;
			else if (_Symbol == "USDJPY")
				realvalue = usdvalue / jpyvalue;
			else if (_Symbol == "AUDUSD")
				realvalue = audvalue / usdvalue;
			else if (_Symbol == "NZDUSD")
				realvalue = nzdvalue / usdvalue;
			else if (_Symbol == "USDCAD")
				realvalue = usdvalue / cadvalue;
			else if (_Symbol == "EURGBP")
				realvalue = eurvalue / gbpvalue;
			else if (_Symbol == "GBPJPY")
				realvalue = gbpvalue / jpyvalue;
			if (realvalue != 0)
				ObjectSetString(0, Name + " RealValue", OBJPROP_TEXT, "RV: " + DoubleToString(realvalue, _Digits) + "   " + DoubleToString((realvalue - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / Pip_Value, 1));
			else
				ObjectSetString(0, Name + " RealValue", OBJPROP_TEXT, "");
			if (realvalue > SymbolInfoDouble(_Symbol, SYMBOL_BID))
				ObjectSetInteger(0, Name + " RealValue", OBJPROP_COLOR, clrBlue);
			else
				ObjectSetInteger(0, Name + " RealValue", OBJPROP_COLOR, clrRed);

			ObjectSetString(0, Name + " 5MValue", OBJPROP_TEXT, "5M O: " + DoubleToString(blue(1), 2));
			if (blue(1) > 0)
				ObjectSetInteger(0, Name + " 5MValue", OBJPROP_COLOR, clrBlue);
			else
				ObjectSetInteger(0, Name + " 5MValue", OBJPROP_COLOR, clrRed);

			ObjectSetString(0, Name + " 5MValueN", OBJPROP_TEXT, "N: " + DoubleToString(blue(6), 2));
			if (blue(6) > 0)
				ObjectSetInteger(0, Name + " 5MValueN", OBJPROP_COLOR, clrBlue);
			else
				ObjectSetInteger(0, Name + " 5MValueN", OBJPROP_COLOR, clrRed);

			ObjectSetString(0, Name + " 5MEU", OBJPROP_TEXT, "5MEU O: " + DoubleToString(blue(2), 2));
			if (blue(2) > 0)
				ObjectSetInteger(0, Name + " 5MEU", OBJPROP_COLOR, clrBlue);
			else
				ObjectSetInteger(0, Name + " 5MEU", OBJPROP_COLOR, clrRed);

			ObjectSetString(0, Name + " 5MEUN", OBJPROP_TEXT, " N: " + DoubleToString(blue(7), 2));
			if (blue(7) > 0)
				ObjectSetInteger(0, Name + " 5MEUN", OBJPROP_COLOR, clrBlue);
			else
				ObjectSetInteger(0, Name + " 5MEUN", OBJPROP_COLOR, clrRed);

			//ObjectSetText(Name + " 5mp", DoubleToString(blue(4), 2));
			//ObjectSetText(Name + " 5mn", " / " + DoubleToString(blue(5), 2));
			ObjectSetString(0, Name + " Range-5m", OBJPROP_TEXT, DoubleToString((iHigh(_Symbol, PERIOD_D1, 0) - iLow(_Symbol, PERIOD_D1, 0)) / Pip_Value, 2) + " / " + DoubleToString(blue(3), 2) + " / " + DoubleToString(blue(8), 2));
			new_2m_check = false;
		}

		/*
		if (trades_on_symbol(_Symbol))
			TradeInfo();
		else if (!trades_on_symbol(_Symbol) && ObjectFind(0, Name + "Average_Price_Line_" + _Symbol) == 0)
		{
			ObjectDelete(0, Name + "Average_Price_Line_" + _Symbol);
			ObjectDelete(0, Name + " BuyPos");
			ObjectDelete(0, Name + " SellPos");
			ObjectDelete(0, Name + " AvgPos");
		}
		*/

		bool new_ctf_check = false;
		static datetime start_ctf_time = 0;
		if (start_ctf_time < iTime(_Symbol, PERIOD_M30, 0))
		{
			new_ctf_check = true;
			start_ctf_time = iTime(_Symbol, PERIOD_M30, 0);
		}
		if (new_ctf_check)
		{
			ObjectsDeleteAll(0, Name + " y");
			ydayweek();
			new_ctf_check = false;
		}
	}
	//--- return value of prev_calculated for next call
	return (rates_total);
}
//+------------------------------------------------------------------+

void LivePrice(){
		double CD1[];
		ArrayResize(CD1, 2);
		ArraySetAsSeries(CD1, true);
		if (!CopyClose(_Symbol, PERIOD_D1, 0, 2, CD1))
			Print("Failed to copy daily closes, ", GetLastError());
		double CW1[];
		ArrayResize(CW1, 2);
		ArraySetAsSeries(CW1, true);
		if (!CopyClose(_Symbol, PERIOD_W1, 0, 2, CW1))
			Print("Failed to copy weekly closes, ", GetLastError());
		double CMN1[];
		ArrayResize(CMN1, 2);
		ArraySetAsSeries(CMN1, true);
		if (!CopyClose(_Symbol, PERIOD_MN1, 0, 2, CMN1))
			Print("Failed to copy montly closes, ", GetLastError());

		if ((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CD1[1]) >= 0)
			ObjectSetText(Name + " LivePCTD", "D: +" + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CD1[1]) / CD1[1] * 100, 2) + "%", 10, "Arial Black", clrBlack);
		else
			ObjectSetText(Name + " LivePCTD", "D: " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CD1[1]) / CD1[1] * 100, 2) + "%", 10, "Arial Black", clrBlack);
		if ((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CD1[1]) / CD1[1] * 100 > 0.25)
			ObjectSetInteger(0, Name + " LivePCTD", OBJPROP_COLOR, clrBlue);
		else if ((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CD1[1]) / CD1[1] * 100 < -0.25)
			ObjectSetInteger(0, Name + " LivePCTD", OBJPROP_COLOR, clrRed);

		if ((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CW1[1]) >= 0)
			ObjectSetText(Name + " LivePCTW", "W: +" + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CW1[1]) / CW1[1] * 100, 2) + "%", 10, "Arial Black", clrBlack);
		else
			ObjectSetText(Name + " LivePCTW", "W: " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CW1[1]) / CW1[1] * 100, 2) + "%", 10, "Arial Black", clrBlack);
		if ((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CW1[1]) / CW1[1] * 100 > 0.25)
			ObjectSetInteger(0, Name + " LivePCTW", OBJPROP_COLOR, clrBlue);
		else if ((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CW1[1]) / CW1[1] * 100 < -0.25)
			ObjectSetInteger(0, Name + " LivePCTW", OBJPROP_COLOR, clrRed);

		if ((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CMN1[1]) >= 0)
			ObjectSetText(Name + " LivePCTM", "M: +" + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CMN1[1]) / CMN1[1] * 100, 2) + "%", 10, "Arial Black", clrBlack);
		else
			ObjectSetText(Name + " LivePCTM", "M: " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CMN1[1]) / CMN1[1] * 100, 2) + "%", 10, "Arial Black", clrBlack);
		if ((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CMN1[1]) / CMN1[1] * 100 > 0.25)
			ObjectSetInteger(0, Name + " LivePCTM", OBJPROP_COLOR, clrBlue);
		else if ((SymbolInfoDouble(_Symbol, SYMBOL_BID) - CMN1[1]) / CMN1[1] * 100 < -0.25)
			ObjectSetInteger(0, Name + " LivePCTM", OBJPROP_COLOR, clrRed);

		//ObjectSetString(0, Name + " LivePCT", OBJPROP_TOOLTIP, "D: " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - iClose(_Symbol, PERIOD_D1, 1)) / iClose(_Symbol, PERIOD_D1, 1) * 100, 2) + " / W: " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - iClose(_Symbol, PERIOD_W1, 1)) / iClose(_Symbol, PERIOD_W1, 1) * 100, 2) + " / M: " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_BID) - iClose(_Symbol, PERIOD_MN1, 1)) / iClose(_Symbol, PERIOD_MN1, 1) * 100, 2));
		ObjectSetText(Name + " LivePrice", DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits), 24, "Arial Black", clrBlack);
}

//+VWAP--------------------------------------------------------------+
void vwap()
{
	long tickvol[];
	//ArrayResize(tickvol, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false) + 1);
	ArraySetAsSeries(tickvol, true);
	CopyTickVolume(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false) + 1, tickvol);
	double close[];
	//ArrayResize(close, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false) + 1);
	ArraySetAsSeries(close, true);
	CopyClose(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false) + 1, close);
	double high[];
	//ArrayResize(high, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false) + 1);
	ArraySetAsSeries(high, true);
	CopyHigh(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false) + 1, high);
	double low[];
	//ArrayResize(low, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false) + 1);
	ArraySetAsSeries(low, true);
	CopyLow(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false) + 1, low);
	datetime time[];
	//ArrayResize(time, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false) + 1);
	ArraySetAsSeries(time, true);
	CopyTime(_Symbol, PERIOD_CURRENT, 0, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false) + 1, time);

	ArraySetAsSeries(vwap, true);
	ArrayInitialize(vwap, EMPTY_VALUE);
	ArraySetAsSeries(SDP, true);
	ArrayInitialize(SDP, EMPTY_VALUE);
	ArraySetAsSeries(SDN, true);
	ArrayInitialize(SDN, EMPTY_VALUE);
	ArraySetAsSeries(SDP2, true);
	ArrayInitialize(SDP2, EMPTY_VALUE);
	ArraySetAsSeries(SDN2, true);
	ArrayInitialize(SDN2, EMPTY_VALUE);
	ArraySetAsSeries(SDP3, true);
	ArrayInitialize(SDP3, EMPTY_VALUE);
	ArraySetAsSeries(SDN3, true);
	ArrayInitialize(SDN3, EMPTY_VALUE);

	datetime newsession = 0;
	double totvol = 0;
	double pervol = 0;
	double SD = 0;

	int daymin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false);
	vwap[daymin] = close[daymin];
	for (int x = daymin; x >= 0; x--)
	{
		totvol += (double)tickvol[x];
		pervol += (double)tickvol[x] * (high[x] + close[x] + low[x]) / 3;
		if (TimeDay(time[x]) != TimeDay(newsession))
		{
			newsession = time[x];
			totvol = 0;
			pervol = 0;
			//vwap[x] = close[x];
		}
		if (totvol != 0)
		{
			vwap[x] = pervol / totvol;
		}

		if (ChartPeriod() <= 30)
		{
			SD = 0;
			int baymin = 0;
			if (ChartPeriod() == 1)
				baymin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false);
			else
				baymin = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 2), false);
			for (int k = baymin; k >= x; k--)
			{
				double avg = (close[k] + high[k] + low[k]) / 3;
				double diff = avg - vwap[x];
				if (TimeDay(time[k]) != TimeDay(newsession))
				{
					newsession = time[k];
					SD = 0;
				}
				if (totvol != 0)
					SD += (tickvol[k] / totvol) * (diff * diff);
			}

			SD = MathSqrt(SD);
			SDP[x] = vwap[x] + SD;
			SDN[x] = vwap[x] - SD;
			SDP2[x] = SDP[x] + SD;
			SDN2[x] = SDN[x] - SD;
			SDP3[x] = SDP2[x] + SD;
			SDN3[x] = SDN2[x] - SD;
		}
	}
}
//+------------------------------------------------------------------+

//+YDAY HIGH/LOW/OPEN/CLOSE------------------------------------------+
void ydayweek()
{
	double o, h, l, c, oo, hh, ll, cc, ooo, hhh, lll, ccc;
	o = iOpen(_Symbol, PERIOD_D1, 1);
	oo = iOpen(_Symbol, PERIOD_W1, 1);
	ooo = iOpen(_Symbol, PERIOD_MN1, 1);
	h = iHigh(_Symbol, PERIOD_D1, 1);
	hh = iHigh(_Symbol, PERIOD_W1, 1);
	hhh = iHigh(_Symbol, PERIOD_MN1, 1);
	l = iLow(_Symbol, PERIOD_D1, 1);
	ll = iLow(_Symbol, PERIOD_W1, 1);
	lll = iLow(_Symbol, PERIOD_MN1, 1);
	c = iClose(_Symbol, PERIOD_D1, 1);
	cc = iClose(_Symbol, PERIOD_W1, 1);
	ccc = iClose(_Symbol, PERIOD_MN1, 1);

	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	int x = 24 + Hour();
	string obname;

	int od = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 1), false);
	int cd = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false);
	int ow = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 1), false);
	int cw = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_W1, 0), false);
	int om = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 1), false);
	int cm = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_MN1, 0), false);

	if (ChartPeriod() >= 1 && ChartPeriod() <= 240)
	{
		if (ChartPeriod() < 70)
		{
			obname = Name + " ydayO";
			objtrend2(obname, o, o, od, 0, 20 * Period() * 30, 1, 0, clrBlack, "Yesterday Open");
		}
		{
			obname = Name + " ydayO F1";
			Texter(obname, o, Time[0] + 20 * Period() * 30, "D-1 O", clrBlack);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		if (DayOfWeek() != 1)
		{
			if (ChartPeriod() < 70)
			{
				obname = Name + " ydayC";
				objtrend2(obname, c, c, cd, 0, 20 * Period() * 30, 1, 0, clrBlack, "Yesterday Close");
			}
			{
				obname = Name + " ydayC F1";
				Texter(obname, c, Time[0] + 20 * Period() * 30, "D-1 C", clrBlack);
				ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
				ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
				ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
			}
		}
		if (ChartPeriod() < 70)
		{
			obname = Name + " ydayH";
			objtrend2(obname, h, h, od, 0, 20 * Period() * 30, 1, 0, clrWhite, "Yesterday High");
		}
		{
			obname = Name + " ydayH F1";
			Texter(obname, h, Time[0] + 20 * Period() * 30, "D-1 H", clrWhite);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		if (ChartPeriod() < 70)
		{
			obname = Name + " ydayL";
			objtrend2(obname, l, l, od, 0, 20 * Period() * 30, 1, 0, clrWhite, "Yesterday Low");
		}
		{
			obname = Name + " ydayL F1";
			Texter(obname, l, Time[0] + 20 * Period() * 30, "D-1 L", clrWhite);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}

		//obname = Name + " ymonthO";
		//objtrend2(obname, ooo, ooo, om, 0, 27 * Period() * 60, 2, 0, clrRed, "Last Month Open");
		//{ obname = Name + " ymonthO F1"; Texter(obname, ooo, Time[0] + 27 * Period() * 60, "M-1 O", clrRed); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " ymonthC";
		objtrend2(obname, ccc, ccc, cm, 0, 27 * Period() * 30, 2, 0, clrRed, "Last Month Close");
		{
			obname = Name + " ymonthC F1";
			Texter(obname, ccc, Time[0] + 27 * Period() * 30, "M-1 C", clrRed);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		obname = Name + " ymonthH";
		objtrend2(obname, hhh, hhh, om, 0, 27 * Period() * 30, 2, 0, clrDarkOrange, "Last Month High");
		{
			obname = Name + " ymonthH F1";
			Texter(obname, hhh, Time[0] + 27 * Period() * 30, "M-1 H", clrDarkOrange);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		obname = Name + " ymonthL";
		objtrend2(obname, lll, lll, om, 0, 27 * Period() * 30, 2, 0, clrDarkOrange, "Last Month Low");
		{
			obname = Name + " ymonthL F1";
			Texter(obname, lll, Time[0] + 27 * Period() * 30, "M-1 L", clrDarkOrange);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}

		//obname = Name + " yweekO";
		//objtrend2(obname, oo, oo, ow, 0, 27 * Period() * 60, 2, 0, clrDarkGoldenrod, "Last Week Open");
		//{ obname = Name + " yweekO F1"; Texter(obname, oo, Time[0] + 27 * Period() * 60, "W-1 O", clrDarkGoldenrod); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " yweekC";
		objtrend2(obname, cc, cc, cw, 0, 27 * Period() * 30, 2, 0, clrDarkGoldenrod, "Last Week Close");
		{
			obname = Name + " yweekC F1";
			Texter(obname, cc, Time[0] + 27 * Period() * 30, "W-1 C", clrDarkGoldenrod);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		obname = Name + " yweekH";
		objtrend2(obname, hh, hh, ow, 0, 27 * Period() * 30, 2, 0, clrYellow, "Last Week High");
		{
			obname = Name + " yweekH F1";
			Texter(obname, hh, Time[0] + 27 * Period() * 30, "W-1 H", clrYellow);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}
		obname = Name + " yweekL";
		objtrend2(obname, ll, ll, ow, 0, 27 * Period() * 30, 2, 0, clrYellow, "Last Week Low");
		{
			obname = Name + " yweekL F1";
			Texter(obname, ll, Time[0] + 27 * Period() * 30, "W-1 L", clrYellow);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
			ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		}

		/*
		obname = Name + " HOM";
		objtrend2(obname, iHigh(_Symbol, PERIOD_MN1, 0), iHigh(_Symbol, PERIOD_MN1, 0), cm, 0, 34 * Period() * 30, 1, 2, clrAqua, "Month High");
		{ obname = Name + " HOM F1"; Texter(obname, iHigh(_Symbol, PERIOD_MN1, 0), Time[0] + 34 * Period() * 30, "M H", clrAqua); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " LOM";
		objtrend2(obname, iLow(_Symbol, PERIOD_MN1, 0), iLow(_Symbol, PERIOD_MN1, 0), cm, 0, 34 * Period() * 30, 1, 2, clrAqua, "Month Low");
		{ obname = Name + " LOM F1"; Texter(obname, iLow(_Symbol, PERIOD_MN1, 0), Time[0] + 34 * Period() * 30, "M L", clrAqua); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " HOW";
		objtrend2(obname, iHigh(_Symbol, PERIOD_W1, 0), iHigh(_Symbol, PERIOD_W1, 0), cw, 0, 34 * Period() * 30, 1, 2, clrAqua, "Week High");
		{ obname = Name + " HOW F1"; Texter(obname, iHigh(_Symbol, PERIOD_W1, 0), Time[0] + 34 * Period() * 30, "W H", clrAqua); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " LOW";
		objtrend2(obname, iLow(_Symbol, PERIOD_W1, 0), iLow(_Symbol, PERIOD_W1, 0), cw, 0, 34 * Period() * 30, 1, 2, clrAqua, "Week Low");
		{ obname = Name + " LOW F1"; Texter(obname, iLow(_Symbol, PERIOD_W1, 0), Time[0] + 34 * Period() * 30, "W L", clrAqua); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " HOD";
		objtrend2(obname, iHigh(_Symbol, PERIOD_D1, 0), iHigh(_Symbol, PERIOD_D1, 0), cd, 0, 34 * Period() * 30, 1, 2, clrAqua, "Day High");
		{ obname = Name + " HOD F1"; Texter(obname, iHigh(_Symbol, PERIOD_D1, 0), Time[0] + 34 * Period() * 30, "D H", clrAqua); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		obname = Name + " LOD";
		objtrend2(obname, iLow(_Symbol, PERIOD_D1, 0), iLow(_Symbol, PERIOD_D1, 0), cd, 0, 34 * Period() * 30, 1, 2, clrAqua, "Day Low");
		{ obname = Name + " LOD F1"; Texter(obname, iLow(_Symbol, PERIOD_D1, 0), Time[0] + 34 * Period() * 30, "D L", clrAqua); ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7); ObjectSetString(0, obname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT); }
		*/
	}
}
//+------------------------------------------------------------------+

//+666 Distances-----------------------------------------------------+
void Bingo666()
{
   int a11 = iMA(_Symbol, PERIOD_M1, 666, 0, MODE_SMA, PRICE_CLOSE);
	int a21 = iMA(_Symbol, PERIOD_M5, 666, 0, MODE_SMA, PRICE_CLOSE);
	int a31 = iMA(_Symbol, PERIOD_M15, 666, 0, MODE_SMA, PRICE_CLOSE);
	int a41 = iMA(_Symbol, PERIOD_M30, 666, 0, MODE_SMA, PRICE_CLOSE);
	int a51 = iMA(_Symbol, PERIOD_H1, 666, 0, MODE_SMA, PRICE_CLOSE);
	int a61 = iMA(_Symbol, PERIOD_H4, 666, 0, MODE_SMA, PRICE_CLOSE);
	int a71 = iMA(_Symbol, PERIOD_D1, 666, 0, MODE_SMA, PRICE_CLOSE);

	int b11 = iMA(_Symbol, PERIOD_M1, 250, 0, MODE_EMA, PRICE_CLOSE);
	int b21 = iMA(_Symbol, PERIOD_M5, 250, 0, MODE_EMA, PRICE_CLOSE);
	int b31 = iMA(_Symbol, PERIOD_M15, 250, 0, MODE_EMA, PRICE_CLOSE);
	int b41 = iMA(_Symbol, PERIOD_M30, 250, 0, MODE_EMA, PRICE_CLOSE);
	int b51 = iMA(_Symbol, PERIOD_H1, 250, 0, MODE_EMA, PRICE_CLOSE);
	int b61 = iMA(_Symbol, PERIOD_H4, 250, 0, MODE_EMA, PRICE_CLOSE);
	int b71 = iMA(_Symbol, PERIOD_D1, 250, 0, MODE_EMA, PRICE_CLOSE);

	double a1 [1], a2 [1], a3 [1], a4 [1], a5 [1], a6 [1], a7 [1];
	double b1 [1], b2 [1], b3 [1], b4 [1], b5 [1], b6 [1], b7 [1];

	//--- get EMA666 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(a11, 0, 0, 1, a1) <= 0)
	{
		Print("Getting EMA666 M1 has failed! Error", GetLastError());
		return;
	}
	//--- get EMA666 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(a21, 0, 0, 1, a2) <= 0)
	{
		Print("Getting EMA666 M5 has failed! Error", GetLastError());
		return;
	}
	//--- get EMA666 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(a31, 0, 0, 1, a3) <= 0)
	{
		Print("Getting EMA666 M15 has failed! Error", GetLastError());
		return;
	}
	//--- get EMA666 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(a41, 0, 0, 1, a4) <= 0)
	{
		Print("Getting EMA666 M30 has failed! Error", GetLastError());
		return;
	}
	//--- get EMA666 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(a51, 0, 0, 1, a5) <= 0)
	{
		Print("Getting EMA666 H1 has failed! Error", GetLastError());
		return;
	}
	//--- get EMA666 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(a61, 0, 0, 1, a6) <= 0)
	{
		Print("Getting EMA666 H4 has failed! Error", GetLastError());
		return;
	}
	//--- get EMA666 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(a71, 0, 0, 1, a7) <= 0)
	{
		Print("Getting EMA666 D1 has failed! Error", GetLastError());
		return;
	}

	//--- get EMA250 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(b11, 0, 0, 1, b1) <= 0)
	{
		Print("Getting EMA250 M1 has failed! Error", GetLastError());
		return;
	}
	//--- get EMA250 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(b21, 0, 0, 1, b2) <= 0)
	{
		Print("Getting EMA250 M5 has failed! Error", GetLastError());
		return;
	}
	//--- get EMA250 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(b31, 0, 0, 1, b3) <= 0)
	{
		Print("Getting EMA250 M15 has failed! Error", GetLastError());
		return;
	}
	//--- get EMA250 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(b41, 0, 0, 1, b4) <= 0)
	{
		Print("Getting EMA250 M30 has failed! Error", GetLastError());
		return;
	}
	//--- get EMA250 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(b51, 0, 0, 1, b5) <= 0)
	{
		Print("Getting EMA250 H1 has failed! Error", GetLastError());
		return;
	}
	//--- get EMA250 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(b61, 0, 0, 1, b6) <= 0)
	{
		Print("Getting EMA250 H4 has failed! Error", GetLastError());
		return;
	}
	//--- get EMA250 buffer
	if (IsStopped()) return; //Checking for stop flag
	if (CopyBuffer(b71, 0, 0, 1, b7) <= 0)
	{
		Print("Getting EMA250 D1 has failed! Error", GetLastError());
		return;
	}
	
	/*
	IndicatorRelease(a11);
	IndicatorRelease(a21);
	IndicatorRelease(a31);
	IndicatorRelease(a41);
	IndicatorRelease(a51);
	IndicatorRelease(a61);
	IndicatorRelease(a71);
	IndicatorRelease(b11);
	IndicatorRelease(b21);
	IndicatorRelease(b31);
	IndicatorRelease(b41);
	IndicatorRelease(b51);
	IndicatorRelease(b61);
	IndicatorRelease(b71);
   */
   
	double p1 = 0, p2 = 0, p3 = 0, p4 = 0, p5 = 0, p6 = 0, p7 = 0;
	double s1 = 0, s2 = 0, s3 = 0, s4 = 0, s5 = 0, s6 = 0, s7 = 0;

	p1 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - a1[0];
	p2 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - a2[0];
	p3 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - a3[0];
	p4 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - a4[0];
	p5 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - a5[0];
	p6 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - a6[0];
	p7 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - a7[0];

	s1 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - b1[0];
	s2 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - b2[0];
	s3 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - b3[0];
	s4 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - b4[0];
	s5 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - b5[0];
	s6 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - b6[0];
	s7 = SymbolInfoDouble(_Symbol, SYMBOL_BID) - b7[0];

	ObjectSetInteger(0, Name + IntegerToString(5) + " L", OBJPROP_COLOR, clrYellow);
	ObjectSetString(0, Name + IntegerToString(5) + " L", OBJPROP_FONT, "Arial Black");

	double Pip_Value = 0;
	if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE)
		Pip_Value = (_Point * MathPow(10, MathMod(_Digits, 2)));
	else
		Pip_Value = 1;

	//666 SMA
	if ((iClose(_Symbol, PERIOD_M1, 0) > a1[0]))
		ObjectSetText(Name + IntegerToString(1) + " 666", DoubleToString(p1 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(1) + " 666", DoubleToString(p1 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(1) + " 666", OBJPROP_TOOLTIP, DoubleToString(a1[0], _Digits));
	if ((iClose(_Symbol, PERIOD_M5, 0) > a2[0]))
		ObjectSetText(Name + IntegerToString(2) + " 666", DoubleToString(p2 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(2) + " 666", DoubleToString(p2 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(2) + " 666", OBJPROP_TOOLTIP, DoubleToString(a2[0], _Digits));
	if ((iClose(_Symbol, PERIOD_M15, 0) > a3[0]))
		ObjectSetText(Name + IntegerToString(3) + " 666", DoubleToString(p3 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(3) + " 666", DoubleToString(p3 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(3) + " 666", OBJPROP_TOOLTIP, DoubleToString(a3[0], _Digits));
	if ((iClose(_Symbol, PERIOD_M30, 0) > a4[0]))
		ObjectSetText(Name + IntegerToString(4) + " 666", DoubleToString(p4 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(4) + " 666", DoubleToString(p4 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(4) + " 666", OBJPROP_TOOLTIP, DoubleToString(a4[0], _Digits));
	if ((iClose(_Symbol, PERIOD_H1, 0) > a5[0]))
		ObjectSetText(Name + IntegerToString(5) + " 666", DoubleToString(p5 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(5) + " 666", DoubleToString(p5 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(5) + " 666", OBJPROP_TOOLTIP, DoubleToString(a5[0], _Digits));
	if ((iClose(_Symbol, PERIOD_H4, 0) > a6[0]))
		ObjectSetText(Name + IntegerToString(6) + " 666", DoubleToString(p6 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(6) + " 666", DoubleToString(p6 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(6) + " 666", OBJPROP_TOOLTIP, DoubleToString(a6[0], _Digits));
	if ((iClose(_Symbol, PERIOD_D1, 0) > a7[0]))
		ObjectSetText(Name + IntegerToString(7) + " 666", DoubleToString(p7 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(7) + " 666", DoubleToString(p7 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(7) + " 666", OBJPROP_TOOLTIP, DoubleToString(a7[0], _Digits));

	//250 EMA
	if ((iClose(_Symbol, PERIOD_M1, 0) > b1[0]))
		ObjectSetText(Name + IntegerToString(1) + " 250", DoubleToString(s1 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(1) + " 250", DoubleToString(s1 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(1) + " 250", OBJPROP_TOOLTIP, DoubleToString(b1[0], _Digits));
	if ((iClose(_Symbol, PERIOD_M5, 0) > b2[0]))
		ObjectSetText(Name + IntegerToString(2) + " 250", DoubleToString(s2 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(2) + " 250", DoubleToString(s2 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(2) + " 250", OBJPROP_TOOLTIP, DoubleToString(b2[0], _Digits));
	if ((iClose(_Symbol, PERIOD_M15, 0) > b3[0]))
		ObjectSetText(Name + IntegerToString(3) + " 250", DoubleToString(s3 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(3) + " 250", DoubleToString(s3 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(3) + " 250", OBJPROP_TOOLTIP, DoubleToString(b3[0], _Digits));
	if ((iClose(_Symbol, PERIOD_M30, 0) > b4[0]))
		ObjectSetText(Name + IntegerToString(4) + " 250", DoubleToString(s4 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(4) + " 250", DoubleToString(s4 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(4) + " 250", OBJPROP_TOOLTIP, DoubleToString(b4[0], _Digits));
	if ((iClose(_Symbol, PERIOD_H1, 0) > b5[0]))
		ObjectSetText(Name + IntegerToString(5) + " 250", DoubleToString(s5 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(5) + " 250", DoubleToString(s5 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(5) + " 250", OBJPROP_TOOLTIP, DoubleToString(b5[0], _Digits));
	if ((iClose(_Symbol, PERIOD_H4, 0) > b6[0]))
		ObjectSetText(Name + IntegerToString(6) + " 250", DoubleToString(s6 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(6) + " 250", DoubleToString(s6 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(6) + " 250", OBJPROP_TOOLTIP, DoubleToString(b6[0], _Digits));
	if ((iClose(_Symbol, PERIOD_D1, 0) > b7[0]))
		ObjectSetText(Name + IntegerToString(7) + " 250", DoubleToString(s7 / Pip_Value, 1), 6, "Arial", clrBlue);
	else
		ObjectSetText(Name + IntegerToString(7) + " 250", DoubleToString(s7 / Pip_Value, 1), 6, "Arial", clrRed);
	ObjectSetString(0, Name + IntegerToString(7) + " 250", OBJPROP_TOOLTIP, DoubleToString(b7[0], _Digits));

	//M1
	if (iClose(_Symbol, PERIOD_M1, 0) > a1[0] && iClose(_Symbol, PERIOD_M1, 0) > b1[0])
		ObjectSetText(Name + IntegerToString(1) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_M1, 0) < a1[0] && iClose(_Symbol, PERIOD_M1, 0) < b1[0])
		ObjectSetText(Name + IntegerToString(1) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
	else if (iClose(_Symbol, PERIOD_M1, 0) > a1[0] && iClose(_Symbol, PERIOD_M1, 0) < b1[0])
		ObjectSetText(Name + IntegerToString(1) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_M1, 0) < a1[0] && iClose(_Symbol, PERIOD_M1, 0) > b1[0])
		ObjectSetText(Name + IntegerToString(1) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
	//M5
	if (iClose(_Symbol, PERIOD_M5, 0) > a2[0] && iClose(_Symbol, PERIOD_M5, 0) > b2[0])
		ObjectSetText(Name + IntegerToString(2) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_M5, 0) < a2[0] && iClose(_Symbol, PERIOD_M5, 0) < b2[0])
		ObjectSetText(Name + IntegerToString(2) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
	else if (iClose(_Symbol, PERIOD_M5, 0) > a2[0] && iClose(_Symbol, PERIOD_M5, 0) < b2[0])
		ObjectSetText(Name + IntegerToString(2) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_M5, 0) < a2[0] && iClose(_Symbol, PERIOD_M5, 0) > b2[0])
		ObjectSetText(Name + IntegerToString(2) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
	//M15
	if (iClose(_Symbol, PERIOD_M15, 0) > a3[0] && iClose(_Symbol, PERIOD_M15, 0) > b3[0])
		ObjectSetText(Name + IntegerToString(3) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_M15, 0) < a3[0] && iClose(_Symbol, PERIOD_M15, 0) < b3[0])
		ObjectSetText(Name + IntegerToString(3) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
	else if (iClose(_Symbol, PERIOD_M15, 0) > a3[0] && iClose(_Symbol, PERIOD_M15, 0) < b3[0])
		ObjectSetText(Name + IntegerToString(3) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_M15, 0) < a3[0] && iClose(_Symbol, PERIOD_M15, 0) > b3[0])
		ObjectSetText(Name + IntegerToString(3) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
	//M30
	if (iClose(_Symbol, PERIOD_M30, 0) > a4[0] && iClose(_Symbol, PERIOD_M30, 0) > b4[0])
		ObjectSetText(Name + IntegerToString(4) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_M30, 0) < a4[0] && iClose(_Symbol, PERIOD_M30, 0) < b4[0])
		ObjectSetText(Name + IntegerToString(4) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
	else if (iClose(_Symbol, PERIOD_M30, 0) > a4[0] && iClose(_Symbol, PERIOD_M30, 0) < b4[0])
		ObjectSetText(Name + IntegerToString(4) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_M30, 0) < a4[0] && iClose(_Symbol, PERIOD_M30, 0) > b4[0])
		ObjectSetText(Name + IntegerToString(4) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
	//H1
	if (iClose(_Symbol, PERIOD_H1, 0) > a5[0] && iClose(_Symbol, PERIOD_H1, 0) > b5[0])
		ObjectSetText(Name + IntegerToString(5) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_H1, 0) < a5[0] && iClose(_Symbol, PERIOD_H1, 0) < b5[0])
		ObjectSetText(Name + IntegerToString(5) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
	else if (iClose(_Symbol, PERIOD_H1, 0) > a5[0] && iClose(_Symbol, PERIOD_H1, 0) < b5[0])
		ObjectSetText(Name + IntegerToString(5) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_H1, 0) < a5[0] && iClose(_Symbol, PERIOD_H1, 0) > b5[0])
		ObjectSetText(Name + IntegerToString(5) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
	//H4
	if (iClose(_Symbol, PERIOD_H4, 0) > a6[0] && iClose(_Symbol, PERIOD_H4, 0) > b6[0])
		ObjectSetText(Name + IntegerToString(6) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_H4, 0) < a6[0] && iClose(_Symbol, PERIOD_H4, 0) < b6[0])
		ObjectSetText(Name + IntegerToString(6) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
	else if (iClose(_Symbol, PERIOD_H4, 0) > a6[0] && iClose(_Symbol, PERIOD_H4, 0) < b6[0])
		ObjectSetText(Name + IntegerToString(6) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_H4, 0) < a6[0] && iClose(_Symbol, PERIOD_H4, 0) > b6[0])
		ObjectSetText(Name + IntegerToString(6) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
	//D1
	if (iClose(_Symbol, PERIOD_D1, 0) > a7[0] && iClose(_Symbol, PERIOD_D1, 0) > b7[0])
		ObjectSetText(Name + IntegerToString(7) + " Signal", CharToString(174), 8, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_D1, 0) < a7[0] && iClose(_Symbol, PERIOD_D1, 0) < b7[0])
		ObjectSetText(Name + IntegerToString(7) + " Signal", CharToString(174), 8, "Wingdings", clrBlack);
	else if (iClose(_Symbol, PERIOD_D1, 0) > a7[0] && iClose(_Symbol, PERIOD_D1, 0) < b7[0])
		ObjectSetText(Name + IntegerToString(7) + " Signal", CharToString(233), 7, "Wingdings", clrYellow);
	else if (iClose(_Symbol, PERIOD_D1, 0) < a7[0] && iClose(_Symbol, PERIOD_D1, 0) > b7[0])
		ObjectSetText(Name + IntegerToString(7) + " Signal", CharToString(234), 7, "Wingdings", clrBlack);
}
//+------------------------------------------------------------------+

/*
//+------------------------------------------------------------------+
void TradeInfo()
{
	int Total_Buy_Trades = 0;
	double Total_Buy_Size = 0, Total_Buy_Price = 0, Buy_Profit = 0;

	int Total_Sell_Trades = 0;
	double Total_Sell_Size = 0, Total_Sell_Price = 0, Sell_Profit = 0;

	int Net_Trades = 0;
	double Net_Lots = 0, Net_Result = 0;

	double Average_Price = 0, distance = 0;
	double Pip_Value = SymbolInfoDouble(_Symbol, MODE_TICKVALUE) * PipAdjust;
	double Pip_Size = SymbolInfoDouble(_Symbol, MODE_TICKSIZE) * PipAdjust;

	int total = OrdersTotal();

	for (int i = 0; i < total; i++)
	{
		int ord = OrderSelect(i, SELECT_BY_POS, MODE_TRADES);
		{
			if (OrderType() == OP_BUY && Order_Symbol == _Symbol)
			{
				Total_Buy_Trades++;
				Total_Buy_Price += OrderOpenPrice() * OrderLots();
				Total_Buy_Size += OrderLots();
				Buy_Profit += OrderProfit() + OrderSwap() + OrderCommission();
			}
			if (OrderType() == OP_SELL && Order_Symbol == _Symbol)
			{
				Total_Sell_Trades++;
				Total_Sell_Size += OrderLots();
				Total_Sell_Price += OrderOpenPrice() * OrderLots();
				Sell_Profit += OrderProfit() + OrderSwap() + OrderCommission();
			}
		}
	}
	if (Total_Buy_Price > 0)
	{
		Total_Buy_Price /= Total_Buy_Size;
	}
	if (Total_Sell_Price > 0)
	{
		Total_Sell_Price /= Total_Sell_Size;
	}

	Net_Trades = Total_Buy_Trades + Total_Sell_Trades;
	Net_Lots = Total_Buy_Size - Total_Sell_Size;
	Net_Result = Buy_Profit + Sell_Profit;

	if (Net_Trades > 0 && Net_Lots != 0)
	{
		distance = (Net_Result / (MathAbs(Net_Lots * SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE))) * SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE));
		if (Net_Lots > 0)
		{
			Average_Price = SymbolInfoDouble(_Symbol, SYMBOL_BID) - distance;
		}
		if (Net_Lots < 0)
		{
			Average_Price = SymbolInfoDouble(_Symbol, SYMBOL_ASK) + distance;
		}
	}
	if (Net_Trades > 0 && Net_Lots == 0)
	{
		distance = (Net_Result / ((SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE))) * SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE));
		Average_Price = NormalizeDouble(SymbolInfoDouble(_Symbol, SYMBOL_BID) - distance, _Digits);
	}

	color cl = clrBlue;
	if (Net_Lots < 0)
		cl = clrRed;
	if (Net_Lots == 0)
		cl = clrWhite;

	if (Average_Price != 0 && ObjectFind(0, Name + "Average_Price_Line_" + _Symbol) < 0)
	{
		ObjectCreate(0, Name + "Average_Price_Line_" + _Symbol, OBJ_HLINE, 0, 0, Average_Price);
		ObjectSetInteger(0, Name + "Average_Price_Line_" + _Symbol, OBJPROP_WIDTH, 2);
	}

	if (Average_Price != 0)
	{
		ObjectSetDouble(0, Name + "Average_Price_Line_" + _Symbol, OBJPROP_PRICE, Average_Price);
		ObjectSetInteger(0, Name + "Average_Price_Line_" + _Symbol, OBJPROP_COLOR, cl);
		if (Total_Buy_Trades >= 1)
			ObjectSetText(Name + " BuyPos", "Buy: " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID) - distance, _Digits) + " / " + DoubleToString(Total_Buy_Size, 2) + " / " + DoubleToString(Buy_Profit, 2), 7, "Arial", clrBlue);
		if (Total_Sell_Trades >= 1)
			ObjectSetText(Name + " SellPos", "Sell: " + DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_ASK) + distance, _Digits) + " / " + DoubleToString(Total_Sell_Size, 2) + " / " + DoubleToString(Sell_Profit, 2), 7, "Arial", clrRed);
		if (Total_Sell_Trades >= 1 && Total_Buy_Trades >= 1)
			ObjectSetText(Name + " AvgPos", "Avg: " + DoubleToString(Average_Price, _Digits) + " / " + DoubleToString(Net_Lots, 2) + " / " + DoubleToString(Net_Result, 2), 7, "Arial", clrBlack);
	}
	else
	{
		ObjectSetText(Name + " BuyPos", "", 7, "Arial", clrBlue);
		ObjectSetText(Name + " SellPos", "", 7, "Arial", clrRed);
		ObjectSetText(Name + " AvgPos", "", 7, "Arial", clrRed);
		ObjectDelete(0, Name + "Average_Price_Line_" + _Symbol);
	}
}
//+------------------------------------------------------------------+
*/

//+5MIN VALUES-------------------------------------------------------+
double blue(int xx)
{
	double Pip_Value = 0;
	if (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX || SymbolInfoInteger(_Symbol, SYMBOL_TRADE_CALC_MODE) == SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE)
		Pip_Value = (_Point * MathPow(10, MathMod(_Digits, 2)));
	else
		Pip_Value = 1;
	double aa[], zz[];
	ArrayInitialize(aa, 0);
	ArrayInitialize(zz, 0);
	ArrayResize(aa, 20);
	ArrayResize(zz, 20);
	int ab = iBarShift("EURUSD", PERIOD_M5, TimeCurrent() - (TimeCurrent() % (PERIOD_D1 * 60))) + 1;
	int bb = 0, bc = 0;
	if (ab > 48)
	{
		bb = ab - 48;
		bc = ab - 49;
		aa[0] = iClose(_Symbol, PERIOD_M5, bb) - iOpen(_Symbol, PERIOD_M5, bb);
		zz[0] = iClose(_Symbol, PERIOD_M5, bc) - iOpen(_Symbol, PERIOD_M5, bc);
	}
	else
	{
		aa[0] = 0;
		zz[0] = 0;
	}
	int cc = 0, cd = 0;
	if (ab > 60)
	{
		cc = ab - 60;
		cd = ab - 61;
		aa[1] = iClose(_Symbol, PERIOD_M5, cc) - iOpen(_Symbol, PERIOD_M5, cc);
		zz[1] = iClose(_Symbol, PERIOD_M5, cd) - iOpen(_Symbol, PERIOD_M5, cd);
	}
	else
	{
		aa[1] = 0;
		zz[1] = 0;
	}
	int dd = 0, de = 0;
	if (ab > 72)
	{
		dd = ab - 72;
		de = ab - 73;
		aa[2] = iClose(_Symbol, PERIOD_M5, dd) - iOpen(_Symbol, PERIOD_M5, dd);
		zz[2] = iClose(_Symbol, PERIOD_M5, de) - iOpen(_Symbol, PERIOD_M5, de);
	}
	else
	{
		aa[2] = 0;
		zz[2] = 0;
	}
	int ee = 0, ef = 0;
	if (ab > 84)
	{
		ee = ab - 84;
		ef = ab - 85;
		aa[3] = iClose(_Symbol, PERIOD_M5, ee) - iOpen(_Symbol, PERIOD_M5, ee);
		zz[3] = iClose(_Symbol, PERIOD_M5, ef) - iOpen(_Symbol, PERIOD_M5, ef);
	}
	else
	{
		aa[3] = 0;
		zz[3] = 0;
	}
	int ff = 0, fg = 0;
	if (ab > 96)
	{
		ff = ab - 96;
		fg = ab - 97;
		aa[4] = iClose(_Symbol, PERIOD_M5, ff) - iOpen(_Symbol, PERIOD_M5, ff);
		zz[4] = iClose(_Symbol, PERIOD_M5, fg) - iOpen(_Symbol, PERIOD_M5, fg);
	}
	else
	{
		aa[4] = 0;
		zz[4] = 0;
	}
	int gg = 0, gh = 0;
	if (ab > 108)
	{
		gg = ab - 108;
		gh = ab - 109;
		aa[5] = iClose(_Symbol, PERIOD_M5, gg) - iOpen(_Symbol, PERIOD_M5, gg);
		zz[5] = iClose(_Symbol, PERIOD_M5, gh) - iOpen(_Symbol, PERIOD_M5, gh);
	}
	else
	{
		aa[5] = 0;
		zz[5] = 0;
	}
	int hh = 0, hi = 0;
	if (ab > 120)
	{
		hh = ab - 120;
		hi = ab - 121;
		aa[6] = iClose(_Symbol, PERIOD_M5, hh) - iOpen(_Symbol, PERIOD_M5, hh);
		zz[6] = iClose(_Symbol, PERIOD_M5, hi) - iOpen(_Symbol, PERIOD_M5, hi);
	}
	else
	{
		aa[6] = 0;
		zz[6] = 0;
	}
	int ii = 0, ij = 0;
	if (ab > 132)
	{
		ii = ab - 132;
		ij = ab - 133;
		aa[7] = iClose(_Symbol, PERIOD_M5, ii) - iOpen(_Symbol, PERIOD_M5, ii);
		zz[7] = iClose(_Symbol, PERIOD_M5, ij) - iOpen(_Symbol, PERIOD_M5, ij);
	}
	else
	{
		aa[7] = 0;
		zz[7] = 0;
	}
	int jj = 0, jk = 0;
	if (ab > 144)
	{
		jj = ab - 144;
		jk = ab - 145;
		aa[8] = iClose(_Symbol, PERIOD_M5, jj) - iOpen(_Symbol, PERIOD_M5, jj);
		zz[8] = iClose(_Symbol, PERIOD_M5, jk) - iOpen(_Symbol, PERIOD_M5, jk);
	}
	else
	{
		aa[8] = 0;
		zz[8] = 0;
	}
	int kk = 0, kl = 0;
	if (ab > 156)
	{
		kk = ab - 156;
		kl = ab - 157;
		aa[9] = iClose(_Symbol, PERIOD_M5, kk) - iOpen(_Symbol, PERIOD_M5, kk);
		zz[9] = iClose(_Symbol, PERIOD_M5, kl) - iOpen(_Symbol, PERIOD_M5, kl);
	}
	else
	{
		aa[9] = 0;
		zz[9] = 0;
	}
	int ll = 0, lm = 0;
	if (ab > 168)
	{
		ll = ab - 168;
		lm = ab - 169;
		aa[10] = iClose(_Symbol, PERIOD_M5, ll) - iOpen(_Symbol, PERIOD_M5, ll);
		zz[10] = iClose(_Symbol, PERIOD_M5, lm) - iOpen(_Symbol, PERIOD_M5, lm);
	}
	else
	{
		aa[10] = 0;
		zz[10] = 0;
	}
	int mm = 0, mn = 0;
	if (ab > 180)
	{
		mm = ab - 180;
		mn = ab - 181;
		aa[11] = iClose(_Symbol, PERIOD_M5, mm) - iOpen(_Symbol, PERIOD_M5, mm);
		zz[11] = iClose(_Symbol, PERIOD_M5, mn) - iOpen(_Symbol, PERIOD_M5, mn);
	}
	else
	{
		aa[11] = 0;
		zz[11] = 0;
	}
	int nn = 0, no = 0;
	if (ab > 192)
	{
		nn = ab - 192;
		no = ab - 193;
		aa[12] = iClose(_Symbol, PERIOD_M5, nn) - iOpen(_Symbol, PERIOD_M5, nn);
		zz[12] = iClose(_Symbol, PERIOD_M5, no) - iOpen(_Symbol, PERIOD_M5, no);
	}
	else
	{
		aa[12] = 0;
		zz[12] = 0;
	}
	int oo = 0, op = 0;
	if (ab > 204)
	{
		oo = ab - 204;
		op = ab - 205;
		aa[13] = iClose(_Symbol, PERIOD_M5, oo) - iOpen(_Symbol, PERIOD_M5, oo);
		zz[13] = iClose(_Symbol, PERIOD_M5, op) - iOpen(_Symbol, PERIOD_M5, op);
	}
	else
	{
		aa[13] = 0;
		zz[13] = 0;
	}
	int pp = 0, pq = 0;
	if (ab > 216)
	{
		pp = ab - 216;
		pq = ab - 217;
		aa[14] = iClose(_Symbol, PERIOD_M5, pp) - iOpen(_Symbol, PERIOD_M5, pp);
		zz[14] = iClose(_Symbol, PERIOD_M5, pq) - iOpen(_Symbol, PERIOD_M5, pq);
	}
	else
	{
		aa[14] = 0;
		zz[14] = 0;
	}
	int qq = 0, qr = 0;
	if (ab > 228)
	{
		qq = ab - 228;
		qr = ab - 229;
		aa[15] = iClose(_Symbol, PERIOD_M5, qq) - iOpen(_Symbol, PERIOD_M5, qq);
		zz[15] = iClose(_Symbol, PERIOD_M5, qr) - iOpen(_Symbol, PERIOD_M5, qr);
	}
	else
	{
		aa[15] = 0;
		zz[15] = 0;
	}
	int rr = 0, rs = 0;
	if (ab > 240)
	{
		rr = ab - 240;
		rs = ab - 241;
		aa[16] = iClose(_Symbol, PERIOD_M5, rr) - iOpen(_Symbol, PERIOD_M5, rr);
		zz[16] = iClose(_Symbol, PERIOD_M5, rs) - iOpen(_Symbol, PERIOD_M5, rs);
	}
	else
		aa[16] = 0;
	int ss = 0, st = 0;
	if (ab > 252)
	{
		ss = ab - 252;
		st = ab - 253;
		aa[17] = iClose(_Symbol, PERIOD_M5, ss) - iOpen(_Symbol, PERIOD_M5, ss);
		zz[17] = iClose(_Symbol, PERIOD_M5, st) - iOpen(_Symbol, PERIOD_M5, st);
	}
	else
	{
		aa[17] = 0;
		zz[17] = 0;
	}
	int tt = 0, tu = 0;
	if (ab > 264)
	{
		tt = ab - 264;
		tu = ab - 265;
		aa[18] = iClose(_Symbol, PERIOD_M5, tt) - iOpen(_Symbol, PERIOD_M5, tt);
		zz[18] = iClose(_Symbol, PERIOD_M5, tu) - iOpen(_Symbol, PERIOD_M5, tu);
	}
	else
	{
		aa[18] = 0;
		zz[18] = 0;
	}
	int uu = 0, uv = 0;
	if (ab > 276)
	{
		uu = ab - 276;
		uv = ab - 277;
		aa[19] = iClose(_Symbol, PERIOD_M5, uu) - iOpen(_Symbol, PERIOD_M5, uu);
		zz[19] = iClose(_Symbol, PERIOD_M5, uv) - iOpen(_Symbol, PERIOD_M5, uv);
	}
	else
	{
		aa[19] = 0;
		zz[19] = 0;
	}

	double mid = 0, mid2 = 0, ddiff = 0, midp = 0, midn = 0;
	double zmid = 0, zmid2 = 0, zddiff = 0, zmidp = 0, zmidn = 0;

	for (int x = 19; x >= 0; x--)
	{
		mid += aa[x] / Pip_Value;
	}
	for (int x = 15; x >= 6; x--)
	{
		mid2 += aa[x] / Pip_Value;
	}

	for (int x = 19; x >= 0; x--)
	{
		if (aa[x] > 0)
			midp += aa[x] / Pip_Value;
		else if (aa[x] < 0)
			midn += aa[x] / Pip_Value;
	}

	for (int x = 19; x >= 0; x--)
	{
		zmid += zz[x] / Pip_Value;
	}
	for (int x = 15; x >= 6; x--)
	{
		zmid2 += zz[x] / Pip_Value;
	}

	for (int x = 19; x >= 0; x--)
	{
		if (aa[x] > 0)
			zmidp += zz[x] / Pip_Value;
		else if (aa[x] < 0)
			zmidn += zz[x] / Pip_Value;
	}

	ddiff = ((iHigh(_Symbol, PERIOD_D1, 0) - iLow(_Symbol, PERIOD_D1, 0)) / Pip_Value) - MathAbs(midn) - midp;
	zddiff = ((iHigh(_Symbol, PERIOD_D1, 0) - iLow(_Symbol, PERIOD_D1, 0)) / Pip_Value) - MathAbs(zmidn) - zmidp;

	switch (xx)
	{
	case 1:
		return (mid);
		break;
	case 2:
		return (mid2);
		break;
	case 3:
		return (ddiff);
		break;
	case 4:
		return (midp);
		break;
	case 5:
		return (midn);
		break;
	case 6:
		return (zmid);
		break;
	case 7:
		return (zmid2);
		break;
	case 8:
		return (zddiff);
		break;
	}

	return (0);
}
//-------------------------------------------------------------------+

//+ARROWPRICE--------------------------------------------------------+
void ArrowPrice(const string name, const double x, const datetime y, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW_RIGHT_PRICE, 0, 0, 0))
		{
			Print("error: can't create arrow_right_price! code #", GetLastError());
		}
	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);
	
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE, x);
	ObjectSetInteger(0, name, OBJPROP_TIME, Time[0] + y);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "SL Price: " + DoubleToString(x, _Digits));
}
//+------------------------------------------------------------------+

//+TEXTCREATE--------------------------------------------------------+
bool TextCreate(const string name, datetime time, double price, const string text, const ENUM_ANCHOR_POINT anchor = ANCHOR_LEFT_UPPER)
{
	ResetLastError();
	if (!ObjectCreate(0, name, OBJ_TEXT, 0, time, price))
	{
		Print(__FUNCTION__,
			  ": failed to create \"Text\" object! Error code = ", GetLastError());
		return (false);
	}
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 12);
	ObjectSetDouble(0, name, OBJPROP_ANGLE, 0.0);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, anchor);
	ObjectSetInteger(0, name, OBJPROP_COLOR, clrBlack);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_ZORDER, 0);
	return (true);
}
//+------------------------------------------------------------------+

//+TEXTMOVE----------------------------------------------------------+
bool TextMove(const string name, datetime time, double price, const string text)
{
	if (!time)
		time = TimeCurrent() + 5 * Period() * 60;
	if (!price)
		price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
	ResetLastError();
	if (!ObjectMove(0, name, 0, time, price))
	{
		Print(__FUNCTION__,
			  ": failed to move the anchor point! Error code = ", GetLastError());
		return (false);
	}
	if (!ObjectSetString(0, name, OBJPROP_TEXT, text))
	{
		Print(__FUNCTION__,
			  ": failed to change the text! Error code = ", GetLastError());
		return (false);
	}
	return (true);
}
//+------------------------------------------------------------------+

//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);
	
	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Time[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
	ObjectSetInteger(0, name, OBJPROP_STYLE, st);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
	ObjectSetInteger(0, name, OBJPROP_RAY, false);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToString(pr1, _Digits) + " Date: " + TimeToString(Time[t1], TIME_DATE));
}
//+------------------------------------------------------------------+

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const datetime y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE, x);
	ObjectSetInteger(0, name, OBJPROP_TIME, y + (Period() * 60));
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	//ObjectSetString(0, name, OBJPROP_TOOLTIP, "Price: " + DoubleToString(x, _Digits));
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToString(x, _Digits));
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetString(0, name, OBJPROP_TEXT, label);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

/*
//+TRADE CHECK-------------------------------------------------------+
bool trades_on_symbol(string symbol)
{
	for (int i = OrdersTotal() - 1; OrderSelect(i, SELECT_BY_POS); i--)
		if (Order_Symbol == symbol && OrderType() < 2)
			return true;
	return false;
}
//+------------------------------------------------------------------+
*/

int TimeDay(datetime date)
  {
   MqlDateTime tm;
   TimeToStruct(date,tm);
   return(tm.day);
  }
int Hour()
  {
   MqlDateTime tm;
   TimeCurrent(tm);
   return(tm.hour);
  }
int DayOfWeek()
  {
   MqlDateTime tm;
   TimeCurrent(tm);
   return(tm.day_of_week);
  }

//+OBJECTSETTEXT FUNCTION--------------------------------------------+
void ObjectSetText(const string name,
                   const string text,
                   const int fontsize,
                   const string font,
                   const color col)
{
	if (ObjectFind(0, name) < 0)
		Print("Error: can't find label_object! code #", GetLastError());
   int object = (int)ObjectGetInteger(0, name, OBJPROP_TYPE);
   if (object != OBJ_LABEL && object != OBJ_TEXT)
      Print("Not a label or text object! code#", GetLastError());
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, fontsize);
   ObjectSetString(0, name, OBJPROP_FONT, font);
   ObjectSetInteger(0, name, OBJPROP_COLOR, col);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+