#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict
#property indicator_buffers 14
#property indicator_color1 clrBlue
#property indicator_color2 clrRed
#property indicator_color3 clrBlue
#property indicator_color4 clrRed

#define Name WindowExpertName()

//+INPUTS------------------------------------------------------------+
//extern int daytocountback = 5000;		  // Lookback period in days
extern int periods = 8000;				  // Candles back to check
int drawlines = 8000;					  // Candles back to mark with trendlines
ENUM_TIMEFRAMES DROP_TF = PERIOD_CURRENT; // Check every X period
double dist = 0.25;						  // Distance of wick vs previous body
enum brtf
{
	M1 = 1,	  // 1
	M5 = 5,	  // 5
	M15 = 15, // 15
};

input int addTP = 20;
input int addSL = 20;

double finp[];
double finn[];
double linp[];
double linn[];
double ffinp[];
double ffinn[];
double flinp[];
double flinn[];

double signalcomboup[];
double signalcombodn[];
double signalbuysl[];
double signalselsl[];
double signalbuytp[];
double signalseltp[];
static double fsignalbuy;
static double fsignalsel;
static double fsignalbuystop;
static double fsignalselstop;
static double signalbuy;
static double signalsel;
static double signalbuystop;
static double signalselstop;
//+------------------------------------------------------------------+

//+INIT--------------------------------------------------------------+
int OnInit()
{
	IndicatorBuffers(14);
	IndicatorShortName("FVG /w limit");
	ObjectsDeleteAll(0, Name);
	SetIndexBuffer(0, finp);
	SetIndexBuffer(1, finn);
	SetIndexBuffer(2, linp);
	SetIndexBuffer(3, linn);
	SetIndexBuffer(4, ffinp);
	SetIndexBuffer(5, ffinn);
	SetIndexBuffer(6, flinp);
	SetIndexBuffer(7, flinn);
	SetIndexBuffer(8, signalcomboup);
	SetIndexBuffer(9, signalcombodn);
	SetIndexBuffer(10, signalbuysl);
	SetIndexBuffer(11, signalselsl);
	SetIndexBuffer(12, signalbuytp);
	SetIndexBuffer(13, signalseltp);

	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN PROGRAM------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	datetime expiry = D'2023.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("FVG expired on " + TimeToStr(expiry, TIME_DATE) + ", contact sakisf on FF for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true)
	{

		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, DROP_TF, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, DROP_TF, 0);
		}
		if (new_1m_check)
		{
			ObjectsDeleteAll(0, Name);
			ChartRedraw();
			RefreshRates();
			checkpre();
			new_1m_check = false;
		}
	} //YesStop (expiry) end
	   
	return (rates_total);
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION FRACTALS--------------------------------------------+
void checkpre()
{
	periods = 100;
	string obname;
	ArrayInitialize(ffinp, EMPTY_VALUE);
	ArrayInitialize(ffinn, EMPTY_VALUE);
	ArrayInitialize(flinp, EMPTY_VALUE);
	ArrayInitialize(flinn, EMPTY_VALUE);
	
	double fcopybuy[], fcopybuystop[];
	double fcopysel[], fcopyselstop[];

	double CD1[], OD1[], HD1[], LD1[];
	datetime TD1[];
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(OD1, true);
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(TD1, true);
	ArrayResize(CD1, periods + 3);
	ArrayResize(OD1, periods + 3);
	ArrayResize(HD1, periods + 3);
	ArrayResize(LD1, periods + 3);
	ArrayResize(TD1, periods + 3);
	CopyClose(_Symbol, PERIOD_CURRENT, 0, periods + 3, CD1);
	CopyOpen(_Symbol, PERIOD_CURRENT, 0, periods + 3, OD1);
	CopyHigh(_Symbol, PERIOD_CURRENT, 0, periods + 3, HD1);
	CopyLow(_Symbol, PERIOD_CURRENT, 0, periods + 3, LD1);
	CopyTime(_Symbol, PERIOD_CURRENT, 0, periods + 3, TD1);

	for (int x = periods - 3; x >= 1; x--)
	{
		if ((CD1[x + 3] < CD1[x + 2]) && LD1[x + 1] - HD1[x + 3] > 0)
		{
			ffinp[x] = HD1[x + 3];
			flinp[x] = LD1[x + 1];			
		}
		else { ffinp[x] = EMPTY_VALUE; flinp[x] = EMPTY_VALUE; }
	}

	for (int x = periods - 3; x >= 1; x--)
	{
		if ((CD1[x + 3] > CD1[x + 2]) && (LD1[x + 3] - HD1[x + 1] > 0))
		{
			ffinn[x] = LD1[x + 3];
			flinn[x] = HD1[x + 1];
		}
		else { ffinn[x] = EMPTY_VALUE; flinn[x] = EMPTY_VALUE; }
	}

	int fcount11 = 0, fcount22 = 0;
	int fadda = -1, faddb = -1;
   
   for (int x = periods; x >= 1; x--)
   {
      double LL = CD1[ArrayMinimum(CD1, x, 1)];
      if (flinp[x] != EMPTY_VALUE && LL > ffinp[x])
      { 
         fadda++;
         ArrayResize(fcopybuy, fadda + 1);
         ArrayResize(fcopybuystop, fadda + 1);
         fcopybuy[fadda] = flinp[x];
         fcopybuystop[fadda] = ffinp[x];
      }
   }
   for (int x = periods; x >= 1; x--)
   {
      double HH = CD1[ArrayMaximum(CD1, x, 1)];
      if (flinn[x] != EMPTY_VALUE && HH < ffinn[x])
      {
         faddb++;
         ArrayResize(fcopysel, faddb + 1);
         ArrayResize(fcopyselstop, faddb + 1);
         fcopysel[faddb] = flinn[x];
         fcopyselstop[faddb] = ffinn[x];
      }
   }
   
   if (ArraySize(fcopysel) > 0)
   {
      ArraySort(fcopysel, 0, 0, MODE_ASCEND);
      ArraySort(fcopyselstop, 0, 0, MODE_ASCEND);
   }
   if (ArraySize(fcopybuy) > 0)
   {
      ArraySort(fcopybuy, 0, 0, MODE_DESCEND);
      ArraySort(fcopybuystop, 0, 0, MODE_DESCEND);
   }
   
   if (ArraySize(fcopybuy) > 0)
   {   
      fsignalbuy = fcopybuy[0];
      fsignalbuystop = fcopybuystop[0];
   }
   if (ArraySize(fcopysel) > 0)
   {
      fsignalsel = fcopysel[0];
      fsignalselstop = fcopyselstop[0];
   }
	
	ArrayInitialize(finp, EMPTY_VALUE);
	ArrayInitialize(finn, EMPTY_VALUE);
	ArrayInitialize(linp, EMPTY_VALUE);
	ArrayInitialize(linn, EMPTY_VALUE);
	
	double copybuy[], copybuystop[];
	double copysel[], copyselstop[];
	
	for (int x = periods - 3; x >= 1; x--)
	{
		if ((OD1[x + 3] > CD1[x + 3]) && (((CD1[x + 2] > OD1[x + 2]) && (CD1[x + 1] > OD1[x + 1]) && ((CD1[x + 1] - OD1[x + 2]) > (CD1[x + 3] - OD1[x + 3]))) || ((CD1[x + 2] > OD1[x + 2]) && ((CD1[x + 2] - OD1[x + 2]) > (CD1[x + 3] - OD1[x + 3]))))
		)//&& CD1[x + 1] > iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_CLOSE, x))
		{
			finp[x] = OD1[x + 3];
			linp[x] = HD1[x + 3];
		}
		else { finp[x] = EMPTY_VALUE; linp[x] = EMPTY_VALUE; }
	}

	for (int x = periods - 3; x >= 1; x--)
	{
		if ((CD1[x + 3] > OD1[x + 3]) && (((OD1[x + 2] > CD1[x + 2]) && (OD1[x + 1] > CD1[x + 1]) && ((OD1[x + 2] - CD1[x + 1]) > (OD1[x + 3] - CD1[x + 3]))) || ((OD1[x + 2] > CD1[x + 2]) && ((OD1[x + 2] - CD1[x + 2]) > (CD1[x + 3] - OD1[x + 3]))))
		)//&& CD1[x + 1] < iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_CLOSE, x))
		{
			finn[x] = OD1[x + 3];
			linn[x] = LD1[x + 3];
		}
		else { finn[x] = EMPTY_VALUE; linn[x] = EMPTY_VALUE; }
	}

	int count11 = 0, count22 = 0;
	int adda = -1, addb = -1;
   
   for (int x = periods; x >= 1; x--)
   {
      double LL = CD1[ArrayMinimum(CD1, x, 1)];
      if (linp[x] != EMPTY_VALUE && LL > finp[x])
      { 
         adda++;
         ArrayResize(copybuy, adda + 1);
         ArrayResize(copybuystop, adda + 1);
         copybuy[adda] = linp[x];
         copybuystop[adda] = finp[x];
      }
   }
   for (int x = periods; x >= 1; x--)
   {
      double HH = CD1[ArrayMaximum(CD1, x, 1)];
      if (linn[x] != EMPTY_VALUE && HH < finn[x])
      {
         addb++;
         ArrayResize(copysel, addb + 1);
         ArrayResize(copyselstop, addb + 1);
         copysel[addb] = linn[x];
         copyselstop[addb] = finn[x];
      }
   }
   
   if (ArraySize(copysel) > 0)
   {
      ArraySort(copysel, 0, 0, MODE_ASCEND);
      ArraySort(copyselstop, 0, 0, MODE_ASCEND);
   }
   if (ArraySize(copybuy) > 0)
   {
      ArraySort(copybuy, 0, 0, MODE_DESCEND);
      ArraySort(copybuystop, 0, 0, MODE_DESCEND);
   }
   
   if (ArraySize(copybuy) > 0)
   {   
      signalbuy = copybuy[0];
      signalbuystop = copybuystop[0];
   }
   if (ArraySize(copysel) > 0)
   {
      signalsel = copysel[0];
      signalselstop = copyselstop[0];
   }
   
   double buystop = 0, selstop = 0;
   double buy = 0, sel = 0;
   if ((fsignalbuy != EMPTY_VALUE || signalbuy != EMPTY_VALUE) && (fsignalsel != EMPTY_VALUE || signalsel != EMPTY_VALUE)) buy = MathMax(fsignalbuy, signalbuy);
   if ((fsignalsel != EMPTY_VALUE || signalsel != EMPTY_VALUE) && (fsignalsel != EMPTY_VALUE || signalsel != EMPTY_VALUE)) sel = MathMin(fsignalsel, signalsel);
   if ((fsignalbuy != EMPTY_VALUE || signalbuy != EMPTY_VALUE) && (fsignalsel != EMPTY_VALUE || signalsel != EMPTY_VALUE)) buystop = MathMax(fsignalbuystop, signalbuystop);
   if ((fsignalsel != EMPTY_VALUE || signalsel != EMPTY_VALUE) && (fsignalbuy != EMPTY_VALUE || signalbuy != EMPTY_VALUE)) selstop = MathMin(fsignalselstop, signalselstop);
   
   
	if (buy != EMPTY_VALUE && (fsignalsel != EMPTY_VALUE || signalsel != EMPTY_VALUE) && (ArraySize(fcopybuy) >= 1 || ArraySize(copybuy) >= 1) && (iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, 1) < 45 || LD1[1] < iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, 1))) signalcomboup[1] = 1;
	if (buy != EMPTY_VALUE && (fsignalsel != EMPTY_VALUE || signalsel != EMPTY_VALUE)) signalbuysl[1] = buystop - addSL * _Point;
	if (buy != EMPTY_VALUE && (fsignalsel != EMPTY_VALUE || signalsel != EMPTY_VALUE)) signalbuytp[1] = sel + addTP * _Point;
	
	if (sel != EMPTY_VALUE && sel != 0 && (fsignalbuy != EMPTY_VALUE || signalbuy != EMPTY_VALUE) && (ArraySize(fcopysel) >= 1 || ArraySize(copysel) >= 1) && (iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, 1) > 55 || HD1[1] > iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, 1))) signalcombodn[1] = 1;
	if (sel != EMPTY_VALUE && sel != 0 && (fsignalbuy != EMPTY_VALUE || signalbuy != EMPTY_VALUE)) signalselsl[1] = selstop + addSL * _Point;
	if (sel != EMPTY_VALUE && sel != 0 && (fsignalbuy != EMPTY_VALUE || signalbuy != EMPTY_VALUE)) signalseltp[1] = buy - addTP * _Point; 
	
	if (buy != EMPTY_VALUE || sel != EMPTY_VALUE && sel != 0) Print(buy + " " + sel + " " + buystop + " " + selstop);
}
//+------------------------------------------------------------------+

//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t1]);
	ObjectSet(name, OBJPROP_TIME2, Time[t2] + t3);
	ObjectSet(name, OBJPROP_PRICE1, pr1);
	ObjectSet(name, OBJPROP_PRICE2, pr2);
	ObjectSet(name, OBJPROP_STYLE, st);
	ObjectSet(name, OBJPROP_WIDTH, wi);
	ObjectSet(name, OBJPROP_RAY, false);
	ObjectSet(name, OBJPROP_BACK, true);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToStr(pr1, _Digits) + " Date: " + TimeToStr(Time[t1], TIME_DATE));
}
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const double pr1, const double pr2, const int t1, const int t2, const int t3, const color BCol, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	//ObjectSetInteger(0, name, OBJPROP_BGCOLOR, FCol);
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME2, Time[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE2, pr2);
	//ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_RAISED);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 0);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const datetime y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, x);
	ObjectSetInteger(0, name, OBJPROP_TIME1, y);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	//ObjectSetString(0, name, OBJPROP_TOOLTIP, "Price: " + DoubleToStr(x, _Digits));
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToStr(x, _Digits));
}
//+------------------------------------------------------------------+

//+ARROW CREATE------------------------------------------------------+
void burnarr(string name, double p, int arrow, int t, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t]);
	ObjectSet(name, OBJPROP_PRICE1, p);
	ObjectSet(name, OBJPROP_ARROWCODE, arrow);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSet(name, OBJPROP_WIDTH, 1);
}
//+------------------------------------------------------------------+