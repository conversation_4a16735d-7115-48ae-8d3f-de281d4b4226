//+------------------------------------------------------------------+
//|                                                        Sakis.mq4 |
//+------------------------------------------------------------------+
#property copyright "Keith Watford"
#property link      ""
#property version   "1.00"
#property strict

#property indicator_chart_window
#property indicator_plots   0
#property indicator_buffers 0

#include <errordescription.mqh>
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
enum eTYPE
  {
   MID,//Middle Point
   PIVOT//Pivot
  };
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
input int InpTimeShift=-2;//Time Shift, hours
input color LineColor=clrRed;//Line Colour
input ENUM_LINE_STYLE LineStyle=STYLE_SOLID;//Line Style
input int FontSize=8;//Font Size
input int CheckHours=1;//Re-check File Every x Hours
input string FileName="m6e.csv";//File Name
input int BeginObjects=5;//Offset Start Index from Current Bar
input int EndObjects=9;//Offset End Index from Current Bar
input eTYPE MidPoint=MID;//Zero Line
//---
input bool EnableAlerts=true;//Enable Alerts
input bool PopUpAlert=true;//Pop Up Alert
input bool email=false;//Email Alert
input bool Push=false;//Push Notification
input bool SoundAlert=false;//Sound Alert
input uint Repeat=60;//Repeat Sound Alert in Seconds
input string SoundFile="expert.wav";//Sound File
input string LineAlert1="3x,";//Alert When Hit
input string LineAlert2="2x,";//Alert When Hit
input string LineAlert3="1x,";//Alert When Hit
input string LineAlert4="x,";//Alert When Hit
input string LineAlert5="";//Alert When Hit

//---
const bool tester=MQLInfoInteger(MQL_TESTER);
const string PROGRAM_NAME=MQLInfoString(MQL_PROGRAM_NAME);
string FileNameAlerted;
string Line1Array[51];
string Line2Array[51];
double LevelArray[51];
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
struct details
  {
   double            price;
   string            description;
  };
details AlertArray[];

int scale_saved;
int cwb_saved;
//+------------------------------------------------------------------+
double iLowD1(const string symbol,const uint index,const int shift=0)
  {
   datetime dt=iTime(symbol,PERIOD_D1,index);
   if(dt==0)
      return(-1);
//---      
   datetime dt_start=dt+PeriodSeconds(PERIOD_H1)*shift-(TimeDayOfWeek(dt)==0?PeriodSeconds(PERIOD_D1)*2:0);
   datetime dt_stop=dt_start+PeriodSeconds(PERIOD_D1)-1;
   //Print(dt," ",dt_start,"-",dt_stop);
//---
   double Arr[];
   if(CopyLow(symbol,PERIOD_H1,dt_start,dt_stop,Arr)>0)
      return Arr[ArrayMinimum(Arr)];
//---
   return(-1);
  }
//+------------------------------------------------------------------+
double iLow(const string symbol,const ENUM_TIMEFRAMES timeframe,const uint index)
  {
   double Arr[];
   if(CopyLow(symbol,timeframe,index,1,Arr)>0)
      return(Arr[0]);
   return(-1);
  }
//+------------------------------------------------------------------+
double iHigh(const string symbol,const ENUM_TIMEFRAMES timeframe,const uint index)
  {
   double Arr[];
   if(CopyHigh(symbol,timeframe,index,1,Arr)>0)
      return(Arr[0]);
   return(-1);
  }
//+------------------------------------------------------------------+
double iHighD1(const string symbol,const uint index,const int shift=0)
  {
   datetime dt=iTime(symbol,PERIOD_D1,index);
   if(dt==0)
      return(-1);
//---      
//sunday->friday
   datetime dt_start=dt+PeriodSeconds(PERIOD_H1)*shift-TimeDayOfWeek(dt)-(TimeDayOfWeek(dt)==0?PeriodSeconds(PERIOD_D1)*2:0);
   datetime dt_stop=dt_start+PeriodSeconds(PERIOD_D1)-1;
   //Print(dt," ",dt_start,"-",dt_stop);
//---
   double Arr[];
   if(CopyHigh(symbol,PERIOD_H1,dt_start,dt_stop,Arr)>0)
      return Arr[ArrayMaximum(Arr)];
//---
   return(-1);
  }
//+------------------------------------------------------------------+
double iClose(const string symbol,const ENUM_TIMEFRAMES timeframe,const uint index)
  {
   double Arr[];
   if(CopyClose(symbol,timeframe,index,1,Arr)>0)
      return(Arr[0]);
   return(-1);
  }
//+------------------------------------------------------------------+
double iCloseD1(const string symbol,const uint index,const int shift=0)
  {
   datetime dt=iTime(symbol,PERIOD_D1,index);
   if(dt==0)
      return(-1);
//---      
   datetime dt_start=dt+PeriodSeconds(PERIOD_H1)*shift-(TimeDayOfWeek(dt)==0?PeriodSeconds(PERIOD_D1)*2:0);
   datetime dt_stop=dt_start+PeriodSeconds(PERIOD_D1)-1;
//Print(dt," ",dt_start,"-",dt_stop);
//---
   double Arr[];
   if(CopyClose(symbol,PERIOD_H1,dt_stop,1,Arr)>0)
      return Arr[0];
//---
   return(-1);
  }
//+------------------------------------------------------------------+
datetime iTime(const string symbol,const ENUM_TIMEFRAMES timeframe,const uint index)
  {
   datetime Arr[];
   if(CopyTime(symbol,timeframe,index,1,Arr)>0)
      return(Arr[0]);
   return(0);
  }
//+------------------------------------------------------------------+
datetime iTimeD1(const string symbol,const uint index,const int shift=0)
  {
   datetime Arr[];
   if(CopyTime(symbol,PERIOD_D1,index,1,Arr)>0)
      return Arr[0]+PeriodSeconds(PERIOD_H1)*shift;
   return(0);
  }
//+------------------------------------------------------------------+
void ObjectSetText(const string name,const string text,const int font_size=0,const string font=NULL,const color text_color=clrNONE)
  {
   ObjectSetString(0,name,OBJPROP_TEXT,text);

   if(font_size>0)
      ObjectSetInteger(0,name,OBJPROP_FONTSIZE,font_size);

   if(font!=NULL)
      ObjectSetString(0,name,OBJPROP_FONT,font);

   if(text_color!=clrNONE)
      ObjectSetInteger(0,name,OBJPROP_COLOR,text_color);
  }
//+------------------------------------------------------------------+
//|   OnInit                                                         |
//+------------------------------------------------------------------+
int OnInit()
  {
   ReadCSVFile(FileName);

   FileNameAlerted=_Symbol+(string)ChartID()+"m.txt";

   scale_saved=(int)ChartGetInteger(0,CHART_SCALE);
   cwb_saved=(int)ChartGetInteger(0,CHART_WIDTH_IN_BARS);

   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//|   OnDeinit                                                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   if(reason==REASON_REMOVE ||
      reason==REASON_CLOSE ||
      reason==REASON_CHARTCLOSE ||
      reason==REASON_TEMPLATE ||
      MQLInfoInteger(MQL_TESTER))
     {
      LogDelete(FileNameAlerted);
     }

   DeleteObjects();
   ChartRedraw();
  }
//+------------------------------------------------------------------+
//|   OnCalculate                                                    |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   ArraySetAsSeries(high,true);
   ArraySetAsSeries(low,true);
   ArraySetAsSeries(time,true);
//---   
   static datetime start_day_time=0;
   datetime now_day_time=iTimeD1(_Symbol,0,InpTimeShift);
   if(now_day_time==0)
      return(0);
//---
   static int check_count=0;
   if(start_day_time!=now_day_time)
     {
      check_count=0;

      string value;
      LogRead(FileNameAlerted,value);
      datetime dt=StringToTime(value);

      if(dt!=now_day_time)
        {
         LogDelete(FileNameAlerted);
         LogWrite(FileNameAlerted,TimeToString(now_day_time));
        }

      //--- done
      start_day_time=now_day_time;
     }

//---
   bool new_check=false;
   static datetime recheck_time=0;
   if(check_count<30)
     {
      new_check=true;
      check_count++;
      recheck_time=TimeCurrent();
      if(check_count==30 && EnableAlerts)
         FillAlertArray();
     }

//---
   if(TimeCurrent()>=recheck_time+PeriodSeconds(PERIOD_H1)*CheckHours)
     {
      new_check=true;
      recheck_time=TimeCurrent();
     }

//---
   if(new_check)
     {
      if(ReadCSVFile(FileName))
        {
         CalculateRange();
         MoveObjects();
         if(EnableAlerts)
            FillAlertArray();
         ChartRedraw();
        }
     }

//---
   static datetime bar_time=0;
   if(bar_time!=time[0])
     {
      bar_time=time[0];
      MoveObjects();
      ChartRedraw();
     }

//---
   if(EnableAlerts)
     {
      int total=ArraySize(AlertArray);
      for(int x=total-1; x>=0; x--)
        {
         double level=NormalizeDouble(AlertArray[x].price,_Digits);
         if(high[0]>=level && low[0]<=level)
           {
            bool alerted=LogCheckLevel(FileNameAlerted,level);
            if(alerted)
              {
               RemoveElement(x,AlertArray);
               continue;
              }
            else
              {
               SendAlerts(" "+DoubleToString(level,_Digits)+" hit ("+AlertArray[x].description+") ");
               LogWrite(FileNameAlerted,DoubleToString(level,_Digits));
              }
           }

        }
     }
//---
   return(rates_total);
  }
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void DeleteObjects()
  {
   int total=ObjectsTotal(0,0);
   for(int i=total-1; i>=0; i--)
     {
      string obj_name=ObjectName(0,i);
      if(StringFind(obj_name,PROGRAM_NAME,0)!=-1)
         ObjectDelete(0,obj_name);
     }
  }
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
bool ReadCSVFile(const string file_name)
  {
   int mode=FILE_SHARE_READ|FILE_READ|FILE_SHARE_WRITE|FILE_TXT|FILE_ANSI;
   string path=TerminalInfoString(TERMINAL_DATA_PATH)+"\\"+(IsMQL5()?"MQL5":"MQL4")+"\\Files\\"+file_name;
   if(tester)
     {
      mode|=FILE_COMMON;
      path=TerminalInfoString(TERMINAL_COMMONDATA_PATH)+"\\Files\\"+file_name;
     }

   int handle=FileOpen(file_name,mode);
   if(handle<0)
     {
      printf("%s '%s'",ErrorDescription(_LastError),path);
      return(false);
     }

   string Line1=FileReadString(handle);
   string Line2=FileReadString(handle);
   FileClose(handle);

//---
   StringReplace(Line1,"\"","");
   StringReplace(Line2,"\"","");
//---
   StringSplit(Line1,';',Line1Array);
   StringSplit(Line2,';',Line2Array);
//---
   return(true);
  }
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
bool CalculateRange()
  {
   double day_high=iHighD1(_Symbol,1,InpTimeShift);
   //Print("high: ",day_high);
   if(day_high<0)
      return(false);
//---
   double day_low=iLowD1(_Symbol,1,InpTimeShift);
//Print("low: ",day_low);
   if(day_low<0)
      return(false);
//---
   double day_close=iCloseD1(_Symbol,1,InpTimeShift);
//Print("close: ",day_close);
   if(day_close<0)
      return(false);
//---
   double day_range=day_high-day_low;
   double step=day_range/10;
   /*
   switch(_Digits)
     {
      case 7: step=0.015625; break;
      case 6: step=0.03125; break;

      case 5:
      case 4: step=0.001; break;

      case 3: step=0.1; break;
      case 2: step=1; break;

      case 1:
      case 0: step=10; break;
     }*/

//---
   double mid_point=(day_low+day_high+day_close)/3;
   if(MidPoint==MID)
      mid_point=day_low+(day_range/2);

//---
   int middle=25;
   LevelArray[middle]=mid_point;
   double level=mid_point-step;

//---
   for(int x=middle-1;x>=0;x--)
     {
      LevelArray[x]=level;
      level-=step;
     }
   level=mid_point+step;

//---
   for(int x=middle+1;x<51;x++)
     {
      LevelArray[x]=level;
      level+=step;
     }
//---   
   return(true);
  }
//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
  {

   if(id==CHARTEVENT_CHART_CHANGE)
     {
      int scale=(int)ChartGetInteger(0,CHART_SCALE);
      int cwb=(int)ChartGetInteger(0,CHART_WIDTH_IN_BARS);
      if(scale!=scale_saved || cwb!=cwb_saved)
        {
         scale_saved=scale;
         cwb_saved=cwb;
         MoveObjects();
         ChartRedraw();
        }
     }
//---
   if(ObjectGetInteger(0,"Timer Stop",OBJPROP_STATE)==true)
     {
      ObjectDelete(0,"Timer Stop");
      EventKillTimer();
     }
  }
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void MoveObjects()
  {
   datetime time[];
   if(CopyTime(_Symbol,PERIOD_CURRENT,0,1,time)!=1)
      return;
//---
   int multiplier=(int)pow(2,5-scale_saved);
   datetime begin_line_time=time[0]+BeginObjects*multiplier*PeriodSeconds();
   datetime end_line_time=time[0]+EndObjects*multiplier*PeriodSeconds();
//---
   int total=ArraySize(Line1Array);
   for(int x=0; x<total; x++)
     {

      bool text1=Line1Array[x]!="x" && StringLen(Line1Array[x])>0;
      if(text1)
        {
         string obname=PROGRAM_NAME+" Label a "+(string)x;
         if(ObjectFind(0,obname)!=0)
           {
            ObjectCreate(0,obname,OBJ_TEXT,0,0,0,0,0);
            ObjectSetInteger(0,obname,OBJPROP_ANCHOR,ANCHOR_RIGHT);
            ObjectSetInteger(0,obname,OBJPROP_COLOR,LineColor);
            ObjectSetInteger(0,obname,OBJPROP_FONTSIZE,FontSize);
            ObjectSetInteger(0,obname,OBJPROP_SELECTABLE,true);
            //ObjectSetString(0,obname,OBJPROP_TOOLTIP,"\n");
           }
         ObjectMove(0,obname,0,begin_line_time,LevelArray[x]);
         ObjectSetText(obname,Line1Array[x],FontSize,"Arial",LineColor);
        }

      //---
      bool text2=Line2Array[x]!="x" && StringLen(Line2Array[x])>0;
      if(text2)
        {
         string obname=PROGRAM_NAME+" Label b "+(string)x;
         if(ObjectFind(0,obname)!=0)
           {
            ObjectCreate(0,obname,OBJ_TEXT,0,0,0,0,0);
            ObjectSetInteger(0,obname,OBJPROP_ANCHOR,ANCHOR_LEFT);
            ObjectSetInteger(0,obname,OBJPROP_COLOR,LineColor);
            ObjectSetInteger(0,obname,OBJPROP_FONTSIZE,FontSize);
            ObjectSetInteger(0,obname,OBJPROP_SELECTABLE,true);
            //ObjectSetString(0,obname,OBJPROP_TOOLTIP,"\n");
           }
         ObjectMove(0,obname,0,end_line_time,LevelArray[x]);
         ObjectSetText(obname,Line2Array[x],FontSize,"Arial",LineColor);
        }

      //---
      if(text1 || text2)
        {
         string obname=PROGRAM_NAME+" "+(string)x;
         if(ObjectFind(0,obname)!=0)
           {
            ObjectCreate(0,obname,OBJ_TREND,0,0,0,0,0);
            ObjectSetInteger(0,obname,OBJPROP_COLOR,LineColor);
            ObjectSetInteger(0,obname,OBJPROP_STYLE,LineStyle);
            ObjectSetInteger(0,obname,OBJPROP_RAY,false);
            //ObjectSetString(0,obname,OBJPROP_TOOLTIP,"\n");
           }
         ObjectMove(0,obname,0,begin_line_time,LevelArray[x]);
         ObjectMove(0,obname,1,end_line_time,LevelArray[x]);
        }
     }

  }
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void FillAlertArray()
  {
   ArrayResize(AlertArray,0);
//---
   if(LineAlert1!="")
     {
      FillAlertCheck(LineAlert1,Line1Array);
      FillAlertCheck(LineAlert1,Line2Array);
     }
//---
   if(LineAlert2!="")
     {
      FillAlertCheck(LineAlert2,Line1Array);
      FillAlertCheck(LineAlert2,Line2Array);
     }
//---
   if(LineAlert3!="")
     {
      FillAlertCheck(LineAlert3,Line1Array);
      FillAlertCheck(LineAlert3,Line2Array);
     }
//---
   if(LineAlert4!="")
     {
      FillAlertCheck(LineAlert4,Line1Array);
      FillAlertCheck(LineAlert4,Line2Array);
     }
//---
   if(LineAlert5!="")
     {
      FillAlertCheck(LineAlert5,Line1Array);
      FillAlertCheck(LineAlert5,Line2Array);
     }
  }
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void FillAlertCheck(string lookFor,string &array[])
  {
   int len=StringLen(lookFor);
   string sc[];
   ArrayResize(sc,len);

   for(int pos=0;pos<len;pos++)
     {
      sc[pos]=StringSubstr(lookFor,pos,1);
     }

   for(int x=ArraySize(array)-1;x>=0;x--)
     {
      string toCheck=array[x];
      if(toCheck=="x" || toCheck=="")
         continue;

      bool found=true;

      for(int pos=0;pos<len;pos++)
        {
         if(sc[pos]=="x" || sc[pos]=="X")
            continue;
         if(sc[pos]!=StringSubstr(toCheck,pos,1))
           {
            found=false;
            break;
           }
        }

      if(found)
        {
         int total=ArraySize(AlertArray);
         ArrayResize(AlertArray,total+1);
         AlertArray[total].price=NormalizeDouble(LevelArray[x],_Digits);
         AlertArray[total].description=toCheck;
        }
     }
  }
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
bool LogDelete(const string file_name)
  {
   if(FileIsExist(file_name,tester?FILE_COMMON:0))
     {
      if(!FileDelete(file_name,tester?FILE_COMMON:0))
        {
         printf("%s '%s'",ErrorDescription(_LastError),file_name);
         return(false);
        }
     }
   return(true);
  }
//+------------------------------------------------------------------+
bool LogRead(const string file_name,string &value)
  {
   int mode=FILE_SHARE_READ|FILE_READ|FILE_WRITE|FILE_TXT|FILE_ANSI;
   string path=TerminalInfoString(TERMINAL_DATA_PATH)+"\\"+(IsMQL5()?"MQL5":"MQL4")+"\\Files\\"+file_name;
   if(tester)
     {
      mode|=FILE_COMMON;
      path=TerminalInfoString(TERMINAL_COMMONDATA_PATH)+"\\Files\\"+file_name;
     }
//---
   int handle=FileOpen(file_name,mode);
   if(handle==INVALID_HANDLE)
     {
      printf("%s '%s'",ErrorDescription(_LastError),path);
      return(false);
     }
//---
   value=FileReadString(handle);
   FileClose(handle);
//---   
   return(true);
  }
//+------------------------------------------------------------------+
bool LogWrite(const string file_name,const string value)
  {
   int mode=FILE_SHARE_READ|FILE_READ|FILE_WRITE|FILE_TXT|FILE_ANSI;
   string path=TerminalInfoString(TERMINAL_DATA_PATH)+"\\"+(IsMQL5()?"MQL5":"MQL4")+"\\Files\\"+file_name;
   if(tester)
     {
      mode|=FILE_COMMON;
      path=TerminalInfoString(TERMINAL_COMMONDATA_PATH)+"\\Files\\"+file_name;
     }

   int handle=FileOpen(file_name,mode);
   if(handle==INVALID_HANDLE)
     {
      printf("%s '%s'",ErrorDescription(_LastError),path);
      return(false);
     }

   FileSeek(handle,0,SEEK_END);

   string line=value;
   if(FileSize(handle)>0)
      line="\n"+value;
//---
   if(FileWriteString(handle,line)<1)
     {
      printf("%s '%s'",ErrorDescription(_LastError),path);
      FileClose(handle);
      return(false);
     }

//---
   FileClose(handle);
   return(true);
  }
//+------------------------------------------------------------------+
bool LogCheckLevel(const string file_name,const double level)
  {

   int mode=FILE_SHARE_READ|FILE_READ|FILE_WRITE|FILE_TXT|FILE_ANSI;
   string path=TerminalInfoString(TERMINAL_DATA_PATH)+"\\"+(IsMQL5()?"MQL5":"MQL4")+"\\Files\\"+file_name;
   if(tester)
     {
      mode|=FILE_COMMON;
      path=TerminalInfoString(TERMINAL_COMMONDATA_PATH)+"\\Files\\"+file_name;
     }
//---
   int handle=FileOpen(file_name,mode);
   if(handle==INVALID_HANDLE)
     {
      printf("%s '%s'",ErrorDescription(_LastError),path);
      return(false);
     }
//--- dt
   string row=FileReadString(handle);
//---
   double log_level=0;
   while(!FileIsEnding(handle))
     {
      row=FileReadString(handle);
      log_level=StringToDouble(row);
      if(fabs(log_level-level)<_Point)
        {
         FileClose(handle);
         return(true);
        }
     }
   FileClose(handle);
   return(false);
  }
//+------------------------------------------------------------------+
void SendAlerts(string s)
  {
   string alert_text=PROGRAM_NAME+" "+_Symbol+s+TimeToString(TimeCurrent());
   if(SoundAlert)
     {
      PlaySound(SoundFile);
      EventSetTimer(Repeat);
      ButtonCreate();
     }

   if(PopUpAlert)
      Alert(alert_text);

   if(email)
      SendMail(PROGRAM_NAME+" "+_Symbol+s+"Alert",alert_text);

   if(Push)
      SendNotification(alert_text);
  }
//+------------------------------------------------------------------+
void OnTimer()
  {
   PlaySound(SoundFile);
  }
//+------------------------------------------------------------------+
void RemoveElement(int index,details &array[])
  {
   int total=ArraySize(array);

//--- invalid
   if(index>total-1 || index<0)
      return;

//--- last
   if(index==total-1)
     {
      ArrayResize(array,total-1);
      return;
     }

//--- middle
   for(int x=index; x<total-1; x++)
     {
      array[x].description=array[x+1].description;
      array[x].price=array[x+1].price;
     }
   ArrayResize(array,total-1);
  }
//+------------------------------------------------------------------+
//| Create the button                                                |
//+------------------------------------------------------------------+

bool ButtonCreate(const long              chart_ID=0,               // chart's ID
                  const string            name="Timer Stop",        // button name
                  const int               sub_window=0,             // subwindow index
                  const int               _x=20,                      // X coordinate
                  const int               _y=30,                      // Y coordinate
                  const int               _width=80,                 // button width
                  const int               _height=20,                // button height
                  const ENUM_BASE_CORNER  corner=CORNER_LEFT_UPPER,// chart corner for anchoring
                  const string            text="Stop Alert",// text
                  const string            font="Arial",             // font
                  const int               font_size=10,             // font size
                  const color             clr=clrBlack,             // text color
                  const color             back_clr=C'236,233,216',  // background color
                  const color             border_clr=clrNONE,       // border color
                  const bool              state=false,              // pressed/released
                  const bool              back=false,               // in the background
                  const bool              selection=false,          // highlight to move
                  const bool              hidden=false,// hidden in the object list
                  const long              z_order=10) // priority for mouse click
  {

   double PixelMultiplier=TerminalInfoInteger(TERMINAL_SCREEN_DPI)/96;

   int x=(int)(_x*PixelMultiplier);
   int y=(int)(_y*PixelMultiplier);
   int width=(int)(_width*PixelMultiplier);
   int height=(int)(_height*PixelMultiplier);
//--- reset the error value
   ResetLastError();
//--- create the button
   if(!ObjectCreate(chart_ID,name,OBJ_BUTTON,sub_window,0,0))
     {
      Print(__FUNCTION__,
            ": failed to create the button! Error code = ",GetLastError());
      return(false);
     }
//--- set button coordinates
   ObjectSetInteger(chart_ID,name,OBJPROP_XDISTANCE,x);
   ObjectSetInteger(chart_ID,name,OBJPROP_YDISTANCE,y);
//--- set button size
   ObjectSetInteger(chart_ID,name,OBJPROP_XSIZE,width);
   ObjectSetInteger(chart_ID,name,OBJPROP_YSIZE,height);
//--- set the chart's corner, relative to which point coordinates are defined
   ObjectSetInteger(chart_ID,name,OBJPROP_CORNER,corner);
//--- set the text
   ObjectSetString(chart_ID,name,OBJPROP_TEXT,text);
//--- set text font
   ObjectSetString(chart_ID,name,OBJPROP_FONT,font);
//--- set font size
   ObjectSetInteger(chart_ID,name,OBJPROP_FONTSIZE,font_size);
//--- set text color
   ObjectSetInteger(chart_ID,name,OBJPROP_COLOR,clr);
//--- set background color
   ObjectSetInteger(chart_ID,name,OBJPROP_BGCOLOR,back_clr);
//--- set border color
   ObjectSetInteger(chart_ID,name,OBJPROP_BORDER_COLOR,border_clr);
//--- display in the foreground (false) or background (true)
   ObjectSetInteger(chart_ID,name,OBJPROP_BACK,back);
//--- set button state
   ObjectSetInteger(chart_ID,name,OBJPROP_STATE,state);
//--- enable (true) or disable (false) the mode of moving the button by mouse
   ObjectSetInteger(chart_ID,name,OBJPROP_SELECTABLE,selection);
   ObjectSetInteger(chart_ID,name,OBJPROP_SELECTED,selection);
//--- hide (true) or display (false) graphical object name in the object list
   ObjectSetInteger(chart_ID,name,OBJPROP_HIDDEN,hidden);
//--- set the priority for receiving the event of a mouse click in the chart
   ObjectSetInteger(chart_ID,name,OBJPROP_ZORDER,z_order);
//--- successful execution
   return(true);
  }
//+------------------------------------------------------------------+
bool IsMQL5()
  {
#ifdef __MQL5__
   return(true);
#endif
   return(false);
  }
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
int TimeDayOfWeek(const datetime time)
  {
   MqlDateTime dt;
   TimeToStruct(time,dt);
   return dt.day_of_week;
  }

//+------------------------------------------------------------------+
