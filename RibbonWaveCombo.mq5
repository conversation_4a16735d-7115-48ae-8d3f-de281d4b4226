//+------------------------------------------------------------------+
//|                                              RibbonWaveCombo.mq5 |
//|                                                        Sakis-Pit |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Sakis-Pit"
#property link      ""
#property version   "1.00"
#property indicator_chart_window

#property strict
#property indicator_buffers 18
#property indicator_plots 18
#property indicator_style5 STYLE_SOLID
#property indicator_width5 2
#property indicator_color5 clrGray
#property indicator_style12 STYLE_SOLID
#property indicator_width12 2
#property indicator_color12 clrMaroon
#property indicator_style13 STYLE_SOLID
#property indicator_width13 4
#property indicator_color13 clrBlack
#property indicator_style14 STYLE_SOLID
#property indicator_width14 4
#property indicator_color14 clrBlack
#property indicator_style113 STYLE_SOLID
#property indicator_width113 4
#property indicator_color113 clrBlack
#property indicator_style114 STYLE_SOLID
#property indicator_width114 4
#property indicator_color114 clrBlack

#define Name MQLInfoString(MQL_PROGRAM_NAME) + "U"

double ma35[], ma40[], ma45[], ma50[], ma55[], ma60[], ma65[], ma70[], ma75[];
double d[], k[];
double realmid[];
double upperw[], lowerw[];

int m35, m40, m45, m50, m55, m60, m65, m70, m75;
int d1, k1;
int mid1, hig1, low1, ma2, ma2l, ma02l, ma25;

input int count = 10; // How many arrows to count for alert (+1)
input bool ribbon = false; // Show original ribbon & wavelet

double above[], below[];
double babove[], bbelow[];

//+INIT FUNCTION-----------------------------------------------------+
int OnInit()
{
	IndicatorSetString(INDICATOR_SHORTNAME,"Cribs");
	IndicatorSetInteger(INDICATOR_DIGITS,_Digits);
	
	SetIndexBuffer(0, ma35);
	PlotIndexSetString(0,PLOT_LABEL,"");
	SetIndexBuffer(1, ma40);
	PlotIndexSetString(1,PLOT_LABEL,"");
	SetIndexBuffer(2, ma45);
	PlotIndexSetString(2,PLOT_LABEL,"");
	SetIndexBuffer(3, ma50);
	PlotIndexSetString(3,PLOT_LABEL,"");
	SetIndexBuffer(4, ma55);
	PlotIndexSetString(4,PLOT_LABEL,"LWMA55");
	SetIndexBuffer(5, ma60);
	PlotIndexSetString(5,PLOT_LABEL,"");
	SetIndexBuffer(6, ma65);
	PlotIndexSetString(6,PLOT_LABEL,"");
	SetIndexBuffer(7, ma70);
	PlotIndexSetString(7,PLOT_LABEL,"");
	SetIndexBuffer(8, ma75);
	PlotIndexSetString(8,PLOT_LABEL,"");
	SetIndexBuffer(9, d);
	PlotIndexSetString(9,PLOT_LABEL,"d");
	SetIndexBuffer(10, k);
	PlotIndexSetString(10,PLOT_LABEL,"k");
	SetIndexBuffer(11, realmid);
	PlotIndexSetString(11,PLOT_LABEL,"realmid");
	SetIndexBuffer(12, upperw);
	SetIndexBuffer(13, lowerw);
	SetIndexBuffer(14, above);
	SetIndexBuffer(15, below);
	SetIndexBuffer(16, babove);
	SetIndexBuffer(17, bbelow);
	if (ribbon) {
		PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(3, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(4, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(5, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(6, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(7, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(8, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(9, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(10, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(11, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(12, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(13, PLOT_DRAW_TYPE, DRAW_LINE);
	}
	if (!ribbon) {
		PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(3, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(4, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(5, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(6, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(7, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(8, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(9, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(10, PLOT_DRAW_TYPE, DRAW_NONE);
		PlotIndexSetInteger(11, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(12, PLOT_DRAW_TYPE, DRAW_LINE);
		PlotIndexSetInteger(13, PLOT_DRAW_TYPE, DRAW_LINE);
	}
	
	m35 = iMA(_Symbol, PERIOD_CURRENT, 35, 0, MODE_LWMA, PRICE_CLOSE);
	m40 = iMA(_Symbol, PERIOD_CURRENT, 40, 0, MODE_LWMA, PRICE_CLOSE);
	m45 = iMA(_Symbol, PERIOD_CURRENT, 45, 0, MODE_LWMA, PRICE_CLOSE);
	m50 = iMA(_Symbol, PERIOD_CURRENT, 50, 0, MODE_LWMA, PRICE_CLOSE);
	m55 = iMA(_Symbol, PERIOD_CURRENT, 55, 0, MODE_LWMA, PRICE_CLOSE);
	m60 = iMA(_Symbol, PERIOD_CURRENT, 60, 0, MODE_LWMA, PRICE_CLOSE);
	m65 = iMA(_Symbol, PERIOD_CURRENT, 65, 0, MODE_LWMA, PRICE_CLOSE);
	m70 = iMA(_Symbol, PERIOD_CURRENT, 70, 0, MODE_LWMA, PRICE_CLOSE);
	m75 = iMA(_Symbol, PERIOD_CURRENT, 75, 0, MODE_LWMA, PRICE_CLOSE);
	
	d1 = iCustom(_Symbol, PERIOD_CURRENT, "WaveletTransAdd - Simple", 1);
	k1 = iCustom(_Symbol, PERIOD_CURRENT, "WaveletTransAdd - Simple", 0);
	
	mid1 = iCustom(_Symbol, PERIOD_CURRENT, "NewWave", 20, 50, 100, 200, MODE_LWMA, 0);
	hig1 = iCustom(_Symbol, PERIOD_CURRENT, "NewWave", 20, 50, 100, 200, MODE_LWMA, 1);
	low1 = iCustom(_Symbol, PERIOD_CURRENT, "NewWave", 20, 50, 100, 200, MODE_LWMA, 2);
	ma2 = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_TYPICAL);
	ma2l = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_LWMA, PRICE_CLOSE);
	ma02l = iMA(_Symbol, PERIOD_CURRENT, 200, 0, MODE_LWMA, PRICE_CLOSE);
   ma25 = iMA(_Symbol, PERIOD_CURRENT, 250, 0, MODE_EMA, PRICE_CLOSE);

	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+CALCULATE---------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	bool new_1s_check = false;
	static datetime start_1s_time = 0;
	if (start_1s_time < iTime(NULL, PERIOD_M1, 0))
	{
		new_1s_check = true;
		start_1s_time = iTime(NULL, PERIOD_M1, 0);
	}
	if (new_1s_check)
	{
      int periods;
      if (Bars(_Symbol,_Period) <= 2001) periods = Bars(_Symbol,_Period) - 1; else periods = 2000;
      ArraySetAsSeries(ma35, true); ArraySetAsSeries(ma40, true);
      ArraySetAsSeries(ma45, true); ArraySetAsSeries(ma50, true);
      ArraySetAsSeries(ma55, true); ArraySetAsSeries(ma60, true);
      ArraySetAsSeries(ma65, true); ArraySetAsSeries(ma70, true);
      ArraySetAsSeries(ma75, true);
      if(CopyBuffer(m35, 0, 0, periods, ma35) <= 0)
         Print("Failed to copy 35MA.");
      if(CopyBuffer(m40, 0, 0, periods, ma40) <= 0)
         Print("Failed to copy 40MA.");
      if(CopyBuffer(m45, 0, 0, periods, ma45) <= 0)
         Print("Failed to copy 45MA.");
      if(CopyBuffer(m50, 0, 0, periods, ma50) <= 0)
         Print("Failed to copy 50MA.");
      if(CopyBuffer(m55, 0, 0, periods, ma55) <= 0)
         Print("Failed to copy 55MA.");
      if(CopyBuffer(m60, 0, 0, periods, ma60) <= 0)
         Print("Failed to copy 60MA.");
      if(CopyBuffer(m65, 0, 0, periods, ma65) <= 0)
         Print("Failed to copy 65MA.");
      if(CopyBuffer(m70, 0, 0, periods, ma70) <= 0)
         Print("Failed to copy 70MA.");
      if(CopyBuffer(m75, 0, 0, periods, ma75) <= 0)
         Print("Failed to copy 75MA.");
   	new_1s_check = false;
	}

	bool new_1m_check = false;
	static datetime start_1m_time = 0;
	if (start_1m_time < iTime(NULL, ChartPeriod(), 0))
	{
		new_1m_check = true;
		start_1m_time = iTime(NULL, ChartPeriod(), 0);
	}
	if (new_1m_check)
	{
		ObjectsDeleteAll(0, Name);
		Ming();
		new_1m_check = false;
	}

	if (ChartPeriod() >= 60) {
		bool new_2m_check = false;
		static datetime start_2m_time = 0;
		if (start_2m_time < iTime(NULL, ChartPeriod(), 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_CURRENT, 0) + 25) && TimeCurrent() <= (iTime(NULL, PERIOD_CURRENT, 0) + 55)))
		{
			new_2m_check = true;
			start_2m_time = iTime(NULL, ChartPeriod(), 0);
		}
		if (new_2m_check)
		{
			alerts();
			new_2m_check = false;
		}
	}
	return(rates_total);
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION-----------------------------------------------------+
void Ming() {
   //int periods = Bars(_Symbol, PERIOD_CURRENT) - 1;
   int periods;
   if (Bars(_Symbol,_Period) <= 2001) periods = Bars(_Symbol,_Period) - 1; else periods = 2000;
   	
	ArrayInitialize(d, 0); ArrayInitialize(k, 0);
	ArraySetAsSeries(d, true); ArraySetAsSeries(k, true);
	if (CopyBuffer(d1, 0, 0, periods, d) <= 0 ||
	   CopyBuffer(k1, 1, 0, periods, k) <= 0)
	   Print("Failed to obtain wavelet values. ", GetLastError());

	double mid[];
	ArrayResize(mid, periods + 1);
	ArrayInitialize(mid, 0);
	ArraySetAsSeries(mid, true);
	
	double hignw[];
	ArrayResize(hignw, periods + 1);
	ArrayInitialize(hignw, 0);
	ArraySetAsSeries(hignw, true);
	
	double lownw[];
	ArrayResize(lownw, periods + 1);
	ArrayInitialize(lownw, 0);
	ArraySetAsSeries(lownw, true);
	
	double ma20[];
	ArrayResize(ma20, periods + 1);
	ArrayInitialize(ma20, 0);
	ArraySetAsSeries(ma20, true);
	
	ArrayInitialize(realmid, 0);
	ArraySetAsSeries(realmid, true);
	
	double ma20l[], ma200l[];
	ArrayResize(ma20l, periods + 1);
	ArrayInitialize(ma20l, 0);
	ArrayResize(ma200l, periods + 1);
	ArrayInitialize(ma200l, 0);
	ArraySetAsSeries(ma20l, true); ArraySetAsSeries(ma200l, true);
	
	if (CopyBuffer(mid1, 0, 0, periods + 1, mid) <= 0 ||
	   CopyBuffer(hig1, 1, 0, periods + 1, hignw) <= 0 ||
	   CopyBuffer(low1, 2, 0, periods + 1, lownw) <= 0)
	   Print("Failed to obtain newwave values. ", GetLastError());
	
	if (CopyBuffer(ma2, 0, 0, periods + 1, ma20) <= 0 ||
	   CopyBuffer(ma2l, 0, 0, periods + 1, ma20l) <= 0 ||
	   CopyBuffer(ma02l, 0, 0, periods + 1, ma200l) <= 0)
	   Print("Failed to copy data for ema20 or realmid. ", GetLastError());
	
	for (int x = periods; x >= 0; x--) {
	   realmid[x] = (ma20l[x] + ma200l[x]) / 2;
	}

	ArraySetAsSeries(above, true); ArraySetAsSeries(below, true);
	ArrayInitialize(above, 0); ArrayInitialize(below, 0);
	
	ArraySetAsSeries(babove, true); ArraySetAsSeries(bbelow, true);
	ArrayInitialize(babove, 0); ArrayInitialize(bbelow, 0);

	for (int x = periods; x >= 0; x--) {
		if (ma75[x] > ma35[x] && d[x] < ma75[x]) above[x] = d[x]; else above[x] = EMPTY_VALUE;
		if (ma75[x] < ma35[x] && k[x] > ma75[x]) below[x] = k[x]; else below[x] = EMPTY_VALUE;

		if (realmid[x] > mid[x]) bbelow[x] = k[x]; else bbelow[x] = EMPTY_VALUE;
		if (realmid[x] < mid[x]) babove[x] = d[x]; else babove[x] = EMPTY_VALUE;
	}

	string obname;
	for (int x = periods; x >= 0; x--) {
		if (babove[x] != EMPTY_VALUE && above[x] == EMPTY_VALUE) { obname = Name + "ArrWD" + IntegerToString(x); burnarr(obname, babove[x], x, 108, clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (bbelow[x] != EMPTY_VALUE && below[x] == EMPTY_VALUE) { obname = Name + "ArrWU" + IntegerToString(x); burnarr(obname, bbelow[x], x, 108, clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (babove[x] != EMPTY_VALUE && above[x] != EMPTY_VALUE) { obname = Name + "ArrD" + IntegerToString(x); burnarr(obname, above[x], x, 108, clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (bbelow[x] != EMPTY_VALUE && below[x] != EMPTY_VALUE) { obname = Name + "ArrU" + IntegerToString(x); burnarr(obname, below[x], x, 108, clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (above[x] != EMPTY_VALUE) { obname = Name + "ArrDD" + IntegerToString(x); burnarr(obname, above[x], x, 108, clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (below[x] != EMPTY_VALUE) { obname = Name + "ArrUU" + IntegerToString(x); burnarr(obname, below[x], x, 108, clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (above[x] != EMPTY_VALUE && bbelow[x] != EMPTY_VALUE) { obname = Name + "ArrBU" + IntegerToString(x); burnarr(obname, bbelow[x], x, 108, clrBlack); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (below[x] != EMPTY_VALUE && babove[x] != EMPTY_VALUE) { obname = Name + "ArrBD" + IntegerToString(x); burnarr(obname, babove[x], x, 108, clrBlack); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
	}

	for (int x = periods - 1; x >= 0; x--) {
		if (babove[x + 1] != EMPTY_VALUE && bbelow[x] != EMPTY_VALUE) { obname = Name + "XLINE" + IntegerToString(x); objtrend(obname, babove[x + 1], bbelow[x], x + 1, x, clrMagenta, "Xline " + IntegerToString(x)); }
		if (bbelow[x + 1] != EMPTY_VALUE && babove[x] != EMPTY_VALUE) { obname = Name + "XLINE" + IntegerToString(x); objtrend(obname, bbelow[x + 1], babove[x], x + 1, x, clrLime, "Xline " + IntegerToString(x)); }
	}

	bool doogie[], boogie[];
	ArrayResize(doogie, periods + 1); ArrayResize(boogie, periods + 1);
	bool soon[];
	ArrayResize(soon, iBars(_Symbol, PERIOD_D1));

	for (int x = periods - 3; x >= 1; x--) {
		if (below[x] == EMPTY_VALUE && bbelow[x] != EMPTY_VALUE) {
			for (int i = x + 3; i > x; i--) {
				if (below[i] != EMPTY_VALUE) {
					boogie[x] = true; continue;
				}
			}
		}
		if (above[x] == EMPTY_VALUE && babove[x] != EMPTY_VALUE) {
			for (int i = x + 3; i > x; i--) {
				if (above[i] != EMPTY_VALUE) {
					doogie[x] = true; continue;
				}
			}
		}
	}

	double wavepp[]; ArrayResize(wavepp, periods + 1); ArrayInitialize(wavepp, 0);
	double meanwave[]; ArrayResize(meanwave, periods + 1); ArrayInitialize(meanwave, 0);
	ArraySetAsSeries(wavepp, true); ArraySetAsSeries(meanwave, true);

	for (int x = periods - 60; x >= 0; x--) {
		for (int i = x + 60; i > x; i--)
			wavepp[x] += hignw[i] - lownw[i];
	}

	for (int x = periods - 60; x >= 0; x--) {
		meanwave[x] = wavepp[x] / 60;
	}
	
   ArrayInitialize(upperw, EMPTY_VALUE); ArrayInitialize(lowerw, EMPTY_VALUE);
   ArraySetAsSeries(upperw, true); ArraySetAsSeries(lowerw, true);
	for (int x = periods - 60; x >= 0; x--) {
		if ((hignw[x] - lownw[x]) < 0.66 * meanwave[x]) {
			for (int i = x + 60; i > x; i--) {
				upperw[x] = hignw[x] + 30 * _Point;
				lowerw[x] = lownw[x] - 30 * _Point;
			}
		}
	}
	
	double ma250[];
	ArrayResize(ma250, periods + 1);
	ArrayInitialize(ma250, 0);
	ArraySetAsSeries(ma250, true);
   if (CopyBuffer(ma25, 0, 0, periods, ma250) <= 0)
      Print("Copying EMA250 failed. ", GetLastError());
   
   
	for (int x = periods - 50; x >= 1; x--) {
		//Main Arrows for changed direction - Red / Blue
		if (below[x] != EMPTY_VALUE && babove[x] != EMPTY_VALUE && ObjectFind(0, Name + "XLINE" + IntegerToString(x)) == 0) { obname = Name + "ArrDW" + IntegerToString(x); burnarr(obname, iHigh(_Symbol, PERIOD_CURRENT, x) + 20 * _Point, x, 238, clrRed); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (above[x] != EMPTY_VALUE && bbelow[x] != EMPTY_VALUE && ObjectFind(0, Name + "XLINE" + IntegerToString(x)) == 0) { obname = Name + "ArrUW" + IntegerToString(x); burnarr(obname, iLow(_Symbol, PERIOD_CURRENT, x) - 20 * _Point, x, 236, clrBlue); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }

		//Change Dots to Green for retracements and re-enter - Retrace to LWMA55 or switch from red / blue dots to white
		if (boogie[x] == true && iLow(_Symbol, PERIOD_CURRENT, x) > mid[x]) { obname = Name + "ArrUNew" + IntegerToString(x); burnarr(obname, k[x], x, 108, clrLimeGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); ObjectDelete(0, Name + "ArrWU" + IntegerToString(x)); }
		if (below[x] != EMPTY_VALUE && iLow(_Symbol, PERIOD_CURRENT, x) < ma55[x] && iLow(_Symbol, PERIOD_CURRENT, x) > mid[x]) { obname = Name + "ArrUByMA" + IntegerToString(x); burnarr(obname, k[x], x, 108, clrLimeGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (doogie[x] == true && iHigh(_Symbol, PERIOD_CURRENT, x) < mid[x]) { obname = Name + "ArrDNew" + IntegerToString(x); burnarr(obname, d[x], x, 108, clrLimeGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); ObjectDelete(0, Name + "ArrWD" + IntegerToString(x)); }
		if (above[x] != EMPTY_VALUE && iHigh(_Symbol, PERIOD_CURRENT, x) > ma55[x] && iHigh(_Symbol, PERIOD_CURRENT, x) < mid[x]) { obname = Name + "ArrDByMA" + IntegerToString(x); burnarr(obname, d[x], x, 108, clrLimeGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }

		//Arrows to point continuation of strength after hard retrace - Low/High breaching red/blue dot (opposing wavelet)
		if (above[x] != EMPTY_VALUE && iHigh(_Symbol, PERIOD_CURRENT, x) > above[x]) { obname = Name + "ArrClU" + IntegerToString(x); burnarr(obname, d[x] + 20 * _Point, x, 234, clrMaroon); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (below[x] != EMPTY_VALUE && iLow(_Symbol, PERIOD_CURRENT, x) < below[x]) { obname = Name + "ArrClD" + IntegerToString(x); burnarr(obname, k[x] - 20 * _Point, x, 233, clrNavy); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }

		//Long term dots (Gold) - when high new wave crosses below ema250 and low new wave crosses above ema250
		if (lownw[x + 1] > ma250[x + 1] && lownw[x] < ma250[x]) { obname = Name + "ArrDLong" + IntegerToString(x); burnarr(obname, iHigh(_Symbol, PERIOD_CURRENT, x) + 20 * _Point, x, 174, clrGold); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (hignw[x + 1] < ma250[x + 1] && hignw[x] > ma250[x]) { obname = Name + "ArrULong" + IntegerToString(x); burnarr(obname, iLow(_Symbol, PERIOD_CURRENT, x) - 20 * _Point, x, 174, clrGold); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
	}
	
}
//+ALERTS------------------------------------------------------------+
void alerts() {
	int redarr[], reddot[], bluarr[], bludot[];
	ArrayResize(redarr, count + 1); ArrayResize(reddot, count + 1); ArrayResize(bluarr, count + 1); ArrayResize(bludot, count + 1);
	ArrayInitialize(redarr, 0); ArrayInitialize(reddot, 0); ArrayInitialize(bluarr, 0); ArrayInitialize(bludot, 0);

	string D1 = "", H4 = "";
	//if (iCustom(_Symbol, PERIOD_CURRENT, "NewWave", 20, 50, 100, 200, MODE_LWMA, 19, 0) == 1) 
	D1 = "UP";// else D1 = "DN";
	//if (iCustom(_Symbol, PERIOD_CURRENT, "NewWave", 20, 50, 100, 200, MODE_LWMA, 20, 0) == 1) 
	H4 = "UP";// else H4 = "DN";

	for (int x = 1; x <= count; x++) {
		if (ObjectFind(0, Name + "ArrDW" + IntegerToString(x)) == 0) redarr[x] = 1;
		if (ObjectFind(0, Name + "ArrD" + IntegerToString(x)) == 0) reddot[x] = 1;
		if (ObjectFind(0, Name + "ArrUW" + IntegerToString(x)) == 0) bluarr[x] = 1;
		if (ObjectFind(0, Name + "ArrU" + IntegerToString(x)) == 0) bludot[x] = 1;
	}

	for (int x = 1; x <= count; x++) {
		if (redarr[x] == 1 && ArrayMaximum(reddot) == 0) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " Red Arrow found " + IntegerToString(x + 1) + " candles away - D1: " + D1 + " H4: " + H4);
		if (redarr[x] == 1 && ArrayMaximum(reddot) == 1) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " Red Arrow found " + IntegerToString(x + 1) + " candles away + RED DOT - D1: " + D1 + " H4: " + H4);
		if (bluarr[x] == 1 && ArrayMaximum(bludot) == 0) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " Blue Arrow found " + IntegerToString(x + 1) + " candles away - D1: " + D1 + " H4: " + H4);
		if (bluarr[x] == 1 && ArrayMaximum(bludot) == 1) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " Blue Arrow found " + IntegerToString(x + 1) + " candles away + BLUE DOT - D1: " + D1 + " H4: " + H4);
	}

	if (reddot[1] == 1) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " RED DOT previous candle - D1: " + D1 + " H4: " + H4);
	if (bludot[1] == 1) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " BLUE DOT previous candle - D1: " + D1 + " H4: " + H4);
	if (ObjectFind(0, Name + "ArrDNew" + IntegerToString(1)) == 0 || ObjectFind(0, Name + "ArrDByMA" + IntegerToString(1)) == 0) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " RETRACE DN DOT printed - D1: " + D1 + " H4: " + H4);
	if (ObjectFind(0, Name + "ArrUNew" + IntegerToString(1)) == 0 || ObjectFind(0, Name + "ArrUByMA" + IntegerToString(1)) == 0) Alert(_Symbol + " " + TFToStr(ChartPeriod()) + " RETRACE UP DOT printed - D1: " + D1 + " H4: " + H4);
}
//+------------------------------------------------------------------+

//+CREATE ARROWS-----------------------------------------------------+
void burnarr(string name, double p, int t, int arrow, color col)
{

   datetime Time[];
   int counta = Bars(_Symbol, PERIOD_CURRENT);   // number of elements to copy
   ArraySetAsSeries(Time,true);
   CopyTime(_Symbol,_Period,0,counta,Time);

	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	ObjectSetInteger(0,name,OBJPROP_TIME,Time[t]);
	ObjectSetDouble(0,name,OBJPROP_PRICE,p);
	ObjectSetInteger(0,name,OBJPROP_ARROWCODE,arrow);
	ObjectSetInteger(0,name,OBJPROP_COLOR,col);
	ObjectSetInteger(0,name,OBJPROP_WIDTH,0);
	ObjectSetInteger(0,name,OBJPROP_BACK,false);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
string TFToStr(int tf)
// Converts a MT4-numeric timeframe to its descriptor string
// Usage:   string s=TFToStr(15) returns s="M15"
{
	switch (tf) {
	case     1:  return("M1");
	case     5:  return("M5");
	case    15:  return("M15");
	case    30:  return("M30");
	case    60:  return("H1");
	case   240:  return("H4");
	case  1440:  return("D1");
	case 10080:  return("W1");
	case 43200:  return("MN");
	}
	return("");
}
//+------------------------------------------------------------------+

//+TL CREATE---------------------------------------------------------+
void objtrend(string name, double pr1, double pr2, int t1, int t2, color col, string buls) {
	
   datetime Time[];
   int counta = Bars(_Symbol, PERIOD_CURRENT);   // number of elements to copy
   ArraySetAsSeries(Time,true);
   CopyTime(_Symbol,_Period,0,counta,Time);
   
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	ObjectSetInteger(0,name,OBJPROP_TIME,Time[t1]);
	ObjectSetInteger(0,name,OBJPROP_TIME,1,Time[t2]);
	ObjectSetDouble(0,name,OBJPROP_PRICE,pr1);
	ObjectSetDouble(0,name,OBJPROP_PRICE,1,pr2);
	ObjectSetInteger(0,name,OBJPROP_STYLE,0);
	ObjectSetInteger(0,name,OBJPROP_WIDTH,1);
	ObjectSetInteger(0,name,OBJPROP_RAY_RIGHT,false);
	ObjectSetInteger(0,name,OBJPROP_BACK,true);
	ObjectSetInteger(0,name,OBJPROP_COLOR,col);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, buls);
}
//+------------------------------------------------------------------+

//TO BE TRASHED IF NOT REUSED - *TESTING*

// By Francesco - only print arrows depending on depth of price over newwave
	/*
	bool uptoumid[]; ArrayResize(uptoumid, periods); ArrayInitialize(uptoumid, false);
	bool umidtolmid[]; ArrayResize(umidtolmid, periods); ArrayInitialize(umidtolmid, false);
	bool lmidtodn[]; ArrayResize(lmidtodn, periods); ArrayInitialize(lmidtodn, false);

	for (int x = periods - 50; x >= 1; x--) {
		if (Close[x + 1] > hignw[x + 1] && Close[x] < hignw[x]) uptoumid[x] = true;
		if (Close[x + 1] < hignw[x + 1] && Close[x + 1] > mid[x + 1] && Close[x] < mid[x]) umidtolmid[x] = true;
		if (Close[x + 1] < mid[x + 1] && Close[x + 1] > lownw[x + 1] && Close[x] < lownw[x]) lmidtodn[x] = true;
	}

	bool dntolmid[]; ArrayResize(dntolmid, periods); ArrayInitialize(dntolmid, false);
	bool lmidtoumid[]; ArrayResize(lmidtoumid, periods); ArrayInitialize(lmidtoumid, false);
	bool umidtoup[]; ArrayResize(umidtoup, periods); ArrayInitialize(umidtoup, false);

	for (int x = periods - 50; x >= 1; x--) {
		if (Close[x + 1] < lownw[x + 1] && Close[x] > lownw[x]) dntolmid[x] = true;
		if (Close[x + 1] > lownw[x + 1] && Close[x + 1] < mid[x + 1] && Close[x] > mid[x]) lmidtoumid[x] = true;
		if (Close[x + 1] > mid[x + 1] && Close[x + 1] < hignw[x + 1] && Close[x] > hignw[x]) umidtoup[x] = true;
	}
	*/

	/*
	for (int x = periods - 50; x >= 1; x--) {
			if (toon[x] == true && d[x + 1] > hignw[x + 1] && d[x] < hignw[x]) { obname = Name + "ArrDW" + IntegerToString(x); burnarr(obname, High[x + 1] + 10 * _Point, x + 1, 238, clrRed); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
			if (toon[x] == true && k[x + 1] < lownw[x + 1] && k[x] > lownw[x]) { obname = Name + "ArrUW" + IntegerToString(x); burnarr(obname, Low[x + 1] - 10 * _Point, x + 1, 236, clrBlue); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
	}
	*/

	//By Francesco - Breach blue/red dot but close above/below it
		/*
		if (above[x] != EMPTY_VALUE && High[x] > above[x] && Close[x] < above[x]) { obname = Name + "ArrUpF" + IntegerToString(x); burnarr(obname, Low[x] - 10 * _Point, x, 176, clrGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (below[x] != EMPTY_VALUE && Low[x] < below[x] && Close[x] > below[x]) { obname = Name + "ArrDnF" + IntegerToString(x); burnarr(obname, High[x] + 10 * _Point, x, 176, clrMagenta); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		*/

		//By sakis - Brown arrows when white to red / blue
			/*
			if (numean[x] == true && below[x + 1] == EMPTY_VALUE && bbelow[x + 1] != EMPTY_VALUE && below[x] != EMPTY_VALUE) { obname = Name + "ArrUMAu" + IntegerToString(x); burnarr(obname, Low[x] - 10 * _Point, x, 236, clrMaroon); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
			if (ndmean[x] == true && above[x + 1] == EMPTY_VALUE && babove[x + 1] != EMPTY_VALUE && above[x] != EMPTY_VALUE) { obname = Name + "ArrDMAd" + IntegerToString(x); burnarr(obname, High[x] + 10 * _Point, x, 238, clrMaroon); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
			*/

			//By sakis - Count of minimum candles inside blue / red area of newwave for testing on arrows
				/*
				int umean[]; ArrayResize(umean, periods + 1); ArrayInitialize(umean, 0);
				int dmean[]; ArrayResize(dmean, periods + 1); ArrayInitialize(dmean, 0);

				for (int x = periods; x >= 1; x--) {
					if (iCustom(_Symbol, PERIOD_CURRENT, "NewWave", 20, 50, 100, 200, MODE_LWMA, 0, x) > mid[x]) umean[x] = 1; else umean[x] = 0;
					if (iCustom(_Symbol, PERIOD_CURRENT, "NewWave", 20, 50, 100, 200, MODE_LWMA, 3, x) < mid[x]) dmean[x] = 1; else dmean[x] = 0;
				}

				int numean[]; ArrayResize(numean, periods + 1); ArrayInitialize(numean, 0);
				int ndmean[]; ArrayResize(ndmean, periods + 1); ArrayInitialize(ndmean, 0);

				for (int x = periods - 10; x >= 1; x--) {
					if (umean[ArrayMinimum(umean, 10, x)] == 0) numean[x] = 0; else numean[x] = 1;
					if (dmean[ArrayMinimum(dmean, 10, x)] == 0) ndmean[x] = 0; else ndmean[x] = 1;
				}
				*/

				//By sakis - Count by daily range filter - ie if previous day broke range of 3 days before it
				/*
				input bool countdown = true; // Enable D1 check for h1/h4 signals

					if (ChartPeriod() <= 1440) {
						if(countdown) {
							for (int x = iBars(_Symbol, PERIOD_D1) - 5; x >= 1; x--) {
								if ((High[iHighest(_Symbol, PERIOD_D1, MODE_HIGH, 3, x)] > iHigh(_Symbol, PERIOD_D1, x + 3) + (iHigh(_Symbol, PERIOD_D1, x + 3) - iLow(_Symbol, PERIOD_D1, x + 3))) || (Low[iLowest(_Symbol, PERIOD_D1, MODE_LOW, 3, x)] < iLow(_Symbol, PERIOD_D1, x + 3) - (iHigh(_Symbol, PERIOD_D1, x + 3) - iLow(_Symbol, PERIOD_D1, x + 3)))) soon[x] = true;
							}
						}
					}

					bool toon[]; ArrayResize(toon, periods); ArrayInitialize(toon, true);
					if (countdown) ArrayCopy(toon, soon, 0, 0, 0);
				*/

				//By Dani - original arrows
				/*
				input color whitey = clrWhite; // Color for main ribbon based arrow
				input bool showarrows = false; // Enable ribbon based arrows

					for (int x = periods - 50; x >= 1; x--) {
							if (showarrows && Close[x + 1] < mid[x + 1] && ma35[x + 2] > ma55[x + 2] && ma35[x + 1] < ma55[x + 1] && boogie[x] == true) { obname = Name + "ArrDnEnd" + IntegerToString(x); burnarr(obname, High[x + 1] + 10 * _Point, x + 1, 238, whitey); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
							if (showarrows && Close[x + 1] > mid[x + 1] && ma35[x + 2] < ma55[x + 2] && ma35[x + 1] > ma55[x + 1] && doogie[x] == true) { obname = Name + "ArrUpEnd" + IntegerToString(x); burnarr(obname, Low[x + 1] - 10 * _Point, x + 1, 236, whitey); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }

							if (showarrows && ma20[x + 2] < ma55[x + 2] && ma20[x + 1] > ma55[x + 1] && doogie[x] == true) { obname = Name + "ArrDnn" + IntegerToString(x); burnarr(obname, Low[x + 1] - 10 * _Point, x + 1, 236, clrBlue); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
							if (showarrows && ma20[x + 2] > ma55[x + 2] && ma20[x + 1] < ma55[x + 1] && boogie[x] == true) { obname = Name + "ArrUpn" + IntegerToString(x); burnarr(obname, High[x + 1] + 10 * _Point, x + 1, 238, clrBlue); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }

							if (showarrows && boogie[x] == true && Close[x + 2] > mid[x + 2] && Close[x + 1] < mid[x + 1]) { obname = Name + "ArrDCl" + IntegerToString(x); burnarr(obname, Low[x + 1] - 10 * _Point, x + 1, 251, clrRed); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
							if (showarrows && doogie[x] == true && Close[x + 2] < mid[x + 2] && Close[x + 1] > mid[x + 1]) { obname = Name + "ArrUCl" + IntegerToString(x); burnarr(obname, High[x + 1] + 10 * _Point, x + 1, 251, clrRed); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
					}
				*/