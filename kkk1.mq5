//+------------------------------------------------------------------+
//|                                                          kkk.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict

#property indicator_buffers    5
#property indicator_plots      3
#property indicator_separate_window
#property indicator_minimum    0
#property indicator_maximum    100

#define Name MQLInfoString(MQL_PROGRAM_NAME)

//RSI
#property indicator_type1  DRAW_LINE
#property indicator_color1 clrRed
#property indicator_style1 STYLE_SOLID
#property indicator_width1 1
#property indicator_label1 "RSI"
//A/D
#property indicator_type2  DRAW_LINE
#property indicator_color2 clrBlue
#property indicator_style2 STYLE_SOLID
#property indicator_width2 1
#property indicator_label2 "A/D"
//OBV
#property indicator_type3  DRAW_LINE
#property indicator_color3 clrGreen
#property indicator_style3 STYLE_SOLID
#property indicator_width3 1
#property indicator_label3 "OBV"

#property indicator_level1     10.0
#property indicator_level2     90.0
#property indicator_level3     28.0
#property indicator_level4     72.0
#property indicator_levelcolor clrWhite
#property indicator_levelstyle STYLE_DOT

//Inputs
input int rsi_period = 14; // RSI
input int rsi_buy = 28; // RSI buy
input int kkk_buy = 10; // KKK buy
input int rsi_sel = 72; // RSI sell
input int kkk_sel = 90; // KKK sell
extern int check = 2000; // Periods

//Buffers
double ExtRSIBuffer[];
double InpRSIBuffer[];
double rsi[];//, iad[], iobv[];
double signalbuy[], signalsel[];
double signalbuya[], signalsela[];

//Handles
int rsi_handle, iad_handle, obv_handle;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
	IndicatorSetString(INDICATOR_SHORTNAME,"RADIS");
	IndicatorSetInteger(INDICATOR_DIGITS, 2);
	
   Print("run");
   
   SetIndexBuffer(0, rsi, INDICATOR_DATA);
   SetIndexBuffer(1, ExtRSIBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(2, InpRSIBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(3, signalbuy, INDICATOR_CALCULATIONS);
   SetIndexBuffer(4, signalsel, INDICATOR_CALCULATIONS);
   SetIndexBuffer(5, signalbuya, INDICATOR_CALCULATIONS);
   SetIndexBuffer(6, signalsela, INDICATOR_CALCULATIONS);
   
   //Handles  
   rsi_handle = iRSI(_Symbol, PERIOD_CURRENT, rsi_period, PRICE_CLOSE);
   iad_handle = iAD(_Symbol, PERIOD_CURRENT, VOLUME_TICK);
   obv_handle = iOBV(_Symbol, PERIOD_CURRENT, VOLUME_TICK);
   
   Print("run2");
//---
   return(INIT_SUCCEEDED);
  }
//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
//---
   int periods = check;
   if (periods > BarsCalculated(rsi_handle)) periods = BarsCalculated(rsi_handle) - 2;
   else periods = check;
   Print("run3");
   int calculated=BarsCalculated(rsi_handle);
   if(calculated<rates_total)
     {
      Print("Not all data of RSI are calculated (",calculated," bars). Error ",GetLastError());
      return(0);
     }
   calculated=BarsCalculated(iad_handle);
   if(calculated<rates_total)
     {
      Print("Not all data of AD are calculated (",calculated," bars). Error ",GetLastError());
      return(0);
     }
   calculated=BarsCalculated(obv_handle);
   if(calculated<rates_total)
     {
      Print("Not all data of OBV are calculated (",calculated," bars). Error ",GetLastError());
      return(0);
     }
//--- we can copy not all data
   int to_copy;
   if(prev_calculated > rates_total || prev_calculated < 0)
      to_copy = rates_total;
   else
     {
      to_copy = rates_total - prev_calculated;
      if(prev_calculated > 0)
         to_copy++;
     }
//---
   double iad[];
   ArrayResize(iad, periods + 2);
   double riad[];
   ArrayResize(riad, periods + 2);
   double iar[];
   ArrayResize(iar, periods + 2);
   double riar[];
   ArrayResize(riar, periods + 2);
      
//--- the main loop of calculations
      if(CopyBuffer(rsi_handle, 0, 0, to_copy, rsi) < 0)
      {
      //--- if the copying fails, tell the error code
      PrintFormat("Failed to copy data from the iRSI indicator, error code %d",GetLastError());
      //--- quit with zero result - it means that the indicator is considered as not calculated
      return(false);
      }
      if(CopyBuffer(iad_handle, 0, 0, periods, iad) < 0)
      {
      //--- if the copying fails, tell the error code
      PrintFormat("Failed to copy data from the iAD indicator, error code %d",GetLastError());
      //--- quit with zero result - it means that the indicator is considered as not calculated
      return(false);
      }
      if(CopyBuffer(obv_handle, 0, 0, periods, iar) < 0)
      {
      //--- if the copying fails, tell the error code
      PrintFormat("Failed to copy data from the iOBV indicator, error code %d",GetLastError());
      //--- quit with zero result - it means that the indicator is considered as not calculated
      return(false);
      }
      		
      ArraySetAsSeries(rsi, true);
      ArraySetAsSeries(iad, true);
      ArraySetAsSeries(iar, true);
      ArraySetAsSeries(riad, true);
      ArraySetAsSeries(riar, true);
      
      double bop = iad[ArrayMinimum(iad, 0, 0)];
      double bon = iar[ArrayMinimum(iar, 0, 0)];
      
      /*
      //Real
      if (bop < 0)
      {
   		for (int i = 0; i < periods; i++)
   		{
            riad[i] = iad[i] + MathAbs(bop);
         }
      }
      else if (bop >= 0)      
      {
   		for (int i = 0; i < periods; i++)
   		{
            riad[i] = iad[i] - bop;
         }
      }*/
         
   		for (int i = 0; i < periods; i++)
   		{
            riad[i] = iad[i] + MathAbs(bop);
         }
         
         
      if (bon < 0)
      {
   		for (int i = 0; i < periods; i++)
   		{
            riar[i] = iar[i] + MathAbs(bon);
         }
      }
      else if (bon >= 0)      
      {
   		for (int i = 0; i < periods; i++)
   		{
            riar[i] = iar[i] - bon;
         }
      }
		
		ArraySetAsSeries(ExtRSIBuffer, true);
		ArraySetAsSeries(InpRSIBuffer, true);
            
		double mid = riad[ArrayMaximum(riad, 0, 0)];
		double mir = riar[ArrayMaximum(riar, 0, 0)];
		
		for (int i = 0; i < periods; i++)
		{
		   ExtRSIBuffer[i] = riad[i] / mid * 100;
		   InpRSIBuffer[i] = riar[i] / mir * 100;
		}
		
		double LD[];
		ArrayResize(LD, periods + 2);
		double HD[];
		ArrayResize(HD, periods + 2);
		if(CopyLow(_Symbol, PERIOD_CURRENT, 0, periods + 1, LD) <= 0) return(0);
		ArraySetAsSeries(LD, true);
		if(CopyHigh(_Symbol, PERIOD_CURRENT, 0, periods + 1, HD) <= 0) return(0);
		ArraySetAsSeries(HD, true);
		
		string obname;
		for (int i = 0; i < periods; i++)
		{
		   if (rsi[i + 1] < rsi_buy && rsi[i] > rsi_buy && ExtRSIBuffer[i] < kkk_buy && ExtRSIBuffer[i] > 0)
		   {
		      obname = Name + "ArrUp" + IntegerToString(i);
		      burnarr(obname, LD[i] - 10 * _Point, 233, i, clrWhite);
		      ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP);
		   }
		   if (rsi[i + 1] > rsi_sel && rsi[i] < rsi_sel && ExtRSIBuffer[i] > kkk_sel && ExtRSIBuffer[i] < 100)
		   {
		      obname = Name + "ArrDn" + IntegerToString(i);
		      burnarr(obname, HD[i] + 10 * _Point, 234, i, clrWhite);
		   }
		   if (rsi[i + 1] < rsi_buy && rsi[i] > rsi_buy && InpRSIBuffer[i] < kkk_buy && InpRSIBuffer[i] > 0)
		   {
		      obname = Name + "ArrOUp" + IntegerToString(i);
		      burnarr(obname, LD[i] - 20 * _Point, 233, i, clrYellow);
		      ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP);
		   }
		   if (rsi[i + 1] > rsi_sel && rsi[i] < rsi_sel && InpRSIBuffer[i] > kkk_sel && InpRSIBuffer[i] < 100)
		   {
		      obname = Name + "ArrODn" + IntegerToString(i);
		      burnarr(obname, HD[i] + 10 * _Point, 234, i, clrYellow);
		   }
		}
      
      ArraySetAsSeries(signalbuy, true);
      ArraySetAsSeries(signalsel, true);
      
		if (rsi[2] < rsi_buy && rsi[1] > rsi_buy && ExtRSIBuffer[1] < kkk_buy && ExtRSIBuffer[1] > 0)
		signalbuy[0] = 1;
		if (rsi[2] > rsi_sel && rsi[1] < rsi_sel && ExtRSIBuffer[1] > kkk_sel && ExtRSIBuffer[1] < 100)
		signalsel[0] = 1;
		
      ArraySetAsSeries(signalbuya, true);
      ArraySetAsSeries(signalsela, true);
      
		if (rsi[2] < rsi_buy && rsi[1] > rsi_buy && InpRSIBuffer[1] < kkk_buy && InpRSIBuffer[1] > 0)
		signalbuya[0] = 1;
		if (rsi[2] > rsi_sel && rsi[1] < rsi_sel && InpRSIBuffer[1] > kkk_sel && InpRSIBuffer[1] < 100)
		signalsela[0] = 1;
//---
//--- return value of prev_calculated for next call
   return(rates_total);
}
//+------------------------------------------------------------------+

//+ARROW CREATE------------------------------------------------------+
void burnarr(string name, double p, int arrow, int t, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
   datetime Time[];
   int count = 2 * check;   // number of elements to copy
   ArraySetAsSeries(Time,true);
   CopyTime(_Symbol,_Period,0,count,Time);
   
	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t]);
	ObjectSetDouble(0, name, OBJPROP_PRICE, p);
	ObjectSetInteger(0, name, OBJPROP_ARROWCODE, arrow);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
}
//+------------------------------------------------------------------+