//+------------------------------------------------------------------+
//|                                           Fractal Lines - 75%.mq5|
//|                                           Copyright 2019, Sakis  |
//|                                                                  |
//+------------------------------------------------------------------+

// Version History
// ---------------
// v1.0 Translated to MQL5

#property strict
#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"
#define Name MQLInfoString(MQL_PROGRAM_NAME)
#property indicator_buffers 2
#property indicator_plots 0

//+INPUTS------------------------------------------------------------+
extern int periods = 8000; // Candles back to check
input int drawlines = 8000; // Candles back to mark with trendlines
ENUM_TIMEFRAMES DROP_TF = PERIOD_CURRENT; // Check every X period
input double dist = 0.25; // Distance of wick vs previous body

double finp[];
double finn[];

//+------------------------------------------------------------------+

//+INIT--------------------------------------------------------------+
int OnInit()
{
	IndicatorSetString(INDICATOR_SHORTNAME, "FractalScalp");
	ObjectsDeleteAll(0, Name);
	SetIndexBuffer(0, finp);
	SetIndexBuffer(1, finn);

	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-init function								 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || MQLInfoInteger(MQL_TESTER))
		if (!MQLInfoInteger(MQL_TESTER))
		{
			DeleteObjects();
		}
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	//ObjectsDeleteAll(0, Name);
	for (int i = ObjectsTotal(0, 0) - 1; i >= 0; i--)
	{
		string ObName = ObjectName(0, i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(0, ObName);
		}
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	datetime expiry = D'2021.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("FractalScalp expired on " + TimeToString(expiry, TIME_DATE) + ", contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true) {

		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, DROP_TF, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, DROP_TF, 0);
		}
		if (new_1m_check)
		{
			checkpre();
			new_1m_check = false;
		}
	}//YesStop (expiry) end

	//---
	return(rates_total);
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION FRACTALS--------------------------------------------+
void checkpre() {
	if (Bars(_Symbol, PERIOD_CURRENT) < periods) periods = Bars(_Symbol, PERIOD_CURRENT) - 3;

	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	string obname;
	double nexp[], prep[];
	double nexn[], pren[];
	ArrayResize(prep, periods + 2); ArrayResize(nexp, periods + 1);
	ArrayResize(pren, periods + 2); ArrayResize(nexn, periods + 1);
	ArrayInitialize(finp, EMPTY_VALUE); ArrayInitialize(finn, EMPTY_VALUE);

	double CD1[], OD1[], HD1[], LD1[];
	datetime TD1[];
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(OD1, true);
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(TD1, true);
	ArrayResize(CD1, periods + 3);
	ArrayResize(OD1, periods + 3);
	ArrayResize(HD1, periods + 3);
	ArrayResize(LD1, periods + 3);
	ArrayResize(TD1, periods + 3);
	CopyClose(_Symbol, PERIOD_CURRENT, 0, periods + 3, CD1);
	CopyOpen(_Symbol, PERIOD_CURRENT, 0, periods + 3, OD1);
	CopyHigh(_Symbol, PERIOD_CURRENT, 0, periods + 3, HD1);
	CopyLow(_Symbol, PERIOD_CURRENT, 0, periods + 3, LD1);
	CopyTime(_Symbol, PERIOD_CURRENT, 0, periods + 3, TD1);

	for (int x = periods; x >= 1; x--)
	{
		if ((CD1[x + 1] - OD1[x + 1]) > 0 && (CD1[x] - OD1[x]) > 0)
		{
			prep[x] = CD1[x + 1] - OD1[x + 1];
			nexp[x] = OD1[x] - LD1[x];
			if (nexp[x] < dist * prep[x] && OD1[x + 1] + 0.75 * prep[x] < LD1[x] && CD1[ArrayMinimum(CD1, 1, x - 1)] >= LD1[x + 1])
				finp[x] = LD1[x] - 40 * _Point;
			else
				finp[x] = EMPTY_VALUE;
		}
	}

	for (int x = periods; x >= 1; x--)
	{
		if ((OD1[x + 1] - CD1[x + 1]) > 0 && (OD1[x] - CD1[x]) > 0)
		{
			pren[x] = OD1[x + 1] - CD1[x + 1];
			nexn[x] = HD1[x] - OD1[x];
			if (nexn[x] < dist * pren[x] && OD1[x + 1] - 0.75 * pren[x] > HD1[x] && CD1[ArrayMaximum(CD1, 1, x - 1)] <= HD1[x + 1])
				finn[x] = HD1[x] + 40 * _Point;
			else
				finn[x] = EMPTY_VALUE;
		}
	}

	for (int x = periods; x >= 1; x--) {
		string intrepl = "s" + TimeToString(TD1[x + 1], TIME_DATE | TIME_MINUTES);
		double LL = LD1[ArrayMinimum(LD1, 1, x - 1)];

		if (finp[x] != EMPTY_VALUE && x < drawlines && CD1[ArrayMinimum(CD1, 1, x - 1)] >= LD1[x + 1])
		{
			obname = Name + "Lin" + intrepl; objtrend2(obname, LD1[x + 1], LD1[x + 1], x + 1, 0, 3 * Period(), 2, 0, clrBlack, "LSup");
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, "LSup" + " Price: " + DoubleToString(LD1[x + 1], _Digits) + " - " + DoubleToString(OD1[x + 1], _Digits) + " Date: " + TimeToString(Time[x + 1], TIME_DATE | TIME_MINUTES));
			if (OD1[x + 1] >= LL)
			   { ObjectSetInteger(0, Name + "Lin" + intrepl, OBJPROP_COLOR, clrWhite); }
		}
		else if (finp[x] == EMPTY_VALUE)
		   { ObjectDelete(0, Name + "Lin" + intrepl); }
	}

	for (int x = periods; x >= 1; x--) {
		string intrepl = "b" + TimeToString(TD1[x + 1], TIME_DATE | TIME_MINUTES);
		double HH = HD1[ArrayMaximum(HD1, 1, x - 1)];

		if (finn[x] != EMPTY_VALUE && x < drawlines && CD1[ArrayMaximum(CD1, 1, x - 1)] <= HD1[x + 1])
		{
			obname = Name + "Lin" + intrepl; objtrend2(obname, HD1[x + 1], HD1[x + 1], x + 1, 0, 3 * Period(), 2, 0, clrBlack, "HRes");
			ObjectSetString(0, obname, OBJPROP_TOOLTIP, "HRes" + " Price: " + DoubleToString(HD1[x + 1], _Digits) + " - " + DoubleToString(OD1[x + 1], _Digits) + " Date: " + TimeToString(Time[x + 1], TIME_DATE | TIME_MINUTES));
			if (OD1[x + 1] <= HH)
			   { ObjectSetInteger(0, Name + "Lin" + intrepl, OBJPROP_COLOR, clrWhite); }
		}
		else if (finn[x] == EMPTY_VALUE)
		   { ObjectDelete(0, Name + "Lin" + intrepl); }
	}
}
//+------------------------------------------------------------------+

//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett) {
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	datetime Time[];
	int counta = Bars(_Symbol, PERIOD_CURRENT);// number of elements to copy
	ArraySetAsSeries(Time, true);
	CopyTime(_Symbol, _Period, 0, counta, Time);

	ObjectSetInteger(0, name, OBJPROP_TIME, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Time[0] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
	ObjectSetInteger(0, name, OBJPROP_STYLE, st);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
	ObjectSetInteger(0, name, OBJPROP_RAY, false);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToString(pr1, _Digits) + " Date: " + TimeToString(Time[t1], TIME_DATE));
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function 
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed"; break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed"; break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed"; break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed"; break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled"; break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart"; break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart"; break;
	default:text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+