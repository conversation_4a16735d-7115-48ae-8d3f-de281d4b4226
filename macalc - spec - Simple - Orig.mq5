//+------------------------------------------------------------------+
//|                                           MACalculator.mq4       |
//|                                           Copyright 2019, Sakis  |
//|                                                                  |
//+------------------------------------------------------------------+

// Version History
// ---------------
// v1.0 Made EMA calcs + build on top right corner

// TODO
// v1.x Make alerts when 1h & 30m emas are close to each other - within 10 pips
// v1.x Make visual presentation for 1-2-3 like WS indi
// v1.4 Changed to very simplified version of macalc

#property strict
#property indicator_chart_window
#define Name MQLInfoString(MQL_PROGRAM_NAME) + "U"
#property indicator_buffers 16
#property indicator_plots 9
//PLOTS
//MA20 LWMA L
#property indicator_type1  DRAW_LINE
#property indicator_color1 clrWhite
#property indicator_style1 STYLE_SOLID
#property indicator_width1 1
#property indicator_label1 "20 L L"
//MA20 LWMA H
#property indicator_type2  DRAW_LINE
#property indicator_color2 clrWhite
#property indicator_style2 STYLE_SOLID
#property indicator_width2 1
#property indicator_label2 "20 L H"
//MA20 EXP TYP
#property indicator_type3  DRAW_LINE
#property indicator_color3 clrBlueViolet
#property indicator_style3 STYLE_SOLID
#property indicator_width3 2
#property indicator_label3 "20 L/E"
//MA20 LWMA TYP
#property indicator_type4  DRAW_NONE
#property indicator_color4 clrBlack
#property indicator_style4 STYLE_SOLID
#property indicator_width4 2
#property indicator_label4 "20 LW"
//MA10 EMA TYP
#property indicator_type5  DRAW_LINE
#property indicator_color5 clrMagenta
#property indicator_style5 STYLE_SOLID
#property indicator_width5 2
#property indicator_label5 "10 EXP"
//MA300 EXP
#property indicator_type6  DRAW_LINE
#property indicator_color6 clrBlack
#property indicator_style6 STYLE_SOLID
#property indicator_width6 3
#property indicator_label6 "300 EXP"
//MA200 EXP
#property indicator_type7  DRAW_LINE
#property indicator_color7 clrRed
#property indicator_style7 STYLE_SOLID
#property indicator_width7 3
#property indicator_label7 "200 EXP"
//MA250 EXP
#property indicator_type8  DRAW_LINE
#property indicator_color8 clrAqua
#property indicator_style8 STYLE_SOLID
#property indicator_width8 3
#property indicator_label8 "250 EXP"
//MA50 EXP
#property indicator_type9  DRAW_LINE
#property indicator_color9 clrSienna
#property indicator_style9 STYLE_SOLID
#property indicator_width9 3
#property indicator_label9 "50 EXP"

//--- bars minimum for calculation
#define DATA_LIMIT 37

double MA200[], MA300[], MA250[], MA20L[], MA20WL[], MA20WLH[], MA20WLL[], MA20ELH[], MA20ELL[], MA20SLH[], MA20SLL[], MA20LL[], MA20HH[], MA10[], MA20WW[], MA50E[];
int M20WL, M20WH, M20EL, M20EH, M20SL, M20SH, M20S, M20E, M20W, M300, M200, M250, M10, M50E;

int howmany;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
	IndicatorSetInteger(INDICATOR_DIGITS,_Digits+1);

	SetIndexBuffer(0, MA20LL, INDICATOR_DATA);
	SetIndexBuffer(1, MA20HH, INDICATOR_DATA);
	SetIndexBuffer(2, MA20WW, INDICATOR_DATA);
	SetIndexBuffer(3, MA20WL, INDICATOR_DATA);
	SetIndexBuffer(4, MA10, INDICATOR_DATA);
	SetIndexBuffer(5, MA300, INDICATOR_DATA);
	SetIndexBuffer(6, MA200, INDICATOR_DATA);
	SetIndexBuffer(7, MA250, INDICATOR_DATA);
	SetIndexBuffer(8, MA50E, INDICATOR_DATA);
	SetIndexBuffer(9, MA20WLL, INDICATOR_DATA);
	SetIndexBuffer(10, MA20WLH, INDICATOR_DATA);
	SetIndexBuffer(11, MA20ELL, INDICATOR_DATA);
	SetIndexBuffer(12, MA20ELH, INDICATOR_DATA);
	SetIndexBuffer(13, MA20SLL, INDICATOR_DATA);
	SetIndexBuffer(14, MA20SLH, INDICATOR_DATA);
	SetIndexBuffer(15, MA20L, INDICATOR_DATA);
	
   
	M20WL = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_LWMA, PRICE_LOW);
	M20WH = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_LWMA, PRICE_HIGH);
	M20EL = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_LOW);
	M20EH = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_HIGH);
	M20SL = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_LOW);
	M20SH = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_HIGH);
	M20E = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_TYPICAL);
	M20W = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_LWMA, PRICE_TYPICAL);
	M20S = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_TYPICAL);
	M10 = iMA(Symbol(), PERIOD_CURRENT, 10, 0, MODE_EMA, PRICE_CLOSE);
	M300 = iMA(Symbol(), PERIOD_CURRENT, 300, 0, MODE_EMA, PRICE_CLOSE);
	M200 = iMA(Symbol(), PERIOD_CURRENT, 200, 0, MODE_EMA, PRICE_CLOSE);
	M250 = iMA(Symbol(), PERIOD_CURRENT, 250, 0, MODE_EMA, PRICE_CLOSE);
	M50E = iMA(Symbol(), PERIOD_CURRENT, 50, 0, MODE_EMA, PRICE_CLOSE);
	
	//---
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || MQLInfoInteger(MQL_TESTER))
		if (!MQLInfoInteger(MQL_TESTER))
		{
			DeleteObjects();
		}
	//Comment("");
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
//--- check for rates total
   if(rates_total<DATA_LIMIT)
      return(0); // not enough bars for calculation
//--- not all data may be calculated
   int calculated=BarsCalculated(M20W);
   if(calculated<rates_total)
     {
      Print("Not all data of LWMA 20 is calculated (",calculated,"bars ). Error",GetLastError());
      return(0);
     }
   calculated=BarsCalculated(M300);
   if(calculated<rates_total)
     {
      Print("Not all data of EXP 300 is calculated (",calculated,"bars ). Error",GetLastError());
      return(0);
     }
//--- we can copy not all data
   int to_copy;
   if(prev_calculated>rates_total || prev_calculated<0) to_copy=rates_total;
   else
     {
      to_copy=rates_total-prev_calculated;
      if(prev_calculated>0) to_copy++;
     }
//--- get LW20L buffer
   if(IsStopped()) return(0); //Checking for stop flag
   if(CopyBuffer(M20WL,0,0,to_copy,MA20WLL)<=0)
     {
      Print("Getting LWMA20L has failed! Error",GetLastError());
      return(0);
     }
//--- get LW20H buffer
   if(IsStopped()) return(0); //Checking for stop flag
   if(CopyBuffer(M20WH,0,0,to_copy,MA20WLH)<=0)
     {
      Print("Getting LWMA20H has failed! Error",GetLastError());
      return(0);
     }
//--- get EX20L buffer
   if(IsStopped()) return(0); //Checking for stop flag
   if(CopyBuffer(M20EL,0,0,to_copy,MA20ELL)<=0)
     {
      Print("Getting EMA20L has failed! Error",GetLastError());
      return(0);
     }
//--- get EX20H buffer
   if(IsStopped()) return(0); //Checking for stop flag
   if(CopyBuffer(M20EH,0,0,to_copy,MA20ELH)<=0)
     {
      Print("Getting EMA20H has failed! Error",GetLastError());
      return(0);
     }
//--- get S20L buffer
   if(IsStopped()) return(0); //Checking for stop flag
   if(CopyBuffer(M20SL,0,0,to_copy,MA20SLL)<=0)
     {
      Print("Getting SMA20L has failed! Error",GetLastError());
      return(0);
     }
//--- get S20H buffer
   if(IsStopped()) return(0); //Checking for stop flag
   if(CopyBuffer(M20SH,0,0,to_copy,MA20SLH)<=0)
     {
      Print("Getting SMA20H has failed! Error",GetLastError());
      return(0);
     }
//--- get EXP20 buffer
   if(IsStopped()) return(0); //Checking for stop flag
   if(CopyBuffer(M20E,0,0,to_copy,MA20L)<=0)
     {
      Print("Getting EMA20 has failed! Error",GetLastError());
      return(0);
     }
//--- get LW20 buffer
   if(IsStopped()) return(0); //Checking for stop flag
   if(CopyBuffer(M20W,0,0,to_copy,MA20WL)<=0)
     {
      Print("Getting LWMA20 has failed! Error",GetLastError());
      return(0);
     }
//--- get EX10 buffer
   if(IsStopped()) return(0); //Checking for stop flag
   if(CopyBuffer(M10,0,0,to_copy,MA10)<=0)
     {
      Print("Getting EMA10 has failed! Error",GetLastError());
      return(0);
     }
//--- get EXP300 buffer
   if(IsStopped()) return(0); //Checking for stop flag
   if(CopyBuffer(M300,0,0,to_copy,MA300)<=0)
     {
      Print("Getting EMA300 has failed! Error",GetLastError());
      return(0);
     }
//--- get EXP200 buffer
   if(IsStopped()) return(0); //Checking for stop flag
   if(CopyBuffer(M200,0,0,to_copy,MA200)<=0)
     {
      Print("Getting EMA200 has failed! Error",GetLastError());
      return(0);
     }
//--- get EXP250 buffer
   if(IsStopped()) return(0); //Checking for stop flag
   if(CopyBuffer(M250,0,0,to_copy,MA250)<=0)
     {
      Print("Getting EMA250 has failed! Error",GetLastError());
      return(0);
     }
//--- get EXP50 buffer
   if(IsStopped()) return(0); //Checking for stop flag
   if(CopyBuffer(M50E,0,0,to_copy,MA50E)<=0)
     {
      Print("Getting EMA50 has failed! Error",GetLastError());
      return(0);
     }
	
	bool new_1m_check = false;
	static datetime start_1m_time = 0;
	if (start_1m_time < iTime(NULL, ChartPeriod(), 0))
	{
		new_1m_check = true;
		start_1m_time = iTime(NULL, ChartPeriod(), 0);
	}
	if (new_1m_check)
	{
	   howmany = to_copy;
      fillbuff20();
      //Print("Did I run?");
		new_1m_check = false;
	}
	
	//--- return value of prev_calculated for next call
	return(rates_total);
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
   ObjectsDeleteAll(0, Name);
	/*for (int i = ObjectsTotal(0, 0) - 1; i >= 0; i--)
	{
		string ObName = ObjectName(0, i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(0, ObName);
		}
	}*/
}
//+------------------------------------------------------------------+

//+Fill past 20 edges & mid------------------------------------------+
void fillbuff20(){
   double MA20WHi[], MA20EHi[], MA20WLo[], MA20ELo[], MA20WCl[], MA20ECl[]; 
   ArrayResize(MA20WHi, howmany); ArrayResize(MA20EHi, howmany); ArrayResize(MA20WLo, howmany); ArrayResize(MA20ELo, howmany); ArrayResize(MA20WCl, howmany); ArrayResize(MA20ECl, howmany);
   if ((CopyBuffer(M20WH, 0, 0, howmany, MA20WHi) < 0) || (CopyBuffer(M20EH, 0, 0, howmany, MA20EHi) < 0) || (CopyBuffer(M20WL, 0, 0, howmany, MA20WLo) < 0) || (CopyBuffer(M20EL, 0, 0, howmany, MA20ELo) < 0) || (CopyBuffer(M20W, 0, 0, howmany, MA20WCl) < 0) || (CopyBuffer(M20E, 0, 0, howmany, MA20ECl) < 0)) Print("Copy Failed");
   ArraySetAsSeries(MA20WHi, true); ArraySetAsSeries(MA20EHi, true); ArraySetAsSeries(MA20WLo, true); ArraySetAsSeries(MA20ELo, true); ArraySetAsSeries(MA20WCl, true); ArraySetAsSeries(MA20ECl, true);
   ArraySetAsSeries(MA20LL, true); ArraySetAsSeries(MA20HH, true); ArraySetAsSeries(MA20WW, true);
   for (int x = 0; x <= howmany - 1; x++) {
      MA20LL[x] = (MA20WLo[x] + MA20ELo[x]) / 2;
      MA20HH[x] = (MA20WHi[x] + MA20EHi[x]) / 2;
      MA20WW[x] = (MA20WCl[x] + MA20ECl[x]) / 2;
      }
}   
//+------------------------------------------------------------------+

//+ChartEvent function-----------------------------------------------+
void OnChartEvent(const int id,
	const long &lparam,
	const double &dparam,
	const string &sparam)
{
	//---
	{//Switch autoscroll
		if (id == CHARTEVENT_KEYDOWN) {
			if (lparam == StringGetCharacter("E", 0)) { ChartSetInteger(0, CHART_AUTOSCROLL, false); }
			if (lparam == StringGetCharacter("Q", 0)) { ChartSetInteger(0, CHART_AUTOSCROLL, true); }
		}
	}
	{
		if (id == CHARTEVENT_KEYDOWN) {
			if (lparam == StringGetCharacter("O", 0)) { ObjectsDeleteAll(0, 0, -1); Comment(""); ChartApplyTemplate(0, "simple28.tpl"); }
			//if (lparam == StringGetChar("P", 0)) { ChartClose(0); }
		}
	}
	{//Switch TF by hand
		if (id == CHARTEVENT_KEYDOWN) {
			switch (int(lparam)) {
			case 97: ChartSetSymbolPeriod(0, _Symbol, PERIOD_M1); break;
			case 98: ChartSetSymbolPeriod(0, _Symbol, PERIOD_M5); break;
			case 99: ChartSetSymbolPeriod(0, _Symbol, PERIOD_M15); break;
			case 100: ChartSetSymbolPeriod(0, _Symbol, PERIOD_M30); break;
			case 101: ChartSetSymbolPeriod(0, _Symbol, PERIOD_H1); break;
			case 102: ChartSetSymbolPeriod(0, _Symbol, PERIOD_H4); break;
			case 103: ChartSetSymbolPeriod(0, _Symbol, PERIOD_D1); break;
			case 104: ChartSetSymbolPeriod(0, _Symbol, PERIOD_M2); break;
			case 105: ChartSetSymbolPeriod(0, _Symbol, PERIOD_H2); break;
			}
		}
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function 
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed"; break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed"; break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed"; break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed"; break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled"; break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart"; break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart"; break;
	default:text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+