//+------------------------------------------------------------------+
//|                                              RibbonWaveCombo.mq5 |
//|                                                        Sakis-Pit |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Sakis-Pit"
#property link      ""
#property version   "1.00"
#property indicator_chart_window

#property strict
#property indicator_buffers 18
#property indicator_plots 18

#define Name MQLInfoString(MQL_PROGRAM_NAME) + "U"

double ma35[], ma40[], ma45[], ma50[], ma55[], ma60[], ma65[], ma70[], ma75[];
double d[], k[];
double realmid[];
double upperw[], lowerw[];

int m35, m40, m45, m50, m55, m60, m65, m70, m75;
int d1, k1;
int mid1, hig1, low1, ma2, ma2l, ma02l, ma25;

input int count = 1; // How many arrows to count for alert (+1)
input bool ribbon = false; // Show original ribbon & wavelet

double above[], below[];
double babove[], bbelow[];

//+INIT FUNCTION-----------------------------------------------------+
int OnInit()
{
	IndicatorSetString(INDICATOR_SHORTNAME,"Cribs");
	IndicatorSetInteger(INDICATOR_DIGITS,_Digits);
	
	SetIndexBuffer(0, ma35);
	SetIndexBuffer(1, ma40);
	SetIndexBuffer(2, ma45);
	SetIndexBuffer(3, ma50);
	SetIndexBuffer(4, ma55);
	SetIndexBuffer(5, ma60);
	SetIndexBuffer(6, ma65);
	SetIndexBuffer(7, ma70);
	SetIndexBuffer(8, ma75);
	SetIndexBuffer(9, d);
	SetIndexBuffer(10, k);
	SetIndexBuffer(11, realmid);;
	SetIndexBuffer(12, upperw);
	SetIndexBuffer(13, lowerw);
	SetIndexBuffer(14, above);
	SetIndexBuffer(15, below);
	SetIndexBuffer(16, babove);
	SetIndexBuffer(17, bbelow);
	
	m35 = iMA(_Symbol, PERIOD_CURRENT, 35, 0, MODE_LWMA, PRICE_CLOSE);
	m40 = iMA(_Symbol, PERIOD_CURRENT, 40, 0, MODE_LWMA, PRICE_CLOSE);
	m45 = iMA(_Symbol, PERIOD_CURRENT, 45, 0, MODE_LWMA, PRICE_CLOSE);
	m50 = iMA(_Symbol, PERIOD_CURRENT, 50, 0, MODE_LWMA, PRICE_CLOSE);
	m55 = iMA(_Symbol, PERIOD_CURRENT, 55, 0, MODE_LWMA, PRICE_CLOSE);
	m60 = iMA(_Symbol, PERIOD_CURRENT, 60, 0, MODE_LWMA, PRICE_CLOSE);
	m65 = iMA(_Symbol, PERIOD_CURRENT, 65, 0, MODE_LWMA, PRICE_CLOSE);
	m70 = iMA(_Symbol, PERIOD_CURRENT, 70, 0, MODE_LWMA, PRICE_CLOSE);
	m75 = iMA(_Symbol, PERIOD_CURRENT, 75, 0, MODE_LWMA, PRICE_CLOSE);
	
	d1 = iCustom(_Symbol, PERIOD_CURRENT, "WaveletTransAdd - Simple", 1);
	k1 = iCustom(_Symbol, PERIOD_CURRENT, "WaveletTransAdd - Simple", 0);
	
	mid1 = iCustom(_Symbol, PERIOD_CURRENT, "NewWave", 20, 50, 100, 200, MODE_LWMA, 0);
	hig1 = iCustom(_Symbol, PERIOD_CURRENT, "NewWave", 20, 50, 100, 200, MODE_LWMA, 1);
	low1 = iCustom(_Symbol, PERIOD_CURRENT, "NewWave", 20, 50, 100, 200, MODE_LWMA, 2);
	ma2 = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_TYPICAL);
	ma2l = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_LWMA, PRICE_CLOSE);
	ma02l = iMA(_Symbol, PERIOD_CURRENT, 200, 0, MODE_LWMA, PRICE_CLOSE);
   ma25 = iMA(_Symbol, PERIOD_CURRENT, 250, 0, MODE_EMA, PRICE_CLOSE);

	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+CALCULATE---------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	bool new_1s_check = false;
	static datetime start_1s_time = 0;
	if (start_1s_time < iTime(NULL, PERIOD_M1, 0))
	{
		new_1s_check = true;
		start_1s_time = iTime(NULL, PERIOD_M1, 0);
	}
	if (new_1s_check)
	{
      int periods;
      if (Bars(_Symbol,_Period) <= 2001) periods = Bars(_Symbol,_Period) - 1; else periods = 2000;
      ArraySetAsSeries(ma35, true); ArraySetAsSeries(ma40, true);
      ArraySetAsSeries(ma45, true); ArraySetAsSeries(ma50, true);
      ArraySetAsSeries(ma55, true); ArraySetAsSeries(ma60, true);
      ArraySetAsSeries(ma65, true); ArraySetAsSeries(ma70, true);
      ArraySetAsSeries(ma75, true);
      if(CopyBuffer(m35, 0, 0, periods, ma35) <= 0)
         Print("Failed to copy 35MA.");
      if(CopyBuffer(m40, 0, 0, periods, ma40) <= 0)
         Print("Failed to copy 40MA.");
      if(CopyBuffer(m45, 0, 0, periods, ma45) <= 0)
         Print("Failed to copy 45MA.");
      if(CopyBuffer(m50, 0, 0, periods, ma50) <= 0)
         Print("Failed to copy 50MA.");
      if(CopyBuffer(m55, 0, 0, periods, ma55) <= 0)
         Print("Failed to copy 55MA.");
      if(CopyBuffer(m60, 0, 0, periods, ma60) <= 0)
         Print("Failed to copy 60MA.");
      if(CopyBuffer(m65, 0, 0, periods, ma65) <= 0)
         Print("Failed to copy 65MA.");
      if(CopyBuffer(m70, 0, 0, periods, ma70) <= 0)
         Print("Failed to copy 70MA.");
      if(CopyBuffer(m75, 0, 0, periods, ma75) <= 0)
         Print("Failed to copy 75MA.");
   	new_1s_check = false;
	}

	bool new_1m_check = false;
	static datetime start_1m_time = 0;
	if (start_1m_time < iTime(NULL, ChartPeriod(), 0))
	{
		new_1m_check = true;
		start_1m_time = iTime(NULL, ChartPeriod(), 0);
	}
	if (new_1m_check)
	{
		ObjectsDeleteAll(0, Name);
		Ming();
		new_1m_check = false;
	}
	return(rates_total);
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION-----------------------------------------------------+
void Ming() {
   //int periods = Bars(_Symbol, PERIOD_CURRENT) - 1;
   int periods;
   if (Bars(_Symbol,_Period) <= 2001) periods = Bars(_Symbol,_Period) - 1; else periods = 2000;
   	
	ArrayInitialize(d, 0); ArrayInitialize(k, 0);
	ArraySetAsSeries(d, true); ArraySetAsSeries(k, true);
	if (CopyBuffer(d1, 0, 0, periods, d) <= 0 ||
	   CopyBuffer(k1, 1, 0, periods, k) <= 0)
	   Print("Failed to obtain wavelet values. ", GetLastError());

	double mid[];
	ArrayResize(mid, periods + 1);
	ArrayInitialize(mid, 0);
	ArraySetAsSeries(mid, true);
	
	double hignw[];
	ArrayResize(hignw, periods + 1);
	ArrayInitialize(hignw, 0);
	ArraySetAsSeries(hignw, true);
	
	double lownw[];
	ArrayResize(lownw, periods + 1);
	ArrayInitialize(lownw, 0);
	ArraySetAsSeries(lownw, true);
	
	double ma20[];
	ArrayResize(ma20, periods + 1);
	ArrayInitialize(ma20, 0);
	ArraySetAsSeries(ma20, true);
	
	ArrayInitialize(realmid, 0);
	ArraySetAsSeries(realmid, true);
	
	double ma20l[], ma200l[];
	ArrayResize(ma20l, periods + 1);
	ArrayInitialize(ma20l, 0);
	ArrayResize(ma200l, periods + 1);
	ArrayInitialize(ma200l, 0);
	ArraySetAsSeries(ma20l, true); ArraySetAsSeries(ma200l, true);
	
	if (CopyBuffer(mid1, 0, 0, periods + 1, mid) <= 0 ||
	   CopyBuffer(hig1, 1, 0, periods + 1, hignw) <= 0 ||
	   CopyBuffer(low1, 2, 0, periods + 1, lownw) <= 0)
	   Print("Failed to obtain newwave values. ", GetLastError());
	
	if (CopyBuffer(ma2, 0, 0, periods + 1, ma20) <= 0 ||
	   CopyBuffer(ma2l, 0, 0, periods + 1, ma20l) <= 0 ||
	   CopyBuffer(ma02l, 0, 0, periods + 1, ma200l) <= 0)
	   Print("Failed to copy data for ema20 or realmid. ", GetLastError());
	
	for (int x = periods; x >= 0; x--) {
	   realmid[x] = (ma20l[x] + ma200l[x]) / 2;
	}

	ArraySetAsSeries(above, true); ArraySetAsSeries(below, true);
	ArrayInitialize(above, 0); ArrayInitialize(below, 0);
	
	ArraySetAsSeries(babove, true); ArraySetAsSeries(bbelow, true);
	ArrayInitialize(babove, 0); ArrayInitialize(bbelow, 0);

	for (int x = periods; x >= 0; x--) {
		if (ma75[x] > ma35[x] && d[x] < ma75[x]) above[x] = d[x]; else above[x] = EMPTY_VALUE;
		if (ma75[x] < ma35[x] && k[x] > ma75[x]) below[x] = k[x]; else below[x] = EMPTY_VALUE;

		if (realmid[x] > mid[x]) bbelow[x] = k[x]; else bbelow[x] = EMPTY_VALUE;
		if (realmid[x] < mid[x]) babove[x] = d[x]; else babove[x] = EMPTY_VALUE;
	}
	
   /*
	string obname;
	for (int x = periods; x >= 0; x--) {
		if (babove[x] != EMPTY_VALUE && above[x] == EMPTY_VALUE) { obname = Name + "ArrWD" + IntegerToString(x); burnarr(obname, babove[x], x, 108, clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (bbelow[x] != EMPTY_VALUE && below[x] == EMPTY_VALUE) { obname = Name + "ArrWU" + IntegerToString(x); burnarr(obname, bbelow[x], x, 108, clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (babove[x] != EMPTY_VALUE && above[x] != EMPTY_VALUE) { obname = Name + "ArrD" + IntegerToString(x); burnarr(obname, above[x], x, 108, clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (bbelow[x] != EMPTY_VALUE && below[x] != EMPTY_VALUE) { obname = Name + "ArrU" + IntegerToString(x); burnarr(obname, below[x], x, 108, clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (above[x] != EMPTY_VALUE) { obname = Name + "ArrDD" + IntegerToString(x); burnarr(obname, above[x], x, 108, clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (below[x] != EMPTY_VALUE) { obname = Name + "ArrUU" + IntegerToString(x); burnarr(obname, below[x], x, 108, clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (above[x] != EMPTY_VALUE && bbelow[x] != EMPTY_VALUE) { obname = Name + "ArrBU" + IntegerToString(x); burnarr(obname, bbelow[x], x, 108, clrBlack); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (below[x] != EMPTY_VALUE && babove[x] != EMPTY_VALUE) { obname = Name + "ArrBD" + IntegerToString(x); burnarr(obname, babove[x], x, 108, clrBlack); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
	}
   */
   string obname;
	for (int x = periods - 1; x >= 0; x--) {
		if (babove[x + 1] != EMPTY_VALUE && bbelow[x] != EMPTY_VALUE) { obname = Name + "XLINE" + IntegerToString(x); objtrend(obname, babove[x + 1], bbelow[x], x + 1, x, clrMagenta, "Xline " + IntegerToString(x)); }
		if (bbelow[x + 1] != EMPTY_VALUE && babove[x] != EMPTY_VALUE) { obname = Name + "XLINE" + IntegerToString(x); objtrend(obname, bbelow[x + 1], babove[x], x + 1, x, clrLime, "Xline " + IntegerToString(x)); }
	}
	

   /*
	bool doogie[], boogie[];
	ArrayResize(doogie, periods + 1); ArrayResize(boogie, periods + 1);
	bool soon[];
	ArrayResize(soon, iBars(_Symbol, PERIOD_D1));

	for (int x = periods - 3; x >= 1; x--) {
		if (below[x] == EMPTY_VALUE && bbelow[x] != EMPTY_VALUE) {
			for (int i = x + 3; i > x; i--) {
				if (below[i] != EMPTY_VALUE) {
					boogie[x] = true; continue;
				}
			}
		}
		if (above[x] == EMPTY_VALUE && babove[x] != EMPTY_VALUE) {
			for (int i = x + 3; i > x; i--) {
				if (above[i] != EMPTY_VALUE) {
					doogie[x] = true; continue;
				}
			}
		}
	}
	*/

	double wavepp[]; ArrayResize(wavepp, periods + 1); ArrayInitialize(wavepp, 0);
	double meanwave[]; ArrayResize(meanwave, periods + 1); ArrayInitialize(meanwave, 0);
	ArraySetAsSeries(wavepp, true); ArraySetAsSeries(meanwave, true);

	for (int x = periods - 60; x >= 0; x--) {
		for (int i = x + 60; i > x; i--)
			wavepp[x] += hignw[i] - lownw[i];
	}

	for (int x = periods - 60; x >= 0; x--) {
		meanwave[x] = wavepp[x] / 60;
	}
	
   ArrayInitialize(upperw, EMPTY_VALUE); ArrayInitialize(lowerw, EMPTY_VALUE);
   ArraySetAsSeries(upperw, true); ArraySetAsSeries(lowerw, true);
	for (int x = periods - 60; x >= 0; x--) {
		if ((hignw[x] - lownw[x]) < 0.66 * meanwave[x]) {
			for (int i = x + 60; i > x; i--) {
				upperw[x] = hignw[x] + 30 * _Point;
				lowerw[x] = lownw[x] - 30 * _Point;
			}
		}
	}
		for (int x = periods - 50; x >= 1; x--) {
		//Main Arrows for changed direction - Red / Blue
		if (below[x] != EMPTY_VALUE && babove[x] != EMPTY_VALUE && ObjectFind(0, Name + "XLINE" + IntegerToString(x)) == 0) { obname = Name + "ArrDW" + IntegerToString(x); burnarr(obname, iHigh(_Symbol, PERIOD_CURRENT, x) + 20 * _Point, x, 238, clrRed); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (above[x] != EMPTY_VALUE && bbelow[x] != EMPTY_VALUE && ObjectFind(0, Name + "XLINE" + IntegerToString(x)) == 0) { obname = Name + "ArrUW" + IntegerToString(x); burnarr(obname, iLow(_Symbol, PERIOD_CURRENT, x) - 20 * _Point, x, 236, clrBlue); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
      }
	/*
	double ma250[];
	ArrayResize(ma250, periods + 1);
	ArrayInitialize(ma250, 0);
	ArraySetAsSeries(ma250, true);
   if (CopyBuffer(ma25, 0, 0, periods, ma250) <= 0)
      Print("Copying EMA250 failed. ", GetLastError());
   
   
	for (int x = periods - 50; x >= 1; x--) {
		//Main Arrows for changed direction - Red / Blue
		if (below[x] != EMPTY_VALUE && babove[x] != EMPTY_VALUE && ObjectFind(0, Name + "XLINE" + IntegerToString(x)) == 0) { obname = Name + "ArrDW" + IntegerToString(x); burnarr(obname, iHigh(_Symbol, PERIOD_CURRENT, x) + 20 * _Point, x, 238, clrRed); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (above[x] != EMPTY_VALUE && bbelow[x] != EMPTY_VALUE && ObjectFind(0, Name + "XLINE" + IntegerToString(x)) == 0) { obname = Name + "ArrUW" + IntegerToString(x); burnarr(obname, iLow(_Symbol, PERIOD_CURRENT, x) - 20 * _Point, x, 236, clrBlue); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }

		//Change Dots to Green for retracements and re-enter - Retrace to LWMA55 or switch from red / blue dots to white
		if (boogie[x] == true && iLow(_Symbol, PERIOD_CURRENT, x) > mid[x]) { obname = Name + "ArrUNew" + IntegerToString(x); burnarr(obname, k[x], x, 108, clrLimeGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); ObjectDelete(0, Name + "ArrWU" + IntegerToString(x)); }
		if (below[x] != EMPTY_VALUE && iLow(_Symbol, PERIOD_CURRENT, x) < ma55[x] && iLow(_Symbol, PERIOD_CURRENT, x) > mid[x]) { obname = Name + "ArrUByMA" + IntegerToString(x); burnarr(obname, k[x], x, 108, clrLimeGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (doogie[x] == true && iHigh(_Symbol, PERIOD_CURRENT, x) < mid[x]) { obname = Name + "ArrDNew" + IntegerToString(x); burnarr(obname, d[x], x, 108, clrLimeGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); ObjectDelete(0, Name + "ArrWD" + IntegerToString(x)); }
		if (above[x] != EMPTY_VALUE && iHigh(_Symbol, PERIOD_CURRENT, x) > ma55[x] && iHigh(_Symbol, PERIOD_CURRENT, x) < mid[x]) { obname = Name + "ArrDByMA" + IntegerToString(x); burnarr(obname, d[x], x, 108, clrLimeGreen); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }

		//Arrows to point continuation of strength after hard retrace - Low/High breaching red/blue dot (opposing wavelet)
		if (above[x] != EMPTY_VALUE && iHigh(_Symbol, PERIOD_CURRENT, x) > above[x]) { obname = Name + "ArrClU" + IntegerToString(x); burnarr(obname, d[x] + 20 * _Point, x, 234, clrMaroon); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (below[x] != EMPTY_VALUE && iLow(_Symbol, PERIOD_CURRENT, x) < below[x]) { obname = Name + "ArrClD" + IntegerToString(x); burnarr(obname, k[x] - 20 * _Point, x, 233, clrNavy); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }

		//Long term dots (Gold) - when high new wave crosses below ema250 and low new wave crosses above ema250
		if (lownw[x + 1] > ma250[x + 1] && lownw[x] < ma250[x]) { obname = Name + "ArrDLong" + IntegerToString(x); burnarr(obname, iHigh(_Symbol, PERIOD_CURRENT, x) + 20 * _Point, x, 174, clrGold); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM); }
		if (hignw[x + 1] < ma250[x + 1] && hignw[x] > ma250[x]) { obname = Name + "ArrULong" + IntegerToString(x); burnarr(obname, iLow(_Symbol, PERIOD_CURRENT, x) - 20 * _Point, x, 174, clrGold); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
	}
	*/	
}

//+CREATE ARROWS-----------------------------------------------------+
void burnarr(string name, double p, int t, int arrow, color col)
{

   datetime Time[];
   int counta = Bars(_Symbol, PERIOD_CURRENT);   // number of elements to copy
   ArraySetAsSeries(Time,true);
   CopyTime(_Symbol,_Period,0,counta,Time);

	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	ObjectSetInteger(0,name,OBJPROP_TIME,Time[t]);
	ObjectSetDouble(0,name,OBJPROP_PRICE,p);
	ObjectSetInteger(0,name,OBJPROP_ARROWCODE,arrow);
	ObjectSetInteger(0,name,OBJPROP_COLOR,col);
	ObjectSetInteger(0,name,OBJPROP_WIDTH,0);
	ObjectSetInteger(0,name,OBJPROP_BACK,false);
}
//+------------------------------------------------------------------+

//+TL CREATE---------------------------------------------------------+
void objtrend(string name, double pr1, double pr2, int t1, int t2, color col, string buls) {
	
   datetime Time[];
   int counta = Bars(_Symbol, PERIOD_CURRENT);   // number of elements to copy
   ArraySetAsSeries(Time,true);
   CopyTime(_Symbol,_Period,0,counta,Time);
   
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	ObjectSetInteger(0,name,OBJPROP_TIME,Time[t1]);
	ObjectSetInteger(0,name,OBJPROP_TIME,1,Time[t2]);
	ObjectSetDouble(0,name,OBJPROP_PRICE,pr1);
	ObjectSetDouble(0,name,OBJPROP_PRICE,1,pr2);
	ObjectSetInteger(0,name,OBJPROP_STYLE,0);
	ObjectSetInteger(0,name,OBJPROP_WIDTH,1);
	ObjectSetInteger(0,name,OBJPROP_RAY_RIGHT,false);
	ObjectSetInteger(0,name,OBJPROP_BACK,true);
	ObjectSetInteger(0,name,OBJPROP_COLOR,col);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, buls);
}
//+------------------------------------------------------------------+